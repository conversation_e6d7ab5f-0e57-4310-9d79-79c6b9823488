//! 重试策略模块
//!
//! 提供统一的重试策略和重试逻辑，将重试决策从错误类型中分离出来。
//!
//! ## 设计原则
//!
//! - **分离关注点**: 错误类型只描述"什么出错了"，重试策略决定"如何处理"
//! - **灵活配置**: 支持不同业务场景的重试策略
//! - **可观测性**: 提供重试过程的监控和日志
//! - **性能优化**: 避免无意义的重试，减少系统负载

use std::time::Duration;
use serde::{Deserialize, Serialize};
use crate::error::{SigmaXError, ErrorCategory};

/// 重试策略配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetryPolicy {
    /// 最大重试次数
    pub max_attempts: u32,
    /// 基础延迟时间
    pub base_delay: Duration,
    /// 最大延迟时间
    pub max_delay: Duration,
    /// 退避倍数
    pub backoff_multiplier: f64,
    /// 是否启用抖动
    pub enable_jitter: bool,
}

impl Default for RetryPolicy {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            base_delay: Duration::from_millis(1000),
            max_delay: Duration::from_secs(30),
            backoff_multiplier: 2.0,
            enable_jitter: true,
        }
    }
}

impl RetryPolicy {
    /// 创建保守的重试策略（用于关键业务）
    pub fn conservative() -> Self {
        Self {
            max_attempts: 2,
            base_delay: Duration::from_millis(2000),
            max_delay: Duration::from_secs(10),
            backoff_multiplier: 1.5,
            enable_jitter: false,
        }
    }

    /// 创建激进的重试策略（用于非关键业务）
    pub fn aggressive() -> Self {
        Self {
            max_attempts: 5,
            base_delay: Duration::from_millis(500),
            max_delay: Duration::from_secs(60),
            backoff_multiplier: 2.5,
            enable_jitter: true,
        }
    }

    /// 创建限流专用重试策略
    pub fn rate_limit() -> Self {
        Self {
            max_attempts: 3,
            base_delay: Duration::from_secs(60), // 限流错误等待更久
            max_delay: Duration::from_secs(300),
            backoff_multiplier: 1.0, // 固定延迟
            enable_jitter: true,
        }
    }

    /// 判断是否应该重试
    pub fn should_retry(&self, error: &SigmaXError, attempt: u32) -> bool {
        // 超过最大重试次数
        if attempt >= self.max_attempts {
            return false;
        }

        // 根据错误分类决定是否重试
        match error.category() {
            ErrorCategory::Transient => true,
            ErrorCategory::RateLimit => true,
            ErrorCategory::Permanent => false,
            ErrorCategory::Unknown => attempt < 2, // 未知错误只重试一次
        }
    }

    /// 计算重试延迟时间
    pub fn delay_for_attempt(&self, error: &SigmaXError, attempt: u32) -> Duration {
        let base_delay = match error.category() {
            ErrorCategory::RateLimit => Duration::from_secs(60), // 限流错误等待更久
            _ => self.base_delay,
        };

        // 指数退避
        let delay_ms = base_delay.as_millis() as f64 * 
                      self.backoff_multiplier.powi(attempt as i32);

        // 限制最大延迟
        let delay_ms = delay_ms.min(self.max_delay.as_millis() as f64);

        // 添加抖动避免惊群效应
        let final_delay_ms = if self.enable_jitter {
            let jitter = fastrand::f64() * 0.1; // ±10% 抖动
            delay_ms * (1.0 + jitter - 0.05)
        } else {
            delay_ms
        };

        Duration::from_millis(final_delay_ms.max(0.0) as u64)
    }

    /// 获取人类可读的策略描述
    pub fn description(&self) -> String {
        format!(
            "最多重试{}次，基础延迟{}ms，最大延迟{}s，退避倍数{}{}",
            self.max_attempts,
            self.base_delay.as_millis(),
            self.max_delay.as_secs(),
            self.backoff_multiplier,
            if self.enable_jitter { "，启用抖动" } else { "" }
        )
    }
}

/// 重试上下文 - 记录重试过程信息
#[derive(Debug, Clone)]
pub struct RetryContext {
    /// 当前重试次数
    pub attempt: u32,
    /// 总耗时
    pub total_duration: Duration,
    /// 上次错误
    pub last_error: Option<SigmaXError>,
    /// 重试历史
    pub retry_history: Vec<RetryAttempt>,
}

/// 单次重试记录
#[derive(Debug, Clone)]
pub struct RetryAttempt {
    /// 重试次数
    pub attempt: u32,
    /// 错误信息
    pub error: SigmaXError,
    /// 延迟时间
    pub delay: Duration,
    /// 重试时间戳
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl RetryContext {
    /// 创建新的重试上下文
    pub fn new() -> Self {
        Self {
            attempt: 0,
            total_duration: Duration::ZERO,
            last_error: None,
            retry_history: Vec::new(),
        }
    }

    /// 记录重试尝试
    pub fn record_attempt(&mut self, error: SigmaXError, delay: Duration) {
        self.attempt += 1;
        self.total_duration += delay;
        
        let attempt = RetryAttempt {
            attempt: self.attempt,
            error: error.clone(),
            delay,
            timestamp: chrono::Utc::now(),
        };
        
        self.retry_history.push(attempt);
        self.last_error = Some(error);
    }

    /// 获取重试统计信息
    pub fn stats(&self) -> RetryStats {
        RetryStats {
            total_attempts: self.attempt,
            total_duration: self.total_duration,
            success_rate: 0.0, // 需要外部计算
            avg_delay: if self.attempt > 0 {
                self.total_duration / self.attempt
            } else {
                Duration::ZERO
            },
        }
    }
}

/// 重试统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetryStats {
    /// 总重试次数
    pub total_attempts: u32,
    /// 总耗时
    pub total_duration: Duration,
    /// 成功率
    pub success_rate: f64,
    /// 平均延迟
    pub avg_delay: Duration,
}

impl Default for RetryContext {
    fn default() -> Self {
        Self::new()
    }
}

/// 重试策略管理器
pub struct RetryManager {
    /// 默认策略
    default_policy: RetryPolicy,
    /// 按错误类型的策略映射
    category_policies: std::collections::HashMap<ErrorCategory, RetryPolicy>,
}

impl RetryManager {
    /// 创建重试管理器
    pub fn new(default_policy: RetryPolicy) -> Self {
        Self {
            default_policy,
            category_policies: std::collections::HashMap::new(),
        }
    }

    /// 为特定错误类型设置策略
    pub fn set_policy_for_category(&mut self, category: ErrorCategory, policy: RetryPolicy) {
        self.category_policies.insert(category, policy);
    }

    /// 获取错误对应的重试策略
    pub fn get_policy(&self, error: &SigmaXError) -> &RetryPolicy {
        self.category_policies
            .get(&error.category())
            .unwrap_or(&self.default_policy)
    }

    /// 判断是否应该重试
    pub fn should_retry(&self, error: &SigmaXError, attempt: u32) -> bool {
        self.get_policy(error).should_retry(error, attempt)
    }

    /// 计算重试延迟
    pub fn delay_for_attempt(&self, error: &SigmaXError, attempt: u32) -> Duration {
        self.get_policy(error).delay_for_attempt(error, attempt)
    }
}

impl Default for RetryManager {
    fn default() -> Self {
        let mut manager = Self::new(RetryPolicy::default());
        
        // 为不同错误类型设置专门的策略
        manager.set_policy_for_category(ErrorCategory::RateLimit, RetryPolicy::rate_limit());
        manager.set_policy_for_category(ErrorCategory::Permanent, RetryPolicy::conservative());
        
        manager
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::error::{SigmaXError, TradingErrorCode};

    #[test]
    fn test_retry_policy_should_retry() {
        let policy = RetryPolicy::default();
        let error = SigmaXError::trading(TradingErrorCode::MarketClosed, "Market closed");
        
        // 临时性错误应该重试
        assert!(policy.should_retry(&error, 1));
        assert!(policy.should_retry(&error, 2));
        
        // 超过最大次数不应该重试
        assert!(!policy.should_retry(&error, 3));
    }

    #[test]
    fn test_retry_policy_delay_calculation() {
        let policy = RetryPolicy::default();
        let error = SigmaXError::trading(TradingErrorCode::MarketClosed, "Market closed");
        
        let delay1 = policy.delay_for_attempt(&error, 0);
        let delay2 = policy.delay_for_attempt(&error, 1);
        
        // 第二次重试的延迟应该更长
        assert!(delay2 > delay1);
    }

    #[test]
    fn test_retry_context() {
        let mut context = RetryContext::new();
        let error = SigmaXError::trading(TradingErrorCode::MarketClosed, "Market closed");
        
        context.record_attempt(error, Duration::from_millis(1000));
        
        assert_eq!(context.attempt, 1);
        assert_eq!(context.retry_history.len(), 1);
        assert!(context.last_error.is_some());
    }
}
