//! SigmaX Core Traits
//!
//! ## 主要职责
//!
//! 1. **核心抽象接口** - 定义系统核心组件的抽象接口
//!    - 策略管理抽象
//!    - 投资组合管理抽象
//!    - 风险管理抽象
//!    - 订单管理抽象
//!
//! 2. **Repository接口** - 定义数据访问层的统一接口
//!    - 订单Repository
//!    - 交易Repository
//!    - 投资组合Repository
//!    - 风险Repository
//!
//! 3. **服务容器抽象** - 定义依赖注入和服务管理接口
//!    - 服务创建和管理
//!    - 生命周期管理
//!    - 健康检查
//!
//! ## 设计原则
//!
//! - **接口分离**: 每个trait专注于单一职责
//! - **异步优先**: 所有接口都是异步的
//! - **类型安全**: 使用强类型确保编译时安全
//! - **可测试性**: 接口设计便于单元测试和模拟

use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::sync::Arc;
use std::collections::HashMap;

use crate::{
    types::*,
    enums::*,
    SigmaXResult,
};

// ============================================================================
// 核心业务抽象接口
// ============================================================================

/// 策略抽象接口
///
/// 定义交易策略的核心行为，所有策略实现都必须实现此trait
#[async_trait]
pub trait Strategy: Send + Sync {
    /// 获取策略ID
    fn id(&self) -> StrategyId;
    
    /// 获取策略名称
    fn name(&self) -> &str;
    
    /// 获取策略类型
    fn strategy_type(&self) -> &str;

    /// 初始化策略
    async fn initialize(&self, config: serde_json::Value) -> SigmaXResult<()>;
    
    /// 处理市场数据
    async fn on_market_data(&self, data: &MarketData) -> SigmaXResult<Vec<OrderRequest>>;
    
    /// 处理订单更新
    async fn on_order_update(&self, order: &OrderUpdate) -> SigmaXResult<()>;
    
    /// 处理交易更新
    async fn on_trade_update(&self, trade: &TradeUpdate) -> SigmaXResult<()>;
    
    /// 关闭策略
    async fn shutdown(&self) -> SigmaXResult<()>;
}

/// 投资组合管理抽象接口
///
/// 定义投资组合管理的核心功能
#[async_trait]
pub trait PortfolioManager: Send + Sync {
    /// 获取资产余额
    async fn get_balance(&self, asset: &str) -> SigmaXResult<PortfolioBalance>;
    
    /// 获取所有资产余额
    async fn get_all_balances(&self) -> SigmaXResult<Vec<PortfolioBalance>>;
    
    /// 更新资产余额
    async fn update_balance(&self, asset: &str, amount: Amount) -> SigmaXResult<()>;
    
    /// 计算总价值
    async fn calculate_total_value(&self, base_currency: &str) -> SigmaXResult<Amount>;
    
    /// 获取持仓信息
    async fn get_positions(&self) -> SigmaXResult<Vec<Position>>;
    
    /// 更新持仓信息
    async fn update_position(&self, trade: &TradeUpdate) -> SigmaXResult<()>;
}

/// 风险管理抽象接口
///
/// 定义风险管理的核心功能
#[async_trait]
pub trait RiskManager: Send + Sync {
    /// 检查订单风险
    async fn check_order_risk(&self, order: &OrderRequest) -> SigmaXResult<RiskCheckResult>;
    
    /// 检查投资组合风险
    async fn check_portfolio_risk(&self, portfolio_id: PortfolioId) -> SigmaXResult<RiskCheckResult>;
    
    /// 计算风险价值(VaR)
    async fn calculate_var(&self, portfolio_id: PortfolioId, confidence: Percentage) -> SigmaXResult<Amount>;
    
    /// 获取风险指标
    async fn get_risk_metrics(&self, strategy_id: StrategyId) -> SigmaXResult<RiskMetrics>;
    
    /// 更新风险限制
    async fn update_risk_limits(&self, strategy_id: StrategyId, limits: RiskLimits) -> SigmaXResult<()>;
}

/// 策略管理抽象接口
///
/// 定义策略生命周期管理功能
#[async_trait]
pub trait StrategyManager: Send + Sync {
    /// 注册策略
    async fn register_strategy(&self, strategy: Arc<dyn Strategy>) -> SigmaXResult<()>;
    
    /// 注销策略
    async fn unregister_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<()>;
    
    /// 启动策略
    async fn start_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<()>;
    
    /// 停止策略
    async fn stop_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<()>;
    
    /// 获取策略实例
    async fn get_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Arc<dyn Strategy>>;
    
    /// 列出所有策略
    async fn list_strategies(&self) -> SigmaXResult<Vec<StrategyId>>;
    
    /// 获取策略状态
    async fn get_strategy_status(&self, strategy_id: StrategyId) -> SigmaXResult<StrategyStatus>;
}

/// 订单管理抽象接口
///
/// 定义订单管理的核心功能
#[async_trait]
pub trait OrderManager: Send + Sync {
    /// 下单
    async fn place_order(&self, request: OrderRequest) -> SigmaXResult<OrderId>;
    
    /// 取消订单
    async fn cancel_order(&self, order_id: OrderId) -> SigmaXResult<()>;
    
    /// 获取订单信息
    async fn get_order(&self, order_id: OrderId) -> SigmaXResult<Option<OrderInfo>>;
    
    /// 列出订单
    async fn list_orders(&self, filter: OrderFilter) -> SigmaXResult<Vec<OrderInfo>>;
    
    /// 获取订单状态
    async fn get_order_status(&self, order_id: OrderId) -> SigmaXResult<OrderStatus>;
}

// ============================================================================
// 支持数据结构
// ============================================================================

/// 订单请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderRequest {
    pub trading_pair: TradingPair,
    pub side: OrderSide,
    pub order_type: OrderType,
    pub quantity: Quantity,
    pub price: Option<Price>,
    pub stop_price: Option<Price>,
}

/// 订单更新
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderUpdate {
    pub order_id: OrderId,
    pub status: OrderStatus,
    pub filled_quantity: Quantity,
    pub avg_fill_price: Option<Price>,
}

/// 交易更新
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeUpdate {
    pub trade_id: TradeId,
    pub order_id: OrderId,
    pub price: Price,
    pub quantity: Quantity,
    pub fee: Fee,
}

/// 持仓信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Position {
    pub trading_pair: TradingPair,
    pub quantity: Quantity,
    pub avg_price: Price,
    pub unrealized_pnl: Amount,
    pub realized_pnl: Amount,
}

/// 风险检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskCheckResult {
    pub passed: bool,
    pub violations: Vec<RiskViolation>,
    pub warnings: Vec<RiskWarning>,
}

/// 风险违规
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskViolation {
    pub rule_name: String,
    pub severity: RiskSeverity,
    pub message: String,
}

/// 风险警告
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskWarning {
    pub rule_name: String,
    pub message: String,
}

/// 风险限制
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskLimits {
    pub max_position_size: Amount,
    pub max_daily_loss: Amount,
    pub max_drawdown: Percentage,
    pub stop_loss_threshold: Percentage,
}

/// 订单信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderInfo {
    pub order_id: OrderId,
    pub strategy_id: StrategyId,
    pub trading_pair: TradingPair,
    pub side: OrderSide,
    pub order_type: OrderType,
    pub quantity: Quantity,
    pub price: Option<Price>,
    pub filled_quantity: Quantity,
    pub avg_fill_price: Option<Price>,
    pub status: OrderStatus,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 订单过滤器
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct OrderFilter {
    pub strategy_id: Option<StrategyId>,
    pub exchange_id: Option<ExchangeId>,
    pub trading_pair: Option<TradingPair>,
    pub status: Option<OrderStatus>,
    pub side: Option<OrderSide>,
    pub order_type: Option<OrderType>,
    pub created_after: Option<DateTime<Utc>>,
    pub created_before: Option<DateTime<Utc>>,
    pub limit: Option<usize>,
    pub offset: Option<usize>,
}

// ============================================================================
// Repository接口
// ============================================================================

/// 订单Repository接口
///
/// 定义订单数据访问的统一接口
#[async_trait]
pub trait OrderRepository: Send + Sync {
    /// 保存订单
    async fn save(&self, order: &crate::models::Order) -> SigmaXResult<OrderId>;

    /// 更新订单
    async fn update(&self, order: &crate::models::Order) -> SigmaXResult<()>;

    /// 删除订单
    async fn delete(&self, id: OrderId) -> SigmaXResult<()>;

    /// 根据ID查找订单
    async fn find_by_id(&self, id: OrderId) -> SigmaXResult<Option<crate::models::Order>>;

    /// 列出订单
    async fn list(&self, filter: OrderFilter) -> SigmaXResult<Vec<crate::models::Order>>;

    /// 统计订单数量
    async fn count(&self, filter: OrderFilter) -> SigmaXResult<u64>;

    /// 根据交易所查找订单
    async fn find_by_exchange(&self, exchange_id: ExchangeId) -> SigmaXResult<Vec<crate::models::Order>>;

    /// 查找活跃订单
    async fn find_active_orders(&self) -> SigmaXResult<Vec<crate::models::Order>>;

    /// 批量保存订单
    async fn batch_save(&self, orders: &[crate::models::Order]) -> SigmaXResult<Vec<OrderId>>;

    /// 批量更新订单
    async fn batch_update(&self, orders: &[crate::models::Order]) -> SigmaXResult<()>;
}

/// 交易Repository接口
///
/// 定义交易数据访问的统一接口
#[async_trait]
pub trait TradeRepository: Send + Sync {
    /// 保存交易
    async fn save(&self, trade: &crate::models::Trade) -> SigmaXResult<TradeId>;

    /// 根据ID查找交易
    async fn find_by_id(&self, id: TradeId) -> SigmaXResult<Option<crate::models::Trade>>;

    /// 列出交易
    async fn list(&self, filter: TradeFilter) -> SigmaXResult<Vec<crate::models::Trade>>;

    /// 统计交易数量
    async fn count(&self, filter: TradeFilter) -> SigmaXResult<u64>;

    /// 根据订单查找交易
    async fn find_by_order(&self, order_id: OrderId) -> SigmaXResult<Vec<crate::models::Trade>>;

    /// 根据交易所查找交易
    async fn find_by_exchange(&self, exchange_id: ExchangeId) -> SigmaXResult<Vec<crate::models::Trade>>;

    /// 查找最近交易
    async fn find_recent_trades(&self, limit: usize) -> SigmaXResult<Vec<crate::models::Trade>>;
}

/// 交易过滤器
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct TradeFilter {
    pub order_id: Option<OrderId>,
    pub strategy_id: Option<StrategyId>,
    pub exchange_id: Option<ExchangeId>,
    pub trading_pair: Option<TradingPair>,
    pub side: Option<OrderSide>,
    pub executed_after: Option<DateTime<Utc>>,
    pub executed_before: Option<DateTime<Utc>>,
    pub min_quantity: Option<Quantity>,
    pub max_quantity: Option<Quantity>,
    pub min_price: Option<Price>,
    pub max_price: Option<Price>,
    pub limit: Option<usize>,
    pub offset: Option<usize>,
}

// ============================================================================
// 服务容器抽象
// ============================================================================

/// 服务容器抽象接口
///
/// 定义依赖注入和服务管理的核心功能
#[async_trait]
pub trait ServiceContainer: Send + Sync {
    /// 创建投资组合管理器
    async fn create_portfolio_manager(&self) -> SigmaXResult<Arc<dyn PortfolioManager>>;

    /// 创建风险管理器
    async fn create_risk_manager(&self) -> SigmaXResult<Arc<dyn RiskManager>>;

    /// 创建策略管理器
    async fn create_strategy_manager(&self) -> SigmaXResult<Arc<dyn StrategyManager>>;

    /// 创建订单管理器
    async fn create_order_manager(&self) -> SigmaXResult<Arc<dyn OrderManager>>;

    /// 获取配置管理器
    async fn get_config_manager(&self) -> SigmaXResult<Arc<dyn ConfigManager>>;

    /// 获取事件总线
    async fn get_event_bus(&self) -> SigmaXResult<Arc<dyn EventBus>>;

    /// 获取事务管理器
    async fn get_transaction_manager(&self) -> SigmaXResult<Arc<dyn TransactionManager>>;

    /// 启动服务
    async fn start_services(&self) -> SigmaXResult<()>;

    /// 停止服务
    async fn stop_services(&self) -> SigmaXResult<()>;

    /// 健康检查
    async fn health_check(&self) -> SigmaXResult<HealthStatus>;
}

/// 配置管理抽象接口
///
/// 定义配置管理的核心功能
#[async_trait]
pub trait ConfigManager: Send + Sync {
    /// 获取配置值（JSON字符串）
    async fn get_raw(&self, key: &str) -> SigmaXResult<Option<String>>;

    /// 设置配置值（JSON字符串）
    async fn set_raw(&self, key: &str, value: &str) -> SigmaXResult<()>;

    /// 删除配置
    async fn delete(&self, key: &str) -> SigmaXResult<()>;

    /// 监听配置变化
    async fn watch_changes(&self) -> SigmaXResult<()>;

    /// 配置变化回调
    async fn on_config_changed(&self, key: &str) -> SigmaXResult<()>;
}

/// 事件总线抽象接口
///
/// 定义事件发布和订阅的核心功能
#[async_trait]
pub trait EventBus: Send + Sync {
    /// 发布事件（JSON字符串）
    async fn publish_raw(&self, event_type: &str, event_data: &str) -> SigmaXResult<()>;

    /// 订阅事件类型
    async fn subscribe_type(&self, event_type: &str, subscriber_id: &str) -> SigmaXResult<()>;

    /// 取消订阅
    async fn unsubscribe(&self, subscription_id: &str) -> SigmaXResult<()>;

    /// 获取待处理事件
    async fn poll_events(&self, subscriber_id: &str) -> SigmaXResult<Vec<EventMessage>>;
}

/// 事件消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EventMessage {
    pub event_type: String,
    pub event_data: String,
    pub timestamp: DateTime<Utc>,
}

/// 事务管理抽象接口
///
/// 定义事务管理的核心功能
#[async_trait]
pub trait TransactionManager: Send + Sync {
    /// 开始事务
    async fn begin_transaction(&self) -> SigmaXResult<TransactionId>;

    /// 提交事务
    async fn commit_transaction(&self, transaction_id: TransactionId) -> SigmaXResult<()>;

    /// 回滚事务
    async fn rollback_transaction(&self, transaction_id: TransactionId) -> SigmaXResult<()>;

    /// 检查事务状态
    async fn get_transaction_status(&self, transaction_id: TransactionId) -> SigmaXResult<TransactionStatus>;
}

// ============================================================================
// 支持类型和枚举
// ============================================================================

/// 健康状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthStatus {
    pub overall: ServiceHealth,
    pub services: HashMap<String, ServiceHealth>,
    pub timestamp: DateTime<Utc>,
}

/// 服务健康状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ServiceHealth {
    Healthy,
    Degraded,
    Unhealthy,
    Unknown,
}

/// 事务ID
pub type TransactionId = uuid::Uuid;

/// 事务状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum TransactionStatus {
    Active,
    Committed,
    RolledBack,
    Failed,
}
