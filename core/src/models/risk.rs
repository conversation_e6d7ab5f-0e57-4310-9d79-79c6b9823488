//! SigmaX Core Risk Models
//!
//! ## 主要职责
//!
//! 1. **风险度量模型** - 核心风险概念
//!    - `RiskMetrics` - 统一的风险度量模型，记录实时风险指标
//!    - 支持多种风险度量方法和计算
//!
//! 2. **风险事件模型** - 风险事件记录
//!    - `RiskEvent` - 风险事件记录，追踪风险触发和处理
//!    - 完整的风险事件生命周期管理
//!
//! 3. **风险计算功能** - 实用的风险计算方法
//!    - VaR、回撤、夏普比率等关键指标计算
//!    - 风险评估和预警功能
//!    - 实时风险监控支持
//!
//! ## 设计原则
//!
//! - **实时性**: 风险度量数据反映实时状态，支持高频更新
//! - **统一模型**: 使用单一模型，避免Record/View分离
//! - **类型安全**: 使用强类型的ID、Amount、Percentage等类型
//! - **业务友好**: 提供实用的风险计算和分析方法

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use validator::{Validate, ValidationError};
use rust_decimal::Decimal;
use rust_decimal::prelude::ToPrimitive;

use crate::{
    types::*,
    enums::*,
    error::*,
    SigmaXResult,
};

// ============================================================================
// 风险度量模型 - 核心风险概念
// ============================================================================

/// 风险度量 - 实时风险指标记录
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct RiskMetrics {
    pub risk_id: RiskId,
    pub strategy_id: Option<StrategyId>,
    pub exchange_id: ExchangeId,
    pub trading_pair: Option<TradingPair>,
    
    // 基础风险指标
    pub current_drawdown: Percentage,
    pub max_drawdown: Percentage,
    pub daily_pnl: Amount,
    pub total_pnl: Amount,
    
    // 持仓风险指标
    pub position_value: Amount,
    pub position_risk: Percentage,
    pub leverage: Decimal,
    pub concentration: Percentage,
    
    // 市场风险指标
    pub var_1d: Amount,        // 1日VaR
    pub var_5d: Amount,        // 5日VaR
    pub volatility: Percentage,
    pub beta: Decimal,
    
    // 流动性风险指标
    pub liquidity_score: Decimal,
    pub bid_ask_spread: Percentage,
    
    pub calculated_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl RiskMetrics {
    /// 创建新的风险度量记录
    pub fn new(
        strategy_id: Option<StrategyId>,
        exchange_id: ExchangeId,
        trading_pair: Option<TradingPair>,
    ) -> Self {
        Self {
            risk_id: RiskId::new(),
            strategy_id,
            exchange_id,
            trading_pair,
            current_drawdown: Percentage::ZERO,
            max_drawdown: Percentage::ZERO,
            daily_pnl: Amount::ZERO,
            total_pnl: Amount::ZERO,
            position_value: Amount::ZERO,
            position_risk: Percentage::ZERO,
            leverage: Decimal::ONE,
            concentration: Percentage::ZERO,
            var_1d: Amount::ZERO,
            var_5d: Amount::ZERO,
            volatility: Percentage::ZERO,
            beta: Decimal::ONE,
            liquidity_score: Decimal::from(100),
            bid_ask_spread: Percentage::ZERO,
            calculated_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    /// 更新回撤指标
    pub fn update_drawdown(&mut self, current_drawdown: Percentage) {
        self.current_drawdown = current_drawdown;
        if current_drawdown > self.max_drawdown {
            self.max_drawdown = current_drawdown;
        }
        self.updated_at = Utc::now();
    }

    /// 更新盈亏指标
    pub fn update_pnl(&mut self, daily_pnl: Amount, total_pnl: Amount) {
        self.daily_pnl = daily_pnl;
        self.total_pnl = total_pnl;
        self.updated_at = Utc::now();
    }

    /// 更新持仓风险指标
    pub fn update_position_risk(
        &mut self,
        position_value: Amount,
        position_risk: Percentage,
        leverage: Decimal,
        concentration: Percentage,
    ) {
        self.position_value = position_value;
        self.position_risk = position_risk;
        self.leverage = leverage;
        self.concentration = concentration;
        self.updated_at = Utc::now();
    }

    /// 更新市场风险指标
    pub fn update_market_risk(
        &mut self,
        var_1d: Amount,
        var_5d: Amount,
        volatility: Percentage,
        beta: Decimal,
    ) {
        self.var_1d = var_1d;
        self.var_5d = var_5d;
        self.volatility = volatility;
        self.beta = beta;
        self.updated_at = Utc::now();
    }

    /// 更新流动性风险指标
    pub fn update_liquidity_risk(&mut self, liquidity_score: Decimal, bid_ask_spread: Percentage) {
        self.liquidity_score = liquidity_score;
        self.bid_ask_spread = bid_ask_spread;
        self.updated_at = Utc::now();
    }

    /// 计算风险调整收益率
    pub fn risk_adjusted_return(&self) -> Decimal {
        if self.volatility.is_zero() {
            return Decimal::ZERO;
        }
        
        let return_rate = if self.position_value.is_zero() {
            Decimal::ZERO
        } else {
            self.total_pnl / self.position_value
        };
        
        let volatility_decimal = self.volatility / Percentage::from(100);
        return_rate / volatility_decimal
    }

    /// 计算夏普比率（假设无风险利率为0）
    pub fn sharpe_ratio(&self) -> Decimal {
        self.risk_adjusted_return()
    }

    /// 检查是否超过风险阈值
    pub fn is_high_risk(&self, max_drawdown_threshold: Percentage, max_var_threshold: Amount) -> bool {
        self.current_drawdown > max_drawdown_threshold || self.var_1d > max_var_threshold
    }

    /// 获取风险等级
    pub fn risk_level(&self) -> RiskLevel {
        let drawdown_pct = self.current_drawdown.to_f64().unwrap_or(0.0);
        let var_ratio = if self.position_value.is_zero() {
            0.0
        } else {
            (self.var_1d / self.position_value).to_f64().unwrap_or(0.0)
        };

        match (drawdown_pct, var_ratio) {
            (d, v) if d > 20.0 || v > 0.1 => RiskLevel::High,
            (d, v) if d > 10.0 || v > 0.05 => RiskLevel::Medium,
            (d, v) if d > 5.0 || v > 0.02 => RiskLevel::Low,
            _ => RiskLevel::VeryLow,
        }
    }

    /// 获取显示用的风险摘要
    pub fn risk_summary(&self) -> String {
        format!(
            "回撤: {:.2}%, VaR: {:.2}, 杠杆: {:.1}x, 风险等级: {:?}",
            self.current_drawdown,
            self.var_1d,
            self.leverage,
            self.risk_level()
        )
    }

    /// 检查数据是否新鲜（在指定分钟内）
    pub fn is_fresh(&self, max_age_minutes: u64) -> bool {
        let now = Utc::now();
        let age = now.signed_duration_since(self.updated_at);
        age.num_minutes() <= max_age_minutes as i64
    }

    /// 执行完整的风险度量验证
    pub fn validate_complete(&self) -> SigmaXResult<()> {
        // 首先执行基础验证（通过validator derive宏）
        if let Err(validation_errors) = <Self as Validate>::validate(self) {
            return Err(SigmaXError::from(validation_errors));
        }

        // 跨字段验证：回撤不能为负数
        if self.current_drawdown < Percentage::ZERO || self.max_drawdown < Percentage::ZERO {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "drawdown",
                "Drawdown cannot be negative"
            ));
        }

        // 跨字段验证：杠杆不能为负数
        if self.leverage < Decimal::ZERO {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "leverage",
                "Leverage cannot be negative"
            ));
        }

        Ok(())
    }
}

// ============================================================================
// 风险事件模型 - 风险事件记录
// ============================================================================

/// 风险事件 - 风险触发和处理记录
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct RiskEvent {
    pub event_id: RiskEventId,
    pub risk_id: RiskId,
    pub strategy_id: Option<StrategyId>,
    pub exchange_id: ExchangeId,

    pub event_type: RiskEventType,
    pub severity: RiskSeverity,
    pub status: RiskEventStatus,

    #[validate(length(min = 1, message = "Event description cannot be empty"))]
    pub description: String,

    pub trigger_value: Decimal,
    pub threshold_value: Decimal,
    pub impact_assessment: String,

    pub created_at: DateTime<Utc>,
    pub resolved_at: Option<DateTime<Utc>>,
    pub resolution_notes: Option<String>,
}

impl RiskEvent {
    /// 创建新的风险事件
    pub fn new(
        risk_id: RiskId,
        strategy_id: Option<StrategyId>,
        exchange_id: ExchangeId,
        event_type: RiskEventType,
        severity: RiskSeverity,
        description: String,
        trigger_value: Decimal,
        threshold_value: Decimal,
    ) -> Self {
        Self {
            event_id: RiskEventId::new(),
            risk_id,
            strategy_id,
            exchange_id,
            event_type,
            severity,
            status: RiskEventStatus::Active,
            description,
            trigger_value,
            threshold_value,
            impact_assessment: String::new(),
            created_at: Utc::now(),
            resolved_at: None,
            resolution_notes: None,
        }
    }

    /// 解决风险事件
    pub fn resolve(&mut self, resolution_notes: String) {
        self.status = RiskEventStatus::Resolved;
        self.resolved_at = Some(Utc::now());
        self.resolution_notes = Some(resolution_notes);
    }

    /// 忽略风险事件
    pub fn ignore(&mut self, reason: String) {
        self.status = RiskEventStatus::Ignored;
        self.resolved_at = Some(Utc::now());
        self.resolution_notes = Some(format!("Ignored: {}", reason));
    }

    /// 升级风险事件
    pub fn escalate(&mut self, new_severity: RiskSeverity) {
        self.severity = new_severity;
        self.status = RiskEventStatus::Escalated;
    }

    /// 检查事件是否已解决
    pub fn is_resolved(&self) -> bool {
        matches!(self.status, RiskEventStatus::Resolved | RiskEventStatus::Ignored)
    }

    /// 检查事件是否为活跃状态
    pub fn is_active(&self) -> bool {
        matches!(self.status, RiskEventStatus::Active | RiskEventStatus::Escalated)
    }

    /// 计算事件持续时间（分钟）
    pub fn duration_minutes(&self) -> i64 {
        let end_time = self.resolved_at.unwrap_or_else(Utc::now);
        end_time.signed_duration_since(self.created_at).num_minutes()
    }

    /// 获取风险事件优先级
    pub fn priority(&self) -> u8 {
        match self.severity {
            RiskSeverity::Critical => 1,
            RiskSeverity::High => 2,
            RiskSeverity::Medium => 3,
            RiskSeverity::Low => 4,
        }
    }

    /// 获取显示用的事件摘要
    pub fn event_summary(&self) -> String {
        format!(
            "{:?} - {} (触发值: {:.4}, 阈值: {:.4})",
            self.event_type,
            self.description,
            self.trigger_value,
            self.threshold_value
        )
    }

    /// 更新影响评估
    pub fn update_impact_assessment(&mut self, assessment: String) {
        self.impact_assessment = assessment;
    }

    /// 执行完整的风险事件验证
    pub fn validate_complete(&self) -> SigmaXResult<()> {
        // 首先执行基础验证（通过validator derive宏）
        if let Err(validation_errors) = <Self as Validate>::validate(self) {
            return Err(SigmaXError::from(validation_errors));
        }

        // 跨字段验证：已解决的事件必须有解决时间
        if self.is_resolved() && self.resolved_at.is_none() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "resolved_at",
                "Resolved events must have resolution time"
            ));
        }

        Ok(())
    }
}

// ============================================================================
// 验证函数 - 用于模型验证
// ============================================================================

/// 验证百分比为非负数
fn validate_non_negative_percentage(percentage: &Percentage) -> Result<(), ValidationError> {
    if *percentage < Percentage::ZERO {
        return Err(ValidationError::new("Percentage cannot be negative"));
    }
    Ok(())
}

/// 验证金额为非负数
fn validate_non_negative_amount(amount: &Amount) -> Result<(), ValidationError> {
    if *amount < Amount::ZERO {
        return Err(ValidationError::new("Amount cannot be negative"));
    }
    Ok(())
}

// ============================================================================
// Tests
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal::Decimal;

    #[test]
    fn test_risk_metrics_creation() {
        let metrics = RiskMetrics::new(
            Some(StrategyId::new()),
            ExchangeId::Binance,
            Some(TradingPair::new("BTC", "USDT")),
        );

        assert_eq!(metrics.current_drawdown, Decimal::ZERO);
        assert_eq!(metrics.max_drawdown, Decimal::ZERO);
        assert_eq!(metrics.daily_pnl, Decimal::ZERO);
        assert_eq!(metrics.leverage, Decimal::ONE);
        assert_eq!(metrics.liquidity_score, Decimal::from(100));
    }

    #[test]
    fn test_risk_metrics_drawdown_update() {
        let mut metrics = RiskMetrics::new(
            Some(StrategyId::new()),
            ExchangeId::Binance,
            Some(TradingPair::new("BTC", "USDT")),
        );

        // 更新回撤
        metrics.update_drawdown(Decimal::from(5));
        assert_eq!(metrics.current_drawdown, Decimal::from(5));
        assert_eq!(metrics.max_drawdown, Decimal::from(5));

        // 更新更大的回撤
        metrics.update_drawdown(Decimal::from(8));
        assert_eq!(metrics.current_drawdown, Decimal::from(8));
        assert_eq!(metrics.max_drawdown, Decimal::from(8));

        // 更新较小的回撤（最大回撤不变）
        metrics.update_drawdown(Decimal::from(3));
        assert_eq!(metrics.current_drawdown, Decimal::from(3));
        assert_eq!(metrics.max_drawdown, Decimal::from(8));
    }

    #[test]
    fn test_risk_metrics_pnl_update() {
        let mut metrics = RiskMetrics::new(
            Some(StrategyId::new()),
            ExchangeId::Binance,
            Some(TradingPair::new("BTC", "USDT")),
        );

        metrics.update_pnl(Decimal::from(100), Decimal::from(500));
        assert_eq!(metrics.daily_pnl, Decimal::from(100));
        assert_eq!(metrics.total_pnl, Decimal::from(500));
    }

    #[test]
    fn test_risk_metrics_risk_level() {
        let mut metrics = RiskMetrics::new(
            Some(StrategyId::new()),
            ExchangeId::Binance,
            Some(TradingPair::new("BTC", "USDT")),
        );

        // 低风险
        metrics.update_drawdown(Decimal::from(2));
        assert_eq!(metrics.risk_level(), RiskLevel::VeryLow);

        // 中等风险
        metrics.update_drawdown(Decimal::from(7));
        assert_eq!(metrics.risk_level(), RiskLevel::Low);

        // 高风险
        metrics.update_drawdown(Decimal::from(15));
        assert_eq!(metrics.risk_level(), RiskLevel::Medium);

        // 极高风险
        metrics.update_drawdown(Decimal::from(25));
        assert_eq!(metrics.risk_level(), RiskLevel::High);
    }

    #[test]
    fn test_risk_metrics_sharpe_ratio() {
        let mut metrics = RiskMetrics::new(
            Some(StrategyId::new()),
            ExchangeId::Binance,
            Some(TradingPair::new("BTC", "USDT")),
        );

        // 设置持仓和盈亏
        metrics.position_value = Decimal::from(10000);
        metrics.total_pnl = Decimal::from(1000); // 10% 收益
        metrics.volatility = Decimal::from(20);  // 20% 波动率

        let sharpe = metrics.sharpe_ratio();
        assert_eq!(sharpe, Decimal::new(5, 1)); // 0.5 (10% / 20%)
    }

    #[test]
    fn test_risk_event_creation() {
        let event = RiskEvent::new(
            RiskId::new(),
            Some(StrategyId::new()),
            ExchangeId::Binance,
            RiskEventType::DrawdownExceeded,
            RiskSeverity::High,
            "Maximum drawdown exceeded".to_string(),
            Decimal::from(15),
            Decimal::from(10),
        );

        assert_eq!(event.event_type, RiskEventType::DrawdownExceeded);
        assert_eq!(event.severity, RiskSeverity::High);
        assert_eq!(event.status, RiskEventStatus::Active);
        assert_eq!(event.trigger_value, Decimal::from(15));
        assert_eq!(event.threshold_value, Decimal::from(10));
        assert!(event.is_active());
        assert!(!event.is_resolved());
    }

    #[test]
    fn test_risk_event_lifecycle() {
        let mut event = RiskEvent::new(
            RiskId::new(),
            Some(StrategyId::new()),
            ExchangeId::Binance,
            RiskEventType::VarExceeded,
            RiskSeverity::Medium,
            "VaR limit exceeded".to_string(),
            Decimal::from(1500),
            Decimal::from(1000),
        );

        // 初始状态
        assert!(event.is_active());
        assert!(!event.is_resolved());
        assert_eq!(event.priority(), 3);

        // 升级事件
        event.escalate(RiskSeverity::High);
        assert_eq!(event.severity, RiskSeverity::High);
        assert_eq!(event.status, RiskEventStatus::Escalated);
        assert_eq!(event.priority(), 2);

        // 解决事件
        event.resolve("Risk mitigated by reducing position size".to_string());
        assert!(event.is_resolved());
        assert!(!event.is_active());
        assert!(event.resolved_at.is_some());
        assert!(event.resolution_notes.is_some());
    }

    #[test]
    fn test_risk_event_ignore() {
        let mut event = RiskEvent::new(
            RiskId::new(),
            Some(StrategyId::new()),
            ExchangeId::Binance,
            RiskEventType::LiquidityLow,
            RiskSeverity::Low,
            "Low liquidity detected".to_string(),
            Decimal::from(50),
            Decimal::from(100),
        );

        event.ignore("Market conditions improved".to_string());
        assert_eq!(event.status, RiskEventStatus::Ignored);
        assert!(event.is_resolved());
        assert!(event.resolved_at.is_some());
    }

    #[test]
    fn test_risk_metrics_validation() {
        let metrics = RiskMetrics::new(
            Some(StrategyId::new()),
            ExchangeId::Binance,
            Some(TradingPair::new("BTC", "USDT")),
        );

        // 正常风险度量应该通过验证
        let result = metrics.validate_complete();
        assert!(result.is_ok());
    }

    #[test]
    fn test_risk_event_validation() {
        let event = RiskEvent::new(
            RiskId::new(),
            Some(StrategyId::new()),
            ExchangeId::Binance,
            RiskEventType::DrawdownExceeded,
            RiskSeverity::High,
            "Test event".to_string(),
            Decimal::from(15),
            Decimal::from(10),
        );

        // 正常风险事件应该通过验证
        let result = event.validate_complete();
        assert!(result.is_ok());
    }

    #[test]
    fn test_risk_metrics_freshness() {
        let metrics = RiskMetrics::new(
            Some(StrategyId::new()),
            ExchangeId::Binance,
            Some(TradingPair::new("BTC", "USDT")),
        );

        // 刚创建的数据应该是新鲜的
        assert!(metrics.is_fresh(5)); // 5分钟内
    }

    #[test]
    fn test_risk_event_duration() {
        let event = RiskEvent::new(
            RiskId::new(),
            Some(StrategyId::new()),
            ExchangeId::Binance,
            RiskEventType::DrawdownExceeded,
            RiskSeverity::High,
            "Test event".to_string(),
            Decimal::from(15),
            Decimal::from(10),
        );

        // 刚创建的事件持续时间应该接近0
        let duration = event.duration_minutes();
        assert!(duration >= 0 && duration <= 1);
    }
}
