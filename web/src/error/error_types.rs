//! 错误类型定义
//!
//! 定义各种错误类型和错误代码

use serde::{Deserialize, Serialize};

/// 错误类型枚举
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ErrorType {
    /// 验证错误
    Validation,
    /// 认证错误
    Authentication,
    /// 授权错误
    Authorization,
    /// 业务错误
    Business,
    /// 系统错误
    System,
    /// 限流错误
    RateLimit,
    /// 资源未找到
    NotFound,
    /// 未知错误
    Unknown,
}

/// 错误严重程度
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ErrorSeverity {
    /// 低严重程度，如验证错误
    Low,
    /// 中等严重程度，如业务错误
    Medium,
    /// 高严重程度，如系统错误
    High,
    /// 严重错误，如认证失败
    Critical,
}

/// 错误上下文信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorContext {
    /// 错误发生的模块
    pub module: String,
    /// 错误发生的函数
    pub function: String,
    /// 错误发生的行号
    pub line: u32,
    /// 错误发生的文件
    pub file: String,
    /// 错误发生的时间戳
    pub timestamp: String,
    /// 请求ID
    pub request_id: Option<String>,
    /// 用户ID
    pub user_id: Option<String>,
    /// 额外的上下文信息
    pub additional_info: Option<serde_json::Value>,
}

impl ErrorContext {
    /// 创建新的错误上下文
    pub fn new(module: impl Into<String>, function: impl Into<String>, line: u32, file: impl Into<String>) -> Self {
        Self {
            module: module.into(),
            function: function.into(),
            line,
            file: file.into(),
            timestamp: chrono::Utc::now().to_rfc3339(),
            request_id: None,
            user_id: None,
            additional_info: None,
        }
    }
    
    /// 设置请求ID
    pub fn with_request_id(mut self, request_id: impl Into<String>) -> Self {
        self.request_id = Some(request_id.into());
        self
    }
    
    /// 设置用户ID
    pub fn with_user_id(mut self, user_id: impl Into<String>) -> Self {
        self.user_id = Some(user_id.into());
        self
    }
    
    /// 设置额外信息
    pub fn with_additional_info(mut self, info: serde_json::Value) -> Self {
        self.additional_info = Some(info);
        self
    }
}

/// 错误代码
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ErrorCode {
    /// 通用错误代码
    Generic(String),
    /// 验证错误代码
    Validation(String),
    /// 认证错误代码
    Authentication(String),
    /// 授权错误代码
    Authorization(String),
    /// 业务错误代码
    Business(String),
    /// 系统错误代码
    System(String),
}

impl ErrorCode {
    /// 创建通用错误代码
    pub fn generic(code: impl Into<String>) -> Self {
        Self::Generic(code.into())
    }
    
    /// 创建验证错误代码
    pub fn validation(code: impl Into<String>) -> Self {
        Self::Validation(code.into())
    }
    
    /// 创建认证错误代码
    pub fn authentication(code: impl Into<String>) -> Self {
        Self::Authentication(code.into())
    }
    
    /// 创建授权错误代码
    pub fn authorization(code: impl Into<String>) -> Self {
        Self::Authorization(code.into())
    }
    
    /// 创建业务错误代码
    pub fn business(code: impl Into<String>) -> Self {
        Self::Business(code.into())
    }
    
    /// 创建系统错误代码
    pub fn system(code: impl Into<String>) -> Self {
        Self::System(code.into())
    }
}

/// 错误详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorDetails {
    /// 错误代码
    pub code: ErrorCode,
    /// 错误消息
    pub message: String,
    /// 错误类型
    pub error_type: ErrorType,
    /// 错误严重程度
    pub severity: ErrorSeverity,
    /// 错误上下文
    pub context: Option<ErrorContext>,
    /// 建议的解决方案
    pub suggestions: Vec<String>,
    /// 相关的文档链接
    pub documentation_url: Option<String>,
}

impl ErrorDetails {
    /// 创建新的错误详情
    pub fn new(
        code: ErrorCode,
        message: impl Into<String>,
        error_type: ErrorType,
        severity: ErrorSeverity,
    ) -> Self {
        Self {
            code,
            message: message.into(),
            error_type,
            severity,
            context: None,
            suggestions: vec![],
            documentation_url: None,
        }
    }
    
    /// 设置错误上下文
    pub fn with_context(mut self, context: ErrorContext) -> Self {
        self.context = Some(context);
        self
    }
    
    /// 添加建议
    pub fn with_suggestion(mut self, suggestion: impl Into<String>) -> Self {
        self.suggestions.push(suggestion.into());
        self
    }
    
    /// 设置文档链接
    pub fn with_documentation(mut self, url: impl Into<String>) -> Self {
        self.documentation_url = Some(url.into());
        self
    }
}
