[package]
name = "sigmax-services"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true

[dependencies]
# 核心依赖
sigmax-core.workspace = true
tokio.workspace = true
async-trait.workspace = true
serde.workspace = true
serde_json.workspace = true
uuid.workspace = true
chrono.workspace = true

# 缓存相关
redis.workspace = true
moka = { version = "0.12", features = ["future"] }

# 指标相关
prometheus = "0.13"
tracing.workspace = true
tracing-subscriber.workspace = true

# 配置相关
config.workspace = true
sqlx.workspace = true

# 错误处理
thiserror.workspace = true
anyhow.workspace = true

[dev-dependencies]
tokio-test = "0.4"
mockall = "0.12"
