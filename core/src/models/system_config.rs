//! SigmaX Core System Configuration Models
//!
//! ## 主要职责
//!
//! 1. **系统配置模型** - 核心配置概念
//!    - `SystemConfig` - 统一的系统配置模型，管理系统级配置
//!    - 支持动态配置更新和版本控制
//!
//! 2. **配置历史模型** - 配置变更追踪
//!    - `ConfigHistory` - 配置变更历史记录
//!    - 支持配置回滚和审计追踪
//!
//! 3. **配置业务逻辑** - 实用的配置管理方法
//!    - 配置验证、更新、回滚
//!    - 配置版本管理和变更追踪
//!    - 配置模板和默认值管理
//!
//! ## 设计原则
//!
//! - **统一模型**: 使用单一SystemConfig模型，支持多种配置类型
//! - **版本控制**: 支持配置版本管理和变更历史
//! - **类型安全**: 使用强类型的配置值和验证规则
//! - **业务友好**: 提供实用的配置管理和审计方法

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use validator::{Validate, ValidationError};
use rust_decimal::Decimal;
use std::collections::HashMap;

use crate::{
    types::*,
    enums::*,
    error::*,
    SigmaXResult,
};

// ============================================================================
// 系统配置模型 - 核心配置概念
// ============================================================================

/// 系统配置 - 统一的系统配置模型
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct SystemConfig {
    pub config_id: SystemConfigId,
    
    #[validate(length(min = 1, message = "Config key cannot be empty"))]
    pub key: String,
    
    pub category: ConfigCategory,
    pub config_type: ConfigType,
    
    #[validate(length(min = 1, message = "Config value cannot be empty"))]
    pub value: String,
    
    pub default_value: String,
    pub description: Option<String>,
    
    // 验证规则
    pub validation_rules: serde_json::Value,
    pub is_required: bool,
    pub is_sensitive: bool,
    
    // 版本控制
    pub version: u32,
    pub previous_value: Option<String>,
    pub change_reason: Option<String>,
    
    // 状态管理
    pub is_active: bool,
    pub is_readonly: bool,
    
    // 环境相关
    pub environment: ConfigEnvironment,
    pub applies_to: Vec<String>, // 适用的服务或模块
    
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub updated_by: Option<String>,
}

impl SystemConfig {
    /// 创建新的系统配置
    pub fn new(
        key: String,
        category: ConfigCategory,
        config_type: ConfigType,
        value: String,
        environment: ConfigEnvironment,
    ) -> Self {
        Self {
            config_id: SystemConfigId::new(),
            key,
            category,
            config_type,
            value: value.clone(),
            default_value: value,
            description: None,
            validation_rules: serde_json::Value::Object(serde_json::Map::new()),
            is_required: false,
            is_sensitive: false,
            version: 1,
            previous_value: None,
            change_reason: None,
            is_active: true,
            is_readonly: false,
            environment,
            applies_to: Vec::new(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            updated_by: None,
        }
    }

    /// 更新配置值
    pub fn update_value(&mut self, new_value: String, updated_by: Option<String>, reason: Option<String>) -> SigmaXResult<()> {
        if self.is_readonly {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "value",
                "Cannot update readonly configuration"
            ));
        }

        // 验证新值
        self.validate_value(&new_value)?;

        // 保存旧值
        self.previous_value = Some(self.value.clone());
        self.value = new_value;
        self.version += 1;
        self.change_reason = reason;
        self.updated_by = updated_by;
        self.updated_at = Utc::now();

        Ok(())
    }

    /// 回滚到上一个值
    pub fn rollback(&mut self, updated_by: Option<String>) -> SigmaXResult<()> {
        if self.is_readonly {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "value",
                "Cannot rollback readonly configuration"
            ));
        }

        match &self.previous_value {
            Some(prev_value) => {
                let current_value = self.value.clone();
                self.value = prev_value.clone();
                self.previous_value = Some(current_value);
                self.version += 1;
                self.change_reason = Some("Rollback to previous value".to_string());
                self.updated_by = updated_by;
                self.updated_at = Utc::now();
                Ok(())
            }
            None => Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "previous_value",
                "No previous value available for rollback"
            ))
        }
    }

    /// 重置为默认值
    pub fn reset_to_default(&mut self, updated_by: Option<String>) -> SigmaXResult<()> {
        if self.is_readonly {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "value",
                "Cannot reset readonly configuration"
            ));
        }

        self.previous_value = Some(self.value.clone());
        self.value = self.default_value.clone();
        self.version += 1;
        self.change_reason = Some("Reset to default value".to_string());
        self.updated_by = updated_by;
        self.updated_at = Utc::now();

        Ok(())
    }

    /// 验证配置值
    pub fn validate_value(&self, value: &str) -> SigmaXResult<()> {
        // 基础验证：必需字段不能为空
        if self.is_required && value.trim().is_empty() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::Required,
                "value",
                "Required configuration value cannot be empty"
            ));
        }

        // 类型验证
        match self.config_type {
            ConfigType::String => {
                // 字符串类型无需额外验证
            }
            ConfigType::Integer => {
                value.parse::<i64>().map_err(|_| {
                    SigmaXError::validation(
                        ValidationErrorCode::InvalidFormat,
                        "value",
                        "Value must be a valid integer"
                    )
                })?;
            }
            ConfigType::Float => {
                value.parse::<f64>().map_err(|_| {
                    SigmaXError::validation(
                        ValidationErrorCode::InvalidFormat,
                        "value",
                        "Value must be a valid float"
                    )
                })?;
            }
            ConfigType::Boolean => {
                value.parse::<bool>().map_err(|_| {
                    SigmaXError::validation(
                        ValidationErrorCode::InvalidFormat,
                        "value",
                        "Value must be a valid boolean (true/false)"
                    )
                })?;
            }
            ConfigType::Json => {
                serde_json::from_str::<serde_json::Value>(value).map_err(|_| {
                    SigmaXError::validation(
                        ValidationErrorCode::InvalidFormat,
                        "value",
                        "Value must be valid JSON"
                    )
                })?;
            }
        }

        Ok(())
    }

    /// 获取类型化的值
    pub fn get_string_value(&self) -> String {
        self.value.clone()
    }

    pub fn get_integer_value(&self) -> SigmaXResult<i64> {
        self.value.parse::<i64>().map_err(|_| {
            SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "value",
                "Cannot parse value as integer"
            )
        })
    }

    pub fn get_float_value(&self) -> SigmaXResult<f64> {
        self.value.parse::<f64>().map_err(|_| {
            SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "value",
                "Cannot parse value as float"
            )
        })
    }

    pub fn get_boolean_value(&self) -> SigmaXResult<bool> {
        self.value.parse::<bool>().map_err(|_| {
            SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "value",
                "Cannot parse value as boolean"
            )
        })
    }

    pub fn get_json_value(&self) -> SigmaXResult<serde_json::Value> {
        serde_json::from_str(&self.value).map_err(|_| {
            SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "value",
                "Cannot parse value as JSON"
            )
        })
    }

    /// 设置为只读
    pub fn set_readonly(&mut self, readonly: bool) {
        self.is_readonly = readonly;
        self.updated_at = Utc::now();
    }

    /// 激活配置
    pub fn activate(&mut self) {
        self.is_active = true;
        self.updated_at = Utc::now();
    }

    /// 停用配置
    pub fn deactivate(&mut self) {
        self.is_active = false;
        self.updated_at = Utc::now();
    }

    /// 添加适用范围
    pub fn add_applies_to(&mut self, target: String) {
        if !self.applies_to.contains(&target) {
            self.applies_to.push(target);
            self.updated_at = Utc::now();
        }
    }

    /// 检查是否适用于指定目标
    pub fn applies_to_target(&self, target: &str) -> bool {
        self.applies_to.is_empty() || self.applies_to.contains(&target.to_string())
    }

    /// 获取显示用的配置摘要
    pub fn summary(&self) -> String {
        let value_display = if self.is_sensitive {
            "***".to_string()
        } else {
            self.value.clone()
        };

        format!(
            "{} ({:?}) = {} (v{})",
            self.key,
            self.config_type,
            value_display,
            self.version
        )
    }

    /// 执行完整的配置验证
    pub fn validate_complete(&self) -> SigmaXResult<()> {
        // 首先执行基础验证（通过validator derive宏）
        if let Err(validation_errors) = <Self as Validate>::validate(self) {
            return Err(SigmaXError::from(validation_errors));
        }

        // 验证当前值
        self.validate_value(&self.value)?;

        // 跨字段验证：配置键必须符合命名规范
        if !self.key.chars().all(|c| c.is_alphanumeric() || c == '_' || c == '.' || c == '-') {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "key",
                "Config key can only contain alphanumeric characters, underscores, dots, and hyphens"
            ));
        }

        Ok(())
    }
}

// ============================================================================
// 配置历史模型 - 配置变更追踪
// ============================================================================

/// 配置历史 - 配置变更历史记录
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct ConfigHistory {
    pub history_id: ConfigHistoryId,
    pub config_id: SystemConfigId,

    #[validate(length(min = 1, message = "Config key cannot be empty"))]
    pub config_key: String,

    pub action: ConfigAction,
    pub old_value: Option<String>,
    pub new_value: Option<String>,
    pub version_from: u32,
    pub version_to: u32,

    pub change_reason: Option<String>,
    pub changed_by: Option<String>,
    pub change_source: ConfigChangeSource,

    pub created_at: DateTime<Utc>,
}

impl ConfigHistory {
    /// 创建配置变更历史记录
    pub fn new(
        config_id: SystemConfigId,
        config_key: String,
        action: ConfigAction,
        old_value: Option<String>,
        new_value: Option<String>,
        version_from: u32,
        version_to: u32,
        changed_by: Option<String>,
        change_reason: Option<String>,
    ) -> Self {
        Self {
            history_id: ConfigHistoryId::new(),
            config_id,
            config_key,
            action,
            old_value,
            new_value,
            version_from,
            version_to,
            change_reason,
            changed_by,
            change_source: ConfigChangeSource::Manual,
            created_at: Utc::now(),
        }
    }

    /// 创建配置创建记录
    pub fn create_record(
        config_id: SystemConfigId,
        config_key: String,
        initial_value: String,
        created_by: Option<String>,
    ) -> Self {
        Self::new(
            config_id,
            config_key,
            ConfigAction::Create,
            None,
            Some(initial_value),
            0,
            1,
            created_by,
            Some("Initial configuration creation".to_string()),
        )
    }

    /// 创建配置更新记录
    pub fn update_record(
        config_id: SystemConfigId,
        config_key: String,
        old_value: String,
        new_value: String,
        version_from: u32,
        version_to: u32,
        updated_by: Option<String>,
        reason: Option<String>,
    ) -> Self {
        Self::new(
            config_id,
            config_key,
            ConfigAction::Update,
            Some(old_value),
            Some(new_value),
            version_from,
            version_to,
            updated_by,
            reason,
        )
    }

    /// 创建配置删除记录
    pub fn delete_record(
        config_id: SystemConfigId,
        config_key: String,
        final_value: String,
        final_version: u32,
        deleted_by: Option<String>,
        reason: Option<String>,
    ) -> Self {
        Self::new(
            config_id,
            config_key,
            ConfigAction::Delete,
            Some(final_value),
            None,
            final_version,
            final_version,
            deleted_by,
            reason,
        )
    }

    /// 设置变更来源
    pub fn set_change_source(&mut self, source: ConfigChangeSource) {
        self.change_source = source;
    }

    /// 获取变更摘要
    pub fn change_summary(&self) -> String {
        match self.action {
            ConfigAction::Create => {
                format!("Created '{}' with value '{}'",
                    self.config_key,
                    self.new_value.as_deref().unwrap_or("N/A"))
            }
            ConfigAction::Update => {
                format!("Updated '{}' from '{}' to '{}'",
                    self.config_key,
                    self.old_value.as_deref().unwrap_or("N/A"),
                    self.new_value.as_deref().unwrap_or("N/A"))
            }
            ConfigAction::Delete => {
                format!("Deleted '{}' (was '{}')",
                    self.config_key,
                    self.old_value.as_deref().unwrap_or("N/A"))
            }
        }
    }

    /// 检查是否为重要变更
    pub fn is_significant_change(&self) -> bool {
        match self.action {
            ConfigAction::Create | ConfigAction::Delete => true,
            ConfigAction::Update => {
                // 如果值变化很大，认为是重要变更
                match (&self.old_value, &self.new_value) {
                    (Some(old), Some(new)) => old != new,
                    _ => true,
                }
            }
        }
    }
}

// ============================================================================
// Tests
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_system_config_creation() {
        let config = SystemConfig::new(
            "database.max_connections".to_string(),
            ConfigCategory::Database,
            ConfigType::Integer,
            "100".to_string(),
            ConfigEnvironment::Production,
        );

        assert_eq!(config.key, "database.max_connections");
        assert_eq!(config.category, ConfigCategory::Database);
        assert_eq!(config.config_type, ConfigType::Integer);
        assert_eq!(config.value, "100");
        assert_eq!(config.default_value, "100");
        assert_eq!(config.version, 1);
        assert!(config.is_active);
        assert!(!config.is_readonly);
    }

    #[test]
    fn test_config_value_update() {
        let mut config = SystemConfig::new(
            "api.rate_limit".to_string(),
            ConfigCategory::Api,
            ConfigType::Integer,
            "1000".to_string(),
            ConfigEnvironment::Production,
        );

        let result = config.update_value(
            "2000".to_string(),
            Some("admin".to_string()),
            Some("Increase rate limit".to_string()),
        );

        assert!(result.is_ok());
        assert_eq!(config.value, "2000");
        assert_eq!(config.previous_value, Some("1000".to_string()));
        assert_eq!(config.version, 2);
        assert_eq!(config.updated_by, Some("admin".to_string()));
        assert_eq!(config.change_reason, Some("Increase rate limit".to_string()));
    }

    #[test]
    fn test_config_rollback() {
        let mut config = SystemConfig::new(
            "cache.ttl".to_string(),
            ConfigCategory::Cache,
            ConfigType::Integer,
            "300".to_string(),
            ConfigEnvironment::Development,
        );

        // 更新值
        config.update_value("600".to_string(), Some("admin".to_string()), None).unwrap();
        assert_eq!(config.value, "600");
        assert_eq!(config.version, 2);

        // 回滚
        let result = config.rollback(Some("admin".to_string()));
        assert!(result.is_ok());
        assert_eq!(config.value, "300");
        assert_eq!(config.previous_value, Some("600".to_string()));
        assert_eq!(config.version, 3);
    }

    #[test]
    fn test_config_reset_to_default() {
        let mut config = SystemConfig::new(
            "logging.level".to_string(),
            ConfigCategory::System,
            ConfigType::String,
            "INFO".to_string(),
            ConfigEnvironment::Development,
        );

        // 更新值
        config.update_value("DEBUG".to_string(), Some("dev".to_string()), None).unwrap();
        assert_eq!(config.value, "DEBUG");

        // 重置为默认值
        let result = config.reset_to_default(Some("admin".to_string()));
        assert!(result.is_ok());
        assert_eq!(config.value, "INFO");
        assert_eq!(config.previous_value, Some("DEBUG".to_string()));
        assert_eq!(config.change_reason, Some("Reset to default value".to_string()));
    }

    #[test]
    fn test_config_type_validation() {
        let mut config = SystemConfig::new(
            "test.integer".to_string(),
            ConfigCategory::System,
            ConfigType::Integer,
            "100".to_string(),
            ConfigEnvironment::Development,
        );

        // 有效的整数值
        let result = config.validate_value("200");
        assert!(result.is_ok());

        // 无效的整数值
        let result = config.validate_value("not_a_number");
        assert!(result.is_err());

        // 测试布尔类型
        config.config_type = ConfigType::Boolean;
        assert!(config.validate_value("true").is_ok());
        assert!(config.validate_value("false").is_ok());
        assert!(config.validate_value("invalid").is_err());

        // 测试JSON类型
        config.config_type = ConfigType::Json;
        assert!(config.validate_value(r#"{"key": "value"}"#).is_ok());
        assert!(config.validate_value("invalid json").is_err());
    }

    #[test]
    fn test_config_typed_getters() {
        let config = SystemConfig::new(
            "test.config".to_string(),
            ConfigCategory::System,
            ConfigType::Integer,
            "42".to_string(),
            ConfigEnvironment::Development,
        );

        assert_eq!(config.get_integer_value().unwrap(), 42);
        assert_eq!(config.get_string_value(), "42");

        // 测试类型不匹配的情况
        let bool_config = SystemConfig::new(
            "test.bool".to_string(),
            ConfigCategory::System,
            ConfigType::Boolean,
            "true".to_string(),
            ConfigEnvironment::Development,
        );

        assert_eq!(bool_config.get_boolean_value().unwrap(), true);
        assert!(bool_config.get_integer_value().is_err());
    }

    #[test]
    fn test_readonly_config() {
        let mut config = SystemConfig::new(
            "system.version".to_string(),
            ConfigCategory::System,
            ConfigType::String,
            "1.0.0".to_string(),
            ConfigEnvironment::Production,
        );

        config.set_readonly(true);
        assert!(config.is_readonly);

        // 尝试更新只读配置应该失败
        let result = config.update_value("2.0.0".to_string(), None, None);
        assert!(result.is_err());

        // 尝试回滚只读配置应该失败
        let result = config.rollback(None);
        assert!(result.is_err());
    }

    #[test]
    fn test_config_history_creation() {
        let config_id = SystemConfigId::new();

        let history = ConfigHistory::create_record(
            config_id,
            "test.config".to_string(),
            "initial_value".to_string(),
            Some("admin".to_string()),
        );

        assert_eq!(history.config_id, config_id);
        assert_eq!(history.action, ConfigAction::Create);
        assert_eq!(history.new_value, Some("initial_value".to_string()));
        assert_eq!(history.old_value, None);
        assert_eq!(history.version_from, 0);
        assert_eq!(history.version_to, 1);
    }

    #[test]
    fn test_config_history_update() {
        let config_id = SystemConfigId::new();

        let history = ConfigHistory::update_record(
            config_id,
            "test.config".to_string(),
            "old_value".to_string(),
            "new_value".to_string(),
            1,
            2,
            Some("user".to_string()),
            Some("Update reason".to_string()),
        );

        assert_eq!(history.action, ConfigAction::Update);
        assert_eq!(history.old_value, Some("old_value".to_string()));
        assert_eq!(history.new_value, Some("new_value".to_string()));
        assert_eq!(history.version_from, 1);
        assert_eq!(history.version_to, 2);
    }

    #[test]
    fn test_config_applies_to() {
        let mut config = SystemConfig::new(
            "service.timeout".to_string(),
            ConfigCategory::System,
            ConfigType::Integer,
            "30".to_string(),
            ConfigEnvironment::Production,
        );

        // 添加适用范围
        config.add_applies_to("trading-service".to_string());
        config.add_applies_to("risk-service".to_string());

        assert!(config.applies_to_target("trading-service"));
        assert!(config.applies_to_target("risk-service"));
        assert!(!config.applies_to_target("other-service"));
    }

    #[test]
    fn test_config_validation() {
        let config = SystemConfig::new(
            "valid.config".to_string(),
            ConfigCategory::System,
            ConfigType::String,
            "valid_value".to_string(),
            ConfigEnvironment::Development,
        );

        // 正常配置应该通过验证
        let result = config.validate_complete();
        assert!(result.is_ok());

        // 测试无效的配置键
        let mut invalid_config = config.clone();
        invalid_config.key = "invalid key with spaces".to_string();
        let result = invalid_config.validate_complete();
        assert!(result.is_err());
    }
}
