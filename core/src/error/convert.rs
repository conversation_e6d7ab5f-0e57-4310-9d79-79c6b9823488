//! 标准错误类型转换
//!
//! 提供从常见标准库和第三方库错误类型到 SigmaXError 的自动转换

use super::{SigmaXError, DatabaseErrorCode, ValidationErrorCode, InternalErrorCode, ConfigErrorCode};

// =================== 标准库错误转换 ===================

impl From<std::io::Error> for SigmaXError {
    fn from(error: std::io::Error) -> Self {
        // Match to determine error type and code
        match error.kind() {
            std::io::ErrorKind::NotFound => {
                SigmaXError::configuration(ConfigErrorCode::FileNotFound, error.to_string())
            },
            std::io::ErrorKind::PermissionDenied => {
                SigmaXError::configuration(ConfigErrorCode::PermissionDenied, error.to_string())
            },
            std::io::ErrorKind::TimedOut => {
                SigmaXError::internal(InternalErrorCode::ServiceUnavailable, error.to_string())
            },
            std::io::ErrorKind::InvalidInput => {
                SigmaXError::internal(InternalErrorCode::ConfigurationError, error.to_string())
            },
            _ => {
                SigmaXError::internal(InternalErrorCode::UnexpectedState, error.to_string())
            }
        }
    }
}

impl From<std::num::ParseIntError> for SigmaXError {
    fn from(error: std::num::ParseIntError) -> Self {
        SigmaXError::validation(
            ValidationErrorCode::InvalidFormat,
            "number",
            format!("数字解析失败: {}", error)
        )
    }
}

impl From<std::num::ParseFloatError> for SigmaXError {
    fn from(error: std::num::ParseFloatError) -> Self {
        SigmaXError::validation(
            ValidationErrorCode::InvalidFormat,
            "decimal",
            format!("小数解析失败: {}", error)
        )
    }
}

// =================== 序列化相关错误转换 ===================

impl From<serde_json::Error> for SigmaXError {
    fn from(error: serde_json::Error) -> Self {
        SigmaXError::validation(
            ValidationErrorCode::InvalidFormat,
            "json",
            format!("JSON解析失败: {}", error)
        ).with_context("error_line", error.line().to_string())
         .with_context("error_column", error.column().to_string())
    }
}

impl From<toml::de::Error> for SigmaXError {
    fn from(error: toml::de::Error) -> Self {
        SigmaXError::configuration(
            ConfigErrorCode::ParseError,
            format!("TOML配置解析失败: {}", error)
        )
    }
}

impl From<toml::ser::Error> for SigmaXError {
    fn from(error: toml::ser::Error) -> Self {
        SigmaXError::internal(
            InternalErrorCode::UnexpectedState,
            format!("TOML序列化失败: {}", error)
        )
    }
}

// =================== 数据库相关错误转换 ===================

#[cfg(feature = "sqlx")]
impl From<sqlx::Error> for SigmaXError {
    fn from(error: sqlx::Error) -> Self {
        let (code, retryable) = match &error {
            sqlx::Error::Database(_db_err) => {
                // 根据数据库错误码判断具体错误类型
                (DatabaseErrorCode::QueryFailed, false)
            },
            sqlx::Error::Io(_) => (DatabaseErrorCode::ConnectionFailed, true),
            sqlx::Error::PoolTimedOut => (DatabaseErrorCode::ConnectionFailed, true),
            sqlx::Error::PoolClosed => (DatabaseErrorCode::ConnectionFailed, true),
            sqlx::Error::WorkerCrashed => (DatabaseErrorCode::ConnectionFailed, true),
            sqlx::Error::Migrate(_) => (DatabaseErrorCode::MigrationFailed, false),
            _ => (DatabaseErrorCode::QueryFailed, false),
        };
        
        let mut err = SigmaXError::database(code, error.to_string());
        if retryable {
            err = err.retryable();
        }
        err
    }
}

// =================== HTTP/网络相关错误转换 ===================

#[cfg(feature = "reqwest")]
impl From<reqwest::Error> for SigmaXError {
    fn from(error: reqwest::Error) -> Self {
        let code = if error.is_timeout() {
            NetworkErrorCode::Timeout
        } else if error.is_connect() {
            NetworkErrorCode::ConnectionRefused
        } else if error.is_request() {
            NetworkErrorCode::HttpError
        } else if error.is_decode() {
            NetworkErrorCode::HttpError
        } else {
            NetworkErrorCode::HttpError
        };
        
        let mut err = SigmaXError::network(code, error.to_string())
            .retryable();
            
        if let Some(url) = error.url() {
            err = err.with_context("url", url.to_string());
        }
        
        if let Some(status) = error.status() {
            err = err.with_context("status_code", status.as_u16().to_string());
        }
        
        err
    }
}

// =================== 验证相关错误转换 ===================

#[cfg(feature = "validator")]
impl From<validator::ValidationErrors> for SigmaXError {
    fn from(errors: validator::ValidationErrors) -> Self {
        let error_messages: Vec<String> = errors
            .field_errors()
            .iter()
            .flat_map(|(field, errors)| {
                errors.iter().map(move |error| {
                    let message = error.message
                        .as_ref()
                        .map(|m| m.to_string())
                        .unwrap_or_else(|| "验证失败".to_string());
                    format!("{}: {}", field, message)
                })
            })
            .collect();
        
        SigmaXError::validation(
            ValidationErrorCode::InvalidFormat,
            "multiple_fields",
            error_messages.join("; ")
        )
    }
}

// =================== UUID 相关错误转换 ===================

impl From<uuid::Error> for SigmaXError {
    fn from(error: uuid::Error) -> Self {
        SigmaXError::validation(
            ValidationErrorCode::InvalidFormat,
            "uuid",
            format!("UUID格式无效: {}", error)
        )
    }
}

// =================== 时间相关错误转换 ===================

impl From<chrono::ParseError> for SigmaXError {
    fn from(error: chrono::ParseError) -> Self {
        SigmaXError::validation(
            ValidationErrorCode::InvalidFormat,
            "datetime",
            format!("时间格式解析失败: {}", error)
        )
    }
}

// =================== 加密相关错误转换 ===================

impl From<base64::DecodeError> for SigmaXError {
    fn from(error: base64::DecodeError) -> Self {
        SigmaXError::validation(
            ValidationErrorCode::InvalidFormat,
            "base64",
            format!("Base64解码失败: {}", error)
        )
    }
}

// =================== 环境变量相关错误转换 ===================

impl From<std::env::VarError> for SigmaXError {
    fn from(error: std::env::VarError) -> Self {
        match error {
            std::env::VarError::NotPresent => {
                SigmaXError::configuration(
                    ConfigErrorCode::MissingValue,
                    "环境变量未设置"
                )
            },
            std::env::VarError::NotUnicode(_) => {
                SigmaXError::configuration(
                    ConfigErrorCode::InvalidValue,
                    "环境变量包含非法字符"
                )
            }
        }
    }
}

// =================== 十进制数相关错误转换 ===================

impl From<rust_decimal::Error> for SigmaXError {
    fn from(error: rust_decimal::Error) -> Self {
        SigmaXError::validation(
            ValidationErrorCode::InvalidFormat,
            "decimal",
            format!("十进制数格式错误: {}", error)
        )
    }
}

// =================== 其他常见错误转换 ===================

impl From<Box<dyn std::error::Error + Send + Sync>> for SigmaXError {
    fn from(error: Box<dyn std::error::Error + Send + Sync>) -> Self {
        SigmaXError::internal(
            InternalErrorCode::UnexpectedState,
            error.to_string()
        )
    }
}

// 为了支持 ? 操作符的链式调用
impl From<anyhow::Error> for SigmaXError {
    fn from(error: anyhow::Error) -> Self {
        // 尝试向下转型到已知的错误类型
        if let Some(io_err) = error.downcast_ref::<std::io::Error>() {
            return SigmaXError::internal(
                InternalErrorCode::UnexpectedState,
                format!("IO Error: {}", io_err)
            );
        }
        
        if let Some(json_err) = error.downcast_ref::<serde_json::Error>() {
            return SigmaXError::configuration(
                ConfigErrorCode::ParseError,
                format!("JSON Error: {}", json_err)
            );
        }
        
        // 如果无法转换，则作为内部错误处理
        SigmaXError::internal(
            InternalErrorCode::UnexpectedState,
            error.to_string()
        ).with_context("error_chain", format!("{:?}", error.chain().collect::<Vec<_>>()))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_io_error_conversion() {
        let io_error = std::io::Error::new(std::io::ErrorKind::NotFound, "文件未找到");
        let sigmax_error: SigmaXError = io_error.into();
        
        assert!(matches!(sigmax_error, SigmaXError::Configuration { .. }));
        assert_eq!(sigmax_error.error_code(), "CONFIG_FILENOTFOUND");
    }
    
    #[test]
    fn test_json_error_conversion() {
        let json_str = r#"{"invalid": json"#;
        let json_error = serde_json::from_str::<serde_json::Value>(json_str).unwrap_err();
        let sigmax_error: SigmaXError = json_error.into();
        
        assert!(matches!(sigmax_error, SigmaXError::Validation { .. }));
        assert_eq!(sigmax_error.error_code(), "VALIDATION_INVALIDFORMAT");
    }
    
    #[test]
    fn test_parse_int_error_conversion() {
        let parse_error = "not_a_number".parse::<i32>().unwrap_err();
        let sigmax_error: SigmaXError = parse_error.into();
        
        assert!(matches!(sigmax_error, SigmaXError::Validation { .. }));
    }
    
    #[test]
    fn test_uuid_error_conversion() {
        let uuid_error = uuid::Uuid::parse_str("invalid-uuid").unwrap_err();
        let sigmax_error: SigmaXError = uuid_error.into();
        
        assert!(matches!(sigmax_error, SigmaXError::Validation { .. }));
        if let SigmaXError::Validation { field, .. } = sigmax_error {
            assert_eq!(field, Some("uuid".to_string()));
        }
    }
    
    #[test]
    fn test_env_var_error_conversion() {
        let env_error = std::env::var("NONEXISTENT_VAR").unwrap_err();
        let sigmax_error: SigmaXError = env_error.into();
        
        assert!(matches!(sigmax_error, SigmaXError::Configuration { .. }));
        assert_eq!(sigmax_error.error_code(), "CONFIG_MISSINGVALUE");
    }
}