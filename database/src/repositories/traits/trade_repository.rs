//! 交易Repository接口
//!
//! 基于Repository的交易数据访问接口

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use sigmax_core::{Trade, TradeId, TradingPair, OrderSide, ExchangeId, StrategyId, SigmaXResult};
use super::base_repository::{Repository, QueryFilter, PaginationResult, SortConfig};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

/// 交易Repository接口
///
/// 继承Repository的基础CRUD操作，并添加交易特有的业务查询方法
#[async_trait]
pub trait TradeRepository: Repository<Trade, TradeId> {
    // ============================================================================
    // 业务查询方法 - 交易特有的查询逻辑
    // ============================================================================
    
    /// 根据交易对查询交易
    /// 
    /// # Arguments
    /// * `trading_pair` - 交易对
    /// 
    /// # Returns
    /// 返回指定交易对的交易列表
    async fn find_by_trading_pair(&self, trading_pair: &TradingPair) -> SigmaXResult<Vec<Trade>>;
    
    /// 根据策略ID查询交易
    /// 
    /// # Arguments
    /// * `strategy_id` - 策略ID
    /// 
    /// # Returns
    /// 返回指定策略的交易列表
    async fn find_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Vec<Trade>>;
    
    /// 根据交易所ID查询交易
    /// 
    /// # Arguments
    /// * `exchange_id` - 交易所ID
    /// 
    /// # Returns
    /// 返回指定交易所的交易列表
    async fn find_by_exchange(&self, exchange_id: ExchangeId) -> SigmaXResult<Vec<Trade>>;
    
    /// 根据交易方向查询交易
    /// 
    /// # Arguments
    /// * `side` - 交易方向（买入/卖出）
    /// 
    /// # Returns
    /// 返回指定方向的交易列表
    async fn find_by_side(&self, side: OrderSide) -> SigmaXResult<Vec<Trade>>;
    
    /// 根据时间范围查询交易
    /// 
    /// # Arguments
    /// * `start` - 开始时间
    /// * `end` - 结束时间
    /// 
    /// # Returns
    /// 返回指定时间范围内的交易列表
    async fn find_by_time_range(&self, start: DateTime<Utc>, end: DateTime<Utc>) -> SigmaXResult<Vec<Trade>>;
    
    // ============================================================================
    // 复合查询方法 - 支持多条件查询
    // ============================================================================
    
    /// 根据过滤器查询交易
    /// 
    /// # Arguments
    /// * `filter` - 查询过滤器
    /// 
    /// # Returns
    /// 返回符合条件的交易列表
    async fn find_by_filter(&self, filter: &TradeQueryFilter) -> SigmaXResult<Vec<Trade>>;
    
    /// 分页查询交易（带过滤和排序）
    /// 
    /// # Arguments
    /// * `filter` - 查询过滤器
    /// * `sort` - 排序配置
    /// * `offset` - 偏移量
    /// * `limit` - 限制数量
    /// 
    /// # Returns
    /// 返回分页查询结果
    async fn find_with_filter_and_pagination(
        &self,
        filter: &TradeQueryFilter,
        sort: &[SortConfig],
        offset: u64,
        limit: u64,
    ) -> SigmaXResult<PaginationResult<Trade>>;
    
    // ============================================================================
    // 聚合查询方法 - 统计和分析
    // ============================================================================
    
    /// 获取交易统计信息
    /// 
    /// # Arguments
    /// * `filter` - 查询过滤器（可选）
    /// 
    /// # Returns
    /// 返回交易统计信息
    async fn get_statistics(&self, filter: Option<&TradeQueryFilter>) -> SigmaXResult<TradeStatistics>;
    
    /// 根据交易对统计交易量
    /// 
    /// # Arguments
    /// * `limit` - 返回的交易对数量限制
    /// 
    /// # Returns
    /// 返回热门交易对的交易量统计
    async fn get_volume_by_trading_pair(&self, limit: u64) -> SigmaXResult<Vec<(TradingPair, rust_decimal::Decimal)>>;
    
    /// 根据时间段统计交易量
    /// 
    /// # Arguments
    /// * `start` - 开始时间
    /// * `end` - 结束时间
    /// * `interval_minutes` - 时间间隔（分钟）
    /// 
    /// # Returns
    /// 返回时间段内的交易量统计
    async fn get_volume_by_time_interval(
        &self,
        start: DateTime<Utc>,
        end: DateTime<Utc>,
        interval_minutes: u32,
    ) -> SigmaXResult<Vec<(DateTime<Utc>, rust_decimal::Decimal)>>;
    
    /// 计算已实现盈亏
    /// 
    /// # Arguments
    /// * `strategy_id` - 策略ID（可选）
    /// * `trading_pair` - 交易对（可选）
    /// 
    /// # Returns
    /// 返回已实现盈亏
    async fn calculate_realized_pnl(
        &self,
        strategy_id: Option<StrategyId>,
        trading_pair: Option<&TradingPair>,
    ) -> SigmaXResult<rust_decimal::Decimal>;
    
    // ============================================================================
    // 性能优化方法
    // ============================================================================
    
    /// 获取最近的交易
    /// 
    /// # Arguments
    /// * `limit` - 限制数量
    /// 
    /// # Returns
    /// 返回最近执行的交易列表
    async fn find_recent_trades(&self, limit: u64) -> SigmaXResult<Vec<Trade>>;
    
    /// 获取大额交易
    /// 
    /// # Arguments
    /// * `min_amount` - 最小交易金额
    /// * `limit` - 限制数量
    /// 
    /// # Returns
    /// 返回大额交易列表
    async fn find_large_trades(&self, min_amount: rust_decimal::Decimal, limit: u64) -> SigmaXResult<Vec<Trade>>;
}

/// 交易查询过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeQueryFilter {
    /// 策略ID过滤
    pub strategy_ids: Option<Vec<StrategyId>>,
    /// 交易所ID过滤
    pub exchange_ids: Option<Vec<ExchangeId>>,
    /// 交易对过滤
    pub trading_pairs: Option<Vec<TradingPair>>,
    /// 交易方向过滤
    pub sides: Option<Vec<OrderSide>>,
    /// 执行时间范围过滤
    pub executed_time_range: Option<(DateTime<Utc>, DateTime<Utc>)>,
    /// 数量范围过滤
    pub quantity_range: Option<(rust_decimal::Decimal, rust_decimal::Decimal)>,
    /// 价格范围过滤
    pub price_range: Option<(rust_decimal::Decimal, rust_decimal::Decimal)>,
    /// 交易金额范围过滤
    pub amount_range: Option<(rust_decimal::Decimal, rust_decimal::Decimal)>,
}

impl Default for TradeQueryFilter {
    fn default() -> Self {
        Self {
            strategy_ids: None,
            exchange_ids: None,
            trading_pairs: None,
            sides: None,
            executed_time_range: None,
            quantity_range: None,
            price_range: None,
            amount_range: None,
        }
    }
}

impl QueryFilter for TradeQueryFilter {
    fn validate(&self) -> SigmaXResult<()> {
        // 验证时间范围
        if let Some((start, end)) = &self.executed_time_range {
            if start >= end {
                return Err(sigmax_core::SigmaXError::validation(
                    sigmax_core::ValidationErrorCode::InvalidRange,
                    "executed_time_range",
                    "执行时间范围无效：开始时间必须小于结束时间"
                ));
            }
        }

        // 验证数量范围
        if let Some((min, max)) = &self.quantity_range {
            if min >= max || *min < rust_decimal::Decimal::ZERO {
                return Err(sigmax_core::SigmaXError::validation(
                    sigmax_core::ValidationErrorCode::InvalidRange,
                    "quantity_range",
                    "数量范围无效"
                ));
            }
        }

        // 验证价格范围
        if let Some((min, max)) = &self.price_range {
            if min >= max || *min < rust_decimal::Decimal::ZERO {
                return Err(sigmax_core::SigmaXError::validation(
                    sigmax_core::ValidationErrorCode::InvalidRange,
                    "price_range",
                    "价格范围无效"
                ));
            }
        }

        // 验证交易金额范围
        if let Some((min, max)) = &self.amount_range {
            if min >= max || *min < rust_decimal::Decimal::ZERO {
                return Err(sigmax_core::SigmaXError::validation(
                    sigmax_core::ValidationErrorCode::InvalidRange,
                    "amount_range",
                    "交易金额范围无效"
                ));
            }
        }
        
        Ok(())
    }
    
    fn description(&self) -> String {
        let mut parts = Vec::new();
        
        if let Some(strategy_ids) = &self.strategy_ids {
            parts.push(format!("策略数量: {}", strategy_ids.len()));
        }
        if let Some(exchange_ids) = &self.exchange_ids {
            parts.push(format!("交易所数量: {}", exchange_ids.len()));
        }
        if let Some(trading_pairs) = &self.trading_pairs {
            parts.push(format!("交易对数量: {}", trading_pairs.len()));
        }
        if let Some(sides) = &self.sides {
            parts.push(format!("交易方向: {:?}", sides));
        }
        if self.executed_time_range.is_some() {
            parts.push("执行时间范围".to_string());
        }
        
        if parts.is_empty() {
            "无过滤条件".to_string()
        } else {
            format!("过滤条件: {}", parts.join(", "))
        }
    }
}

/// 交易统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeStatistics {
    /// 总交易数
    pub total_count: u64,
    /// 总交易量
    pub total_volume: rust_decimal::Decimal,
    /// 总交易金额
    pub total_amount: rust_decimal::Decimal,
    /// 平均交易大小
    pub average_trade_size: rust_decimal::Decimal,
    /// 平均交易价格
    pub average_price: rust_decimal::Decimal,
    /// 最大交易大小
    pub max_trade_size: rust_decimal::Decimal,
    /// 最小交易大小
    pub min_trade_size: rust_decimal::Decimal,
    /// 买入交易数量
    pub buy_count: u64,
    /// 卖出交易数量
    pub sell_count: u64,
    /// 买入交易量
    pub buy_volume: rust_decimal::Decimal,
    /// 卖出交易量
    pub sell_volume: rust_decimal::Decimal,
    /// 各交易对统计
    pub pair_statistics: HashMap<TradingPair, PairTradeStatistics>,
    /// 统计时间
    pub calculated_at: DateTime<Utc>,
}

/// 交易对交易统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PairTradeStatistics {
    /// 交易数量
    pub count: u64,
    /// 交易量
    pub volume: rust_decimal::Decimal,
    /// 交易金额
    pub amount: rust_decimal::Decimal,
    /// 平均价格
    pub average_price: rust_decimal::Decimal,
}
