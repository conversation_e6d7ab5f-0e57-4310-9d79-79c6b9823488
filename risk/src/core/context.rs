//! 风控上下文

use sigmax_core::{Order, Balance, TradingPair};
use sigmax_interfaces::Portfolio;
use std::collections::HashMap;

/// 风控上下文 - 简化版本
#[derive(Debug, Clone)]
pub struct RiskContext {
    /// 订单（如果是订单风控）
    pub order: Option<Order>,
    /// 余额（如果是持仓风控）
    pub balances: Vec<Balance>,
    /// 投资组合
    pub portfolio: Option<Portfolio>,
    /// 附加数据
    pub metadata: HashMap<String, String>,
}

impl RiskContext {
    /// 创建订单风控上下文
    pub fn for_order(order: Order) -> Self {
        Self {
            order: Some(order),
            balances: Vec::new(),
            portfolio: None,
            metadata: HashMap::new(),
        }
    }
    
    /// 创建持仓风控上下文
    pub fn for_balances(balances: Vec<Balance>) -> Self {
        Self {
            order: None,
            balances,
            portfolio: None,
            metadata: HashMap::new(),
        }
    }
    
    /// 创建投资组合风控上下文
    pub fn for_portfolio(portfolio: Portfolio) -> Self {
        let balances = portfolio.balances.clone();
        Self {
            order: None,
            balances,
            portfolio: Some(portfolio),
            metadata: HashMap::new(),
        }
    }
    
    /// 是否是订单上下文
    pub fn is_order_context(&self) -> bool {
        self.order.is_some()
    }
    
    /// 是否是持仓上下文
    pub fn is_position_context(&self) -> bool {
        !self.balances.is_empty()
    }
    
    /// 获取交易对（如果有）
    pub fn trading_pair(&self) -> Option<&TradingPair> {
        self.order.as_ref().map(|o| &o.trading_pair)
    }
}
