//! Mock订单仓储实现
//!
//! 提供完整的订单数据访问功能的内存实现，用于测试和开发

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use uuid::Uuid;

use sigmax_core::{
    Order, OrderId, OrderStatus, StrategyId, TradingPair, ExchangeId, 
    OrderSide, OrderType, Quantity, Price, Amount, SigmaXResult
};
use rust_decimal::Decimal;
use std::str::FromStr;

use super::traits::enhanced_order_repository::{
    EnhancedOrderRepository, OrderQueryFilter, Pagination, OrderSort, 
    OrderSortBy, SortDirection, OrderStatistics
};

/// Mock订单仓储实现
pub struct MockOrderRepository {
    /// 内存存储
    orders: Arc<RwLock<HashMap<OrderId, Order>>>,
    /// 统计信息
    stats: Arc<RwLock<RepositoryStats>>,
}

#[derive(Debug, <PERSON><PERSON>, Default)]
struct RepositoryStats {
    total_queries: u64,
    total_saves: u64,
    total_updates: u64,
    total_deletes: u64,
    last_operation: Option<DateTime<Utc>>,
}

impl MockOrderRepository {
    /// 创建新的Mock仓储
    pub fn new() -> Self {
        Self {
            orders: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(RwLock::new(RepositoryStats::default())),
        }
    }

    /// 创建带初始数据的Mock仓储
    pub fn with_initial_data(orders: Vec<Order>) -> Self {
        let mut order_map = HashMap::new();
        for order in orders {
            order_map.insert(order.id, order);
        }

        Self {
            orders: Arc::new(RwLock::new(order_map)),
            stats: Arc::new(RwLock::new(RepositoryStats::default())),
        }
    }

    /// 更新统计信息
    async fn update_stats<F>(&self, update_fn: F) 
    where 
        F: FnOnce(&mut RepositoryStats)
    {
        let mut stats = self.stats.write().await;
        update_fn(&mut stats);
        stats.last_operation = Some(Utc::now());
    }

    /// 过滤订单
    fn filter_orders(&self, orders: &[Order], filter: &OrderQueryFilter) -> Vec<Order> {
        orders.iter()
            .filter(|order| {
                // 策略ID过滤
                if let Some(ref strategy_id) = filter.strategy_id {
                    if order.strategy_id != Some(*strategy_id) {
                        return false;
                    }
                }

                // 交易所ID过滤
                if let Some(ref exchange_id) = filter.exchange_id {
                    if &order.exchange_id != exchange_id {
                        return false;
                    }
                }

                // 交易对过滤
                if let Some(ref trading_pair) = filter.trading_pair {
                    if &order.trading_pair != trading_pair {
                        return false;
                    }
                }

                // 订单状态过滤
                if let Some(ref status) = filter.status {
                    if &order.status != status {
                        return false;
                    }
                }

                // 订单方向过滤
                if let Some(ref side) = filter.side {
                    if &order.side != side {
                        return false;
                    }
                }

                // 订单类型过滤
                if let Some(ref order_type) = filter.order_type {
                    if &order.order_type != order_type {
                        return false;
                    }
                }

                // 时间范围过滤
                if let Some(created_after) = filter.created_after {
                    if order.created_at < created_after {
                        return false;
                    }
                }

                if let Some(created_before) = filter.created_before {
                    if order.created_at > created_before {
                        return false;
                    }
                }

                // 数量范围过滤
                if let Some(ref min_quantity) = filter.min_quantity {
                    if order.quantity < *min_quantity {
                        return false;
                    }
                }

                if let Some(ref max_quantity) = filter.max_quantity {
                    if order.quantity > *max_quantity {
                        return false;
                    }
                }

                // 价格范围过滤
                if let Some(ref min_price) = filter.min_price {
                    if let Some(ref order_price) = order.price {
                        if order_price < min_price {
                            return false;
                        }
                    } else {
                        return false; // 没有价格但需要价格过滤
                    }
                }

                if let Some(ref max_price) = filter.max_price {
                    if let Some(ref order_price) = order.price {
                        if order_price > max_price {
                            return false;
                        }
                    } else {
                        return false; // 没有价格但需要价格过滤
                    }
                }

                true
            })
            .cloned()
            .collect()
    }

    /// 排序订单
    fn sort_orders(&self, mut orders: Vec<Order>, sort: &OrderSort) -> Vec<Order> {
        orders.sort_by(|a, b| {
            let ordering = match sort.sort_by {
                OrderSortBy::CreatedAt => a.created_at.cmp(&b.created_at),
                OrderSortBy::UpdatedAt => a.updated_at.cmp(&b.updated_at),
                OrderSortBy::Quantity => a.quantity.cmp(&b.quantity),
                OrderSortBy::Price => {
                    match (&a.price, &b.price) {
                        (Some(price_a), Some(price_b)) => price_a.cmp(price_b),
                        (Some(_), None) => std::cmp::Ordering::Greater,
                        (None, Some(_)) => std::cmp::Ordering::Less,
                        (None, None) => std::cmp::Ordering::Equal,
                    }
                },
                OrderSortBy::Status => a.status.cmp(&b.status),
            };

            match sort.direction {
                SortDirection::Asc => ordering,
                SortDirection::Desc => ordering.reverse(),
            }
        });

        orders
    }

    /// 应用分页
    fn apply_pagination(&self, orders: Vec<Order>, pagination: &Pagination) -> Vec<Order> {
        orders.into_iter()
            .skip(pagination.offset)
            .take(pagination.limit)
            .collect()
    }
}

#[async_trait]
impl EnhancedOrderRepository for MockOrderRepository {
    // ============================================================================
    // 基础 CRUD 操作
    // ============================================================================
    
    async fn save(&self, order: &Order) -> SigmaXResult<()> {
        let mut orders = self.orders.write().await;
        
        let order_id = order.id;
        let mut order_to_save = order.clone();
        order_to_save.updated_at = Utc::now();
        
        orders.insert(order_id, order_to_save);
        
        self.update_stats(|stats| {
            stats.total_saves += 1;
        }).await;
        
        tracing::info!("💾 Mock保存订单: {}", order_id);
        Ok(())
    }
    
    async fn find_by_id(&self, id: OrderId) -> SigmaXResult<Option<Order>> {
        let orders = self.orders.read().await;
        
        self.update_stats(|stats| {
            stats.total_queries += 1;
        }).await;
        
        Ok(orders.get(&id).cloned())
    }
    
    async fn update(&self, order: &Order) -> SigmaXResult<()> {
        let mut orders = self.orders.write().await;
        
        let order_id = order.id;
        if orders.contains_key(&order_id) {
            let mut updated_order = order.clone();
            updated_order.updated_at = Utc::now();
            orders.insert(order_id, updated_order);
            
            self.update_stats(|stats| {
                stats.total_updates += 1;
            }).await;
            
            tracing::info!("📝 Mock更新订单: {}", order_id);
            Ok(())
        } else {
            Err(sigmax_core::SigmaXError::validation(
                sigmax_core::ValidationErrorCode::Required,
                "order_id",
                format!("Order not found: {}", order_id)
            ))
        }
    }
    
    async fn delete(&self, id: OrderId) -> SigmaXResult<()> {
        let mut orders = self.orders.write().await;
        
        if orders.remove(&id).is_some() {
            self.update_stats(|stats| {
                stats.total_deletes += 1;
            }).await;
            
            tracing::info!("🗑️ Mock删除订单: {}", id);
            Ok(())
        } else {
            Err(sigmax_core::SigmaXError::validation(
                sigmax_core::ValidationErrorCode::Required,
                "order_id",
                format!("Order not found: {}", id)
            ))
        }
    }
    
    async fn exists(&self, id: OrderId) -> SigmaXResult<bool> {
        let orders = self.orders.read().await;
        Ok(orders.contains_key(&id))
    }

    // ============================================================================
    // 批量操作
    // ============================================================================
    
    async fn save_batch(&self, orders: &[Order]) -> SigmaXResult<()> {
        for order in orders {
            self.save(order).await?;
        }
        tracing::info!("📦 Mock批量保存订单: {} 个", orders.len());
        Ok(())
    }
    
    async fn update_status_batch(&self, ids: &[OrderId], status: OrderStatus) -> SigmaXResult<()> {
        let mut orders = self.orders.write().await;
        let mut updated_count = 0;
        
        for id in ids {
            if let Some(order) = orders.get_mut(id) {
                order.status = status;
                order.updated_at = Utc::now();
                updated_count += 1;
            }
        }
        
        self.update_stats(|stats| {
            stats.total_updates += updated_count;
        }).await;
        
        tracing::info!("📊 Mock批量更新订单状态: {} 个订单更新为 {:?}", updated_count, status);
        Ok(())
    }
    
    async fn delete_batch(&self, ids: &[OrderId]) -> SigmaXResult<()> {
        let mut orders = self.orders.write().await;
        let mut deleted_count = 0;
        
        for id in ids {
            if orders.remove(id).is_some() {
                deleted_count += 1;
            }
        }
        
        self.update_stats(|stats| {
            stats.total_deletes += deleted_count;
        }).await;
        
        tracing::info!("🗑️ Mock批量删除订单: {} 个", deleted_count);
        Ok(())
    }

    // ============================================================================
    // 条件查询
    // ============================================================================
    
    async fn find_by_filter(
        &self, 
        filter: &OrderQueryFilter,
        pagination: Option<&Pagination>,
        sort: Option<&OrderSort>
    ) -> SigmaXResult<Vec<Order>> {
        let orders = self.orders.read().await;
        let all_orders: Vec<Order> = orders.values().cloned().collect();
        
        // 应用过滤器
        let mut filtered_orders = self.filter_orders(&all_orders, filter);
        
        // 应用排序
        if let Some(sort) = sort {
            filtered_orders = self.sort_orders(filtered_orders, sort);
        }
        
        // 应用分页
        if let Some(pagination) = pagination {
            filtered_orders = self.apply_pagination(filtered_orders, pagination);
        }
        
        self.update_stats(|stats| {
            stats.total_queries += 1;
        }).await;
        
        Ok(filtered_orders)
    }
    
    async fn find_by_status(&self, status: OrderStatus) -> SigmaXResult<Vec<Order>> {
        let orders = self.orders.read().await;
        let result: Vec<Order> = orders.values()
            .filter(|order| order.status == status)
            .cloned()
            .collect();
        
        self.update_stats(|stats| {
            stats.total_queries += 1;
        }).await;
        
        Ok(result)
    }
    
    async fn find_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Vec<Order>> {
        let orders = self.orders.read().await;
        let result: Vec<Order> = orders.values()
            .filter(|order| order.strategy_id == Some(strategy_id))
            .cloned()
            .collect();
        
        self.update_stats(|stats| {
            stats.total_queries += 1;
        }).await;
        
        Ok(result)
    }
    
    async fn find_by_trading_pair(&self, trading_pair: &TradingPair) -> SigmaXResult<Vec<Order>> {
        let orders = self.orders.read().await;
        let result: Vec<Order> = orders.values()
            .filter(|order| &order.trading_pair == trading_pair)
            .cloned()
            .collect();
        
        self.update_stats(|stats| {
            stats.total_queries += 1;
        }).await;
        
        Ok(result)
    }
    
    async fn find_by_exchange(&self, exchange_id: &ExchangeId) -> SigmaXResult<Vec<Order>> {
        let orders = self.orders.read().await;
        let result: Vec<Order> = orders.values()
            .filter(|order| &order.exchange_id == exchange_id)
            .cloned()
            .collect();
        
        self.update_stats(|stats| {
            stats.total_queries += 1;
        }).await;
        
        Ok(result)
    }
    
    async fn find_pending_orders(&self) -> SigmaXResult<Vec<Order>> {
        self.find_by_status(OrderStatus::Pending).await
    }
    
    async fn find_active_orders(&self) -> SigmaXResult<Vec<Order>> {
        let orders = self.orders.read().await;
        let result: Vec<Order> = orders.values()
            .filter(|order| matches!(order.status, OrderStatus::Pending | OrderStatus::PartiallyFilled))
            .cloned()
            .collect();
        
        self.update_stats(|stats| {
            stats.total_queries += 1;
        }).await;
        
        Ok(result)
    }
    
    async fn find_by_timerange(
        &self, 
        start: DateTime<Utc>, 
        end: DateTime<Utc>
    ) -> SigmaXResult<Vec<Order>> {
        let orders = self.orders.read().await;
        let result: Vec<Order> = orders.values()
            .filter(|order| order.created_at >= start && order.created_at <= end)
            .cloned()
            .collect();
        
        self.update_stats(|stats| {
            stats.total_queries += 1;
        }).await;
        
        Ok(result)
    }

    // ============================================================================
    // 聚合查询
    // ============================================================================
    
    async fn count_by_status(&self, status: OrderStatus) -> SigmaXResult<u64> {
        let orders = self.orders.read().await;
        let count = orders.values()
            .filter(|order| order.status == status)
            .count() as u64;
        
        self.update_stats(|stats| {
            stats.total_queries += 1;
        }).await;
        
        Ok(count)
    }
    
    async fn count_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<u64> {
        let orders = self.orders.read().await;
        let count = orders.values()
            .filter(|order| order.strategy_id == Some(strategy_id))
            .count() as u64;
        
        self.update_stats(|stats| {
            stats.total_queries += 1;
        }).await;
        
        Ok(count)
    }
    
    async fn get_total_volume_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Quantity> {
        let orders = self.orders.read().await;
        let total = orders.values()
            .filter(|order| order.strategy_id == Some(strategy_id))
            .map(|order| order.quantity)
            .fold(Quantity::from(0), |acc, qty| acc + qty);
        
        self.update_stats(|stats| {
            stats.total_queries += 1;
        }).await;
        
        Ok(total)
    }
    
    async fn get_total_value_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Amount> {
        let orders = self.orders.read().await;
        let total = orders.values()
            .filter(|order| order.strategy_id == Some(strategy_id))
            .filter_map(|order| {
                order.price.map(|price| {
                    Amount::from(order.quantity * price)
                })
            })
            .fold(Amount::ZERO, |acc, value| acc + value);
        
        self.update_stats(|stats| {
            stats.total_queries += 1;
        }).await;
        
        Ok(total)
    }
    
    async fn get_statistics(&self, filter: Option<&OrderQueryFilter>) -> SigmaXResult<OrderStatistics> {
        let orders = self.orders.read().await;
        let all_orders: Vec<Order> = orders.values().cloned().collect();
        
        let target_orders = if let Some(filter) = filter {
            self.filter_orders(&all_orders, filter)
        } else {
            all_orders
        };
        
        let total_count = target_orders.len() as u64;
        let pending_count = target_orders.iter().filter(|o| o.status == OrderStatus::Pending).count() as u64;
        let filled_count = target_orders.iter().filter(|o| o.status == OrderStatus::Filled).count() as u64;
        let cancelled_count = target_orders.iter().filter(|o| o.status == OrderStatus::Cancelled).count() as u64;
        
        let total_volume = target_orders.iter()
            .map(|o| o.quantity)
            .fold(Quantity::from(0), |acc, qty| acc + qty);
        
        let total_value = target_orders.iter()
            .filter_map(|o| o.price.map(|price| Amount::from(o.quantity * price)))
            .fold(Amount::ZERO, |acc, value| acc + value);
        
        let average_order_size = if total_count > 0 {
            Quantity::from(total_volume / Decimal::from(total_count))
        } else {
            Quantity::from(0)
        };
        
        self.update_stats(|stats| {
            stats.total_queries += 1;
        }).await;
        
        Ok(OrderStatistics {
            total_count,
            pending_count,
            filled_count,
            cancelled_count,
            total_volume,
            total_value,
            average_order_size,
        })
    }
    
    async fn get_strategy_statistics(&self, strategy_id: StrategyId) -> SigmaXResult<OrderStatistics> {
        let filter = OrderQueryFilter {
            strategy_id: Some(strategy_id),
            ..Default::default()
        };
        self.get_statistics(Some(&filter)).await
    }

    // ============================================================================
    // 业务查询
    // ============================================================================
    
    async fn find_orders_for_risk_check(&self) -> SigmaXResult<Vec<Order>> {
        // 查找需要风险检查的订单：待处理的大额订单
        let orders = self.orders.read().await;
        let result: Vec<Order> = orders.values()
            .filter(|order| {
                order.status == OrderStatus::Pending && 
                order.price.map(|p| p * order.quantity).unwrap_or(Decimal::ZERO) > Decimal::from(10000)
            })
            .cloned()
            .collect();
        
        self.update_stats(|stats| {
            stats.total_queries += 1;
        }).await;
        
        Ok(result)
    }
    
    async fn find_timeout_pending_orders(&self, timeout_minutes: u32) -> SigmaXResult<Vec<Order>> {
        let orders = self.orders.read().await;
        let timeout_threshold = Utc::now() - chrono::Duration::minutes(timeout_minutes as i64);
        
        let result: Vec<Order> = orders.values()
            .filter(|order| {
                order.status == OrderStatus::Pending && order.created_at < timeout_threshold
            })
            .cloned()
            .collect();
        
        self.update_stats(|stats| {
            stats.total_queries += 1;
        }).await;
        
        Ok(result)
    }
    
    async fn find_large_orders(&self, min_value: Amount) -> SigmaXResult<Vec<Order>> {
        let orders = self.orders.read().await;
        let result: Vec<Order> = orders.values()
            .filter(|order| {
                order.price.map(|p| Amount::from(p * order.quantity))
                    .unwrap_or(Amount::ZERO) >= min_value
            })
            .cloned()
            .collect();
        
        self.update_stats(|stats| {
            stats.total_queries += 1;
        }).await;
        
        Ok(result)
    }
    
    async fn find_abnormal_orders(
        &self, 
        trading_pair: &TradingPair, 
        market_price: Price, 
        deviation_percent: f64
    ) -> SigmaXResult<Vec<Order>> {
        let orders = self.orders.read().await;
        let lower_bound = market_price * Decimal::from_str(&format!("{:.6}", 1.0 - deviation_percent / 100.0)).unwrap_or(Decimal::ZERO);
        let upper_bound = market_price * Decimal::from_str(&format!("{:.6}", 1.0 + deviation_percent / 100.0)).unwrap_or(Decimal::ZERO);
        
        let result: Vec<Order> = orders.values()
            .filter(|order| {
                &order.trading_pair == trading_pair &&
                order.price.map(|p| {
                    let price_val = p;
                    price_val < lower_bound || price_val > upper_bound
                }).unwrap_or(false)
            })
            .cloned()
            .collect();
        
        self.update_stats(|stats| {
            stats.total_queries += 1;
        }).await;
        
        Ok(result)
    }

    // ============================================================================
    // 性能优化查询
    // ============================================================================
    
    async fn get_recent_orders(&self, limit: usize) -> SigmaXResult<Vec<Order>> {
        let orders = self.orders.read().await;
        let mut all_orders: Vec<Order> = orders.values().cloned().collect();
        
        // 按创建时间降序排列
        all_orders.sort_by(|a, b| b.created_at.cmp(&a.created_at));
        all_orders.truncate(limit);
        
        self.update_stats(|stats| {
            stats.total_queries += 1;
        }).await;
        
        Ok(all_orders)
    }
    
    async fn get_orders_by_popular_pairs(&self, limit: usize) -> SigmaXResult<Vec<Order>> {
        let orders = self.orders.read().await;
        
        // 统计交易对的订单数量
        let mut pair_counts = HashMap::new();
        for order in orders.values() {
            *pair_counts.entry(order.trading_pair.clone()).or_insert(0) += 1;
        }
        
        // 获取最热门的交易对
        let mut sorted_pairs: Vec<_> = pair_counts.into_iter().collect();
        sorted_pairs.sort_by(|a, b| b.1.cmp(&a.1));
        
        let popular_pairs: Vec<TradingPair> = sorted_pairs.into_iter()
            .take(5) // 取前5个最热门的交易对
            .map(|(pair, _)| pair)
            .collect();
        
        // 获取这些交易对的最新订单
        let mut result = Vec::new();
        for pair in popular_pairs {
            let pair_orders: Vec<Order> = orders.values()
                .filter(|order| order.trading_pair == pair)
                .cloned()
                .collect();
            result.extend(pair_orders);
        }
        
        result.sort_by(|a, b| b.created_at.cmp(&a.created_at));
        result.truncate(limit);
        
        self.update_stats(|stats| {
            stats.total_queries += 1;
        }).await;
        
        Ok(result)
    }
    
    async fn preload_orders_with_relations(&self, order_ids: &[OrderId]) -> SigmaXResult<Vec<Order>> {
        let orders = self.orders.read().await;
        let result: Vec<Order> = order_ids.iter()
            .filter_map(|id| orders.get(id).cloned())
            .collect();
        
        self.update_stats(|stats| {
            stats.total_queries += 1;
        }).await;
        
        tracing::info!("🔗 Mock预加载订单关联数据: {}/{} 个订单", result.len(), order_ids.len());
        Ok(result)
    }
}

impl Default for MockOrderRepository {
    fn default() -> Self {
        Self::new()
    }
}