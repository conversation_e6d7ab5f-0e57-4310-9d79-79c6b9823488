//! 风控相关接口定义
//!
//! 实现面向接口设计，定义清晰的风控接口契约
//! 
//! ## 设计原则
//! - 单一职责：每个接口有明确的职责范围
//! - 一致性：统一的方法命名和返回类型
//! - 可扩展：支持未来功能扩展
//! - 可测试：便于Mock和单元测试

use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use sigmax_core::{Order, Balance, TradingPair, Quantity, SigmaXResult, RiskCheckResult, MarketData};
use sigmax_core::types::RiskMetrics;
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;

// ============================================================================
// 统一风控接口 - 重构版本
// ============================================================================

/// 风控控制器接口 - 统一的风控检查入口
/// 
/// 此接口整合了所有风控相关功能，提供一致的API体验
#[async_trait]
pub trait RiskController: Send + Sync {
    /// 核心风控检查 - 订单风控
    /// 
    /// # 参数
    /// - `order`: 待检查的订单
    /// - `context`: 可选的风控上下文（策略类型、市场数据等）
    /// 
    /// # 返回
    /// - `RiskCheckResult`: 详细的风控检查结果
    async fn check_order_risk(&self, order: &Order, context: Option<&RiskContext>) -> SigmaXResult<RiskCheckResult>;

    /// 核心风控检查 - 持仓风控
    /// 
    /// # 参数
    /// - `balances`: 当前持仓余额
    /// - `context`: 可选的风控上下文
    /// 
    /// # 返回
    /// - `RiskCheckResult`: 详细的风控检查结果
    async fn check_position_risk(&self, balances: &[Balance], context: Option<&RiskContext>) -> SigmaXResult<RiskCheckResult>;

    /// 批量订单风控检查
    /// 
    /// # 参数
    /// - `orders`: 待检查的订单列表
    /// - `context`: 可选的风控上下文
    /// 
    /// # 返回
    /// - `Vec<RiskCheckResult>`: 每个订单的风控检查结果
    async fn batch_check_orders(&self, orders: &[Order], context: Option<&RiskContext>) -> SigmaXResult<Vec<RiskCheckResult>>;

    /// 规则管理 - 重新加载风控规则
    async fn reload_rules(&self) -> SigmaXResult<()>;

    /// 获取风险指标
    async fn get_risk_metrics(&self) -> SigmaXResult<RiskMetrics>;

    /// 获取最大订单数量限制
    /// 
    /// # 参数
    /// - `trading_pair`: 交易对
    /// - `context`: 可选的风控上下文
    /// 
    /// # 返回
    /// - `Quantity`: 允许的最大订单数量
    async fn get_max_order_size(&self, trading_pair: &TradingPair, context: Option<&RiskContext>) -> SigmaXResult<Quantity>;

    // ========== 性能优化接口 ==========
    
    /// 并行批量风控检查 - 性能优化版本
    /// 
    /// 与 batch_check_orders 的区别：
    /// - 支持并行处理，提升性能
    /// - 返回包含处理时间的详细结果
    /// - 支持部分失败场景
    async fn parallel_check_orders(&self, orders: &[Order], context: Option<&RiskContext>) -> SigmaXResult<BatchRiskResult>;

    /// 预检查订单（快速模式）
    /// 
    /// 仅执行关键风控规则，用于高频交易场景
    async fn pre_check_order(&self, order: &Order, context: Option<&RiskContext>) -> SigmaXResult<bool>;

    /// 缓存风险计算结果
    /// 
    /// 将风险计算结果缓存，用于重复查询优化
    async fn cache_risk_calculation(&self, cache_key: &str, context: &RiskContext, ttl_seconds: Option<u64>) -> SigmaXResult<()>;

    /// 获取缓存的风险计算结果
    async fn get_cached_risk_calculation(&self, cache_key: &str) -> SigmaXResult<Option<RiskMetrics>>;
}

/// 风控适配器接口 - 用于引擎集成
/// 
/// 此接口提供简化的风控检查方法，主要用于引擎内部集成
#[async_trait]
pub trait RiskAdapter: Send + Sync {
    /// 简化的订单风控检查（返回布尔值）
    /// 
    /// 基于 RiskController 实现，但返回简化的布尔结果
    async fn check_order_simple(&self, order: &Order) -> SigmaXResult<bool>;

    /// 简化的持仓风控检查（返回布尔值）
    async fn check_position_simple(&self, balances: &[Balance]) -> SigmaXResult<bool>;

    /// 获取适配器类型标识
    fn adapter_type(&self) -> &str;

    /// 获取适配器指标
    async fn get_adapter_metrics(&self) -> SigmaXResult<AdapterMetrics>;
}

/// 风控策略接口
pub trait RiskPolicy: Send + Sync {
    /// 评估风控策略
    fn evaluate(&self, context: &RiskContext) -> PolicyResult;
    
    /// 获取策略名称
    fn name(&self) -> &str;
    
    /// 获取策略描述
    fn description(&self) -> &str;
    
    /// 获取策略优先级
    fn priority(&self) -> i32;
    
    /// 检查策略是否启用
    fn is_enabled(&self) -> bool;
}

/// 风控计算器接口
#[async_trait]
pub trait RiskCalculator: Send + Sync {
    /// 计算基础风控指标
    async fn calculate_basic_metrics(&self, context: &RiskContext) -> BasicRiskMetrics;
    
    /// 计算高级风控指标
    async fn calculate_advanced_metrics(&self, context: &RiskContext) -> AdvancedRiskMetrics;
    
    /// 计算投资组合风险
    async fn calculate_portfolio_risk(&self, balances: &[Balance]) -> PortfolioRisk;
}

// ============================================================================
// 数据模型
// ============================================================================

/// 风控结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RiskResult {
    /// 通过风控检查
    Approved,
    /// 被风控拒绝
    Rejected(String),
    /// 需要人工审核
    RequiresApproval(String),
    /// 风控检查出错
    Error(String),
}

impl RiskResult {
    pub fn is_approved(&self) -> bool {
        matches!(self, RiskResult::Approved)
    }
    
    pub fn is_rejected(&self) -> bool {
        matches!(self, RiskResult::Rejected(_))
    }
}

// ============================================================================
// 数据模型 - 统一版本
// ============================================================================

/// 风控上下文 - 增强版本
/// 
/// 提供风控检查所需的所有上下文信息，支持订单级和投资组合级的风控检查
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskContext {
    // ========== 核心业务数据 ==========
    /// 待检查的订单（订单级风控时使用）
    pub order: Option<Order>,
    /// 投资组合信息（投资组合级风控时使用）
    pub portfolio: Option<crate::Portfolio>,  // 使用统一的 Portfolio 定义
    /// 账户余额列表（持仓风控时使用）
    pub balances: Vec<Balance>,
    
    // ========== 策略相关信息 ==========
    /// 策略类型（可选）
    pub strategy_type: Option<String>,
    /// 策略ID（可选）
    pub strategy_id: Option<Uuid>,
    
    // ========== 市场相关信息 ==========
    /// 交易对信息
    pub trading_pair: Option<TradingPair>,
    /// 当前市场数据
    pub market_data: Option<MarketData>,
    
    // ========== 用户和会话信息 ==========
    /// 用户ID（用于个性化风控）
    pub user_id: Option<String>,
    /// 会话ID（用于追踪）
    pub session_id: Option<String>,
    
    // ========== 系统信息 ==========
    /// 时间戳
    pub timestamp: DateTime<Utc>,
    /// 扩展元数据
    pub metadata: HashMap<String, serde_json::Value>,
}

impl RiskContext {
    /// 创建新的风控上下文
    pub fn new() -> Self {
        Self {
            order: None,
            portfolio: None,
            balances: Vec::new(),
            strategy_type: None,
            strategy_id: None,
            trading_pair: None,
            market_data: None,
            user_id: None,
            session_id: None,
            timestamp: Utc::now(),
            metadata: HashMap::new(),
        }
    }

    /// 设置订单
    pub fn with_order(mut self, order: Order) -> Self {
        self.order = Some(order);
        self
    }

    /// 设置投资组合
    pub fn with_portfolio(mut self, portfolio: crate::Portfolio) -> Self {
        self.portfolio = Some(portfolio);
        self
    }

    /// 设置余额列表
    pub fn with_balances(mut self, balances: Vec<Balance>) -> Self {
        self.balances = balances;
        self
    }

    /// 添加单个余额
    pub fn add_balance(mut self, balance: Balance) -> Self {
        self.balances.push(balance);
        self
    }

    /// 设置策略类型
    pub fn with_strategy_type(mut self, strategy_type: impl Into<String>) -> Self {
        self.strategy_type = Some(strategy_type.into());
        self
    }

    /// 设置策略ID
    pub fn with_strategy_id(mut self, strategy_id: Uuid) -> Self {
        self.strategy_id = Some(strategy_id);
        self
    }

    /// 设置交易对
    pub fn with_trading_pair(mut self, trading_pair: TradingPair) -> Self {
        self.trading_pair = Some(trading_pair);
        self
    }

    /// 设置市场数据
    pub fn with_market_data(mut self, market_data: MarketData) -> Self {
        self.market_data = Some(market_data);
        self
    }

    /// 设置用户ID
    pub fn with_user_id(mut self, user_id: impl Into<String>) -> Self {
        self.user_id = Some(user_id.into());
        self
    }

    /// 设置会话ID
    pub fn with_session_id(mut self, session_id: impl Into<String>) -> Self {
        self.session_id = Some(session_id.into());
        self
    }

    /// 添加元数据
    pub fn with_metadata(mut self, key: impl Into<String>, value: serde_json::Value) -> Self {
        self.metadata.insert(key.into(), value);
        self
    }

    /// 获取交易对符号字符串
    pub fn get_trading_pair_symbol(&self) -> Option<String> {
        self.trading_pair.as_ref().map(|tp| tp.symbol())
    }

    /// 检查是否有基本的上下文信息
    pub fn is_valid(&self) -> bool {
        // 至少需要有时间戳
        true // 因为timestamp总是有值
    }

    /// 检查是否为订单级风控上下文
    pub fn is_order_context(&self) -> bool {
        self.order.is_some()
    }

    /// 检查是否为投资组合级风控上下文
    pub fn is_portfolio_context(&self) -> bool {
        self.portfolio.is_some() || !self.balances.is_empty()
    }

    /// 获取上下文类型描述
    pub fn context_type(&self) -> &'static str {
        if self.is_order_context() {
            "order"
        } else if self.is_portfolio_context() {
            "portfolio"
        } else {
            "general"
        }
    }

    /// 创建订单风控上下文的快捷方法
    pub fn for_order(order: Order) -> Self {
        Self::new().with_order(order)
    }

    /// 创建持仓风控上下文的快捷方法
    pub fn for_balances(balances: Vec<Balance>) -> Self {
        Self::new().with_balances(balances)
    }

    /// 创建投资组合风控上下文的快捷方法
    pub fn for_portfolio(portfolio: crate::Portfolio) -> Self {
        Self::new().with_portfolio(portfolio)
    }
}

impl Default for RiskContext {
    fn default() -> Self {
        Self::new()
    }
}

// 注意：Portfolio 相关类型统一使用 core 模块或 trading 接口中的定义
// 不在此处重复定义，避免数据模型分散

/// 适配器指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdapterMetrics {
    /// 适配器类型
    pub adapter_type: String,
    /// 缓存命中率
    pub cache_hit_rate: f64,
    /// 平均延迟（毫秒）
    pub avg_latency_ms: f64,
    /// 吞吐量（每秒请求数）
    pub throughput_rps: f64,
    /// 错误率
    pub error_rate: f64,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
}

// 注意：MarketData 统一使用 sigmax_core::MarketData，不在此处重复定义

// 注意：风控指标统一使用 sigmax_core::RiskMetrics，不在此处重复定义

/// 基础风控指标（接口层特有）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BasicRiskMetrics {
    pub volatility: Option<Decimal>,
    pub leverage: Option<Decimal>,
    pub concentration: Option<Decimal>,
    pub liquidity_score: Option<Decimal>,
    pub calculated_at: DateTime<Utc>,
}

/// 高级风控指标（接口层特有）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdvancedRiskMetrics {
    pub var_1d: Option<Decimal>,
    pub var_5d: Option<Decimal>,
    pub expected_shortfall: Option<Decimal>,
    pub beta: Option<Decimal>,
    pub correlation: Option<HashMap<String, Decimal>>,
}

/// 投资组合风险（接口层特有）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortfolioRisk {
    pub total_exposure: Decimal,
    pub concentration_risk: Decimal,
    pub correlation_risk: Decimal,
    pub liquidity_risk: Decimal,
}

/// 批量风控检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchRiskResult {
    /// 每个订单的检查结果
    pub results: Vec<RiskCheckResult>,
    /// 成功处理的订单数量
    pub success_count: usize,
    /// 失败处理的订单数量  
    pub failure_count: usize,
    /// 总处理时间（毫秒）
    pub total_processing_time_ms: u64,
    /// 平均处理时间（毫秒）
    pub avg_processing_time_ms: f64,
    /// 并行度（实际使用的并发数）
    pub parallelism: usize,
    /// 处理时间戳
    pub processed_at: DateTime<Utc>,
}

impl BatchRiskResult {
    /// 检查是否所有订单都通过了风控
    pub fn all_passed(&self) -> bool {
        self.failure_count == 0 && self.results.iter().all(|r| r.passed)
    }

    /// 获取失败的订单索引
    pub fn failed_indices(&self) -> Vec<usize> {
        self.results
            .iter()
            .enumerate()
            .filter(|(_, result)| !result.passed)
            .map(|(index, _)| index)
            .collect()
    }

    /// 获取成功率
    pub fn success_rate(&self) -> f64 {
        if self.results.is_empty() {
            0.0
        } else {
            self.success_count as f64 / self.results.len() as f64
        }
    }
}

/// 风控告警
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAlert {
    pub id: Uuid,
    pub severity: AlertSeverity,
    pub message: String,
    pub source: String,
    pub timestamp: DateTime<Utc>,
}

/// 告警严重程度
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertSeverity {
    Info,
    Low,
    Medium,
    High,
    Critical,
    Emergency,
}

impl std::fmt::Display for AlertSeverity {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AlertSeverity::Info => write!(f, "INFO"),
            AlertSeverity::Low => write!(f, "LOW"),
            AlertSeverity::Medium => write!(f, "MEDIUM"),
            AlertSeverity::High => write!(f, "HIGH"),
            AlertSeverity::Critical => write!(f, "CRITICAL"),
            AlertSeverity::Emergency => write!(f, "EMERGENCY"),
        }
    }
}

/// 策略结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PolicyResult {
    /// 策略通过
    Approved,
    /// 策略拒绝
    Rejected(String),
    /// 需要人工审核
    RequiresApproval(String),
    /// 策略警告（向后兼容）
    Warning(String),
    /// 策略错误
    Error(String),
}

// ============================================================================
// 错误类型
// ============================================================================

/// 风控错误
#[derive(Debug, thiserror::Error)]
pub enum RiskError {
    #[error("Risk calculation failed: {message}")]
    CalculationFailed { message: String },
    
    #[error("Risk policy violation: {policy} - {reason}")]
    PolicyViolation { policy: String, reason: String },
    
    #[error("Insufficient data for risk assessment: {missing}")]
    InsufficientData { missing: String },
    
    #[error("Risk service unavailable: {service}")]
    ServiceUnavailable { service: String },
    
    #[error("Configuration error: {message}")]
    Configuration { message: String },
    
    #[error("Timeout: operation timed out after {timeout_ms}ms")]
    Timeout { timeout_ms: u64 },
}

/// 策略错误
#[derive(Debug, thiserror::Error)]
pub enum PolicyError {
    #[error("Policy evaluation failed: {policy} - {reason}")]
    EvaluationFailed { policy: String, reason: String },
    
    #[error("Policy not found: {name}")]
    NotFound { name: String },
    
    #[error("Policy configuration invalid: {message}")]
    InvalidConfiguration { message: String },
}