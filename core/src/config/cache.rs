//! SigmaX Core Cache Configuration
//!
//! ## 主要职责
//!
//! 1. **缓存配置模型** - 定义缓存相关的配置参数
//!    - 基础缓存参数
//!    - TTL和容量限制
//!    - 清理和维护配置
//!
//! 2. **配置验证** - 提供配置参数的验证规则
//!    - 数值范围验证
//!    - 逻辑一致性验证
//!    - 必填字段验证
//!
//! ## 设计原则
//!
//! - **配置导向**: 专注于配置参数定义，不包含实现逻辑
//! - **技术无关**: 不绑定特定的缓存技术（如Redis）
//! - **简洁明确**: 只包含核心的缓存配置参数
//! - **验证友好**: 使用validator进行参数验证

use serde::{Deserialize, Serialize};
use validator::Validate;
use crate::SigmaXResult;

/// 缓存配置
///
/// 定义系统缓存的核心配置参数，专注于配置模型而非具体实现
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Validate)]
pub struct CacheConfig {
    /// 配置名称
    #[validate(length(min = 1, max = 100, message = "缓存配置名称长度必须在1-100个字符之间"))]
    pub name: String,

    /// 配置描述
    #[validate(length(max = 500, message = "缓存配置描述长度不能超过500个字符"))]
    pub description: Option<String>,

    /// 是否启用缓存
    pub enabled: bool,

    // ============================================================================
    // 核心缓存配置参数
    // ============================================================================

    /// 默认缓存TTL(秒)
    #[validate(range(min = 1, max = 86400, message = "默认TTL必须在1-86400秒之间"))]
    pub default_ttl_seconds: u32,

    /// 最大缓存内存(MB)
    #[validate(range(min = 1, max = 10240, message = "最大缓存内存必须在1-10240MB之间"))]
    pub max_memory_mb: u32,

    /// 最大缓存条目数
    #[validate(range(min = 1, max = 10000000, message = "最大缓存条目数必须在1-10000000之间"))]
    pub max_entries: u64,

    /// 缓存清理间隔(秒)
    #[validate(range(min = 1, max = 3600, message = "缓存清理间隔必须在1-3600秒之间"))]
    pub cleanup_interval_seconds: u32,

    /// 内存使用阈值(百分比)
    #[validate(range(min = 1, max = 100, message = "内存使用阈值必须在1-100%之间"))]
    pub memory_threshold_percentage: u32,

    // ============================================================================
    // 缓存策略配置
    // ============================================================================

    /// 缓存淘汰策略
    pub eviction_policy: CacheEvictionPolicy,

    /// 是否启用缓存预热
    pub warmup_enabled: bool,

    /// 预热超时时间(秒)
    #[validate(range(min = 1, max = 300, message = "预热超时时间必须在1-300秒之间"))]
    pub warmup_timeout_seconds: u32,
}

// ============================================================================
// 缓存淘汰策略枚举
// ============================================================================

/// 缓存淘汰策略
///
/// 定义缓存满时的数据淘汰策略
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum CacheEvictionPolicy {
    /// 最近最少使用 (Least Recently Used)
    LRU,
    /// 最不经常使用 (Least Frequently Used)
    LFU,
    /// 先进先出 (First In First Out)
    FIFO,
    /// 随机淘汰
    Random,
    /// 基于TTL的淘汰
    TTL,
}

// ============================================================================
// 默认实现
// ============================================================================

impl Default for CacheEvictionPolicy {
    fn default() -> Self {
        CacheEvictionPolicy::LRU
    }
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            name: "Default Cache Config".to_string(),
            description: Some("默认缓存配置".to_string()),
            enabled: true,
            default_ttl_seconds: 3600, // 1小时
            max_memory_mb: 512, // 512MB
            max_entries: 100000, // 10万条目
            cleanup_interval_seconds: 300, // 5分钟
            memory_threshold_percentage: 80, // 80%
            eviction_policy: CacheEvictionPolicy::default(),
            warmup_enabled: false,
            warmup_timeout_seconds: 60, // 1分钟
        }
    }
}

// ============================================================================
// 配置验证实现
// ============================================================================

impl CacheConfig {
    /// 创建新的缓存配置
    pub fn new(name: impl Into<String>) -> Self {
        Self {
            name: name.into(),
            ..Default::default()
        }
    }

    /// 设置是否启用
    pub fn with_enabled(mut self, enabled: bool) -> Self {
        self.enabled = enabled;
        self
    }

    /// 设置默认TTL
    pub fn with_default_ttl(mut self, ttl_seconds: u32) -> Self {
        self.default_ttl_seconds = ttl_seconds;
        self
    }

    /// 设置最大内存
    pub fn with_max_memory(mut self, memory_mb: u32) -> Self {
        self.max_memory_mb = memory_mb;
        self
    }

    /// 设置最大条目数
    pub fn with_max_entries(mut self, max_entries: u64) -> Self {
        self.max_entries = max_entries;
        self
    }

    /// 设置淘汰策略
    pub fn with_eviction_policy(mut self, policy: CacheEvictionPolicy) -> Self {
        self.eviction_policy = policy;
        self
    }

    /// 执行完整的缓存配置验证
    pub fn validate_complete(&self) -> SigmaXResult<()> {
        // 首先执行基础验证（通过validator derive宏）
        if let Err(validation_errors) = <Self as Validate>::validate(self) {
            return Err(crate::SigmaXError::from(validation_errors));
        }

        // 跨字段验证逻辑
        if self.enabled {
            // 如果启用缓存，检查关键参数的合理性
            if self.max_memory_mb == 0 {
                return Err(crate::SigmaXError::validation(
                    crate::ValidationErrorCode::InvalidRange,
                    "max_memory_mb",
                    "启用缓存时，最大内存不能为0"
                ));
            }

            if self.max_entries == 0 {
                return Err(crate::SigmaXError::validation(
                    crate::ValidationErrorCode::InvalidRange,
                    "max_entries",
                    "启用缓存时，最大条目数不能为0"
                ));
            }
        }

        // 验证清理间隔不能大于默认TTL
        if self.cleanup_interval_seconds > self.default_ttl_seconds {
            return Err(crate::SigmaXError::validation(
                crate::ValidationErrorCode::InvalidRange,
                "cleanup_interval_seconds",
                "缓存清理间隔不应大于默认TTL"
            ));
        }

        Ok(())
    }

    /// 检查配置是否有效
    pub fn is_valid(&self) -> bool {
        self.validate_complete().is_ok()
    }

    /// 获取配置摘要信息
    pub fn summary(&self) -> String {
        format!(
            "CacheConfig[{}]: enabled={}, memory={}MB, entries={}, ttl={}s, policy={:?}",
            self.name,
            self.enabled,
            self.max_memory_mb,
            self.max_entries,
            self.default_ttl_seconds,
            self.eviction_policy
        )
    }
}