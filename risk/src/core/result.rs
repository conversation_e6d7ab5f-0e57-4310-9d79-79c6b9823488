//! 风控结果定义

use serde::{Deserialize, Serialize};

/// 风控结果
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum RiskResult {
    /// 批准
    Approved,
    /// 拒绝
    Rejected { reason: String },
    /// 需要审批
    RequiresApproval { reason: String },
}

impl RiskResult {
    /// 创建批准结果
    pub fn approved() -> Self {
        Self::Approved
    }
    
    /// 创建拒绝结果
    pub fn rejected<S: Into<String>>(reason: S) -> Self {
        Self::Rejected {
            reason: reason.into(),
        }
    }
    
    /// 创建需要审批结果
    pub fn requires_approval<S: Into<String>>(reason: S) -> Self {
        Self::RequiresApproval {
            reason: reason.into(),
        }
    }
    
    /// 是否批准
    pub fn is_approved(&self) -> bool {
        matches!(self, Self::Approved)
    }
    
    /// 是否拒绝
    pub fn is_rejected(&self) -> bool {
        matches!(self, Self::Rejected { .. })
    }
    
    /// 获取原因
    pub fn reason(&self) -> Option<&str> {
        match self {
            Self::Approved => None,
            Self::Rejected { reason } => Some(reason),
            Self::RequiresApproval { reason } => Some(reason),
        }
    }
}
