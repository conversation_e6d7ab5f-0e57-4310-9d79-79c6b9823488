
//! SigmaX Core Account Models
//!
//! ## 主要职责
//!
//! 1. **账户余额管理** - 基础金融概念
//!    - `Balance` - 账户余额模型，支持资金锁定/解锁
//!    - 提供完整的验证和业务方法
//!
//! 2. **持仓信息管理** - 基础交易概念
//!    - `Position` - 持仓信息模型
//!    - 支持多空持仓和盈亏计算
//!
//! ## 设计原则
//!
//! - **统一模型**: 使用单一模型，通过impl方法提供计算字段
//! - **类型安全**: 使用强类型的Amount、Price等类型
//! - **验证完整**: 提供字段级和业务级验证
//! - **业务友好**: 提供实用的业务方法

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use validator::{Validate, ValidationError};

use crate::{
    types::*,
    error::*,
    SigmaXResult,
};

// ============================================================================
// 账户余额模型 - 基础金融概念
// ============================================================================

/// 账户余额 - 基础金融模型
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Balance {
    pub exchange_id: ExchangeId,

    #[validate(length(min = 1, message = "Asset name cannot be empty"))]
    pub asset: String,

    #[validate(custom = "validate_non_negative_amount")]
    pub free: Amount,

    #[validate(custom = "validate_non_negative_amount")]
    pub locked: Amount,

    pub updated_at: DateTime<Utc>,
}

impl Balance {
    /// 创建新的余额实例
    pub fn new(exchange_id: ExchangeId, asset: String, free: Amount, locked: Amount) -> Self {
        Self {
            exchange_id,
            asset,
            free,
            locked,
            updated_at: Utc::now(),
        }
    }

    /// 计算总余额（可用 + 冻结）
    pub fn total(&self) -> Amount {
        self.free + self.locked
    }

    /// 锁定资金（例如，因下达限价单）
    pub fn lock_funds(&mut self, amount: Amount) -> SigmaXResult<()> {
        if self.free < amount {
            return Err(SigmaXError::trading(
                TradingErrorCode::InsufficientBalance,
                "Insufficient free balance"
            ));
        }
        self.free -= amount;
        self.locked += amount;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 释放资金（例如，因订单取消）
    pub fn unlock_funds(&mut self, amount: Amount) -> SigmaXResult<()> {
        if self.locked < amount {
            return Err(SigmaXError::trading(
                TradingErrorCode::InsufficientBalance,
                "Insufficient locked balance"
            ));
        }
        self.locked -= amount;
        self.free += amount;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 检查余额数据是否一致（非负）
    pub fn is_consistent(&self) -> bool {
        self.free >= Amount::ZERO && self.locked >= Amount::ZERO
    }

    /// 执行完整的余额验证
    pub fn validate_complete(&self) -> SigmaXResult<()> {
        // 首先执行基础验证（通过validator derive宏）
        if let Err(validation_errors) = <Self as Validate>::validate(self) {
            return Err(SigmaXError::from(validation_errors));
        }

        // 然后执行跨字段验证
        if self.exchange_id.to_string().is_empty() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::Required,
                "exchange_id",
                "Exchange ID cannot be empty"
            ));
        }

        Ok(())
    }
}

// ============================================================================
// 持仓模型 - 基础交易概念
// ============================================================================

/// 持仓方向
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PositionSide {
    Long,
    Short,
}

impl PositionSide {
    /// 获取相反方向
    pub fn opposite(&self) -> Self {
        match self {
            PositionSide::Long => PositionSide::Short,
            PositionSide::Short => PositionSide::Long,
        }
    }

    /// 检查是否为多头
    pub fn is_long(&self) -> bool {
        matches!(self, PositionSide::Long)
    }

    /// 检查是否为空头
    pub fn is_short(&self) -> bool {
        matches!(self, PositionSide::Short)
    }
}

/// 持仓信息 - 基础交易模型
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Position {
    pub exchange_id: ExchangeId,
    pub trading_pair: TradingPair,
    pub side: PositionSide,

    #[validate(custom = "validate_positive_quantity")]
    pub quantity: Quantity,

    pub average_price: Price,
    pub unrealized_pnl: Amount,
    pub realized_pnl: Amount,
    pub updated_at: DateTime<Utc>,
}

impl Position {
    /// 创建新的持仓
    pub fn new(
        exchange_id: ExchangeId,
        trading_pair: TradingPair,
        side: PositionSide,
        quantity: Quantity,
        average_price: Price,
    ) -> Self {
        Self {
            exchange_id,
            trading_pair,
            side,
            quantity,
            average_price,
            unrealized_pnl: Amount::ZERO,
            realized_pnl: Amount::ZERO,
            updated_at: Utc::now(),
        }
    }

    /// 计算持仓价值
    pub fn position_value(&self) -> Amount {
        self.quantity * self.average_price
    }

    /// 计算总盈亏
    pub fn total_pnl(&self) -> Amount {
        self.unrealized_pnl + self.realized_pnl
    }

    /// 更新未实现盈亏
    pub fn update_unrealized_pnl(&mut self, current_price: Price) {
        let price_diff = match self.side {
            PositionSide::Long => current_price - self.average_price,
            PositionSide::Short => self.average_price - current_price,
        };
        self.unrealized_pnl = price_diff * self.quantity;
        self.updated_at = Utc::now();
    }

    /// 检查持仓是否盈利
    pub fn is_profitable(&self) -> bool {
        self.total_pnl() > Amount::ZERO
    }

    /// 检查持仓是否为零
    pub fn is_zero(&self) -> bool {
        self.quantity.is_zero()
    }
}

// ============================================================================
// 验证函数 - 用于模型验证
// ============================================================================

/// 验证金额为非负数
fn validate_non_negative_amount(amount: &Amount) -> Result<(), ValidationError> {
    if *amount < Amount::ZERO {
        return Err(ValidationError::new("Amount cannot be negative"));
    }
    Ok(())
}

/// 验证数量为正数
fn validate_positive_quantity(quantity: &Quantity) -> Result<(), ValidationError> {
    if *quantity <= Quantity::ZERO {
        return Err(ValidationError::new("Quantity must be positive"));
    }
    Ok(())
}

// ============================================================================
// Tests
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal::Decimal;

    #[test]
    fn test_balance_creation() {
        let balance = Balance::new(
            ExchangeId::Binance,
            "BTC".to_string(),
            Decimal::from(10),
            Decimal::from(5),
        );

        assert_eq!(balance.asset, "BTC");
        assert_eq!(balance.free, Decimal::from(10));
        assert_eq!(balance.locked, Decimal::from(5));
        assert_eq!(balance.total(), Decimal::from(15));
    }

    #[test]
    fn test_balance_lock_funds() {
        let mut balance = Balance::new(
            ExchangeId::Binance,
            "BTC".to_string(),
            Decimal::from(10),
            Decimal::ZERO,
        );

        balance.lock_funds(Decimal::from(3)).unwrap();
        assert_eq!(balance.free, Decimal::from(7));
        assert_eq!(balance.locked, Decimal::from(3));

        // 测试余额不足的情况
        let result = balance.lock_funds(Decimal::from(10));
        assert!(result.is_err());
    }

    #[test]
    fn test_balance_unlock_funds() {
        let mut balance = Balance::new(
            ExchangeId::Binance,
            "BTC".to_string(),
            Decimal::from(5),
            Decimal::from(5),
        );

        balance.unlock_funds(Decimal::from(2)).unwrap();
        assert_eq!(balance.free, Decimal::from(7));
        assert_eq!(balance.locked, Decimal::from(3));

        // 测试锁定余额不足的情况
        let result = balance.unlock_funds(Decimal::from(10));
        assert!(result.is_err());
    }

    #[test]
    fn test_balance_consistency() {
        let balance = Balance::new(
            ExchangeId::Binance,
            "BTC".to_string(),
            Decimal::from(10),
            Decimal::from(5),
        );
        assert!(balance.is_consistent());

        let mut bad_balance = balance.clone();
        bad_balance.free = Decimal::from(-1);
        assert!(!bad_balance.is_consistent());
    }

    #[test]
    fn test_position_creation() {
        let position = Position::new(
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            PositionSide::Long,
            Decimal::from(1),
            Decimal::from(50000),
        );

        assert_eq!(position.side, PositionSide::Long);
        assert_eq!(position.quantity, Decimal::from(1));
        assert_eq!(position.position_value(), Decimal::from(50000));
    }

    #[test]
    fn test_position_side() {
        assert_eq!(PositionSide::Long.opposite(), PositionSide::Short);
        assert_eq!(PositionSide::Short.opposite(), PositionSide::Long);
        assert!(PositionSide::Long.is_long());
        assert!(PositionSide::Short.is_short());
    }

    #[test]
    fn test_position_pnl_calculation() {
        let mut position = Position::new(
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            PositionSide::Long,
            Decimal::from(1),
            Decimal::from(50000),
        );

        // 测试多头盈利
        position.update_unrealized_pnl(Decimal::from(55000));
        assert_eq!(position.unrealized_pnl, Decimal::from(5000));
        assert!(position.is_profitable());

        // 测试多头亏损
        position.update_unrealized_pnl(Decimal::from(45000));
        assert_eq!(position.unrealized_pnl, Decimal::from(-5000));
        assert!(!position.is_profitable());
    }

    #[test]
    fn test_position_short_pnl() {
        let mut position = Position::new(
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            PositionSide::Short,
            Decimal::from(1),
            Decimal::from(50000),
        );

        // 测试空头盈利（价格下跌）
        position.update_unrealized_pnl(Decimal::from(45000));
        assert_eq!(position.unrealized_pnl, Decimal::from(5000));
        assert!(position.is_profitable());

        // 测试空头亏损（价格上涨）
        position.update_unrealized_pnl(Decimal::from(55000));
        assert_eq!(position.unrealized_pnl, Decimal::from(-5000));
        assert!(!position.is_profitable());
    }
}