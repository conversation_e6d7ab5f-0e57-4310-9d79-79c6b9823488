//! SQLx Repository工厂实现
//! 
//! 实现基于SQLx的Repository工厂

use super::factory_traits::*;
use crate::repositories::sqlx::*;
use crate::repositories::traits::base_repository::Repository;
use crate::managers::DatabaseManager;
use sigmax_core::{SigmaXResult, OrderId, StrategyId, EventId, TradingPair, EventType};
use sigmax_core::{Order, Trade, StrategyState, StoredEvent, OrderStatus, StrategyInfo};
use crate::repositories::traits::risk_repository::{RiskCheckRecord, RiskRuleRecord};
use crate::repositories::traits::{
    order_repository::OrderRepository,
    trade_repository::TradeRepository,
    strategy_repository::StrategyRepository,
    event_repository::EventRepository,
    risk_repository::RiskRepository,
};
use uuid::Uuid;

// TradeId 类型别名
pub type TradeId = Uuid;
use std::sync::Arc;
use async_trait::async_trait;

/// SQLx订单Repository工厂
pub struct SqlxOrderRepositoryFactory {
    database_manager: Arc<DatabaseManager>,
}

impl SqlxOrderRepositoryFactory {
    pub fn new(database_manager: Arc<DatabaseManager>) -> Self {
        Self { database_manager }
    }
}

#[async_trait]
impl RepositoryFactory for SqlxOrderRepositoryFactory {
    fn factory_type(&self) -> &'static str {
        "sqlx_order"
    }
    
    async fn health_check(&self) -> SigmaXResult<()> {
        self.database_manager.health_check().await
    }
}

#[async_trait]
impl OrderRepositoryFactory for SqlxOrderRepositoryFactory {
    async fn save_order(&self, order: &Order) -> SigmaXResult<()> {
        let repo = OrderRepositoryImpl::new(self.database_manager.clone());
        repo.save(order).await?;
        Ok(())
    }
    
    async fn find_order_by_id(&self, order_id: OrderId) -> SigmaXResult<Option<Order>> {
        let repo = OrderRepositoryImpl::new(self.database_manager.clone());
        repo.find_by_id(order_id).await
    }

    async fn find_all_orders(&self) -> SigmaXResult<Vec<Order>> {
        let repo = OrderRepositoryImpl::new(self.database_manager.clone());
        repo.find_all().await
    }

    async fn find_orders_by_status(&self, status: OrderStatus) -> SigmaXResult<Vec<Order>> {
        let repo = OrderRepositoryImpl::new(self.database_manager.clone());
        repo.find_by_status(status).await
    }

    async fn find_orders_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Vec<Order>> {
        let repo = OrderRepositoryImpl::new(self.database_manager.clone());
        repo.find_by_strategy(strategy_id).await
    }
}

/// SQLx交易Repository工厂
pub struct SqlxTradeRepositoryFactory {
    database_manager: Arc<DatabaseManager>,
}

impl SqlxTradeRepositoryFactory {
    pub fn new(database_manager: Arc<DatabaseManager>) -> Self {
        Self { database_manager }
    }
}

#[async_trait]
impl RepositoryFactory for SqlxTradeRepositoryFactory {
    fn factory_type(&self) -> &'static str {
        "sqlx_trade"
    }
    
    async fn health_check(&self) -> SigmaXResult<()> {
        self.database_manager.health_check().await
    }
}

#[async_trait]
impl TradeRepositoryFactory for SqlxTradeRepositoryFactory {
    async fn save_trade(&self, trade: &Trade) -> SigmaXResult<()> {
        let repo = TradeRepositoryImpl::new(self.database_manager.clone());
        repo.save(trade).await?;
        Ok(())
    }

    async fn find_all_trades(&self) -> SigmaXResult<Vec<Trade>> {
        let repo = TradeRepositoryImpl::new(self.database_manager.clone());
        repo.find_all().await
    }

    async fn find_trades_by_pair(&self, pair: &TradingPair) -> SigmaXResult<Vec<Trade>> {
        let repo = TradeRepositoryImpl::new(self.database_manager.clone());
        repo.find_by_trading_pair(pair).await
    }

    async fn find_trades_by_time_range(
        &self,
        start: chrono::DateTime<chrono::Utc>,
        end: chrono::DateTime<chrono::Utc>
    ) -> SigmaXResult<Vec<Trade>> {
        let repo = TradeRepositoryImpl::new(self.database_manager.clone());
        repo.find_by_time_range(start, end).await
    }
}

/// SQLx策略Repository工厂
pub struct SqlxStrategyRepositoryFactory {
    database_manager: Arc<DatabaseManager>,
}

impl SqlxStrategyRepositoryFactory {
    pub fn new(database_manager: Arc<DatabaseManager>) -> Self {
        Self { database_manager }
    }
}

#[async_trait]
impl RepositoryFactory for SqlxStrategyRepositoryFactory {
    fn factory_type(&self) -> &'static str {
        "sqlx_strategy"
    }
    
    async fn health_check(&self) -> SigmaXResult<()> {
        self.database_manager.health_check().await
    }
}

#[async_trait]
impl StrategyRepositoryFactory for SqlxStrategyRepositoryFactory {
    async fn save_strategy_state(&self, strategy_id: StrategyId, state: &StrategyState) -> SigmaXResult<()> {
        let repo = StrategyRepositoryImpl::new(self.database_manager.clone());
        repo.save_strategy_state(strategy_id, state).await
    }

    async fn find_strategy_state_by_id(&self, strategy_id: StrategyId) -> SigmaXResult<Option<StrategyState>> {
        let repo = StrategyRepositoryImpl::new(self.database_manager.clone());
        repo.find_by_id(strategy_id).await.map(|opt| opt)
    }

    async fn find_all_strategies(&self) -> SigmaXResult<Vec<StrategyInfo>> {
        let repo = StrategyRepositoryImpl::new(self.database_manager.clone());
        repo.find_all().await.map(|strategies| {
            strategies.into_iter().map(|s| StrategyInfo {
                id: s.strategy_id,
                name: s.name,
                strategy_type: s.strategy_type,
                description: s.description,
                status: s.status,
                trading_pair: s.trading_pair.unwrap_or_else(|| TradingPair::new("BTC".to_string(), "USDT".to_string())),
                portfolio_id: s.portfolio_id,
                created_at: s.created_at,
                updated_at: s.updated_at,
            }).collect()
        })
    }
}

/// SQLx事件Repository工厂
pub struct SqlxEventRepositoryFactory {
    database_manager: Arc<DatabaseManager>,
}

impl SqlxEventRepositoryFactory {
    pub fn new(database_manager: Arc<DatabaseManager>) -> Self {
        Self { database_manager }
    }
}

#[async_trait]
impl RepositoryFactory for SqlxEventRepositoryFactory {
    fn factory_type(&self) -> &'static str {
        "sqlx_event"
    }
    
    async fn health_check(&self) -> SigmaXResult<()> {
        self.database_manager.health_check().await
    }
}

#[async_trait]
impl EventRepositoryFactory for SqlxEventRepositoryFactory {
    async fn save_event(&self, event: &StoredEvent) -> SigmaXResult<EventId> {
        let repo = SqlEventRepository::new(self.database_manager.clone());
        repo.save_event(event).await
    }
    
    async fn get_event(&self, event_id: EventId) -> SigmaXResult<Option<StoredEvent>> {
        let repo = SqlEventRepository::new(self.database_manager.clone());
        repo.get_event(event_id).await
    }
    
    async fn get_events(&self, offset: usize, limit: usize) -> SigmaXResult<Vec<StoredEvent>> {
        let repo = SqlEventRepository::new(self.database_manager.clone());
        repo.get_events(offset, limit).await
    }
    
    async fn get_events_by_type(&self, event_type: EventType, limit: usize) -> SigmaXResult<Vec<StoredEvent>> {
        let repo = SqlEventRepository::new(self.database_manager.clone());
        repo.get_events_by_type(event_type, limit).await
    }
    
    async fn mark_event_processed(&self, event_id: EventId) -> SigmaXResult<()> {
        let repo = SqlEventRepository::new(self.database_manager.clone());
        repo.mark_event_processed(event_id).await
    }
}

/// SQLx风控Repository工厂
pub struct SqlxRiskRepositoryFactory {
    database_manager: Arc<DatabaseManager>,
}

impl SqlxRiskRepositoryFactory {
    pub fn new(database_manager: Arc<DatabaseManager>) -> Self {
        Self { database_manager }
    }
}

#[async_trait]
impl RepositoryFactory for SqlxRiskRepositoryFactory {
    fn factory_type(&self) -> &'static str {
        "sqlx_risk"
    }
    
    async fn health_check(&self) -> SigmaXResult<()> {
        self.database_manager.health_check().await
    }
}

#[async_trait]
impl RiskRepositoryFactory for SqlxRiskRepositoryFactory {
    async fn save_risk_check(&self, record: &RiskCheckRecord) -> SigmaXResult<()> {
        let repo = SqlRiskRepository::new(self.database_manager.clone());
        repo.save_risk_check(record).await
    }

    async fn get_risk_checks(&self, _limit: usize) -> SigmaXResult<Vec<RiskCheckRecord>> {
        let _repo = SqlRiskRepository::new(self.database_manager.clone());
        // 注意：SqlRiskRepository 没有 get_risk_checks 方法，我们需要使用其他方法
        // 暂时返回空向量，稍后实现
        Ok(vec![])
    }

    async fn save_risk_rule(&self, rule: &RiskRuleRecord) -> SigmaXResult<()> {
        let repo = SqlRiskRepository::new(self.database_manager.clone());
        repo.save_risk_rule(rule).await
    }

    async fn get_risk_rules(&self) -> SigmaXResult<Vec<RiskRuleRecord>> {
        let _repo = SqlRiskRepository::new(self.database_manager.clone());
        // 注意：SqlRiskRepository 没有 get_risk_rules 方法，我们需要使用其他方法
        // 暂时返回空向量，稍后实现
        Ok(vec![])
    }
}

/// SQLx组合Repository工厂
pub struct SqlxCompositeRepositoryFactory {
    order_factory: Arc<SqlxOrderRepositoryFactory>,
    trade_factory: Arc<SqlxTradeRepositoryFactory>,
    strategy_factory: Arc<SqlxStrategyRepositoryFactory>,
    event_factory: Arc<SqlxEventRepositoryFactory>,
    risk_factory: Arc<SqlxRiskRepositoryFactory>,
}

impl SqlxCompositeRepositoryFactory {
    pub fn new(database_manager: Arc<DatabaseManager>) -> Self {
        Self {
            order_factory: Arc::new(SqlxOrderRepositoryFactory::new(database_manager.clone())),
            trade_factory: Arc::new(SqlxTradeRepositoryFactory::new(database_manager.clone())),
            strategy_factory: Arc::new(SqlxStrategyRepositoryFactory::new(database_manager.clone())),
            event_factory: Arc::new(SqlxEventRepositoryFactory::new(database_manager.clone())),
            risk_factory: Arc::new(SqlxRiskRepositoryFactory::new(database_manager)),
        }
    }
}

#[async_trait]
impl CompositeRepositoryFactory for SqlxCompositeRepositoryFactory {
    fn order_factory(&self) -> Arc<dyn OrderRepositoryFactory> {
        self.order_factory.clone()
    }
    
    fn trade_factory(&self) -> Arc<dyn TradeRepositoryFactory> {
        self.trade_factory.clone()
    }
    
    fn strategy_factory(&self) -> Arc<dyn StrategyRepositoryFactory> {
        self.strategy_factory.clone()
    }
    
    fn event_factory(&self) -> Arc<dyn EventRepositoryFactory> {
        self.event_factory.clone()
    }
    
    fn risk_factory(&self) -> Arc<dyn RiskRepositoryFactory> {
        self.risk_factory.clone()
    }
}

/// SQLx工厂构建器
pub struct SqlxFactoryBuilder {
    config: SqlxFactoryConfig,
}

impl SqlxFactoryBuilder {
    pub fn new() -> Self {
        Self {
            config: SqlxFactoryConfig::default(),
        }
    }
    
    pub fn with_database_manager(mut self, database_manager: Arc<DatabaseManager>) -> Self {
        self.config.database_manager = Some(database_manager);
        self
    }
    
    pub fn with_transactions(mut self, enable: bool) -> Self {
        self.config.enable_transactions = enable;
        self
    }
    
    pub fn with_query_timeout(mut self, timeout_seconds: u64) -> Self {
        self.config.query_timeout_seconds = timeout_seconds;
        self
    }
}

impl Default for SqlxFactoryBuilder {
    fn default() -> Self {
        Self::new()
    }
}

impl RepositoryFactoryBuilder for SqlxFactoryBuilder {
    type Factory = SqlxCompositeRepositoryFactory;
    
    fn build(self) -> SigmaXResult<Self::Factory> {
        self.config.validate()?;
        
        let database_manager = self.config.database_manager
            .ok_or_else(|| sigmax_core::SigmaXError::validation(
                sigmax_core::ValidationErrorCode::Required,
                "database_manager",
                "Database manager is required"
            ))?;
        
        Ok(SqlxCompositeRepositoryFactory::new(database_manager))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::managers::DatabaseManager;

    #[tokio::test]
    async fn test_sqlx_factory_builder() {
        let db_manager = Arc::new(DatabaseManager::new_mock().unwrap());
        
        let factory = SqlxFactoryBuilder::new()
            .with_database_manager(db_manager)
            .with_transactions(true)
            .with_query_timeout(60)
            .build()
            .unwrap();
        
        // 测试各个工厂
        let order_factory = factory.order_factory();
        assert_eq!(order_factory.factory_type(), "sqlx_order");
        
        let trade_factory = factory.trade_factory();
        assert_eq!(trade_factory.factory_type(), "sqlx_trade");
        
        // 测试健康检查 (Mock数据库管理器可能没有真实连接，所以跳过)
        // assert!(factory.health_check_all().await.is_ok());
    }
}
