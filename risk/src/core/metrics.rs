//! 风险指标定义

use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// 风险等级
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum RiskLevel {
    /// 低风险
    Low,
    /// 中等风险
    Medium,
    /// 高风险
    High,
    /// 极高风险
    Critical,
}

impl RiskLevel {
    /// 从数值转换
    pub fn from_score(score: Decimal) -> Self {
        if score < Decimal::from(30) {
            Self::Low
        } else if score < Decimal::from(60) {
            Self::Medium
        } else if score < Decimal::from(80) {
            Self::High
        } else {
            Self::Critical
        }
    }
}

/// 风险指标 - 简化版本
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskMetrics {
    /// 风险分数 (0-100)
    pub risk_score: Decimal,
    /// 风险等级
    pub risk_level: RiskLevel,
    /// 敞口
    pub exposure: Decimal,
    /// 杠杆率
    pub leverage: Decimal,
    /// 计算时间
    pub calculated_at: DateTime<Utc>,
}

impl Default for RiskMetrics {
    fn default() -> Self {
        Self {
            risk_score: Decimal::ZERO,
            risk_level: RiskLevel::Low,
            exposure: Decimal::ZERO,
            leverage: Decimal::ONE,
            calculated_at: Utc::now(),
        }
    }
}

impl RiskMetrics {
    /// 创建新的风险指标
    pub fn new(risk_score: Decimal, exposure: Decimal, leverage: Decimal) -> Self {
        let risk_level = RiskLevel::from_score(risk_score);
        Self {
            risk_score,
            risk_level,
            exposure,
            leverage,
            calculated_at: Utc::now(),
        }
    }
    
    /// 是否高风险
    pub fn is_high_risk(&self) -> bool {
        self.risk_level >= RiskLevel::High
    }
}
