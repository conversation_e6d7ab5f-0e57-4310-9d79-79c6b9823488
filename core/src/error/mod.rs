//! 错误处理模块
//!
//! 提供统一的错误类型和处理机制

pub mod types;
mod convert;
pub mod macros;
pub mod adapters;

// SigmaX主错误类型定义
use thiserror::Error;
use serde::{Serialize, Deserialize};
use uuid::Uuid;

// 重新导出类型和转换
pub use types::*;
// pub use convert::*; // 不自动导出convert，由From trait自动生效

/// SigmaX 主错误类型
#[derive(Erro<PERSON>, Debug, Clone, Serialize, Deserialize)]
pub enum SigmaXError {
    /// 交易相关错误
    #[error("Trading error: {message} (code: {code:?})")]
    Trading {
        code: TradingErrorCode,
        message: String,
        order_id: Option<Uuid>,
        symbol: Option<String>,
        retryable: bool,
        context: ErrorContext,
    },

    /// 策略执行错误
    #[error("Strategy error: {message} (strategy: {strategy_id})")]
    Strategy {
        code: StrategyErrorCode,
        message: String,
        strategy_id: Uuid,
        strategy_type: String,
        retryable: bool,
        context: ErrorContext,
    },

    /// 风险管理错误
    #[error("Risk management error: {message}")]
    Risk {
        code: RiskErrorCode,
        message: String,
        risk_type: String,
        current_value: Option<f64>,
        limit_value: Option<f64>,
        retryable: bool,
        context: ErrorContext,
    },

    /// 数据库错误
    #[error("Database error: {message}")]
    Database {
        code: DatabaseErrorCode,
        message: String,
        query: Option<String>,
        table: Option<String>,
        retryable: bool,
        context: ErrorContext,
    },

    /// 网络/API错误
    #[error("Network error: {message}")]
    Network {
        code: NetworkErrorCode,
        message: String,
        endpoint: Option<String>,
        status_code: Option<u16>,
        retryable: bool,
        context: ErrorContext,
    },

    /// 配置错误
    #[error("Configuration error: {message}")]
    Configuration {
        code: ConfigErrorCode,
        message: String,
        config_key: Option<String>,
        config_value: Option<String>,
        retryable: bool,
        context: ErrorContext,
    },

    /// 输入验证错误
    #[error("Validation error: {message}")]
    Validation {
        code: ValidationErrorCode,
        message: String,
        field: Option<String>,
        value: Option<String>,
        expected: Option<String>,
        context: ErrorContext,
    },

    /// 交易所API错误
    #[error("Exchange API error: {message} (exchange: {exchange})")]
    ExchangeApi {
        code: ExchangeErrorCode,
        message: String,
        exchange: String,
        api_error_code: Option<String>,
        retryable: bool,
        context: ErrorContext,
    },

    /// 市场数据错误
    #[error("Market data error: {message}")]
    MarketData {
        code: MarketDataErrorCode,
        message: String,
        symbol: Option<String>,
        error_source: Option<String>,
        retryable: bool,
        context: ErrorContext,
    },

    /// 内部系统错误
    #[error("Internal system error: {message}")]
    Internal {
        code: InternalErrorCode,
        message: String,
        component: Option<String>,
        retryable: bool,
        context: ErrorContext,
    },
}

impl SigmaXError {
    // =================== 便捷构造器 ===================
    
    /// 创建交易错误
    pub fn trading(code: TradingErrorCode, message: impl Into<String>) -> Self {
        Self::Trading {
            code,
            message: message.into(),
            order_id: None,
            symbol: None,
            retryable: false,
            context: ErrorContext::new(),
        }
    }

    /// 创建交易错误（带订单ID）
    pub fn trading_with_order(
        code: TradingErrorCode,
        message: impl Into<String>,
        order_id: Uuid,
    ) -> Self {
        Self::Trading {
            code,
            message: message.into(),
            order_id: Some(order_id),
            symbol: None,
            retryable: false,
            context: ErrorContext::new(),
        }
    }

    /// 创建策略错误
    pub fn strategy(
        code: StrategyErrorCode,
        message: impl Into<String>,
        strategy_id: Uuid,
        strategy_type: impl Into<String>,
    ) -> Self {
        Self::Strategy {
            code,
            message: message.into(),
            strategy_id,
            strategy_type: strategy_type.into(),
            retryable: false,
            context: ErrorContext::new(),
        }
    }

    /// 创建风险错误
    pub fn risk(
        code: RiskErrorCode,
        message: impl Into<String>,
        risk_type: impl Into<String>,
    ) -> Self {
        Self::Risk {
            code,
            message: message.into(),
            risk_type: risk_type.into(),
            current_value: None,
            limit_value: None,
            retryable: false,
            context: ErrorContext::new(),
        }
    }

    /// 创建数据库错误
    pub fn database(code: DatabaseErrorCode, message: impl Into<String>) -> Self {
        Self::Database {
            code,
            message: message.into(),
            query: None,
            table: None,
            retryable: matches!(code, DatabaseErrorCode::ConnectionFailed),
            context: ErrorContext::new(),
        }
    }

    /// 创建网络错误
    pub fn network(code: NetworkErrorCode, message: impl Into<String>) -> Self {
        Self::Network {
            code,
            message: message.into(),
            endpoint: None,
            status_code: None,
            retryable: true,
            context: ErrorContext::new(),
        }
    }

    /// 创建配置错误
    pub fn configuration(code: ConfigErrorCode, message: impl Into<String>) -> Self {
        Self::Configuration {
            code,
            message: message.into(),
            config_key: None,
            config_value: None,
            retryable: false,
            context: ErrorContext::new(),
        }
    }

    /// 创建验证错误
    pub fn validation(
        code: ValidationErrorCode,
        field: impl Into<String>,
        message: impl Into<String>,
    ) -> Self {
        Self::Validation {
            code,
            message: message.into(),
            field: Some(field.into()),
            value: None,
            expected: None,
            context: ErrorContext::new(),
        }
    }

    /// 创建交易所API错误
    pub fn exchange_api(
        code: ExchangeErrorCode,
        message: impl Into<String>,
        exchange: impl Into<String>,
    ) -> Self {
        Self::ExchangeApi {
            code,
            message: message.into(),
            exchange: exchange.into(),
            api_error_code: None,
            retryable: matches!(code, ExchangeErrorCode::RateLimitExceeded),
            context: ErrorContext::new(),
        }
    }

    /// 创建市场数据错误
    pub fn market_data(code: MarketDataErrorCode, message: impl Into<String>) -> Self {
        Self::MarketData {
            code,
            message: message.into(),
            symbol: None,
            error_source: None,
            retryable: !matches!(code, MarketDataErrorCode::InvalidSymbol),
            context: ErrorContext::new(),
        }
    }

    /// 创建内部错误
    pub fn internal(code: InternalErrorCode, message: impl Into<String>) -> Self {
        Self::Internal {
            code,
            message: message.into(),
            component: None,
            retryable: false,
            context: ErrorContext::new(),
        }
    }

    // =================== 链式方法 ===================
    
    /// 设置错误为可重试
    pub fn retryable(mut self) -> Self {
        match &mut self {
            SigmaXError::Trading { retryable, .. } |
            SigmaXError::Strategy { retryable, .. } |
            SigmaXError::Risk { retryable, .. } |
            SigmaXError::Database { retryable, .. } |
            SigmaXError::Network { retryable, .. } |
            SigmaXError::Configuration { retryable, .. } |
            SigmaXError::ExchangeApi { retryable, .. } |
            SigmaXError::MarketData { retryable, .. } |
            SigmaXError::Internal { retryable, .. } => *retryable = true,
            SigmaXError::Validation { .. } => {} // 验证错误不可重试
        }
        self
    }

    /// 添加上下文信息
    pub fn with_context(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        self.context_mut().metadata.insert(key.into(), value.into());
        self
    }

    /// 设置追踪ID
    pub fn with_trace_id(mut self, trace_id: impl Into<String>) -> Self {
        self.context_mut().trace_id = Some(trace_id.into());
        self
    }

    /// 设置用户ID
    pub fn with_user_id(mut self, user_id: impl Into<String>) -> Self {
        self.context_mut().user_id = Some(user_id.into());
        self
    }

    // =================== 查询方法 ===================
    
    /// 检查错误是否可重试
    pub fn is_retryable(&self) -> bool {
        match self {
            SigmaXError::Trading { retryable, .. } |
            SigmaXError::Strategy { retryable, .. } |
            SigmaXError::Risk { retryable, .. } |
            SigmaXError::Database { retryable, .. } |
            SigmaXError::Network { retryable, .. } |
            SigmaXError::Configuration { retryable, .. } |
            SigmaXError::ExchangeApi { retryable, .. } |
            SigmaXError::MarketData { retryable, .. } |
            SigmaXError::Internal { retryable, .. } => *retryable,
            SigmaXError::Validation { .. } => false,
        }
    }

    /// 获取错误代码字符串
    pub fn error_code(&self) -> String {
        match self {
            SigmaXError::Trading { code, .. } => format!("TRADING_{:?}", code).to_uppercase(),
            SigmaXError::Strategy { code, .. } => format!("STRATEGY_{:?}", code).to_uppercase(),
            SigmaXError::Risk { code, .. } => format!("RISK_{:?}", code).to_uppercase(),
            SigmaXError::Database { code, .. } => format!("DATABASE_{:?}", code).to_uppercase(),
            SigmaXError::Network { code, .. } => format!("NETWORK_{:?}", code).to_uppercase(),
            SigmaXError::Configuration { code, .. } => format!("CONFIG_{:?}", code).to_uppercase(),
            SigmaXError::Validation { code, .. } => format!("VALIDATION_{:?}", code).to_uppercase(),
            SigmaXError::ExchangeApi { code, .. } => format!("EXCHANGE_{:?}", code).to_uppercase(),
            SigmaXError::MarketData { code, .. } => format!("MARKET_DATA_{:?}", code).to_uppercase(),
            SigmaXError::Internal { code, .. } => format!("INTERNAL_{:?}", code).to_uppercase(),
        }
    }

    /// 获取错误类别
    pub fn category(&self) -> ErrorCategory {
        match self {
            SigmaXError::Trading { .. } |
            SigmaXError::Strategy { .. } |
            SigmaXError::Risk { .. } => ErrorCategory::Business,
            
            SigmaXError::Database { .. } |
            SigmaXError::Network { .. } |
            SigmaXError::Configuration { .. } |
            SigmaXError::Internal { .. } => ErrorCategory::Infrastructure,
            
            SigmaXError::ExchangeApi { .. } |
            SigmaXError::MarketData { .. } => ErrorCategory::External,
            
            SigmaXError::Validation { .. } => ErrorCategory::Validation,
        }
    }

    /// 获取错误严重程度
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            SigmaXError::Trading { code, .. } => match code {
                TradingErrorCode::MarketClosed => ErrorSeverity::Warning,
                TradingErrorCode::OrderNotFound => ErrorSeverity::Warning,
                _ => ErrorSeverity::Error,
            },
            SigmaXError::Strategy { code, .. } => match code {
                StrategyErrorCode::NotFound => ErrorSeverity::Warning,
                StrategyErrorCode::StateInconsistent => ErrorSeverity::Critical,
                _ => ErrorSeverity::Error,
            },
            SigmaXError::Risk { code, .. } => match code {
                RiskErrorCode::LimitExceeded => ErrorSeverity::Critical,
                RiskErrorCode::ThresholdTriggered => ErrorSeverity::Warning,
                RiskErrorCode::EmergencyStop => ErrorSeverity::Critical,
                _ => ErrorSeverity::Error,
            },
            SigmaXError::Database { code, .. } => match code {
                DatabaseErrorCode::ConnectionFailed => ErrorSeverity::Critical,
                DatabaseErrorCode::TransactionFailed => ErrorSeverity::Error,
                _ => ErrorSeverity::Error,
            },
            SigmaXError::Internal { code, .. } => match code {
                InternalErrorCode::Panic => ErrorSeverity::Critical,
                InternalErrorCode::ResourceExhausted => ErrorSeverity::Critical,
                _ => ErrorSeverity::Error,
            },
            SigmaXError::ExchangeApi { code, .. } => match code {
                ExchangeErrorCode::ApiKeyInvalid => ErrorSeverity::Critical,
                ExchangeErrorCode::RateLimitExceeded => ErrorSeverity::Warning,
                _ => ErrorSeverity::Error,
            },
            SigmaXError::MarketData { .. } => ErrorSeverity::Warning,
            SigmaXError::Validation { .. } => ErrorSeverity::Error,
            SigmaXError::Configuration { .. } => ErrorSeverity::Critical,
            SigmaXError::Network { .. } => ErrorSeverity::Warning,
        }
    }

    /// 转换为用户友好的消息
    pub fn user_message(&self) -> String {
        match self {
            SigmaXError::Trading { code, symbol, .. } => match code {
                TradingErrorCode::InsufficientBalance => "账户余额不足，无法完成交易".to_string(),
                TradingErrorCode::MarketClosed => {
                    if let Some(s) = symbol {
                        format!("交易对 {} 当前不可交易", s)
                    } else {
                        "市场已关闭，无法交易".to_string()
                    }
                },
                TradingErrorCode::InvalidTradingPair => "交易对不存在或不支持".to_string(),
                _ => "交易执行失败，请稍后重试".to_string(),
            },
            SigmaXError::Strategy { strategy_type, .. } => {
                format!("策略 {} 执行异常，已自动停止", strategy_type)
            },
            SigmaXError::Risk { risk_type, .. } => {
                format!("触发 {} 风险控制规则，交易已暂停", risk_type)
            },
            SigmaXError::ExchangeApi { exchange, .. } => {
                format!("交易所 {} 连接异常，请检查网络连接", exchange)
            },
            SigmaXError::Validation { field, .. } => {
                if let Some(f) = field {
                    format!("输入参数 {} 格式不正确", f)
                } else {
                    "输入参数格式不正确".to_string()
                }
            },
            _ => "系统暂时不可用，请稍后重试".to_string(),
        }
    }

    /// 获取建议的重试延迟（毫秒）
    pub fn retry_delay_ms(&self) -> Option<u64> {
        if !self.is_retryable() {
            return None;
        }
        
        Some(match self {
            SigmaXError::Network { .. } => 1000,      // 1秒
            SigmaXError::Database { .. } => 2000,     // 2秒
            SigmaXError::ExchangeApi { .. } => 5000,  // 5秒
            SigmaXError::MarketData { .. } => 3000,   // 3秒
            _ => 1000,
        })
    }

    /// 获取可变的上下文引用
    fn context_mut(&mut self) -> &mut ErrorContext {
        match self {
            SigmaXError::Trading { context, .. } |
            SigmaXError::Strategy { context, .. } |
            SigmaXError::Risk { context, .. } |
            SigmaXError::Database { context, .. } |
            SigmaXError::Network { context, .. } |
            SigmaXError::Configuration { context, .. } |
            SigmaXError::Validation { context, .. } |
            SigmaXError::ExchangeApi { context, .. } |
            SigmaXError::MarketData { context, .. } |
            SigmaXError::Internal { context, .. } => context,
        }
    }

    /// 获取错误上下文
    pub fn context(&self) -> &ErrorContext {
        match self {
            SigmaXError::Trading { context, .. } |
            SigmaXError::Strategy { context, .. } |
            SigmaXError::Risk { context, .. } |
            SigmaXError::Database { context, .. } |
            SigmaXError::Network { context, .. } |
            SigmaXError::Configuration { context, .. } |
            SigmaXError::Validation { context, .. } |
            SigmaXError::ExchangeApi { context, .. } |
            SigmaXError::MarketData { context, .. } |
            SigmaXError::Internal { context, .. } => context,
        }
    }
}