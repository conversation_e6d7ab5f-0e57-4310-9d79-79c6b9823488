//! 策略应用服务
//!
//! 编排策略相关的业务流程，使用Repository模式和事件驱动

use std::sync::Arc;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use sigmax_core::Strategy;
use crate::error::ApiError;
use crate::domain::repositories::StrategyRepository;
use crate::domain::StrategyDomainService;
use crate::events::{WebEvent, StrategyEvent, WebEventBus};

/// 策略应用服务
///
/// 负责策略相关的业务流程编排，包括事务管理、事件发布等
pub struct StrategyApplicationService {
    strategy_domain_service: Arc<StrategyDomainService>,
    event_bus: Arc<WebEventBus>,
}

impl StrategyApplicationService {
    /// 创建新的策略应用服务
    pub fn new(
        strategy_domain_service: Arc<StrategyDomainService>,
        event_bus: Arc<WebEventBus>,
    ) -> Self {
        Self {
            strategy_domain_service,
            event_bus,
        }
    }
    
    /// 创建策略
    pub async fn create_strategy(
        &self,
        name: String,
        strategy_type: String,
    ) -> Result<Strategy, ApiError> {
        info!("🚀 策略应用服务开始创建策略: {}", name);
        
        // 1. 调用领域服务创建策略
        let strategy = self.strategy_domain_service.create_strategy(name.clone(), strategy_type.clone()).await?;
        
        // 2. 发布策略创建事件
        let event = WebEvent::StrategyCreated(StrategyEvent {
            strategy_id: strategy.id,
            name: strategy.name.clone(),
            action: "created".to_string(),
            timestamp: Utc::now(),
            user_id: None, // TODO: 从认证上下文获取
            metadata: serde_json::json!({
                "strategy_type": strategy.strategy_type,
                "created_at": strategy.created_at,
            }),
        });
        
        self.event_bus.publish_web_event(event).await
            .map_err(|e| {
                warn!("策略创建事件发布失败: {}", e);
                // 事件发布失败不影响主流程
                Ok::<(), ApiError>(())
            })?;
        
        info!("✅ 策略应用服务完成策略创建: {} (ID: {})", strategy.name, strategy.id);
        Ok(strategy)
    }
    
    /// 获取策略列表
    pub async fn get_strategies(&self) -> Result<Vec<Strategy>, ApiError> {
        info!("📋 策略应用服务获取策略列表");
        
        self.strategy_domain_service.get_strategies().await
            .map_err(|e| ApiError::BusinessError(format!("获取策略列表失败: {}", e)))
    }
    
    /// 获取单个策略
    pub async fn get_strategy(&self, id: Uuid) -> Result<Strategy, ApiError> {
        info!("🔍 策略应用服务获取策略: {}", id);
        
        self.strategy_domain_service.get_strategy(id).await
            .map_err(|e| ApiError::BusinessError(format!("获取策略失败: {}", e)))
    }
    
    /// 更新策略
    pub async fn update_strategy(
        &self,
        id: Uuid,
        name: Option<String>,
        strategy_type: Option<String>,
    ) -> Result<Strategy, ApiError> {
        info!("📝 策略应用服务更新策略: {}", id);
        
        // 1. 调用领域服务更新策略
        let updated_strategy = self.strategy_domain_service.update_strategy(id, name, strategy_type).await?;
        
        // 2. 发布策略更新事件
        let event = WebEvent::StrategyUpdated(StrategyEvent {
            strategy_id: updated_strategy.id,
            name: updated_strategy.name.clone(),
            action: "updated".to_string(),
            timestamp: Utc::now(),
            user_id: None, // TODO: 从认证上下文获取
            metadata: serde_json::json!({
                "strategy_type": updated_strategy.strategy_type,
                "updated_at": updated_strategy.updated_at,
            }),
        });
        
        self.event_bus.publish_web_event(event).await
            .map_err(|e| {
                warn!("策略更新事件发布失败: {}", e);
                // 事件发布失败不影响主流程
                Ok::<(), ApiError>(())
            })?;
        
        info!("✅ 策略应用服务完成策略更新: {}", updated_strategy.name);
        Ok(updated_strategy)
    }
    
    /// 删除策略
    pub async fn delete_strategy(&self, id: Uuid) -> Result<(), ApiError> {
        info!("🗑️ 策略应用服务删除策略: {}", id);
        
        // 1. 获取策略信息用于事件发布
        let strategy = self.strategy_domain_service.get_strategy(id).await?;
        
        // 2. 调用领域服务删除策略
        self.strategy_domain_service.delete_strategy(id).await?;
        
        // 3. 发布策略删除事件
        let event = WebEvent::StrategyDeleted(StrategyEvent {
            strategy_id: strategy.id,
            name: strategy.name.clone(),
            action: "deleted".to_string(),
            timestamp: Utc::now(),
            user_id: None, // TODO: 从认证上下文获取
            metadata: serde_json::json!({
                "strategy_type": strategy.strategy_type,
                "deleted_at": Utc::now().to_rfc3339(),
            }),
        });
        
        self.event_bus.publish_web_event(event).await
            .map_err(|e| {
                warn!("策略删除事件发布失败: {}", e);
                // 事件发布失败不影响主流程
                Ok::<(), ApiError>(())
            })?;
        
        info!("✅ 策略应用服务完成策略删除: {}", strategy.name);
        Ok(())
    }
    
    /// 启动策略
    pub async fn start_strategy(&self, id: Uuid) -> Result<Strategy, ApiError> {
        info!("▶️ 策略应用服务启动策略: {}", id);
        
        // 1. 获取策略
        let mut strategy = self.strategy_domain_service.get_strategy(id).await?;
        
        // 2. 更新状态
        strategy.status = "active".to_string();
        strategy.updated_at = Utc::now().to_rfc3339();
        
        // 3. 保存更新
        self.strategy_domain_service.update_strategy(id, None, None).await?;
        
        // 4. 发布策略启动事件
        let event = WebEvent::StrategyStarted(StrategyEvent {
            strategy_id: strategy.id,
            name: strategy.name.clone(),
            action: "started".to_string(),
            timestamp: Utc::now(),
            user_id: None, // TODO: 从认证上下文获取
            metadata: serde_json::json!({
                "strategy_type": strategy.strategy_type,
                "started_at": Utc::now().to_rfc3339(),
            }),
        });
        
        self.event_bus.publish_web_event(event).await
            .map_err(|e| {
                warn!("策略启动事件发布失败: {}", e);
                Ok::<(), ApiError>(())
            })?;
        
        info!("✅ 策略应用服务完成策略启动: {}", strategy.name);
        Ok(strategy)
    }
    
    /// 停止策略
    pub async fn stop_strategy(&self, id: Uuid) -> Result<Strategy, ApiError> {
        info!("⏹️ 策略应用服务停止策略: {}", id);
        
        // 1. 获取策略
        let mut strategy = self.strategy_domain_service.get_strategy(id).await?;
        
        // 2. 更新状态
        strategy.status = "stopped".to_string();
        strategy.updated_at = Utc::now().to_rfc3339();
        
        // 3. 保存更新
        self.strategy_domain_service.update_strategy(id, None, None).await?;
        
        // 4. 发布策略停止事件
        let event = WebEvent::StrategyStopped(StrategyEvent {
            strategy_id: strategy.id,
            name: strategy.name.clone(),
            action: "stopped".to_string(),
            timestamp: Utc::now(),
            user_id: None, // TODO: 从认证上下文获取
            metadata: serde_json::json!({
                "strategy_type": strategy.strategy_type,
                "stopped_at": Utc::now().to_rfc3339(),
            }),
        });
        
        self.event_bus.publish_web_event(event).await
            .map_err(|e| {
                warn!("策略停止事件发布失败: {}", e);
                Ok::<(), ApiError>(())
            })?;
        
        info!("✅ 策略应用服务完成策略停止: {}", strategy.name);
        Ok(strategy)
    }
}
