//! 指标服务模块
//! 
//! 提供多种指标收集实现：
//! - Prometheus指标收集
//! - 日志指标收集
//! - 指标聚合器

pub mod prometheus;
pub mod log;
pub mod aggregator;
pub mod config;

pub use prometheus::PrometheusMetricsCollector;
pub use log::LogMetricsCollector;
pub use aggregator::MetricsAggregator;
pub use config::MetricsConfig;

use async_trait::async_trait;
use sigmax_core::traits::MetricsCollector;
use std::collections::HashMap;
use std::time::Duration;
use crate::errors::{ServiceError, ServiceResult};

/// 指标类型
#[derive(Debug, Clone, PartialEq)]
pub enum MetricType {
    Counter,
    Gauge,
    Histogram,
    Summary,
}

/// 指标值
#[derive(Debug, Clone)]
pub enum MetricValue {
    Counter(u64),
    Gauge(f64),
    Histogram(Vec<f64>),
    Summary { sum: f64, count: u64 },
}

/// 指标数据
#[derive(Debug, <PERSON>lone)]
pub struct MetricData {
    pub name: String,
    pub value: MetricValue,
    pub labels: HashMap<String, String>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub help: Option<String>,
}

impl MetricData {
    pub fn new(name: String, value: MetricValue) -> Self {
        Self {
            name,
            value,
            labels: HashMap::new(),
            timestamp: chrono::Utc::now(),
            help: None,
        }
    }
    
    pub fn with_labels(mut self, labels: HashMap<String, String>) -> Self {
        self.labels = labels;
        self
    }
    
    pub fn with_label(mut self, key: String, value: String) -> Self {
        self.labels.insert(key, value);
        self
    }
    
    pub fn with_help(mut self, help: String) -> Self {
        self.help = Some(help);
        self
    }
}

/// 扩展的指标收集器接口
#[async_trait]
pub trait ExtendedMetricsCollector: MetricsCollector {
    /// 记录自定义指标
    async fn record_metric(&self, metric: MetricData);
    
    /// 增加计数器
    async fn increment_counter(&self, name: &str, labels: HashMap<String, String>);
    
    /// 设置仪表盘值
    async fn set_gauge(&self, name: &str, value: f64, labels: HashMap<String, String>);
    
    /// 记录直方图值
    async fn record_histogram(&self, name: &str, value: f64, labels: HashMap<String, String>);
    
    /// 获取所有指标
    async fn get_all_metrics(&self) -> ServiceResult<Vec<MetricData>>;
    
    /// 重置所有指标
    async fn reset_metrics(&self) -> ServiceResult<()>;
    
    /// 导出指标（Prometheus格式）
    async fn export_prometheus(&self) -> ServiceResult<String>;
}

/// 指标统计信息
#[derive(Debug, Clone)]
pub struct MetricsStats {
    pub total_metrics: usize,
    pub counters: usize,
    pub gauges: usize,
    pub histograms: usize,
    pub summaries: usize,
    pub last_updated: chrono::DateTime<chrono::Utc>,
}

impl MetricsStats {
    pub fn new() -> Self {
        Self {
            total_metrics: 0,
            counters: 0,
            gauges: 0,
            histograms: 0,
            summaries: 0,
            last_updated: chrono::Utc::now(),
        }
    }
    
    pub fn update_from_metrics(&mut self, metrics: &[MetricData]) {
        self.total_metrics = metrics.len();
        self.counters = metrics.iter().filter(|m| matches!(m.value, MetricValue::Counter(_))).count();
        self.gauges = metrics.iter().filter(|m| matches!(m.value, MetricValue::Gauge(_))).count();
        self.histograms = metrics.iter().filter(|m| matches!(m.value, MetricValue::Histogram(_))).count();
        self.summaries = metrics.iter().filter(|m| matches!(m.value, MetricValue::Summary { .. })).count();
        self.last_updated = chrono::Utc::now();
    }
}

/// 指标标签构建器
pub struct MetricLabelsBuilder {
    labels: HashMap<String, String>,
}

impl MetricLabelsBuilder {
    pub fn new() -> Self {
        Self {
            labels: HashMap::new(),
        }
    }
    
    pub fn add(mut self, key: &str, value: &str) -> Self {
        self.labels.insert(key.to_string(), value.to_string());
        self
    }
    
    pub fn add_engine_type(self, engine_type: &str) -> Self {
        self.add("engine_type", engine_type)
    }
    
    pub fn add_operation(self, operation: &str) -> Self {
        self.add("operation", operation)
    }
    
    pub fn add_status(self, status: &str) -> Self {
        self.add("status", status)
    }
    
    pub fn build(self) -> HashMap<String, String> {
        self.labels
    }
}

impl Default for MetricLabelsBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 常用指标名称常量
pub mod metric_names {
    pub const RISK_CHECK_TOTAL: &str = "sigmax_risk_check_total";
    pub const RISK_CHECK_DURATION: &str = "sigmax_risk_check_duration_seconds";
    pub const CACHE_HIT_TOTAL: &str = "sigmax_cache_hit_total";
    pub const CACHE_MISS_TOTAL: &str = "sigmax_cache_miss_total";
    pub const CACHE_OPERATION_DURATION: &str = "sigmax_cache_operation_duration_seconds";
    pub const ENGINE_STATUS: &str = "sigmax_engine_status";
    pub const ORDER_TOTAL: &str = "sigmax_order_total";
    pub const TRADE_TOTAL: &str = "sigmax_trade_total";
    pub const PORTFOLIO_VALUE: &str = "sigmax_portfolio_value";
    pub const STRATEGY_PNL: &str = "sigmax_strategy_pnl";
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_metric_data_creation() {
        let metric = MetricData::new(
            "test_counter".to_string(),
            MetricValue::Counter(42),
        )
        .with_label("engine".to_string(), "backtest".to_string())
        .with_help("Test counter metric".to_string());
        
        assert_eq!(metric.name, "test_counter");
        assert!(matches!(metric.value, MetricValue::Counter(42)));
        assert_eq!(metric.labels.get("engine"), Some(&"backtest".to_string()));
        assert_eq!(metric.help, Some("Test counter metric".to_string()));
    }
    
    #[test]
    fn test_metric_labels_builder() {
        let labels = MetricLabelsBuilder::new()
            .add_engine_type("backtest")
            .add_operation("risk_check")
            .add_status("success")
            .build();
        
        assert_eq!(labels.get("engine_type"), Some(&"backtest".to_string()));
        assert_eq!(labels.get("operation"), Some(&"risk_check".to_string()));
        assert_eq!(labels.get("status"), Some(&"success".to_string()));
    }
    
    #[test]
    fn test_metrics_stats() {
        let mut stats = MetricsStats::new();
        
        let metrics = vec![
            MetricData::new("counter1".to_string(), MetricValue::Counter(1)),
            MetricData::new("gauge1".to_string(), MetricValue::Gauge(1.0)),
            MetricData::new("histogram1".to_string(), MetricValue::Histogram(vec![1.0, 2.0])),
        ];
        
        stats.update_from_metrics(&metrics);
        
        assert_eq!(stats.total_metrics, 3);
        assert_eq!(stats.counters, 1);
        assert_eq!(stats.gauges, 1);
        assert_eq!(stats.histograms, 1);
        assert_eq!(stats.summaries, 0);
    }
}
