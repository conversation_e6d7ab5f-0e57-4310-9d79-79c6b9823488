//! 事务管理器
//!
//! 提供简洁直观的事务管理功能

use async_trait::async_trait;
use sigmax_core::{SigmaXResult, SigmaXError, DatabaseErrorCode};
use super::transaction_context::{
    TransactionContext, TransactionContextManager, TransactionIsolationLevel,
    TransactionScope, TransactionStatistics
};
pub use super::transaction_context::TransactionConfig;
use super::unit_of_work::{UnitOfWork, DefaultUnitOfWork, UnitOfWorkBuilder};
use super::database_transaction::{DatabaseTransaction, TransactionStats, LongRunningTransaction};
use crate::managers::DatabaseManager;
use sqlx::{Transaction, Postgres};
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use tracing::{info, warn, error, debug};

/// 事务管理器
///
/// 提供标准的事务管理功能，支持简单事务和复杂工作单元模式
#[derive(Debug)]
pub struct TransactionManager {
    /// 数据库管理器
    database_manager: Arc<DatabaseManager>,
    /// 事务配置
    config: TransactionConfig,
    /// 活跃事务上下文
    active_transactions: Arc<RwLock<HashMap<Uuid, Arc<Mutex<TransactionContext>>>>>,
    /// 事务统计
    statistics: Arc<Mutex<TransactionStatistics>>,
}

impl TransactionManager {
    /// 创建新的事务管理器
    pub fn new(database_manager: Arc<DatabaseManager>, config: TransactionConfig) -> Self {
        Self {
            database_manager,
            config,
            active_transactions: Arc::new(RwLock::new(HashMap::new())),
            statistics: Arc::new(Mutex::new(TransactionStatistics::new())),
        }
    }
    
    /// 创建新的事务管理器（兼容旧接口）
    pub fn new_with_connection_manager(connection_manager: Arc<crate::managers::ConnectionManager>) -> Self {
        // 创建默认配置
        let config = TransactionConfig::default();
        // 创建一个DatabaseManager以保持兼容性
        // TODO: 后续需要重构以直接使用ConnectionManager
        let database_manager = Arc::new(DatabaseManager::new_mock().expect("Failed to create mock DatabaseManager"));
        Self::new(database_manager, config)
    }
    
    /// 简单事务执行 - 推荐使用
    ///
    /// 在事务中执行操作，自动处理提交和回滚
    pub async fn execute<F, R>(&self, operation: F) -> SigmaXResult<R>
    where
        F: for<'a> FnOnce(&mut Transaction<'a, Postgres>) -> std::pin::Pin<Box<dyn std::future::Future<Output = SigmaXResult<R>> + Send + 'a>> + Send,
        R: Send,
    {
        let mut tx = self.database_manager.pool().begin().await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::TransactionFailed,
                format!("Failed to begin transaction: {}", e)
            ))?;

        match operation(&mut tx).await {
            Ok(result) => {
                tx.commit().await
                    .map_err(|e| SigmaXError::database(
                        DatabaseErrorCode::TransactionFailed,
                        format!("Failed to commit transaction: {}", e)
                    ))?;
                self.update_statistics(true, false, false).await;
                Ok(result)
            }
            Err(e) => {
                let _ = tx.rollback().await; // 忽略回滚错误
                self.update_statistics(false, true, false).await;
                Err(e)
            }
        }
    }
    
    /// 开始一个新事务（兼容旧接口）
    pub async fn begin_transaction(&self) -> SigmaXResult<DatabaseTransaction> {
        let tx = self.database_manager.pool().begin().await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::TransactionFailed,
                format!("Failed to begin transaction: {}", e)
            ))?;
        
        Ok(DatabaseTransaction::new(tx))
    }
    
    /// 在事务中执行操作（兼容旧接口，更简单的签名）
    pub async fn run_in_transaction<F, Fut, R>(&self, operation: F) -> SigmaXResult<R>
    where
        F: FnOnce(&mut Transaction<'_, Postgres>) -> Fut + Send,
        Fut: std::future::Future<Output = SigmaXResult<R>> + Send,
        R: Send,
    {
        self.execute_in_transaction(operation).await
    }

    /// 创建工作单元 - 复杂场景使用
    pub fn create_unit_of_work(&self) -> UnitOfWorkBuilder {
        UnitOfWorkBuilder::new()
            .isolation_level(self.config.default_isolation_level)
            .scope(self.config.default_scope)
            .read_only(self.config.default_read_only)
    }
    
    /// 在工作单元中执行操作
    pub async fn execute_unit_of_work<F, Fut, R>(&self, mut unit_of_work: DefaultUnitOfWork, operation: F) -> SigmaXResult<R>
    where
        F: FnOnce(&mut DefaultUnitOfWork) -> Fut + Send,
        Fut: std::future::Future<Output = SigmaXResult<R>> + Send,
        R: Send,
    {
        // 开始工作单元
        unit_of_work.begin().await?;
        
        let transaction_id = unit_of_work.get_id();
        info!("开始执行工作单元: {}", transaction_id);
        
        // 开始数据库事务
        let mut tx = self.database_manager.pool().begin().await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::TransactionFailed,
                format!("开始事务失败: {}", e)
            ))?;
        
        // 设置事务上下文
        if let Some(context) = unit_of_work.get_transaction_context() {
            // 设置隔离级别
            let isolation_sql = format!("SET TRANSACTION ISOLATION LEVEL {}", context.isolation_level.to_sql());
            sqlx::query(&isolation_sql).execute(&mut *tx).await
                .map_err(|e| SigmaXError::database(
                    DatabaseErrorCode::TransactionFailed,
                    format!("设置事务隔离级别失败: {}", e)
                ))?;
            
            // 设置只读模式
            if context.read_only {
                sqlx::query("SET TRANSACTION READ ONLY").execute(&mut *tx).await
                    .map_err(|e| SigmaXError::database(
                        DatabaseErrorCode::TransactionFailed,
                        format!("设置只读事务失败: {}", e)
                    ))?;
            }
        }
        
        // 执行操作
        match operation(&mut unit_of_work).await {
            Ok(result) => {
                // 提交工作单元
                unit_of_work.commit().await?;
                
                // 提交数据库事务
                tx.commit().await
                    .map_err(|e| SigmaXError::database(
                        DatabaseErrorCode::TransactionFailed,
                        format!("提交事务失败: {}", e)
                    ))?;
                
                info!("工作单元执行成功: {}", transaction_id);
                self.update_statistics(true, false, false).await;
                Ok(result)
            }
            Err(e) => {
                warn!("工作单元执行失败: {}, 错误: {}", transaction_id, e);
                
                // 回滚工作单元
                if let Err(rollback_err) = unit_of_work.rollback().await {
                    error!("回滚工作单元失败: {}", rollback_err);
                }
                
                // 回滚数据库事务
                if let Err(rollback_err) = tx.rollback().await {
                    error!("回滚数据库事务失败: {}", rollback_err);
                }
                
                self.update_statistics(false, true, false).await;
                Err(e)
            }
        }
    }
    
    /// 在简单事务中执行操作
    pub async fn execute_in_transaction<F, Fut, R>(&self, operation: F) -> SigmaXResult<R>
    where
        F: FnOnce(&mut Transaction<'_, Postgres>) -> Fut + Send,
        Fut: std::future::Future<Output = SigmaXResult<R>> + Send,
        R: Send,
    {
        let mut tx = self.database_manager.pool().begin().await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::TransactionFailed,
                format!("开始事务失败: {}", e)
            ))?;
        
        match operation(&mut tx).await {
            Ok(result) => {
                tx.commit().await
                    .map_err(|e| SigmaXError::database(
                        DatabaseErrorCode::TransactionFailed,
                        format!("提交事务失败: {}", e)
                    ))?;
                
                self.update_statistics(true, false, false).await;
                Ok(result)
            }
            Err(e) => {
                if let Err(rollback_err) = tx.rollback().await {
                    error!("回滚事务失败: {}", rollback_err);
                }
                
                self.update_statistics(false, true, false).await;
                Err(e)
            }
        }
    }
    
    /// 获取事务统计信息
    pub async fn get_statistics(&self) -> TransactionStatistics {
        let stats = self.statistics.lock().await;
        stats.clone()
    }
    
    /// 获取活跃事务数量
    pub async fn get_active_transaction_count(&self) -> usize {
        let transactions = self.active_transactions.read().await;
        transactions.len()
    }
    
    /// 清理超时事务
    pub async fn cleanup_timeout_transactions(&self) -> SigmaXResult<u32> {
        let mut transactions = self.active_transactions.write().await;
        let mut timeout_count = 0;
        let mut to_remove = Vec::new();
        
        for (id, context_mutex) in transactions.iter() {
            let context = context_mutex.lock().await;
            if context.is_timeout() {
                to_remove.push(*id);
                timeout_count += 1;
            }
        }
        
        for id in to_remove {
            transactions.remove(&id);
            warn!("清理超时事务: {}", id);
        }
        
        if timeout_count > 0 {
            self.update_statistics(false, false, true).await;
        }
        
        Ok(timeout_count)
    }
    
    /// 更新统计信息
    async fn update_statistics(&self, committed: bool, rolled_back: bool, timeout: bool) {
        let mut stats = self.statistics.lock().await;
        stats.total_transactions += 1;
        
        if committed {
            stats.committed_transactions += 1;
        }
        if rolled_back {
            stats.rolled_back_transactions += 1;
        }
        if timeout {
            stats.timeout_transactions += 1;
        }
        
        // 更新活跃事务数
        let active_count = self.active_transactions.read().await.len() as u32;
        stats.active_transactions = active_count;
        
        stats.calculated_at = Utc::now();
    }
}

#[async_trait]
impl TransactionContextManager for TransactionManager {
    async fn begin_transaction(
        &self,
        isolation_level: TransactionIsolationLevel,
        scope: TransactionScope,
        read_only: bool,
        timeout_seconds: Option<u64>,
    ) -> SigmaXResult<TransactionContext> {
        let context = TransactionContext::new(isolation_level, scope, read_only, timeout_seconds);
        let context_id = context.transaction_id;
        
        // 检查最大并发事务数限制
        if let Some(max_concurrent) = self.config.max_concurrent_transactions {
            let active_count = self.active_transactions.read().await.len();
            if active_count >= max_concurrent as usize {
                return Err(SigmaXError::database(
                    DatabaseErrorCode::TransactionFailed,
                    format!("超过最大并发事务数限制: {}", max_concurrent)
                ));
            }
        }
        
        // 添加到活跃事务列表
        let mut transactions = self.active_transactions.write().await;
        transactions.insert(context_id, Arc::new(Mutex::new(context.clone())));
        
        debug!("创建新事务上下文: {}", context_id);
        Ok(context)
    }
    
    async fn commit_transaction(&self, context: &mut TransactionContext) -> SigmaXResult<()> {
        if !context.is_active() {
            return Err(SigmaXError::database(
                DatabaseErrorCode::TransactionFailed,
                "事务已完成或未开始".to_string()
            ));
        }
        
        context.mark_committed();
        
        // 从活跃事务列表中移除
        let mut transactions = self.active_transactions.write().await;
        transactions.remove(&context.transaction_id);
        
        debug!("提交事务: {}", context.transaction_id);
        Ok(())
    }
    
    async fn rollback_transaction(&self, context: &mut TransactionContext) -> SigmaXResult<()> {
        if context.is_committed() {
            return Err(SigmaXError::database(
                DatabaseErrorCode::TransactionFailed,
                "事务已提交，无法回滚".to_string()
            ));
        }
        
        context.mark_rolled_back();
        
        // 从活跃事务列表中移除
        let mut transactions = self.active_transactions.write().await;
        transactions.remove(&context.transaction_id);
        
        debug!("回滚事务: {}", context.transaction_id);
        Ok(())
    }
    
    async fn execute_in_transaction<F, Fut, R>(
        &self,
        context: &TransactionContext,
        operation: F,
    ) -> SigmaXResult<R>
    where
        F: FnOnce(&mut Transaction<'_, Postgres>) -> Fut + Send,
        Fut: std::future::Future<Output = SigmaXResult<R>> + Send,
        R: Send,
    {
        if !context.is_active() {
            return Err(SigmaXError::database(
                DatabaseErrorCode::TransactionFailed,
                "事务未激活".to_string()
            ));
        }
        
        // 这里需要从context中获取实际的SQLx事务
        // 由于生命周期限制，这个实现需要进一步优化
        let mut tx = self.database_manager.pool().begin().await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::TransactionFailed,
                format!("开始事务失败: {}", e)
            ))?;
        
        operation(&mut tx).await
    }
}

// ============================================================================
// 兼容性方法 - 与旧的 managers/transaction_manager.rs 保持兼容
// ============================================================================

impl TransactionManager {
    /// 获取事务统计信息（兼容旧接口）
    pub async fn get_transaction_stats(&self) -> SigmaXResult<TransactionStats> {
        let statistics = self.get_statistics().await;
        let active_count = self.get_active_transaction_count().await;
        
        Ok(TransactionStats {
            total_transactions: statistics.total_transactions as u64,
            committed_transactions: statistics.committed_transactions as u64,
            rolled_back_transactions: statistics.rolled_back_transactions as u64,
            active_transactions: active_count as u64,
            avg_transaction_duration_ms: statistics.max_execution_time_ms as u64,
        })
    }
    
    /// 检查长时间运行的事务（兼容旧接口）
    pub async fn check_long_running_transactions(&self, threshold_seconds: u32) -> SigmaXResult<Vec<LongRunningTransaction>> {
        let transactions = self.active_transactions.read().await;
        let now = Utc::now();
        let mut long_running = Vec::new();
        
        for (id, context_mutex) in transactions.iter() {
            let context = context_mutex.lock().await;
            let duration = (now.timestamp() - context.started_at.timestamp()) as u64;
            if duration > threshold_seconds as u64 {
                long_running.push(LongRunningTransaction {
                    transaction_id: id.to_string(),
                    start_time: context.started_at,
                    duration_seconds: duration,
                    query: None, // 当前实现不跟踪具体查询
                });
            }
        }
        
        Ok(long_running)
    }
}
