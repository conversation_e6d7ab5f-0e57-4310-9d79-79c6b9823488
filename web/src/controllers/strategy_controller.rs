//! 策略控制器 - 纯HTTP处理
//!
//! 只负责HTTP层面的处理，不包含业务逻辑

use std::sync::Arc;
use axum::{
    extract::{Path, Query, State},
    Json,
};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use tracing::info;

use crate::{
    controllers::BaseController,
    application::strategy_application_service::StrategyApplicationService,
    error::{ApiResult, ApiError},
    state::AppState,
};

/// 策略创建请求
#[derive(Debug, Deserialize)]
pub struct CreateStrategyRequest {
    pub strategy_type: String,
    pub name: String,
    pub description: Option<String>,
    pub trading_pairs: Vec<String>,
    pub initial_capital: String,
    pub parameters: serde_json::Value,
    pub risk_config: Option<StrategyRiskConfigRequest>,
    pub enabled: Option<bool>,
}

/// 风控配置请求
#[derive(Debug, Deserialize)]
pub struct StrategyRiskConfigRequest {
    pub max_position_size: Option<String>,
    pub max_daily_loss: Option<String>,
    pub max_drawdown: Option<String>,
    pub stop_loss_percentage: Option<String>,
    pub take_profit_percentage: Option<String>,
    pub max_leverage: Option<String>,
}

/// 策略更新请求
#[derive(Debug, Deserialize)]
pub struct UpdateStrategyRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub trading_pairs: Option<Vec<String>>,
    pub parameters: Option<serde_json::Value>,
    pub risk_config: Option<StrategyRiskConfigRequest>,
    pub enabled: Option<bool>,
}

/// 策略响应
#[derive(Debug, Serialize)]
pub struct StrategyResponse {
    pub id: Uuid,
    pub name: String,
    pub strategy_type: String,
    pub status: String,
    pub created_at: String,
    pub updated_at: String,
}

/// 策略控制器
pub struct StrategyController {
    strategy_app_service: Arc<dyn StrategyApplicationService>,
}

impl StrategyController {
    /// 创建新的策略控制器
    pub fn new(strategy_app_service: Arc<dyn StrategyApplicationService>) -> Self {
        Self { strategy_app_service }
    }
    
    /// 验证请求参数
    fn validate_request(&self, request: &CreateStrategyRequest) -> Result<(), ApiError> {
        if request.name.trim().is_empty() {
            return Err(ApiError::ValidationError("策略名称不能为空".to_string()));
        }
        
        if request.trading_pairs.is_empty() {
            return Err(ApiError::ValidationError("交易对不能为空".to_string()));
        }
        
        if request.initial_capital.trim().is_empty() {
            return Err(ApiError::ValidationError("初始资金不能为空".to_string()));
        }
        
        Ok(())
    }
}

impl BaseController for StrategyController {
    fn name(&self) -> &str {
        "StrategyController"
    }
    
    async fn health_check(&self) -> bool {
        // 简单的健康检查：检查应用服务是否可用
        true // 简化实现
    }
}

impl StrategyController {
    /// 创建策略 - 只负责HTTP层面的处理
    pub async fn create_strategy(
        &self,
        Json(request): Json<CreateStrategyRequest>,
    ) -> ApiResult<Json<StrategyResponse>> {
        info!("📝 策略控制器收到创建策略请求: {}", request.name);
        
        // 1. 请求验证（格式、必填字段）
        self.validate_request(&request)?;
        
        // 2. 数据转换（HTTP DTO -> Domain Model）
        let command = CreateStrategyCommand::from(request);
        
        // 3. 委托给应用服务处理业务逻辑
        let result = self.strategy_app_service.create_strategy(command).await
            .map_err(|e| ApiError::BusinessError(e.to_string()))?;
        
        // 4. 响应转换（Domain Model -> HTTP DTO）
        let response = StrategyResponse::from(result);
        
        info!("✅ 策略控制器完成创建策略请求: {}", response.name);
        Ok(Json(response))
    }
    
    /// 获取策略列表
    pub async fn get_strategies(
        &self,
        Query(query): Query<StrategyQueryParams>,
    ) -> ApiResult<Json<Vec<StrategyResponse>>> {
        info!("📋 策略控制器收到获取策略列表请求");
        
        // 委托给应用服务
        let strategies = self.strategy_app_service.get_strategies(query).await
            .map_err(|e| ApiError::BusinessError(e.to_string()))?;
        
        // 转换为响应格式
        let responses: Vec<StrategyResponse> = strategies.into_iter()
            .map(StrategyResponse::from)
            .collect();
        
        Ok(Json(responses))
    }
    
    /// 获取单个策略
    pub async fn get_strategy(
        &self,
        Path(id): Path<Uuid>,
    ) -> ApiResult<Json<StrategyResponse>> {
        info!("🔍 策略控制器收到获取策略请求: {}", id);
        
        // 委托给应用服务
        let strategy = self.strategy_app_service.get_strategy(id).await
            .map_err(|e| ApiError::BusinessError(e.to_string()))?;
        
        let response = StrategyResponse::from(strategy);
        Ok(Json(response))
    }
    
    /// 更新策略
    pub async fn update_strategy(
        &self,
        Path(id): Path<Uuid>,
        Json(request): Json<UpdateStrategyRequest>,
    ) -> ApiResult<Json<StrategyResponse>> {
        info!("📝 策略控制器收到更新策略请求: {}", id);
        
        // 委托给应用服务
        let command = UpdateStrategyCommand::new(id, request);
        let strategy = self.strategy_app_service.update_strategy(command).await
            .map_err(|e| ApiError::BusinessError(e.to_string()))?;
        
        let response = StrategyResponse::from(strategy);
        Ok(Json(response))
    }
    
    /// 删除策略
    pub async fn delete_strategy(
        &self,
        Path(id): Path<Uuid>,
    ) -> ApiResult<Json<()>> {
        info!("🗑️ 策略控制器收到删除策略请求: {}", id);
        
        // 委托给应用服务
        self.strategy_app_service.delete_strategy(id).await
            .map_err(|e| ApiError::BusinessError(e.to_string()))?;
        
        Ok(Json(()))
    }
}

// 临时类型定义（后续会移到专门的DTO模块）
#[derive(Debug)]
pub struct CreateStrategyCommand {
    pub strategy_type: String,
    pub name: String,
    pub description: Option<String>,
    pub trading_pairs: Vec<String>,
    pub initial_capital: String,
    pub parameters: serde_json::Value,
    pub risk_config: Option<StrategyRiskConfigRequest>,
    pub enabled: Option<bool>,
}

impl From<CreateStrategyRequest> for CreateStrategyCommand {
    fn from(request: CreateStrategyRequest) -> Self {
        Self {
            strategy_type: request.strategy_type,
            name: request.name,
            description: request.description,
            trading_pairs: request.trading_pairs,
            initial_capital: request.initial_capital,
            parameters: request.parameters,
            risk_config: request.risk_config,
            enabled: request.enabled,
        }
    }
}

#[derive(Debug)]
pub struct UpdateStrategyCommand {
    pub id: Uuid,
    pub name: Option<String>,
    pub description: Option<String>,
    pub trading_pairs: Option<Vec<String>>,
    pub parameters: Option<serde_json::Value>,
    pub risk_config: Option<StrategyRiskConfigRequest>,
    pub enabled: Option<bool>,
}

impl UpdateStrategyCommand {
    pub fn new(id: Uuid, request: UpdateStrategyRequest) -> Self {
        Self {
            id,
            name: request.name,
            description: request.description,
            trading_pairs: request.trading_pairs,
            parameters: request.parameters,
            risk_config: request.risk_config,
            enabled: request.enabled,
        }
    }
}

#[derive(Debug)]
pub struct StrategyQueryParams {
    pub page: Option<u32>,
    pub size: Option<u32>,
    pub strategy_type: Option<String>,
    pub enabled: Option<bool>,
}

// 临时策略类型（后续会从domain模块导入）
#[derive(Debug, Clone)]
pub struct Strategy {
    pub id: Uuid,
    pub name: String,
    pub strategy_type: String,
    pub status: String,
    pub created_at: String,
    pub updated_at: String,
}

impl From<Strategy> for StrategyResponse {
    fn from(strategy: Strategy) -> Self {
        Self {
            id: strategy.id,
            name: strategy.name,
            strategy_type: strategy.strategy_type,
            status: strategy.status,
            created_at: strategy.created_at,
            updated_at: strategy.updated_at,
        }
    }
}

// 临时应用服务接口（后续会从application模块导入）
#[async_trait::async_trait]
pub trait StrategyApplicationService: Send + Sync {
    async fn create_strategy(&self, command: CreateStrategyCommand) -> Result<Strategy, Box<dyn std::error::Error>>;
    async fn get_strategies(&self, query: StrategyQueryParams) -> Result<Vec<Strategy>, Box<dyn std::error::Error>>;
    async fn get_strategy(&self, id: Uuid) -> Result<Strategy, Box<dyn std::error::Error>>;
    async fn update_strategy(&self, command: UpdateStrategyCommand) -> Result<Strategy, Box<dyn std::error::Error>>;
    async fn delete_strategy(&self, id: Uuid) -> Result<(), Box<dyn std::error::Error>>;
}
