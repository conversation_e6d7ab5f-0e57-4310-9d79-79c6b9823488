//! 结果类型定义和扩展方法

use crate::error::SigmaXError;
use crate::error::ErrorCategory;

/// SigmaX 统一结果类型
pub type SigmaXResult<T> = Result<T, SigmaXError>;

/// 结果类型扩展方法
pub trait SigmaXResultExt<T> {
    /// 添加错误上下文信息
    /// 
    /// # 示例
    /// 
    /// ```rust
    /// use sigmax_core::{SigmaXResult, SigmaXResultExt};
    /// 
    /// fn process_order() -> SigmaXResult<()> {
    ///     validate_order()
    ///         .with_context("operation", "order_processing")
    ///         .with_context("module", "trading_engine")
    /// }
    /// ```
    fn with_context(self, key: &str, value: &str) -> SigmaXResult<T>;
    
    /// 添加追踪ID
    /// 
    /// # 示例
    /// 
    /// ```rust
    /// use sigmax_core::{SigmaXResult, SigmaXResultExt};
    /// 
    /// fn execute_trade(trace_id: &str) -> SigmaXResult<()> {
    ///     perform_trade()
    ///         .with_trace_id(trace_id)
    /// }
    /// ```
    fn with_trace_id(self, trace_id: &str) -> SigmaXResult<T>;
    
    /// 添加用户ID
    /// 
    /// # 示例
    /// 
    /// ```rust
    /// use sigmax_core::{SigmaXResult, SigmaXResultExt};
    /// 
    /// fn user_operation(user_id: &str) -> SigmaXResult<()> {
    ///     risky_operation()
    ///         .with_user_id(user_id)
    /// }
    /// ```
    fn with_user_id(self, user_id: &str) -> SigmaXResult<T>;
    
    /// 转换为用户友好的错误消息
    /// 
    /// # 示例
    /// 
    /// ```rust
    /// use sigmax_core::{SigmaXResult, SigmaXResultExt};
    /// 
    /// fn api_handler() -> Result<String, String> {
    ///     internal_operation()
    ///         .user_friendly()
    /// }
    /// ```
    fn user_friendly(self) -> Result<T, String>;
    
    /// 获取错误代码（如果存在）
    /// 
    /// # 示例
    /// 
    /// ```rust
    /// use sigmax_core::{SigmaXResult, SigmaXResultExt};
    /// 
    /// let result: SigmaXResult<()> = Err(trading_error!(...));
    /// if let Some(code) = result.error_code() {
    ///     println!("错误代码: {}", code);
    /// }
    /// ```
    fn error_code(&self) -> Option<String>;
    
    /// 检查是否为可重试错误
    /// 
    /// # 示例
    /// 
    /// ```rust
    /// use sigmax_core::{SigmaXResult, SigmaXResultExt};
    /// 
    /// let result: SigmaXResult<()> = perform_network_call();
    /// if result.is_retryable() {
    ///     // 可以重试
    /// }
    /// ```
    fn is_retryable(&self) -> bool;
    
    /// 映射错误但保持成功值不变
    /// 
    /// # 示例
    /// 
    /// ```rust
    /// use sigmax_core::{SigmaXResult, SigmaXResultExt};
    /// 
    /// let result = network_call()
    ///     .map_error(|e| e.with_context("retry_count", "3"));
    /// ```
    fn map_error<F>(self, f: F) -> SigmaXResult<T>
    where
        F: FnOnce(SigmaXError) -> SigmaXError;
    
    /// 在错误时执行操作但不改变结果
    /// 
    /// # 示例
    /// 
    /// ```rust
    /// use sigmax_core::{SigmaXResult, SigmaXResultExt};
    /// 
    /// let result = risky_operation()
    ///     .on_error(|e| {
    ///         log_error!(e, "操作失败");
    ///         send_alert(&e);
    ///     });
    /// ```
    fn on_error<F>(self, f: F) -> SigmaXResult<T>
    where
        F: FnOnce(&SigmaXError);
    
    /// 提供默认值（仅在特定错误时）
    /// 
    /// # 示例
    /// 
    /// ```rust
    /// use sigmax_core::{SigmaXResult, SigmaXResultExt, ErrorCategory};
    /// 
    /// let result = load_config()
    ///     .or_default_on(ErrorCategory::External, Config::default());
    /// ```
    fn or_default_on(self, category: ErrorCategory, default: T) -> SigmaXResult<T>;
}

impl<T> SigmaXResultExt<T> for SigmaXResult<T> {
    fn with_context(self, key: &str, value: &str) -> SigmaXResult<T> {
        self.map_err(|e| e.with_context(key, value))
    }
    
    fn with_trace_id(self, trace_id: &str) -> SigmaXResult<T> {
        self.map_err(|e| e.with_trace_id(trace_id))
    }
    
    fn with_user_id(self, user_id: &str) -> SigmaXResult<T> {
        self.map_err(|e| e.with_user_id(user_id))
    }
    
    fn user_friendly(self) -> Result<T, String> {
        self.map_err(|e| e.user_message())
    }
    
    fn error_code(&self) -> Option<String> {
        self.as_ref().err().map(|e| e.error_code())
    }
    
    fn is_retryable(&self) -> bool {
        self.as_ref().err().map_or(false, |e| e.is_retryable())
    }
    
    fn map_error<F>(self, f: F) -> SigmaXResult<T>
    where
        F: FnOnce(SigmaXError) -> SigmaXError,
    {
        self.map_err(f)
    }
    
    fn on_error<F>(self, f: F) -> SigmaXResult<T>
    where
        F: FnOnce(&SigmaXError),
    {
        if let Err(ref e) = self {
            f(e);
        }
        self
    }
    
    fn or_default_on(self, category: ErrorCategory, default: T) -> SigmaXResult<T> {
        match self {
            Ok(value) => Ok(value),
            Err(e) if e.category() == category => Ok(default),
            Err(e) => Err(e),
        }
    }
}

/// 批量处理结果的辅助函数和类型

/// 批量结果类型 - 包含成功和失败的结果
#[derive(Debug, Clone)]
pub struct BatchResult<T> {
    pub successes: Vec<T>,
    pub failures: Vec<SigmaXError>,
}

impl<T> BatchResult<T> {
    /// 创建新的批量结果
    pub fn new() -> Self {
        Self {
            successes: Vec::new(),
            failures: Vec::new(),
        }
    }
    
    /// 添加成功结果
    pub fn add_success(&mut self, item: T) {
        self.successes.push(item);
    }
    
    /// 添加失败结果
    pub fn add_failure(&mut self, error: SigmaXError) {
        self.failures.push(error);
    }
    
    /// 检查是否有失败
    pub fn has_failures(&self) -> bool {
        !self.failures.is_empty()
    }
    
    /// 获取成功数量
    pub fn success_count(&self) -> usize {
        self.successes.len()
    }
    
    /// 获取失败数量
    pub fn failure_count(&self) -> usize {
        self.failures.len()
    }
    
    /// 获取总数量
    pub fn total_count(&self) -> usize {
        self.success_count() + self.failure_count()
    }
    
    /// 获取成功率
    pub fn success_rate(&self) -> f64 {
        if self.total_count() == 0 {
            return 0.0;
        }
        self.success_count() as f64 / self.total_count() as f64
    }
    
    /// 转换为结果 - 如果有失败则返回第一个错误
    pub fn into_result(self) -> SigmaXResult<Vec<T>> {
        if let Some(first_error) = self.failures.into_iter().next() {
            Err(first_error)
        } else {
            Ok(self.successes)
        }
    }
    
    /// 合并多个批量结果
    pub fn merge(mut self, other: BatchResult<T>) -> Self {
        self.successes.extend(other.successes);
        self.failures.extend(other.failures);
        self
    }
}

impl<T> Default for BatchResult<T> {
    fn default() -> Self {
        Self::new()
    }
}

impl<T> FromIterator<SigmaXResult<T>> for BatchResult<T> {
    fn from_iter<I: IntoIterator<Item = SigmaXResult<T>>>(iter: I) -> Self {
        let mut batch_result = BatchResult::new();
        
        for result in iter {
            match result {
                Ok(item) => batch_result.add_success(item),
                Err(error) => batch_result.add_failure(error),
            }
        }
        
        batch_result
    }
}

/// 批量结果扩展方法
pub trait BatchResultExt<T> {
    /// 从结果向量创建批量结果
    fn from_results(results: Vec<SigmaXResult<T>>) -> BatchResult<T>;
    
    /// 处理结果向量，应用函数到每个成功值
    fn process_results<U, F>(results: Vec<SigmaXResult<T>>, f: F) -> BatchResult<U>
    where
        F: Fn(T) -> U;
}

impl<T> BatchResultExt<T> for BatchResult<T> {
    fn from_results(results: Vec<SigmaXResult<T>>) -> BatchResult<T> {
        results.into_iter().collect()
    }
    
    fn process_results<U, F>(results: Vec<SigmaXResult<T>>, f: F) -> BatchResult<U>
    where
        F: Fn(T) -> U,
    {
        let mut batch_result = BatchResult::new();
        
        for result in results {
            match result {
                Ok(item) => batch_result.add_success(f(item)),
                Err(error) => batch_result.add_failure(error),
            }
        }
        
        batch_result
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::error::{SigmaXError, TradingErrorCode, ValidationErrorCode, MarketDataErrorCode, ErrorCategory};
    use crate::error::InternalErrorCode;
    
    #[test]
    fn test_result_extensions() {
        let result: SigmaXResult<String> = Err(
            SigmaXError::trading(TradingErrorCode::InsufficientBalance, "余额不足")
        );
        
        let enhanced_result = result
            .with_context("user_id", "123")
            .with_trace_id("trace-456");
        
        assert!(enhanced_result.is_err());
        assert_eq!(enhanced_result.error_code(), Some("TRADING_INSUFFICIENT_BALANCE".to_string()));
        assert!(!enhanced_result.is_retryable());
    }
    
    #[test]
    fn test_user_friendly_error() {
        let result: SigmaXResult<()> = Err(
            SigmaXError::trading(TradingErrorCode::InsufficientBalance, "余额不足")
        );
        
        let user_result = result.user_friendly();
        assert_eq!(user_result.unwrap_err(), "账户余额不足，无法完成交易");
    }
    
    #[test]
    fn test_or_default_on() {
        let external_error = SigmaXError::market_data(
            crate::MarketDataErrorCode::SourceUnavailable,
            "数据源不可用"
        );
        
        let result: SigmaXResult<String> = Err(external_error);
        let with_default = result.or_default_on(ErrorCategory::External, "默认值".to_string());
        
        assert_eq!(with_default.unwrap(), "默认值");
    }
    
    #[test]
    fn test_batch_result() {
        let results = vec![
            Ok("成功1".to_string()),
            Err(SigmaXError::validation(ValidationErrorCode::Required, "field1", "必填")),
            Ok("成功2".to_string()),
            Err(SigmaXError::validation(ValidationErrorCode::InvalidFormat, "field2", "格式错误")),
        ];
        
        let batch_result: BatchResult<String> = results.into_iter().collect();
        
        assert_eq!(batch_result.success_count(), 2);
        assert_eq!(batch_result.failure_count(), 2);
        assert_eq!(batch_result.success_rate(), 0.5);
        assert!(batch_result.has_failures());
    }
    
    #[test]
    fn test_batch_result_processing() {
        let results = vec![
            Ok(1),
            Ok(2),
            Err(SigmaXError::validation(ValidationErrorCode::Required, "field", "错误")),
            Ok(3),
        ];
        
        let processed = BatchResult::process_results(results, |x| x * 2);
        
        assert_eq!(processed.successes, vec![2, 4, 6]);
        assert_eq!(processed.failure_count(), 1);
    }
    
    #[test]
    fn test_on_error() {
        let mut error_logged = false;
        
        let result: SigmaXResult<()> = Err(
            SigmaXError::internal(crate::InternalErrorCode::UnexpectedState, "内部错误")
        );
        
        let _ = result.on_error(|_| {
            error_logged = true;
        });
        
        assert!(error_logged);
    }
}