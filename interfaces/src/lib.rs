//! SigmaX 接口层 - Interface Layer
//!
//! 实现面向接口设计原则，定义清晰的接口契约
//! 
//! ## 核心设计原则体现
//!
//! ### 3. 面向接口设计 (Design to an Interface)
//! - 定义清晰的接口契约
//! - 依赖抽象而非具体实现
//! - 支持多种实现方式
//!
//! ### 4. 可测试性设计 (Design for Testability)
//! - 所有接口都支持Mock实现
//! - 依赖注入友好的设计
//! - 便于单元测试和集成测试
//!
//! ## 接口分类
//!
//! - **交易相关**: 订单处理、交易执行
//! - **风控相关**: 风险检查、限制管理
//! - **数据相关**: 市场数据、历史数据
//! - **执行相关**: 引擎管理、订单路由
//!
//! ## 使用方式
//!
//! ```rust
//! use sigmax_interfaces::{RiskController, ExecutionEngine, TradingOrchestrator};
//! use std::sync::Arc;
//!
//! // 依赖抽象接口而非具体实现
//! struct TradingSystem {
//!     risk_controller: Arc<dyn RiskController>,
//!     execution_engine: Arc<dyn ExecutionEngine>,
//!     orchestrator: Arc<dyn TradingOrchestrator>,
//! }
//! ```

// ============================================================================
// 模块导出
// ============================================================================

/// 交易相关接口
pub mod trading;

/// 风控相关接口  
pub mod risk;

/// 数据相关接口
pub mod data;

/// 执行相关接口
pub mod execution;

// ============================================================================
// 统一导出
// ============================================================================

// 统一交易接口
pub use trading::{
    ExchangeConnector,  // 🆕 统一交易所接口 (替代 core::Exchange)
    TradingOrchestrator, TradeRequest, TradeError,
    OrderManager, OrderError,
    PortfolioManager, PortfolioError,
    Portfolio, PortfolioSnapshot  // 🆕 统一的投资组合数据模型
};

// 风控接口
pub use risk::{
    RiskController, RiskAdapter, RiskResult, RiskError,
    RiskContext, RiskPolicy, RiskCalculator,
    PolicyResult, PolicyError,
    BatchRiskResult, BasicRiskMetrics, AdvancedRiskMetrics, PortfolioRisk,
    RiskAlert, AlertSeverity, AdapterMetrics
};

// 统一数据接口
pub use data::{
    DataProvider,  // 🔄 统一数据提供者 (合并 core + interfaces 功能)
    MarketDataProvider, HistoricalDataProvider,
    PriceProvider, MarketData, OrderBook, Trade
};

// 执行接口
pub use execution::{
    ExecutionEngine, ExecutionResult, ExecutionError, ExecutionStatus,
    EngineRunStatus, EngineManager, EngineConfig, EngineInfo,
    PerformanceMetrics, HealthCheckResult, ResourceConfig
};



// ============================================================================
// 统一结果类型 - 接口统一化标准
// ============================================================================

/// 接口层统一结果类型 - 所有接口都使用此类型
///
/// 统一化原则：
/// - 所有接口方法都返回 SigmaXResult<T>
/// - 不再使用 DataResult, TradeResult 等分散的类型
/// - 错误处理统一到 sigmax_core::SigmaXError
pub type InterfaceResult<T> = sigmax_core::SigmaXResult<T>;

// 为了向后兼容，提供类型别名（将逐步移除）
#[deprecated(since = "0.3.0", note = "请直接使用 sigmax_core::SigmaXResult")]
pub type DataResult<T> = sigmax_core::SigmaXResult<T>;

#[deprecated(since = "0.3.0", note = "请直接使用 sigmax_core::SigmaXResult")]
pub type TradeResult = sigmax_core::SigmaXResult<()>;

/// 接口层错误转换 - 将接口特定错误转换为SigmaXError
pub mod error_conversion {
    use sigmax_core::{SigmaXError, TradingErrorCode, RiskErrorCode, NetworkErrorCode, ConfigErrorCode, ValidationErrorCode, InternalErrorCode};
    use super::*;

    /// 将TradeError转换为SigmaXError
    pub fn trade_error_to_sigmax(error: TradeError) -> SigmaXError {
        SigmaXError::trading(TradingErrorCode::OrderExecutionFailed, error.to_string())
    }

    /// 将RiskError转换为SigmaXError
    pub fn risk_error_to_sigmax(error: RiskError) -> SigmaXError {
        SigmaXError::risk(RiskErrorCode::CheckFailed, error.to_string(), "interface_layer")
    }

    // DataError 转换函数已移除 - 统一使用 SigmaXResult

    /// 将ExecutionError转换为SigmaXError
    pub fn execution_error_to_sigmax(error: ExecutionError) -> SigmaXError {
        SigmaXError::internal(InternalErrorCode::UnexpectedState, error.to_string())
    }

    /// 创建配置错误
    pub fn configuration_error(message: String) -> SigmaXError {
        SigmaXError::configuration(ConfigErrorCode::InvalidValue, message)
    }

    /// 创建验证错误
    pub fn validation_error(message: String) -> SigmaXError {
        SigmaXError::validation(ValidationErrorCode::InvalidFormat, "interface", message)
    }

    /// 创建超时错误
    pub fn timeout_error(timeout_ms: u64) -> SigmaXError {
        SigmaXError::network(NetworkErrorCode::Timeout, format!("操作超时: {}ms", timeout_ms))
    }

    /// 创建未实现错误
    pub fn not_implemented_error(feature: String) -> SigmaXError {
        SigmaXError::internal(InternalErrorCode::UnexpectedState, format!("功能未实现: {}", feature))
    }
}