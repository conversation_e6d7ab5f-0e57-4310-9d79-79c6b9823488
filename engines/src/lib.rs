//! SigmaX 执行引擎模块 - 重构版
//!
//! ## 核心设计原则体现
//!
//! ### 1. 高内聚，低耦合
//! - 专注执行逻辑，风控逻辑移至 risk_control 模块
//! - 引擎间通过接口松耦合
//!
//! ### 2. 关注点分离
//! - 纯执行逻辑，不涉及风控决策
//! - 配置、数据访问分离到专门模块
//!
//! ### 3. 面向接口设计  
//! - 实现 sigmax-interfaces 定义的执行接口
//! - 依赖抽象的 RiskController 接口
//!
//! ### 4. 可测试性设计
//! - 支持依赖注入
//! - 可Mock的风控依赖
//!
//! ### 5. 简洁与可演化性
//! - 简化的引擎架构
//! - 可扩展的引擎类型

use sigmax_interfaces::{RiskController, ExecutionEngine, EngineRunStatus, ExecutionResult, PerformanceMetrics};
use sigmax_core::{EngineType, SigmaXResult, SigmaXError, InternalErrorCode, UnifiedEngineConfig};
use std::sync::Arc;

// ============================================================================
// 执行引擎模块 - 专注执行逻辑
// ============================================================================

/// 回测执行引擎
pub mod backtest;

/// 实盘执行引擎
pub mod live;

/// 模拟执行引擎
pub mod paper;

/// 引擎管理器
pub mod manager;

/// 引擎工厂
pub mod factory;

/// 基础引擎组件
pub mod base;

// ============================================================================
// 风控相关模块已完全迁移到 sigmax-risk 模块
// ============================================================================

// 注意：风控相关的所有功能现在通过 sigmax-risk 模块提供
// 使用 sigmax_interfaces::RiskController 接口进行依赖注入

// ============================================================================
// 其他保留模块
// ============================================================================

/// 性能基准测试
pub mod benchmark;

/// 恢复管理器
pub mod recovery_manager;

/// 增强指标
pub mod enhanced_metrics;

/// 配置验证器
pub mod config_validator;

/// 统一错误处理
pub mod error_handling;

/// 测试模块
#[cfg(test)]
pub mod tests;

// ============================================================================
// 公共导出 - 仅执行相关
// ============================================================================

// 执行引擎导出
pub use backtest::BacktestEngine;
pub use live::LiveTradingEngine as LiveEngine;
pub use paper::PaperEngine;

// 管理组件导出
pub use manager::EngineManager;
pub use factory::EngineFactory;
pub use base::BaseEngineConfig;

// 其他组件导出
pub use benchmark::*;
pub use recovery_manager::*;
pub use enhanced_metrics::*;
pub use config_validator::{ConfigurationManager, ConfigValidator};
pub use error_handling::{ErrorContext, EngineErrorExt, ResultExt, AsyncResultExt, BatchErrorHandler};

// ============================================================================
// 执行引擎配置
// ============================================================================

use serde::{Deserialize, Serialize};
// EngineType already imported above
use std::collections::HashMap;

/// 执行引擎配置 - 已迁移到 UnifiedEngineConfig
/// 保留此类型以便向后兼容
pub type ExecutionEngineConfig = UnifiedEngineConfig;

/// 执行引擎构建器
pub struct ExecutionEngineBuilder {
    config: UnifiedEngineConfig,
}

impl ExecutionEngineBuilder {
    pub fn new(engine_type: EngineType) -> Self {
        use sigmax_core::UnifiedEngineConfigBuilder;

        let config = UnifiedEngineConfigBuilder::new(engine_type, "Default Engine".to_string())
            .build()
            .expect("Failed to create default config");

        Self { config }
    }

    /// 注入风控控制器 (通过引擎特定配置)
    pub fn with_risk_controller(mut self, _risk_controller: Arc<dyn RiskController>) -> Self {
        // 风控控制器现在通过依赖注入系统管理
        // 这里保留接口以便向后兼容
        self
    }

    /// 添加设置
    pub fn with_setting(mut self, key: &str, value: serde_json::Value) -> Self {
        self.config.engine_specific.insert(key.to_string(), value);
        self
    }
    
    /// 构建执行引擎
    pub async fn build(self) -> sigmax_core::SigmaXResult<Box<dyn ExecutionEngine>> {
        // 注意：这个方法需要 ServiceContainer 来创建引擎
        // 建议使用 EngineFactory 而不是直接使用 Builder
        Err(sigmax_core::SigmaXError::internal(
            InternalErrorCode::UnexpectedState,
            "ExecutionEngineBuilder is deprecated. Use EngineFactory instead."
        ))
    }
}

// ============================================================================
// 迁移说明
// ============================================================================

/// 迁移指南
/// 
/// ## 风控相关迁移
/// 
/// ### 旧代码
/// ```rust,ignore
/// use sigmax_engines::risk::{UnifiedRiskManager, SimpleRiskAdapter};
/// 
/// let risk_manager = UnifiedRiskManager::new().await?;
/// let adapter = SimpleRiskAdapter::new().await?;
/// ```
/// 
/// ### 新代码
/// ```rust,ignore
/// use sigmax_risk_control::{RiskControllerFactory};
/// use sigmax_interfaces::RiskController;
/// 
/// let risk_controller = RiskControllerFactory::create_standard().await;
/// ```
/// 
/// ## 执行引擎迁移
/// 
/// ### 旧代码
/// ```rust,ignore
/// use sigmax_engines::{LiveTradingEngine};
/// 
/// let engine = LiveTradingEngine::new(config).await?;
/// ```
/// 
/// ### 新代码
/// ```rust,ignore
/// use sigmax_engines::{ExecutionEngineBuilder};
/// use sigmax_interfaces::ExecutionEngine;
/// 
/// let engine = ExecutionEngineBuilder::new(EngineType::Live)
///     .with_risk_controller(risk_controller)
///     .build()
///     .await?;
/// ```
pub mod migration_guide {
    //! 迁移指南模块
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_execution_engine_builder() {
        // TODO: 实现测试
        // 测试新的ExecutionEngineBuilder
    }
}