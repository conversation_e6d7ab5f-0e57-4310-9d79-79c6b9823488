{"permissions": {"allow": ["mcp__zen__analyze", "Bash(find:*)", "Bash(grep:*)", "Bash(cargo check:*)", "Bash(cargo test:*)", "Bash(rm:*)", "Bash(cargo build:*)", "Bash(cargo run:*)", "Bash(rustc:*)", "mcp__zen__chat", "Bash(psql:*)", "Bash(brew list:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(for file in sql_*.rs)", "Bash(do echo \"Fixing $file...\")", "<PERSON><PERSON>(sed:*)", "Bash(done)", "Bash(do echo \"Adding imports to $file...\")"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["zen"]}