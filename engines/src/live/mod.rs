//! 实盘引擎子模块
//! 
//! 设计原则：
//! - 低延迟优化：针对实盘交易的毫秒级响应要求
//! - 高可靠性：熔断器、重试机制、故障恢复
//! - 实时监控：详细的性能指标和告警

pub mod engine;
pub mod config;
// pub mod metrics; // 暂时注释掉，等待实现
pub mod circuit_breaker;

// 重新导出核心组件
pub use engine::LiveTradingEngine;
pub use config::{LiveTradingConfig, LiveAdapterConfig};
// pub use metrics::LiveTradingMetrics; // TODO: 创建metrics模块
pub use circuit_breaker::CircuitBreaker;

// 重新导出现有的实盘引擎以保持兼容性
// pub use crate::live::*; // 不能glob导入自己

use async_trait::async_trait;
use sigmax_core::{EngineType, SigmaXResult, SigmaXError};
use std::sync::Arc;
use std::time::Duration;

/// 实盘交易模式
#[derive(Debug, Clone, PartialEq)]
pub enum LiveTradingMode {
    /// 生产模式 - 最高可靠性
    Production,
    /// 测试模式 - 平衡性能和安全
    Testing,
    /// 开发模式 - 详细日志和调试
    Development,
}

/// 实盘优化策略
#[derive(Debug, Clone)]
pub struct LiveTradingOptimization {
    /// 低延迟模式
    pub low_latency_mode: bool,
    /// 热缓存大小
    pub hot_cache_size: usize,
    /// 预热缓存
    pub preload_hot_data: bool,
    /// 连接池大小
    pub connection_pool_size: usize,
    /// 熔断器阈值
    pub circuit_breaker_threshold: f64,
    /// 超时时间 (毫秒)
    pub timeout_ms: u64,
}

impl Default for LiveTradingOptimization {
    fn default() -> Self {
        Self {
            low_latency_mode: true,
            hot_cache_size: 1000,
            preload_hot_data: true,
            connection_pool_size: 10,
            circuit_breaker_threshold: 0.1, // 10%错误率
            timeout_ms: 100, // 100ms
        }
    }
}

/// 实盘交易统计
#[derive(Debug, Clone)]
pub struct LiveTradingStats {
    /// 处理的订单数量
    pub orders_processed: u64,
    /// 风控检查次数
    pub risk_checks: u64,
    /// 风控拒绝次数
    pub risk_rejections: u64,
    /// 缓存命中次数
    pub cache_hits: u64,
    /// 平均延迟 (毫秒)
    pub avg_latency_ms: f64,
    /// P99延迟 (毫秒)
    pub p99_latency_ms: f64,
    /// 错误次数
    pub error_count: u64,
    /// 熔断器触发次数
    pub circuit_breaker_trips: u64,
    /// 连接重试次数
    pub connection_retries: u64,
}

impl LiveTradingStats {
    pub fn new() -> Self {
        Self {
            orders_processed: 0,
            risk_checks: 0,
            risk_rejections: 0,
            cache_hits: 0,
            avg_latency_ms: 0.0,
            p99_latency_ms: 0.0,
            error_count: 0,
            circuit_breaker_trips: 0,
            connection_retries: 0,
        }
    }
    
    /// 计算风控通过率
    pub fn risk_pass_rate(&self) -> f64 {
        if self.risk_checks > 0 {
            (self.risk_checks - self.risk_rejections) as f64 / self.risk_checks as f64
        } else {
            0.0
        }
    }
    
    /// 计算缓存命中率
    pub fn cache_hit_rate(&self) -> f64 {
        if self.risk_checks > 0 {
            self.cache_hits as f64 / self.risk_checks as f64
        } else {
            0.0
        }
    }
    
    /// 计算错误率
    pub fn error_rate(&self) -> f64 {
        if self.orders_processed > 0 {
            self.error_count as f64 / self.orders_processed as f64
        } else {
            0.0
        }
    }
    
    /// 检查是否健康
    pub fn is_healthy(&self) -> bool {
        self.error_rate() < 0.05 && // 错误率低于5%
        self.avg_latency_ms < 50.0 && // 平均延迟低于50ms
        self.p99_latency_ms < 200.0   // P99延迟低于200ms
    }
}

/// 实盘引擎工厂
pub struct LiveTradingEngineFactory;

impl LiveTradingEngineFactory {
    /// 创建实盘引擎
    pub async fn create_engine(config: LiveTradingConfig) -> SigmaXResult<LiveTradingEngine> {
        // 将 LiveTradingConfig 转换为 UnifiedEngineConfig
        use sigmax_core::{UnifiedEngineConfigBuilder, EngineType};

        let unified_config = UnifiedEngineConfigBuilder::new(EngineType::Live, "Live Trading Engine".to_string())
            .build()
            .expect("Failed to create unified config");

        // 注意：这里需要提供service_container，但在这个上下文中我们没有
        // 这个方法可能需要重新设计或者需要传入service_container参数
        Err(SigmaXError::internal(
            sigmax_core::InternalErrorCode::ConfigurationError,
            "LiveTradingEngine creation requires service_container parameter"
        ))
    }
    
    // 注意：风控适配器已迁移至 sigmax-risk 模块
    // 使用 sigmax_interfaces::RiskController 接口进行风控集成
    
    /// 创建优化的实盘环境
    pub async fn create_optimized_environment(
        mode: LiveTradingMode,
        optimization: LiveTradingOptimization,
    ) -> SigmaXResult<LiveTradingEngine> {
        // 根据模式调整配置
        let (engine_config, adapter_config) = match mode {
            LiveTradingMode::Production => {
                let engine_config = LiveTradingConfig {
                    enable_detailed_logging: false,
                    enable_performance_monitoring: true,
                    connection_timeout_ms: optimization.timeout_ms,
                    max_retries: 3,
                    ..Default::default()
                };
                
                let adapter_config = LiveAdapterConfig {
                    low_latency_mode: optimization.low_latency_mode,
                    hot_cache_size: optimization.hot_cache_size,
                    circuit_breaker_threshold: optimization.circuit_breaker_threshold,
                    timeout_ms: optimization.timeout_ms,
                    max_retries: 2,
                    ..Default::default()
                };
                
                (engine_config, adapter_config)
            }
            LiveTradingMode::Testing => {
                let engine_config = LiveTradingConfig {
                    enable_detailed_logging: true,
                    enable_performance_monitoring: true,
                    connection_timeout_ms: optimization.timeout_ms * 2,
                    max_retries: 5,
                    ..Default::default()
                };
                
                let adapter_config = LiveAdapterConfig {
                    low_latency_mode: false,
                    hot_cache_size: optimization.hot_cache_size / 2,
                    circuit_breaker_threshold: optimization.circuit_breaker_threshold * 2.0,
                    timeout_ms: optimization.timeout_ms * 2,
                    max_retries: 3,
                    ..Default::default()
                };
                
                (engine_config, adapter_config)
            }
            LiveTradingMode::Development => {
                let engine_config = LiveTradingConfig {
                    enable_detailed_logging: true,
                    enable_performance_monitoring: true,
                    enable_debug_mode: true,
                    connection_timeout_ms: optimization.timeout_ms * 5,
                    max_retries: 10,
                    ..Default::default()
                };
                
                let adapter_config = LiveAdapterConfig {
                    low_latency_mode: false,
                    hot_cache_size: optimization.hot_cache_size / 4,
                    circuit_breaker_threshold: 0.5, // 50%错误率
                    timeout_ms: optimization.timeout_ms * 5,
                    max_retries: 5,
                    enable_debug_logging: true,
                    ..Default::default()
                };
                
                (engine_config, adapter_config)
            }
        };
        
        // 创建引擎
        let engine = Self::create_engine(engine_config).await?;
        
        // 注意：风控功能现在通过 sigmax_interfaces::RiskController 接口注入
        // 在使用时需要通过依赖注入提供风控控制器
        
        Ok(engine)
    }
}

/// 实盘性能监控器
pub struct LiveTradingMonitor {
    stats: Arc<tokio::sync::RwLock<LiveTradingStats>>,
    latency_samples: Arc<tokio::sync::RwLock<Vec<f64>>>,
    start_time: std::time::Instant,
}

impl LiveTradingMonitor {
    pub fn new() -> Self {
        Self {
            stats: Arc::new(tokio::sync::RwLock::new(LiveTradingStats::new())),
            latency_samples: Arc::new(tokio::sync::RwLock::new(Vec::new())),
            start_time: std::time::Instant::now(),
        }
    }
    
    /// 记录订单处理
    pub async fn record_order_processing(&self, latency_ms: f64) {
        let mut stats = self.stats.write().await;
        stats.orders_processed += 1;
        
        // 更新平均延迟
        let count = stats.orders_processed as f64;
        stats.avg_latency_ms = (stats.avg_latency_ms * (count - 1.0) + latency_ms) / count;
        
        // 记录延迟样本用于计算P99
        let mut samples = self.latency_samples.write().await;
        samples.push(latency_ms);
        
        // 保持最近1000个样本
        if samples.len() > 1000 {
            samples.remove(0);
        }
        
        // 更新P99延迟
        if samples.len() >= 10 {
            let mut sorted_samples = samples.clone();
            sorted_samples.sort_by(|a, b| {
                a.partial_cmp(b).unwrap_or(std::cmp::Ordering::Equal)
            });
            let p99_index = (sorted_samples.len() as f64 * 0.99) as usize;
            let safe_index = p99_index.min(sorted_samples.len().saturating_sub(1));
            stats.p99_latency_ms = sorted_samples[safe_index];
        }
    }
    
    /// 记录风控检查
    pub async fn record_risk_check(&self, passed: bool, cache_hit: bool) {
        let mut stats = self.stats.write().await;
        stats.risk_checks += 1;
        
        if !passed {
            stats.risk_rejections += 1;
        }
        
        if cache_hit {
            stats.cache_hits += 1;
        }
    }
    
    /// 记录错误
    pub async fn record_error(&self) {
        let mut stats = self.stats.write().await;
        stats.error_count += 1;
    }
    
    /// 记录熔断器触发
    pub async fn record_circuit_breaker_trip(&self) {
        let mut stats = self.stats.write().await;
        stats.circuit_breaker_trips += 1;
    }
    
    /// 记录连接重试
    pub async fn record_connection_retry(&self) {
        let mut stats = self.stats.write().await;
        stats.connection_retries += 1;
    }
    
    /// 获取当前统计信息
    pub async fn get_stats(&self) -> LiveTradingStats {
        self.stats.read().await.clone()
    }
    
    /// 检查系统健康状态
    pub async fn health_check(&self) -> HealthStatus {
        let stats = self.stats.read().await;
        
        if stats.is_healthy() {
            HealthStatus::Healthy
        } else if stats.error_rate() > 0.1 || stats.avg_latency_ms > 100.0 {
            HealthStatus::Critical
        } else {
            HealthStatus::Warning
        }
    }
    
    /// 生成告警
    pub async fn check_alerts(&self) -> Vec<Alert> {
        let stats = self.stats.read().await;
        let mut alerts = Vec::new();
        
        if stats.error_rate() > 0.05 {
            alerts.push(Alert {
                level: AlertLevel::Warning,
                message: format!("错误率过高: {:.2}%", stats.error_rate() * 100.0),
                metric: "error_rate".to_string(),
                value: stats.error_rate(),
                threshold: 0.05,
            });
        }
        
        if stats.avg_latency_ms > 50.0 {
            alerts.push(Alert {
                level: AlertLevel::Warning,
                message: format!("平均延迟过高: {:.1}ms", stats.avg_latency_ms),
                metric: "avg_latency".to_string(),
                value: stats.avg_latency_ms,
                threshold: 50.0,
            });
        }
        
        if stats.p99_latency_ms > 200.0 {
            alerts.push(Alert {
                level: AlertLevel::Critical,
                message: format!("P99延迟过高: {:.1}ms", stats.p99_latency_ms),
                metric: "p99_latency".to_string(),
                value: stats.p99_latency_ms,
                threshold: 200.0,
            });
        }
        
        alerts
    }
}

/// 健康状态
#[derive(Debug, Clone, PartialEq)]
pub enum HealthStatus {
    Healthy,
    Warning,
    Critical,
}

/// 告警级别
#[derive(Debug, Clone, PartialEq)]
pub enum AlertLevel {
    Info,
    Warning,
    Critical,
}

/// 告警信息
#[derive(Debug, Clone)]
pub struct Alert {
    pub level: AlertLevel,
    pub message: String,
    pub metric: String,
    pub value: f64,
    pub threshold: f64,
}

impl Alert {
    /// 打印告警
    pub fn print(&self) {
        let level_str = match self.level {
            AlertLevel::Info => "ℹ️ INFO",
            AlertLevel::Warning => "⚠️ WARNING",
            AlertLevel::Critical => "🚨 CRITICAL",
        };
        
        println!("{}: {} (当前值: {:.2}, 阈值: {:.2})", 
                 level_str, self.message, self.value, self.threshold);
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_live_trading_stats() {
        let mut stats = LiveTradingStats::new();
        stats.orders_processed = 1000;
        stats.risk_checks = 1000;
        stats.risk_rejections = 50;
        stats.cache_hits = 800;
        stats.error_count = 10;
        stats.avg_latency_ms = 25.0;
        stats.p99_latency_ms = 150.0;
        
        assert_eq!(stats.risk_pass_rate(), 0.95);
        assert_eq!(stats.cache_hit_rate(), 0.8);
        assert_eq!(stats.error_rate(), 0.01);
        assert!(stats.is_healthy());
    }
    
    #[test]
    fn test_optimization_default() {
        let opt = LiveTradingOptimization::default();
        assert!(opt.low_latency_mode);
        assert_eq!(opt.hot_cache_size, 1000);
        assert_eq!(opt.timeout_ms, 100);
    }
    
    #[test]
    fn test_alert_creation() {
        let alert = Alert {
            level: AlertLevel::Warning,
            message: "测试告警".to_string(),
            metric: "test_metric".to_string(),
            value: 0.1,
            threshold: 0.05,
        };
        
        assert_eq!(alert.level, AlertLevel::Warning);
        assert_eq!(alert.metric, "test_metric");
    }
}
