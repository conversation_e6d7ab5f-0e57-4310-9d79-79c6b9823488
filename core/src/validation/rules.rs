//! SigmaX Core Validation Rules
//!
//! ## 主要职责
//!
//! 1. **业务验证规则** - 具体的业务逻辑验证规则
//!    - 订单验证规则
//!    - 风险管理验证规则
//!    - 交易验证规则
//!    - 投资组合验证规则
//!
//! 2. **静态验证方法** - 无状态的验证函数
//!    - 数据格式验证
//!    - 数值范围验证
//!    - 业务逻辑验证
//!
//! 3. **验证规则组合** - 复合验证规则
//!    - 规则链
//!    - 条件验证
//!    - 批量验证
//!
//! ## 设计原则
//!
//! - **无状态**: 验证规则不依赖外部状态
//! - **可重用**: 规则可以在不同场景中重用
//! - **组合性**: 支持规则的组合和链式调用
//! - **性能优化**: 快速失败和高效验证

use rust_decimal::Decimal;
use chrono::{DateTime, Utc};

use crate::{
    types::*,
    enums::*,
    error::*,
    constants,
    SigmaXResult,
};

use super::{OrderRequest, RiskLimits};

// ============================================================================
// 订单验证规则
// ============================================================================

/// 订单验证规则集
pub struct OrderValidationRules;

impl OrderValidationRules {
    /// 验证订单数量
    pub fn validate_quantity(quantity: Quantity) -> SigmaXResult<()> {
        if quantity <= Decimal::ZERO {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "quantity",
                "Order quantity must be positive"
            ));
        }
        
        // 验证数量精度（最多8位小数）
        if quantity.scale() > constants::models::numeric::DECIMAL_PRECISION {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "quantity",
                format!("Order quantity precision cannot exceed {} decimal places", constants::models::numeric::DECIMAL_PRECISION)
            ));
        }
        
        // 验证最小数量
        if quantity < Decimal::new(1, 8) { // 0.00000001
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "quantity",
                "Order quantity is too small (minimum: 0.00000001)"
            ));
        }
        
        Ok(())
    }
    
    /// 验证订单价格
    pub fn validate_price(price: Option<Price>, order_type: OrderType) -> SigmaXResult<()> {
        match order_type {
            OrderType::Market => {
                if price.is_some() {
                    return Err(SigmaXError::validation(
                        ValidationErrorCode::InvalidFormat,
                        "price",
                        "Market orders should not have a price"
                    ));
                }
            }
            OrderType::Limit | OrderType::StopLimit => {
                match price {
                    None => {
                        return Err(SigmaXError::validation(
                            ValidationErrorCode::Required,
                            "price",
                            "Limit orders must have a price"
                        ));
                    }
                    Some(p) => {
                        if p <= Decimal::ZERO {
                            return Err(SigmaXError::validation(
                                ValidationErrorCode::OutOfRange,
                                "price",
                                "Order price must be positive"
                            ));
                        }
                        
                        // 验证价格精度
                        if p.scale() > constants::models::numeric::DECIMAL_PRECISION {
                            return Err(SigmaXError::validation(
                                ValidationErrorCode::InvalidFormat,
                                "price",
                                format!("Order price precision cannot exceed {} decimal places", constants::models::numeric::DECIMAL_PRECISION)
                            ));
                        }
                    }
                }
            }
            OrderType::StopLoss => {
                // Stop orders may have a trigger price
                if let Some(p) = price {
                    if p <= Decimal::ZERO {
                        return Err(SigmaXError::validation(
                            ValidationErrorCode::OutOfRange,
                            "price",
                            "Stop price must be positive"
                        ));
                    }
                }
            }
        }
        
        Ok(())
    }
    
    /// 验证订单金额
    pub fn validate_order_amount(quantity: Quantity, price: Option<Price>) -> SigmaXResult<()> {
        if let Some(p) = price {
            let amount = quantity * p;
            
            // 验证最小订单金额
            if amount < Decimal::new(1, 2) { // $0.01
                return Err(SigmaXError::validation(
                    ValidationErrorCode::OutOfRange,
                    "order_amount",
                    "Order amount is too small (minimum: $0.01)"
                ));
            }
            
            // 验证最大订单金额
            if amount > Decimal::new(constants::trading::order::MAX_ORDER_AMOUNT as i64, 0) {
                return Err(SigmaXError::validation(
                    ValidationErrorCode::OutOfRange,
                    "order_amount",
                    format!("Order amount exceeds maximum allowed ({})", constants::trading::order::MAX_ORDER_AMOUNT)
                ));
            }
        }
        
        Ok(())
    }
    
    /// 验证订单时间有效性
    pub fn validate_order_timing(
        created_at: DateTime<Utc>,
        expires_at: Option<DateTime<Utc>>,
    ) -> SigmaXResult<()> {
        let now = Utc::now();
        
        // 验证创建时间不能是未来时间
        if created_at > now {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "created_at",
                "Order creation time cannot be in the future"
            ));
        }
        
        // 验证过期时间
        if let Some(expires) = expires_at {
            if expires <= created_at {
                return Err(SigmaXError::validation(
                    ValidationErrorCode::InvalidRange,
                    "expires_at",
                    "Order expiration time must be after creation time"
                ));
            }

            if expires <= now {
                return Err(SigmaXError::validation(
                    ValidationErrorCode::OutOfRange,
                    "expires_at",
                    "Order has already expired"
                ));
            }
            
            // 验证过期时间不能太远
            let max_duration = chrono::Duration::seconds(constants::trading::order::MAX_ORDER_TIMEOUT_SECONDS as i64);
            if expires - created_at > max_duration {
                return Err(SigmaXError::validation(
                    ValidationErrorCode::OutOfRange,
                    "expires_at",
                    format!("Order expiration time cannot exceed {} seconds from creation", constants::trading::order::MAX_ORDER_TIMEOUT_SECONDS)
                ));
            }
        }
        
        Ok(())
    }
    
    /// 验证完整订单请求
    pub fn validate_order_request(request: &OrderRequest) -> SigmaXResult<()> {
        // 验证数量
        Self::validate_quantity(request.quantity)?;
        
        // 验证价格
        Self::validate_price(request.price, request.order_type)?;
        
        // 验证订单金额
        Self::validate_order_amount(request.quantity, request.price)?;
        
        // 验证交易对
        TradingValidationRules::validate_trading_pair(&request.trading_pair)?;
        
        Ok(())
    }
}

// ============================================================================
// 风险管理验证规则
// ============================================================================

/// 风险管理验证规则集
pub struct RiskValidationRules;

impl RiskValidationRules {
    /// 验证回撤百分比
    pub fn validate_drawdown(drawdown: Percentage) -> SigmaXResult<()> {
        if drawdown < Decimal::ZERO {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "drawdown",
                "Drawdown cannot be negative"
            ));
        }
        
        if drawdown > Decimal::new(100, 0) {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "drawdown",
                "Drawdown cannot exceed 100%"
            ));
        }
        
        // 验证合理的回撤范围
        if drawdown > Decimal::new(constants::trading::risk::MAX_STOP_LOSS_PERCENT as i64, 0) {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "drawdown",
                format!("Drawdown exceeds recommended maximum of {}%", constants::trading::risk::MAX_STOP_LOSS_PERCENT)
            ));
        }
        
        Ok(())
    }
    
    /// 验证持仓规模
    pub fn validate_position_size(size: Amount, max_size: Amount) -> SigmaXResult<()> {
        if size < Decimal::ZERO {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "position_size",
                "Position size cannot be negative"
            ));
        }
        
        if size > max_size {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "position_size",
                format!("Position size ({}) exceeds maximum allowed ({})", size, max_size)
            ));
        }
        
        Ok(())
    }
    
    /// 验证风险比率
    pub fn validate_risk_ratio(risk_amount: Amount, total_capital: Amount) -> SigmaXResult<()> {
        if total_capital <= Decimal::ZERO {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "total_capital",
                "Total capital must be positive"
            ));
        }
        
        if risk_amount < Decimal::ZERO {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "risk_amount",
                "Risk amount cannot be negative"
            ));
        }
        
        let risk_ratio = (risk_amount / total_capital) * Decimal::new(100, 0);
        let max_risk_ratio = Decimal::new(constants::trading::risk::DEFAULT_MAX_POSITION_PERCENT as i64, 0);
        
        if risk_ratio > max_risk_ratio {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "risk_ratio",
                format!("Risk ratio ({:.2}%) exceeds maximum allowed ({:.2}%)", risk_ratio, max_risk_ratio)
            ));
        }
        
        Ok(())
    }
    
    /// 验证风险限制
    pub fn validate_risk_limits(limits: &RiskLimits) -> SigmaXResult<()> {
        // 验证最大持仓规模
        if limits.max_position_size <= Decimal::ZERO {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "max_position_size",
                "Maximum position size must be positive"
            ));
        }
        
        // 验证最大回撤
        Self::validate_drawdown(limits.max_drawdown)?;
        
        // 验证最大日亏损
        if limits.max_daily_loss <= Decimal::ZERO {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "max_daily_loss",
                "Maximum daily loss must be positive"
            ));
        }
        
        // 验证逻辑一致性：日亏损不应超过最大持仓
        if limits.max_daily_loss > limits.max_position_size {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidRange,
                "risk_limits",
                "Maximum daily loss should not exceed maximum position size"
            ));
        }
        
        Ok(())
    }
}

// ============================================================================
// 交易验证规则
// ============================================================================

/// 交易验证规则集
pub struct TradingValidationRules;

impl TradingValidationRules {
    /// 验证交易对
    pub fn validate_trading_pair(pair: &TradingPair) -> SigmaXResult<()> {
        // 验证基础货币
        if pair.base.is_empty() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::Required,
                "base_currency",
                "Base currency is required"
            ));
        }

        // 验证报价货币
        if pair.quote.is_empty() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::Required,
                "quote_currency",
                "Quote currency is required"
            ));
        }

        // 验证货币代码长度
        if pair.base.len() < 2 || pair.base.len() > 10 {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "base_currency",
                "Base currency code must be 2-10 characters long"
            ));
        }

        if pair.quote.len() < 2 || pair.quote.len() > 10 {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "quote_currency",
                "Quote currency code must be 2-10 characters long"
            ));
        }

        // 验证货币代码格式
        if !pair.base.chars().all(|c| c.is_ascii_alphabetic()) {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "base_currency",
                "Base currency code must contain only letters"
            ));
        }

        if !pair.quote.chars().all(|c| c.is_ascii_alphabetic()) {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "quote_currency",
                "Quote currency code must contain only letters"
            ));
        }

        // 验证基础货币和报价货币不能相同
        if pair.base.eq_ignore_ascii_case(&pair.quote) {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "trading_pair",
                "Base and quote currencies must be different"
            ));
        }
        
        Ok(())
    }
    
    /// 验证交易时间
    pub fn validate_trading_time(timestamp: DateTime<Utc>) -> SigmaXResult<()> {
        let now = Utc::now();
        
        // 验证时间不能是未来时间
        if timestamp > now {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "timestamp",
                "Trading time cannot be in the future"
            ));
        }
        
        // 验证时间不能太久远
        let max_age = chrono::Duration::days(365); // 1年
        if now - timestamp > max_age {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "timestamp",
                "Trading time is too old (maximum age: 1 year)"
            ));
        }
        
        Ok(())
    }
}
