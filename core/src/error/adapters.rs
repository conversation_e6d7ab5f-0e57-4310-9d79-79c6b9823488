//! 错误适配层 - 纯SigmaXError版本
//! 
//! 提供标准库和第三方库错误到SigmaXError的转换
//! 
//! ## 设计原则
//! - **统一错误类型**: 所有错误最终转换为SigmaXError
//! - **保留错误信息**: 转换过程中保留原始错误的详细信息
//! - **类型安全**: 编译时检查错误转换的正确性

use crate::{SigmaXError, SigmaXResult};
use crate::error::types::*;

/// 错误适配器特征 - 定义转换契约
pub trait ErrorAdapter<T> {
    fn adapt(error: T) -> SigmaXError;
}

// ============================================================================
// 标准库错误适配器
// ============================================================================

/// 从标准库IO错误转换
impl ErrorAdapter<std::io::Error> for SigmaXError {
    fn adapt(error: std::io::Error) -> SigmaXError {
        let (error_code, message) = match error.kind() {
            std::io::ErrorKind::NotFound => (
                NetworkErrorCode::HttpError,
                format!("文件或资源未找到: {}", error)
            ),
            std::io::ErrorKind::PermissionDenied => (
                NetworkErrorCode::HttpError,
                format!("权限被拒绝: {}", error)
            ),
            std::io::ErrorKind::ConnectionRefused
            | std::io::ErrorKind::ConnectionAborted
            | std::io::ErrorKind::ConnectionReset => (
                NetworkErrorCode::ConnectionRefused,
                format!("网络连接失败: {}", error)
            ),
            std::io::ErrorKind::TimedOut => (
                NetworkErrorCode::Timeout,
                format!("操作超时: {}", error)
            ),
            _ => (
                NetworkErrorCode::HttpError,
                format!("IO操作失败: {}", error)
            ),
        };
        
        SigmaXError::network(error_code, message)
            .with_context("source_error", "std::io::Error")
    }
}

// ============================================================================
// 数据库错误适配器
// ============================================================================

/// 从SQLx错误转换
impl ErrorAdapter<sqlx::Error> for SigmaXError {
    fn adapt(error: sqlx::Error) -> SigmaXError {
        let (error_code, message, table_info) = match &error {
            sqlx::Error::RowNotFound => (
                DatabaseErrorCode::QueryFailed,
                "数据库记录未找到".to_string(),
                None
            ),
            sqlx::Error::Database(db_err) => {
                let table = db_err.table().map(|t| t.to_string());
                (
                    DatabaseErrorCode::ConstraintViolation,
                    format!("数据库约束违反: {}", db_err.message()),
                    table
                )
            },
            sqlx::Error::PoolTimedOut => (
                DatabaseErrorCode::ConnectionFailed,
                "数据库连接池超时".to_string(),
                None
            ),
            sqlx::Error::Io(io_err) => (
                DatabaseErrorCode::ConnectionFailed,
                format!("数据库IO错误: {}", io_err),
                None
            ),
            sqlx::Error::Configuration(config_err) => (
                DatabaseErrorCode::ConnectionFailed,
                format!("数据库配置错误: {}", config_err),
                None
            ),
            _ => (
                DatabaseErrorCode::QueryFailed,
                format!("数据库操作失败: {}", error),
                None
            ),
        };
        
        let mut db_error = SigmaXError::database(error_code, message);
        if let Some(table) = table_info {
            db_error = db_error.with_context("table", &table);
        }
        db_error.with_context("source_error", "sqlx::Error")
    }
}

// ============================================================================
// 第三方库错误适配器
// ============================================================================

/// 从serde_json错误转换
impl ErrorAdapter<serde_json::Error> for SigmaXError {
    fn adapt(error: serde_json::Error) -> SigmaXError {
        let error_code = if error.is_data() {
            ConfigErrorCode::InvalidValue
        } else if error.is_syntax() {
            ConfigErrorCode::ParseError
        } else if error.is_eof() {
            ConfigErrorCode::ParseError
        } else {
            ConfigErrorCode::InvalidValue
        };
        
        SigmaXError::configuration(error_code, format!("JSON处理错误: {}", error))
            .with_context("source_error", "serde_json::Error")
    }
}

// 注意：reqwest适配器已移除，因为reqwest不是core模块的依赖
// 如果需要HTTP错误适配，请在web或其他模块中实现

/// 从tokio::time::error::Elapsed错误转换
impl ErrorAdapter<tokio::time::error::Elapsed> for SigmaXError {
    fn adapt(error: tokio::time::error::Elapsed) -> SigmaXError {
        SigmaXError::network(NetworkErrorCode::Timeout, format!("异步操作超时: {}", error))
            .with_context("source_error", "tokio::time::error::Elapsed")
    }
}

/// 从uuid::Error错误转换
impl ErrorAdapter<uuid::Error> for SigmaXError {
    fn adapt(error: uuid::Error) -> SigmaXError {
        SigmaXError::validation(
            ValidationErrorCode::InvalidFormat,
            "uuid",
            format!("UUID格式错误: {}", error)
        ).with_context("source_error", "uuid::Error")
    }
}

/// 从chrono::ParseError错误转换
impl ErrorAdapter<chrono::ParseError> for SigmaXError {
    fn adapt(error: chrono::ParseError) -> SigmaXError {
        SigmaXError::validation(
            ValidationErrorCode::InvalidFormat,
            "datetime",
            format!("日期时间格式错误: {}", error)
        ).with_context("source_error", "chrono::ParseError")
    }
}

/// 从字符串错误转换（用于兼容性）
impl ErrorAdapter<String> for SigmaXError {
    fn adapt(error: String) -> SigmaXError {
        // 尝试从错误消息推断错误类型
        if error.to_lowercase().contains("exchange") {
            SigmaXError::exchange_api(
                ExchangeErrorCode::OrderRejected,
                error,
                "unknown"
            )
        } else if error.to_lowercase().contains("order") {
            SigmaXError::trading(TradingErrorCode::OrderExecutionFailed, error)
        } else if error.to_lowercase().contains("strategy") {
            SigmaXError::strategy(
                StrategyErrorCode::ExecutionFailed,
                error,
                uuid::Uuid::new_v4(),
                "unknown"
            )
        } else if error.to_lowercase().contains("risk") {
            SigmaXError::risk(RiskErrorCode::CheckFailed, error, "unknown")
        } else if error.to_lowercase().contains("data") {
            SigmaXError::market_data(MarketDataErrorCode::ParseError, error)
        } else {
            SigmaXError::internal(InternalErrorCode::UnexpectedState, error)
        }
    }
}

// ============================================================================
// 便利函数和扩展
// ============================================================================

/// 通用转换函数
pub fn adapt_error<T>(error: T) -> SigmaXError 
where 
    SigmaXError: ErrorAdapter<T>
{
    SigmaXError::adapt(error)
}

/// Result 转换扩展
pub trait AdapterResultExt<T, E> {
    /// 适配错误类型到SigmaXError
    fn adapt_error(self) -> SigmaXResult<T>
    where
        SigmaXError: ErrorAdapter<E>;
        
    /// 适配错误类型并添加上下文
    fn adapt_error_with_context(self, context_key: &str, context_value: &str) -> SigmaXResult<T>
    where
        SigmaXError: ErrorAdapter<E>;
}

impl<T, E> AdapterResultExt<T, E> for Result<T, E> {
    fn adapt_error(self) -> SigmaXResult<T>
    where
        SigmaXError: ErrorAdapter<E>,
    {
        self.map_err(SigmaXError::adapt)
    }
    
    fn adapt_error_with_context(self, context_key: &str, context_value: &str) -> SigmaXResult<T>
    where
        SigmaXError: ErrorAdapter<E>,
    {
        self.map_err(|e| SigmaXError::adapt(e).with_context(context_key, context_value))
    }
}

/// 批量适配错误的函数
pub fn adapt_results<T, E>(results: Vec<Result<T, E>>) -> Vec<SigmaXResult<T>>
where
    SigmaXError: ErrorAdapter<E>,
{
    results.into_iter()
        .map(|r| r.adapt_error())
        .collect()
}

/// 适配Option为SigmaXResult的便利函数
pub fn adapt_option<T>(option: Option<T>, field_name: &str, message: &str) -> SigmaXResult<T> {
    option.ok_or_else(|| {
        SigmaXError::validation(ValidationErrorCode::Required, field_name, message)
    })
}

/// 从任意错误创建SigmaXError的便利函数
pub fn from_any_error<E: std::error::Error>(error: E, context: &str) -> SigmaXError {
    SigmaXError::internal(
        InternalErrorCode::UnexpectedState,
        format!("{}: {}", context, error)
    ).with_context("source_error", std::any::type_name::<E>())
}

/// 快速适配错误的宏
#[macro_export]
macro_rules! adapt_err {
    ($result:expr) => {
        $result.adapt_error()
    };
    ($result:expr, $context_key:expr, $context_value:expr) => {
        $result.adapt_error_with_context($context_key, $context_value)
    };
}

/// 重新导出适配器扩展trait，方便使用
pub use AdapterResultExt as SigmaXAdapterExt;

// ============================================================================
// 测试模块
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;
    use std::io::ErrorKind;

    #[test]
    fn test_io_error_adaptation() {
        let io_error = std::io::Error::new(ErrorKind::NotFound, "文件未找到");
        let sigmax_error = SigmaXError::adapt(io_error);

        assert!(matches!(sigmax_error, SigmaXError::Network { .. }));
        assert!(sigmax_error.to_string().contains("文件或资源未找到"));
    }

    #[test]
    fn test_json_error_adaptation() {
        let json_str = r#"{"invalid": json"#;
        let json_error = serde_json::from_str::<serde_json::Value>(json_str).unwrap_err();
        let sigmax_error = SigmaXError::adapt(json_error);

        assert!(matches!(sigmax_error, SigmaXError::Configuration { .. }));
        assert!(sigmax_error.to_string().contains("JSON处理错误"));
    }

    #[test]
    fn test_result_adapter_extension() {
        let result: Result<i32, std::io::Error> = Err(
            std::io::Error::new(ErrorKind::PermissionDenied, "权限不足")
        );

        let adapted = result.adapt_error();
        assert!(adapted.is_err());

        let error = adapted.unwrap_err();
        assert!(matches!(error, SigmaXError::Network { .. }));
    }

    #[test]
    fn test_result_adapter_with_context() {
        let result: Result<String, std::io::Error> = Err(
            std::io::Error::new(ErrorKind::TimedOut, "操作超时")
        );

        let adapted = result.adapt_error_with_context("operation", "file_read");
        assert!(adapted.is_err());

        let error = adapted.unwrap_err();
        assert_eq!(error.context().metadata.get("operation"), Some(&"file_read".to_string()));
    }

    #[test]
    fn test_adapt_option() {
        let none_value: Option<String> = None;
        let result = adapt_option(none_value, "username", "用户名不能为空");

        assert!(result.is_err());
        let error = result.unwrap_err();
        assert!(matches!(error, SigmaXError::Validation { .. }));
    }

    #[test]
    fn test_adapt_results() {
        let results = vec![
            Ok(1),
            Err(std::io::Error::new(ErrorKind::NotFound, "文件1未找到")),
            Ok(2),
            Err(std::io::Error::new(ErrorKind::PermissionDenied, "权限不足")),
        ];

        let adapted = adapt_results(results);
        assert_eq!(adapted.len(), 4);
        assert!(adapted[0].is_ok());
        assert!(adapted[1].is_err());
        assert!(adapted[2].is_ok());
        assert!(adapted[3].is_err());
    }

    #[test]
    fn test_string_error_adaptation() {
        let error_msg = "exchange connection failed";
        let sigmax_error = SigmaXError::adapt(error_msg.to_string());

        assert!(matches!(sigmax_error, SigmaXError::ExchangeApi { .. }));
    }

    #[test]
    fn test_macro_usage() {
        let result: Result<i32, std::io::Error> = Err(
            std::io::Error::new(ErrorKind::NotFound, "测试错误")
        );

        // 测试基本宏
        let adapted1 = adapt_err!(result.clone());
        assert!(adapted1.is_err());

        // 测试带上下文的宏
        let adapted2 = adapt_err!(result, "test_context", "macro_test");
        assert!(adapted2.is_err());
        let error = adapted2.unwrap_err();
        assert_eq!(error.context().metadata.get("test_context"), Some(&"macro_test".to_string()));
    }
}
