//! 统一交易接口定义
//!
//! 🎯 接口统一化标准：
//! - 合并原 core::Exchange 和 interfaces 的交易接口
//! - 统一返回类型为 sigmax_core::SigmaXResult<T>
//! - 清晰的职责分离

use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use sigmax_core::{Order, Balance, SigmaXResult, OrderId, ExchangeId, TradingPair, OrderBook, Candle, Trade, TimeFrame};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use std::collections::HashMap;

// ============================================================================
// 统一交易接口 - 合并原 core 和 interfaces 功能
// ============================================================================

/// 统一交易所连接器接口
///
/// 合并了原 core::Exchange 的所有功能：
/// - 账户管理 (余额查询)
/// - 订单管理 (下单、撤单、查询)
/// - 市场数据 (深度、K线、交易历史)
#[async_trait]
pub trait ExchangeConnector: Send + Sync {
    /// 获取交易所ID
    fn id(&self) -> ExchangeId;

    /// 获取账户余额
    async fn get_balances(&self) -> SigmaXResult<Vec<Balance>>;

    /// 下单
    async fn place_order(&self, order: &Order) -> SigmaXResult<OrderId>;

    /// 取消订单
    async fn cancel_order(&self, order_id: OrderId) -> SigmaXResult<()>;

    /// 获取订单状态
    async fn get_order(&self, order_id: OrderId) -> SigmaXResult<Order>;

    /// 获取市场深度
    async fn get_order_book(&self, trading_pair: &TradingPair) -> SigmaXResult<OrderBook>;

    /// 获取K线数据
    async fn get_candles(
        &self,
        trading_pair: &TradingPair,
        timeframe: TimeFrame,
        limit: Option<u32>,
    ) -> SigmaXResult<Vec<Candle>>;

    /// 获取交易历史
    async fn get_trades(&self, trading_pair: &TradingPair) -> SigmaXResult<Vec<Trade>>;

    // ========== 批量操作接口 ==========
    
    /// 批量下单
    async fn batch_place_orders(&self, orders: &[Order]) -> SigmaXResult<Vec<SigmaXResult<OrderId>>>;

    /// 批量取消订单
    async fn batch_cancel_orders(&self, order_ids: &[OrderId]) -> SigmaXResult<Vec<SigmaXResult<()>>>;

    /// 批量获取订单状态
    async fn batch_get_orders(&self, order_ids: &[OrderId]) -> SigmaXResult<Vec<SigmaXResult<Order>>>;
}

/// 交易编排器接口 - 协调各个服务 (统一返回类型)
#[async_trait]
pub trait TradingOrchestrator: Send + Sync {
    /// 提交交易请求
    async fn submit_trade(&self, request: &TradeRequest) -> SigmaXResult<()>;

    /// 获取投资组合
    async fn get_portfolio(&self) -> SigmaXResult<Portfolio>;

    /// 获取交易状态
    async fn get_trade_status(&self, trade_id: &Uuid) -> SigmaXResult<Option<TradeStatus>>;

    /// 取消交易
    async fn cancel_trade(&self, trade_id: &Uuid) -> SigmaXResult<()>;

    // ========== 批量操作接口 ==========
    
    /// 批量提交交易请求
    async fn batch_submit_trades(&self, requests: &[TradeRequest]) -> SigmaXResult<Vec<SigmaXResult<()>>>;

    /// 批量取消交易
    async fn batch_cancel_trades(&self, trade_ids: &[Uuid]) -> SigmaXResult<Vec<SigmaXResult<()>>>;
}

/// 订单管理器接口
#[async_trait]
pub trait OrderManager: Send + Sync {
    /// 创建订单
    async fn create_order(&self, order: &Order) -> OrderResult;
    
    /// 更新订单
    async fn update_order(&self, order_id: &Uuid, updates: &OrderUpdate) -> OrderResult;
    
    /// 取消订单
    async fn cancel_order(&self, order_id: &Uuid) -> OrderResult;
    
    /// 获取订单状态
    async fn get_order_status(&self, order_id: &Uuid) -> Option<OrderStatus>;
    
    /// 获取活跃订单
    async fn get_active_orders(&self) -> Vec<Order>;

    // ========== 批量操作接口 ==========
    
    /// 批量创建订单
    async fn batch_create_orders(&self, orders: &[Order]) -> Vec<OrderResult>;

    /// 批量取消订单
    async fn batch_cancel_orders(&self, order_ids: &[Uuid]) -> Vec<OrderResult>;

    /// 批量获取订单状态
    async fn batch_get_order_status(&self, order_ids: &[Uuid]) -> Vec<Option<OrderStatus>>;
}

/// 统一投资组合管理器接口 - 合并所有现有投资组合接口的方法
#[async_trait]
pub trait PortfolioManager: Send + Sync {
    // ========== 来自 interfaces/src/trading.rs 的方法 ==========
    /// 获取投资组合
    async fn get_portfolio(&self) -> Portfolio;

    /// 更新投资组合
    async fn update_portfolio(&self, updates: &PortfolioUpdate) -> Result<(), PortfolioError>;

    /// 获取余额
    async fn get_balances(&self) -> Vec<Balance>;

    /// 计算投资组合价值
    async fn calculate_portfolio_value(&self) -> Decimal;

    /// 获取投资组合历史
    async fn get_portfolio_history(&self, start: DateTime<Utc>, end: DateTime<Utc>) -> Vec<PortfolioSnapshot>;

    // ========== 来自 core/src/traits.rs PortfolioManager 的方法 ==========
    /// 获取当前余额 (来自 core PortfolioManager)
    async fn get_balances_map(&self) -> sigmax_core::SigmaXResult<HashMap<String, Balance>>;

    /// 更新余额 (来自 core PortfolioManager)
    async fn update_balance(&mut self, balance: Balance) -> sigmax_core::SigmaXResult<()>;

    /// 计算总资产价值 (来自 core PortfolioManager)
    async fn calculate_total_value(&self) -> sigmax_core::SigmaXResult<sigmax_core::Amount>;

    /// 获取PnL (来自 core PortfolioManager)
    async fn get_pnl(&self) -> sigmax_core::SigmaXResult<sigmax_core::Amount>;

    /// 支持类型转换的方法 (来自 core PortfolioManager)
    fn as_any(&self) -> &dyn std::any::Any;

    // ========== 来自 core/src/traits.rs EnhancedPortfolioManager 的方法 ==========
    /// 根据交易更新投资组合（并发安全）(来自 EnhancedPortfolioManager)
    async fn update_from_trade(&self, trade: &sigmax_core::Trade) -> sigmax_core::SigmaXResult<()>;

    /// 更新当前价格（用于价值计算）(来自 EnhancedPortfolioManager)
    async fn update_current_price(&self, asset: &str, price: sigmax_core::Amount) -> sigmax_core::SigmaXResult<()>;

    /// 批量更新余额（并发安全）(来自 EnhancedPortfolioManager)
    async fn batch_update_balances(&self, balances: HashMap<String, Balance>) -> sigmax_core::SigmaXResult<()>;

    /// 获取特定资产余额 (来自 EnhancedPortfolioManager)
    async fn get_asset_balance(&self, asset: &str) -> sigmax_core::SigmaXResult<Option<Balance>>;

    /// 检查资金充足性 (来自 EnhancedPortfolioManager)
    async fn check_sufficient_balance(&self, asset: &str, required_amount: sigmax_core::Amount) -> sigmax_core::SigmaXResult<bool>;
}

// ============================================================================
// 数据模型
// ============================================================================

/// 交易请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeRequest {
    pub id: Uuid,
    pub order: Order,
    pub strategy_id: Option<Uuid>,
    pub metadata: HashMap<String, serde_json::Value>,
    pub created_at: DateTime<Utc>,
}

/// 交易结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeResult {
    pub trade_id: Uuid,
    pub status: TradeStatus,
    pub message: String,
    pub order_id: Option<Uuid>,
    pub executed_at: Option<DateTime<Utc>>,
}

/// 交易状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TradeStatus {
    /// 待处理
    Pending,
    /// 风控检查中
    RiskChecking,
    /// 执行中
    Executing,
    /// 已完成
    Completed,
    /// 已取消
    Cancelled,
    /// 失败
    Failed(String),
    /// 部分成交
    PartiallyFilled,
}

/// 统一的投资组合定义 - 系统标准接口
/// 
/// 这是系统中标准的 Portfolio 定义，用于所有模块间的数据交换
/// 提供完整的投资组合信息，支持风控、交易、报告等所有场景
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Portfolio {
    /// 投资组合ID
    pub id: Option<uuid::Uuid>,
    /// 投资组合名称
    pub name: Option<String>,
    /// 账户余额列表
    pub balances: Vec<sigmax_core::Balance>,
    /// 扩展的余额信息（包含价格等风控所需字段）
    pub portfolio_balances: Option<Vec<sigmax_core::PortfolioBalance>>,
    /// 总价值
    pub total_value: rust_decimal::Decimal,
    /// 现金余额
    pub cash_balance: Option<rust_decimal::Decimal>,
    /// 持仓信息
    pub positions: Option<std::collections::HashMap<String, serde_json::Value>>,
    /// 货币单位
    pub currency: Option<String>,
    /// 创建时间
    pub created_at: Option<DateTime<Utc>>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

impl Portfolio {
    /// 创建新的投资组合
    pub fn new(name: Option<String>) -> Self {
        Self {
            id: Some(uuid::Uuid::new_v4()),
            name,
            balances: Vec::new(),
            portfolio_balances: None,
            total_value: rust_decimal::Decimal::ZERO,
            cash_balance: Some(rust_decimal::Decimal::ZERO),
            positions: Some(std::collections::HashMap::new()),
            currency: Some("USD".to_string()),
            created_at: Some(Utc::now()),
            updated_at: Utc::now(),
        }
    }

    /// 从余额列表创建投资组合
    pub fn from_balances(balances: Vec<sigmax_core::Balance>, name: Option<String>) -> Self {
        let mut portfolio = Self::new(name);
        portfolio.balances = balances;
        portfolio
    }

    /// 获取特定资产的余额
    pub fn get_balance(&self, asset: &str) -> Option<&sigmax_core::Balance> {
        self.balances.iter().find(|balance| balance.asset == asset)
    }

    /// 获取可用余额总和（以指定资产计价）
    pub fn get_available_balance(&self, asset: &str) -> rust_decimal::Decimal {
        self.balances
            .iter()
            .filter(|balance| balance.asset == asset)
            .map(|balance| balance.available())
            .sum()
    }

    /// 检查资金是否充足
    pub fn has_sufficient_balance(&self, asset: &str, required_amount: rust_decimal::Decimal) -> bool {
        self.get_available_balance(asset) >= required_amount
    }

    /// 更新余额
    pub fn update_balance(&mut self, balance: sigmax_core::Balance) {
        if let Some(existing) = self.balances.iter_mut().find(|b| b.asset == balance.asset && b.exchange_id == balance.exchange_id) {
            *existing = balance;
        } else {
            self.balances.push(balance);
        }
        self.updated_at = Utc::now();
    }

    /// 转换为风控所需的扩展余额格式
    pub fn to_portfolio_balances(&self) -> Vec<sigmax_core::PortfolioBalance> {
        self.balances
            .iter()
            .map(|balance| sigmax_core::PortfolioBalance::from_balance(balance.clone(), balance.exchange_id.clone()))
            .collect()
    }
}

/// 投资组合快照
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortfolioSnapshot {
    pub timestamp: DateTime<Utc>,
    pub portfolio: Portfolio,
    pub pnl: Decimal,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// 订单更新
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderUpdate {
    pub status: Option<OrderStatus>,
    pub filled_quantity: Option<Decimal>,
    pub average_price: Option<Decimal>,
    pub updated_at: DateTime<Utc>,
}

/// 订单状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OrderStatus {
    Pending,
    Submitted,
    PartiallyFilled,
    Filled,
    Cancelled,
    Rejected,
    Expired,
}

/// 投资组合更新
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortfolioUpdate {
    pub balances: Option<Vec<Balance>>,
    pub updated_at: DateTime<Utc>,
}

/// 订单结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderResult {
    pub order_id: Uuid,
    pub status: OrderStatus,
    pub message: String,
    pub created_at: DateTime<Utc>,
}

// ============================================================================
// 错误类型
// ============================================================================

/// 交易错误
#[derive(Debug, thiserror::Error)]
pub enum TradeError {
    #[error("Risk rejection: {reason}")]
    RiskRejection { reason: String },
    
    #[error("Execution failed: {reason}")]
    ExecutionFailed { reason: String },
    
    #[error("Insufficient balance: need {required}, have {available}")]
    InsufficientBalance { required: Decimal, available: Decimal },
    
    #[error("Invalid trade request: {reason}")]
    InvalidRequest { reason: String },
    
    #[error("Trade not found: {trade_id}")]
    NotFound { trade_id: Uuid },
    
    #[error("Service unavailable: {service}")]
    ServiceUnavailable { service: String },
    
    #[error("Timeout: operation timed out after {timeout_ms}ms")]
    Timeout { timeout_ms: u64 },
}

/// 订单错误
#[derive(Debug, thiserror::Error)]
pub enum OrderError {
    #[error("Order creation failed: {reason}")]
    CreationFailed { reason: String },
    
    #[error("Order not found: {order_id}")]
    NotFound { order_id: Uuid },
    
    #[error("Order update failed: {reason}")]
    UpdateFailed { reason: String },
    
    #[error("Invalid order status transition: {from} -> {to}")]
    InvalidStatusTransition { from: String, to: String },
    
    #[error("Order already cancelled: {order_id}")]
    AlreadyCancelled { order_id: Uuid },
}

/// 投资组合错误
#[derive(Debug, thiserror::Error)]
pub enum PortfolioError {
    #[error("Portfolio calculation failed: {reason}")]
    CalculationFailed { reason: String },
    
    #[error("Portfolio update failed: {reason}")]
    UpdateFailed { reason: String },
    
    #[error("Portfolio data inconsistent: {reason}")]
    DataInconsistent { reason: String },
    
    #[error("Portfolio not found: {portfolio_id}")]
    NotFound { portfolio_id: Uuid },
}