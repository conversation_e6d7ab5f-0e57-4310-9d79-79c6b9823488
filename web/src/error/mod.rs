//! 错误处理模块
//!
//! 提供统一的错误类型定义和错误处理机制

pub mod error_types;
pub mod unified_error_handler;

// 重新导出错误类型
pub use error_types::*;
pub use unified_error_handler::*;

/// API结果类型
pub type ApiResult<T> = Result<T, ApiError>;

/// API错误类型
#[derive(Debug, thiserror::Error)]
pub enum ApiError {
    /// 验证错误
    #[error("验证错误: {0}")]
    ValidationError(String),
    
    /// 认证错误
    #[error("认证错误: {0}")]
    AuthenticationError(String),
    
    /// 授权错误
    #[error("授权错误: {0}")]
    AuthorizationError(String),
    
    /// 业务错误
    #[error("业务错误: {0}")]
    BusinessError(String),
    
    /// 系统错误
    #[error("系统错误: {0}")]
    SystemError(String),
    
    /// 资源未找到
    #[error("资源未找到: {0}")]
    NotFound(String),
    
    /// 请求过于频繁
    #[error("请求过于频繁: {0}")]
    TooManyRequests(String),
    
    /// 内部服务器错误
    #[error("内部服务器错误: {0}")]
    InternalServerError(String),
}

impl ApiError {
    /// 创建验证错误
    pub fn validation(message: impl Into<String>) -> Self {
        Self::ValidationError(message.into())
    }
    
    /// 创建认证错误
    pub fn authentication(message: impl Into<String>) -> Self {
        Self::AuthenticationError(message.into())
    }
    
    /// 创建授权错误
    pub fn authorization(message: impl Into<String>) -> Self {
        Self::AuthorizationError(message.into())
    }
    
    /// 创建业务错误
    pub fn business(message: impl Into<String>) -> Self {
        Self::BusinessError(message.into())
    }
    
    /// 创建系统错误
    pub fn system(message: impl Into<String>) -> Self {
        Self::SystemError(message.into())
    }
    
    /// 创建未找到错误
    pub fn not_found(message: impl Into<String>) -> Self {
        Self::NotFound(message.into())
    }
    
    /// 创建请求过于频繁错误
    pub fn too_many_requests(message: impl Into<String>) -> Self {
        Self::TooManyRequests(message.into())
    }
    
    /// 创建内部服务器错误
    pub fn internal(message: impl Into<String>) -> Self {
        Self::InternalServerError(message.into())
    }
}

impl From<ApiError> for axum::http::StatusCode {
    fn from(error: ApiError) -> Self {
        match error {
            ApiError::ValidationError(_) => axum::http::StatusCode::UNPROCESSABLE_ENTITY,
            ApiError::AuthenticationError(_) => axum::http::StatusCode::UNAUTHORIZED,
            ApiError::AuthorizationError(_) => axum::http::StatusCode::FORBIDDEN,
            ApiError::BusinessError(_) => axum::http::StatusCode::BAD_REQUEST,
            ApiError::SystemError(_) => axum::http::StatusCode::INTERNAL_SERVER_ERROR,
            ApiError::NotFound(_) => axum::http::StatusCode::NOT_FOUND,
            ApiError::TooManyRequests(_) => axum::http::StatusCode::TOO_MANY_REQUESTS,
            ApiError::InternalServerError(_) => axum::http::StatusCode::INTERNAL_SERVER_ERROR,
        }
    }
}
