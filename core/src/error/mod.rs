//! 错误处理模块
//!
//! 提供统一的错误类型和处理机制

pub mod types;
mod convert;
pub mod macros;
pub mod adapters;

// SigmaX主错误类型定义
use thiserror::Error;
use serde::{Serialize, Deserialize};
use uuid::Uuid;

// 重新导出类型和转换
pub use types::*;
// pub use convert::*; // 不自动导出convert，由From trait自动生效

/// 错误分类 - 用于重试决策和错误处理策略
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ErrorCategory {
    /// 临时性错误 - 通常可以重试
    Transient,
    /// 永久性错误 - 不应该重试
    Permanent,
    /// 限流错误 - 需要延迟后重试
    RateLimit,
    /// 未知错误 - 谨慎重试
    Unknown,
}

/// SigmaX 主错误类型
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum SigmaXError {
    /// 交易相关错误
    #[error("Trading error: {message} (code: {code:?})")]
    Trading {
        code: TradingErrorCode,
        message: String,
        order_id: Option<Uuid>,
        symbol: Option<String>,
        context: Option<ErrorContext>,
    },

    /// 策略执行错误
    #[error("Strategy error: {message} (strategy: {strategy_id})")]
    Strategy {
        code: StrategyErrorCode,
        message: String,
        strategy_id: Uuid,
        strategy_type: String,
        context: Option<ErrorContext>,
    },

    /// 风险管理错误
    #[error("Risk management error: {message}")]
    Risk {
        code: RiskErrorCode,
        message: String,
        risk_type: String,
        current_value: Option<f64>,
        limit_value: Option<f64>,
        context: Option<ErrorContext>,
    },

    /// 数据库错误
    #[error("Database error: {message}")]
    Database {
        code: DatabaseErrorCode,
        message: String,
        query: Option<String>,
        table: Option<String>,
        context: Option<ErrorContext>,
    },

    /// 网络/API错误
    #[error("Network error: {message}")]
    Network {
        code: NetworkErrorCode,
        message: String,
        endpoint: Option<String>,
        status_code: Option<u16>,
        context: Option<ErrorContext>,
    },

    /// 配置错误
    #[error("Configuration error: {message}")]
    Configuration {
        code: ConfigErrorCode,
        message: String,
        config_key: Option<String>,
        config_value: Option<String>,
        context: Option<ErrorContext>,
    },

    /// 输入验证错误
    #[error("Validation error: {message}")]
    Validation {
        code: ValidationErrorCode,
        message: String,
        field: Option<String>,
        value: Option<String>,
        expected: Option<String>,
        context: Option<ErrorContext>,
    },

    /// 交易所API错误
    #[error("Exchange API error: {message} (exchange: {exchange})")]
    ExchangeApi {
        code: ExchangeErrorCode,
        message: String,
        exchange: String,
        api_error_code: Option<String>,
        context: Option<ErrorContext>,
    },

    /// 市场数据错误
    #[error("Market data error: {message}")]
    MarketData {
        code: MarketDataErrorCode,
        message: String,
        symbol: Option<String>,
        error_source: Option<String>,
        context: Option<ErrorContext>,
    },

    /// 内部系统错误
    #[error("Internal system error: {message}")]
    Internal {
        code: InternalErrorCode,
        message: String,
        component: Option<String>,
        context: Option<ErrorContext>,
    },
}

impl SigmaXError {
    // =================== 便捷构造器 ===================

    /// 创建交易错误
    pub fn trading(code: TradingErrorCode, message: impl Into<String>) -> Self {
        Self::Trading {
            code,
            message: message.into(),
            order_id: None,
            symbol: None,
            context: None,
        }
    }

    /// 创建交易错误（带订单ID）
    pub fn trading_with_order(
        code: TradingErrorCode,
        message: impl Into<String>,
        order_id: Uuid,
    ) -> Self {
        Self::Trading {
            code,
            message: message.into(),
            order_id: Some(order_id),
            symbol: None,
            context: None,
        }
    }

    /// 创建策略错误
    pub fn strategy(
        code: StrategyErrorCode,
        message: impl Into<String>,
        strategy_id: Uuid,
        strategy_type: impl Into<String>,
    ) -> Self {
        Self::Strategy {
            code,
            message: message.into(),
            strategy_id,
            strategy_type: strategy_type.into(),
            context: None,
        }
    }

    /// 创建风险错误
    pub fn risk(
        code: RiskErrorCode,
        message: impl Into<String>,
        risk_type: impl Into<String>,
    ) -> Self {
        Self::Risk {
            code,
            message: message.into(),
            risk_type: risk_type.into(),
            current_value: None,
            limit_value: None,
            context: None,
        }
    }

    /// 创建数据库错误
    pub fn database(code: DatabaseErrorCode, message: impl Into<String>) -> Self {
        Self::Database {
            code,
            message: message.into(),
            query: None,
            table: None,
            context: None,
        }
    }

    /// 创建网络错误
    pub fn network(code: NetworkErrorCode, message: impl Into<String>) -> Self {
        Self::Network {
            code,
            message: message.into(),
            endpoint: None,
            status_code: None,
            context: None,
        }
    }

    /// 创建配置错误
    pub fn configuration(code: ConfigErrorCode, message: impl Into<String>) -> Self {
        Self::Configuration {
            code,
            message: message.into(),
            config_key: None,
            config_value: None,
            context: None,
        }
    }

    /// 创建验证错误
    pub fn validation(
        code: ValidationErrorCode,
        field: impl Into<String>,
        message: impl Into<String>,
    ) -> Self {
        Self::Validation {
            code,
            message: message.into(),
            field: Some(field.into()),
            value: None,
            expected: None,
            context: None,
        }
    }

    /// 创建交易所API错误
    pub fn exchange_api(
        code: ExchangeErrorCode,
        message: impl Into<String>,
        exchange: impl Into<String>,
    ) -> Self {
        Self::ExchangeApi {
            code,
            message: message.into(),
            exchange: exchange.into(),
            api_error_code: None,
            context: None,
        }
    }

    /// 创建市场数据错误
    pub fn market_data(code: MarketDataErrorCode, message: impl Into<String>) -> Self {
        Self::MarketData {
            code,
            message: message.into(),
            symbol: None,
            error_source: None,
            context: None,
        }
    }

    /// 创建内部错误
    pub fn internal(code: InternalErrorCode, message: impl Into<String>) -> Self {
        Self::Internal {
            code,
            message: message.into(),
            component: None,
            context: None,
        }
    }

    // =================== 链式方法 ===================

    /// 添加上下文信息
    pub fn with_context(mut self, context: ErrorContext) -> Self {
        match &mut self {
            SigmaXError::Trading { context: ctx, .. } |
            SigmaXError::Strategy { context: ctx, .. } |
            SigmaXError::Risk { context: ctx, .. } |
            SigmaXError::Database { context: ctx, .. } |
            SigmaXError::Network { context: ctx, .. } |
            SigmaXError::Configuration { context: ctx, .. } |
            SigmaXError::Validation { context: ctx, .. } |
            SigmaXError::ExchangeApi { context: ctx, .. } |
            SigmaXError::MarketData { context: ctx, .. } |
            SigmaXError::Internal { context: ctx, .. } => *ctx = Some(context),
        }
        self
    }

    /// 添加上下文元数据
    pub fn with_metadata(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        let mut context = self.context().cloned().unwrap_or_else(ErrorContext::new);
        context.metadata.insert(key.into(), value.into());
        self.with_context(context)
    }

    /// 设置追踪ID
    pub fn with_trace_id(mut self, trace_id: impl Into<String>) -> Self {
        let mut context = self.context().cloned().unwrap_or_else(ErrorContext::new);
        context.trace_id = Some(trace_id.into());
        self.with_context(context)
    }

    /// 设置用户ID
    pub fn with_user_id(mut self, user_id: impl Into<String>) -> Self {
        let mut context = self.context().cloned().unwrap_or_else(ErrorContext::new);
        context.user_id = Some(user_id.into());
        self.with_context(context)
    }

    // =================== 查询方法 ===================

    /// 获取错误分类（用于重试决策）
    pub fn category(&self) -> ErrorCategory {
        match self {
            SigmaXError::Trading { code, .. } => match code {
                TradingErrorCode::InsufficientBalance => ErrorCategory::Permanent,
                TradingErrorCode::MarketClosed => ErrorCategory::Transient,
                TradingErrorCode::InvalidTradingPair => ErrorCategory::Permanent,
                _ => ErrorCategory::Unknown,
            },
            SigmaXError::Strategy { code, .. } => match code {
                StrategyErrorCode::NotFound => ErrorCategory::Permanent,
                StrategyErrorCode::StateInconsistent => ErrorCategory::Transient,
                _ => ErrorCategory::Unknown,
            },
            SigmaXError::Risk { .. } => ErrorCategory::Permanent, // 风险错误通常不应重试
            SigmaXError::Database { code, .. } => match code {
                DatabaseErrorCode::ConnectionFailed => ErrorCategory::Transient,
                DatabaseErrorCode::TransactionFailed => ErrorCategory::Transient,
                DatabaseErrorCode::ConstraintViolation => ErrorCategory::Permanent,
                _ => ErrorCategory::Unknown,
            },
            SigmaXError::Network { code, .. } => match code {
                NetworkErrorCode::Timeout => ErrorCategory::Transient,
                NetworkErrorCode::ConnectionRefused => ErrorCategory::Transient,
                _ => ErrorCategory::Unknown,
            },
            SigmaXError::Configuration { .. } => ErrorCategory::Permanent,
            SigmaXError::Validation { .. } => ErrorCategory::Permanent,
            SigmaXError::ExchangeApi { code, .. } => match code {
                ExchangeErrorCode::RateLimitExceeded => ErrorCategory::RateLimit,
                ExchangeErrorCode::ApiKeyInvalid => ErrorCategory::Permanent,
                ExchangeErrorCode::InsufficientBalance => ErrorCategory::Permanent,
                _ => ErrorCategory::Unknown,
            },
            SigmaXError::MarketData { code, .. } => match code {
                MarketDataErrorCode::InvalidSymbol => ErrorCategory::Permanent,
                MarketDataErrorCode::DataUnavailable => ErrorCategory::Transient,
                _ => ErrorCategory::Unknown,
            },
            SigmaXError::Internal { .. } => ErrorCategory::Unknown,
        }
    }

    /// 获取错误代码字符串
    pub fn error_code(&self) -> String {
        match self {
            SigmaXError::Trading { code, .. } => format!("TRADING_{:?}", code).to_uppercase(),
            SigmaXError::Strategy { code, .. } => format!("STRATEGY_{:?}", code).to_uppercase(),
            SigmaXError::Risk { code, .. } => format!("RISK_{:?}", code).to_uppercase(),
            SigmaXError::Database { code, .. } => format!("DATABASE_{:?}", code).to_uppercase(),
            SigmaXError::Network { code, .. } => format!("NETWORK_{:?}", code).to_uppercase(),
            SigmaXError::Configuration { code, .. } => format!("CONFIG_{:?}", code).to_uppercase(),
            SigmaXError::Validation { code, .. } => format!("VALIDATION_{:?}", code).to_uppercase(),
            SigmaXError::ExchangeApi { code, .. } => format!("EXCHANGE_{:?}", code).to_uppercase(),
            SigmaXError::MarketData { code, .. } => format!("MARKET_DATA_{:?}", code).to_uppercase(),
            SigmaXError::Internal { code, .. } => format!("INTERNAL_{:?}", code).to_uppercase(),
        }
    }



    /// 获取错误严重程度
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            SigmaXError::Trading { code, .. } => match code {
                TradingErrorCode::MarketClosed => ErrorSeverity::Warning,
                TradingErrorCode::OrderNotFound => ErrorSeverity::Warning,
                _ => ErrorSeverity::Error,
            },
            SigmaXError::Strategy { code, .. } => match code {
                StrategyErrorCode::NotFound => ErrorSeverity::Warning,
                StrategyErrorCode::StateInconsistent => ErrorSeverity::Critical,
                _ => ErrorSeverity::Error,
            },
            SigmaXError::Risk { code, .. } => match code {
                RiskErrorCode::LimitExceeded => ErrorSeverity::Critical,
                RiskErrorCode::ThresholdTriggered => ErrorSeverity::Warning,
                RiskErrorCode::EmergencyStop => ErrorSeverity::Critical,
                _ => ErrorSeverity::Error,
            },
            SigmaXError::Database { code, .. } => match code {
                DatabaseErrorCode::ConnectionFailed => ErrorSeverity::Critical,
                DatabaseErrorCode::TransactionFailed => ErrorSeverity::Error,
                _ => ErrorSeverity::Error,
            },
            SigmaXError::Internal { code, .. } => match code {
                InternalErrorCode::Panic => ErrorSeverity::Critical,
                InternalErrorCode::ResourceExhausted => ErrorSeverity::Critical,
                _ => ErrorSeverity::Error,
            },
            SigmaXError::ExchangeApi { code, .. } => match code {
                ExchangeErrorCode::ApiKeyInvalid => ErrorSeverity::Critical,
                ExchangeErrorCode::RateLimitExceeded => ErrorSeverity::Warning,
                _ => ErrorSeverity::Error,
            },
            SigmaXError::MarketData { .. } => ErrorSeverity::Warning,
            SigmaXError::Validation { .. } => ErrorSeverity::Error,
            SigmaXError::Configuration { .. } => ErrorSeverity::Critical,
            SigmaXError::Network { .. } => ErrorSeverity::Warning,
        }
    }

    /// 转换为用户友好的消息
    pub fn user_message(&self) -> String {
        match self {
            SigmaXError::Trading { code, symbol, .. } => match code {
                TradingErrorCode::InsufficientBalance => "账户余额不足，无法完成交易".to_string(),
                TradingErrorCode::MarketClosed => {
                    if let Some(s) = symbol {
                        format!("交易对 {} 当前不可交易", s)
                    } else {
                        "市场已关闭，无法交易".to_string()
                    }
                },
                TradingErrorCode::InvalidTradingPair => "交易对不存在或不支持".to_string(),
                _ => "交易执行失败，请稍后重试".to_string(),
            },
            SigmaXError::Strategy { strategy_type, .. } => {
                format!("策略 {} 执行异常，已自动停止", strategy_type)
            },
            SigmaXError::Risk { risk_type, .. } => {
                format!("触发 {} 风险控制规则，交易已暂停", risk_type)
            },
            SigmaXError::ExchangeApi { exchange, .. } => {
                format!("交易所 {} 连接异常，请检查网络连接", exchange)
            },
            SigmaXError::Validation { field, .. } => {
                if let Some(f) = field {
                    format!("输入参数 {} 格式不正确", f)
                } else {
                    "输入参数格式不正确".to_string()
                }
            },
            _ => "系统暂时不可用，请稍后重试".to_string(),
        }
    }

    /// 获取错误上下文
    pub fn context(&self) -> Option<&ErrorContext> {
        match self {
            SigmaXError::Trading { context, .. } |
            SigmaXError::Strategy { context, .. } |
            SigmaXError::Risk { context, .. } |
            SigmaXError::Database { context, .. } |
            SigmaXError::Network { context, .. } |
            SigmaXError::Configuration { context, .. } |
            SigmaXError::Validation { context, .. } |
            SigmaXError::ExchangeApi { context, .. } |
            SigmaXError::MarketData { context, .. } |
            SigmaXError::Internal { context, .. } => context.as_ref(),
        }
    }

    /// 检查是否有上下文信息
    pub fn has_context(&self) -> bool {
        self.context().is_some()
    }

    /// 获取追踪ID
    pub fn trace_id(&self) -> Option<&str> {
        self.context()?.trace_id.as_deref()
    }

    /// 获取用户ID
    pub fn user_id(&self) -> Option<&str> {
        self.context()?.user_id.as_deref()
    }
}