//! Application Services层 - 业务流程编排
//!
//! 负责协调多个领域服务完成复杂的业务流程
//! 包含事务管理、事件发布、错误恢复等

pub mod strategy_application_service;
pub mod risk_application_service;
pub mod portfolio_application_service;
pub mod report_application_service;

// 重新导出应用服务
pub use strategy_application_service::StrategyApplicationService;
pub use risk_application_service::RiskApplicationService;
pub use portfolio_application_service::PortfolioApplicationService;
pub use report_application_service::ReportApplicationService;

/// 应用服务基类
pub trait BaseApplicationService {
    /// 获取服务名称
    fn name(&self) -> &str;
    
    /// 健康检查
    async fn health_check(&self) -> bool;
    
    /// 获取服务版本
    fn version(&self) -> &str;
}
