//! 风险集成服务
//!
//! 负责与sigmax-risk模块的集成，提供风险管理和监控

use std::sync::Arc;
use async_trait::async_trait;
use uuid::Uuid;
use tracing::info;

use crate::{
    integration::BaseIntegration,
    error::ApiError,
};

/// 风险集成服务
pub struct RiskIntegration {
    // 后续会注入风险管理器
    risk_manager: Option<Arc<dyn RiskManager>>,
}

impl RiskIntegration {
    /// 创建新的风险集成服务
    pub fn new() -> Self {
        Self {
            risk_manager: None,
        }
    }
    
    /// 设置风险管理器
    pub fn with_risk_manager(mut self, risk_manager: Arc<dyn RiskManager>) -> Self {
        self.risk_manager = Some(risk_manager);
        self
    }
}

#[async_trait]
impl BaseIntegration for RiskIntegration {
    fn name(&self) -> &str {
        "RiskIntegration"
    }
    
    async fn health_check(&self) -> bool {
        // 检查风险管理器是否可用
        self.risk_manager.is_some()
    }
    
    fn version(&self) -> &str {
        "1.0.0"
    }
    
    fn external_module(&self) -> &str {
        "sigmax-risk"
    }
}

impl RiskIntegration {
    /// 检查订单风险
    pub async fn check_order_risk(
        &self,
        order: &Order,
    ) -> Result<RiskCheckResult, ApiError> {
        info!("⚠️ 风险集成服务检查订单风险: {} {}", order.symbol, order.side);
        
        if let Some(manager) = &self.risk_manager {
            let result = manager.check_order_risk(order).await
                .map_err(|e| ApiError::BusinessError(format!("风险检查失败: {}", e)))?;
            
            info!("✅ 风险集成服务完成风险检查: {}", order.id);
            Ok(result)
        } else {
            Err(ApiError::SystemError("风险管理器未配置".to_string()))
        }
    }
    
    /// 获取风险指标
    pub async fn get_risk_metrics(&self) -> Result<RiskMetrics, ApiError> {
        info!("📊 风险集成服务获取风险指标");
        
        if let Some(manager) = &self.risk_manager {
            let metrics = manager.get_risk_metrics().await
                .map_err(|e| ApiError::BusinessError(format!("获取风险指标失败: {}", e)))?;
            
            Ok(metrics)
        } else {
            Err(ApiError::SystemError("风险管理器未配置".to_string()))
        }
    }
    
    /// 设置风险限制
    pub async fn set_risk_limits(
        &self,
        limits: RiskLimits,
    ) -> Result<(), ApiError> {
        info!("🔒 风险集成服务设置风险限制");
        
        if let Some(manager) = &self.risk_manager {
            manager.set_risk_limits(limits).await
                .map_err(|e| ApiError::BusinessError(format!("设置风险限制失败: {}", e)))?;
            
            info!("✅ 风险集成服务完成风险限制设置");
            Ok(())
        } else {
            Err(ApiError::SystemError("风险管理器未配置".to_string()))
        }
    }
    
    /// 获取风险警报
    pub async fn get_risk_alerts(&self) -> Result<Vec<RiskAlert>, ApiError> {
        info!("🚨 风险集成服务获取风险警报");
        
        if let Some(manager) = &self.risk_manager {
            let alerts = manager.get_risk_alerts().await
                .map_err(|e| ApiError::BusinessError(format!("获取风险警报失败: {}", e)))?;
            
            Ok(alerts)
        } else {
            Err(ApiError::SystemError("风险管理器未配置".to_string()))
        }
    }
}

// 临时类型定义（后续会从专门的模块导入）
#[derive(Debug)]
pub struct Order {
    pub id: Uuid,
    pub symbol: String,
    pub side: String,
    pub quantity: f64,
    pub price: f64,
}

#[derive(Debug)]
pub struct RiskCheckResult {
    pub risk_level: String,
    pub risk_score: f64,
    pub message: String,
    pub recommendations: Vec<String>,
}

#[derive(Debug)]
pub struct RiskMetrics {
    pub total_risk_score: f64,
    pub portfolio_risk: f64,
    pub market_risk: f64,
    pub credit_risk: f64,
    pub var_95: f64,
    pub var_99: f64,
}

#[derive(Debug)]
pub struct RiskLimits {
    pub max_position_size: f64,
    pub max_daily_loss: f64,
    pub max_drawdown: f64,
}

#[derive(Debug)]
pub struct RiskAlert {
    pub id: Uuid,
    pub alert_type: String,
    pub message: String,
    pub severity: String,
    pub created_at: String,
}

#[async_trait::async_trait]
pub trait RiskManager: Send + Sync {
    async fn check_order_risk(&self, order: &Order) -> Result<RiskCheckResult, Box<dyn std::error::Error>>;
    async fn get_risk_metrics(&self) -> Result<RiskMetrics, Box<dyn std::error::Error>>;
    async fn set_risk_limits(&self, limits: RiskLimits) -> Result<(), Box<dyn std::error::Error>>;
    async fn get_risk_alerts(&self) -> Result<Vec<RiskAlert>, Box<dyn std::error::Error>>;
}
