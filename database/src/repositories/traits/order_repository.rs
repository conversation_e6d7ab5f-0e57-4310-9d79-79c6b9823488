//! 订单Repository接口
//!
//! 基于Repository的订单数据访问接口

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use sigmax_core::{Order, OrderId, OrderStatus, StrategyId, TradingPair, ExchangeId, SigmaXResult};
use super::base_repository::{Repository, QueryFilter, PaginationResult, SortConfig};
use serde::{Serialize, Deserialize};

/// 订单Repository接口
///
/// 继承Repository的基础CRUD操作，并添加订单特有的业务查询方法
#[async_trait]
pub trait OrderRepository: Repository<Order, OrderId> {
    // ============================================================================
    // 业务查询方法 - 订单特有的查询逻辑
    // ============================================================================
    
    /// 根据状态查询订单
    /// 
    /// # Arguments
    /// * `status` - 订单状态
    /// 
    /// # Returns
    /// 返回指定状态的订单列表
    async fn find_by_status(&self, status: OrderStatus) -> SigmaXResult<Vec<Order>>;
    
    /// 根据策略ID查询订单
    /// 
    /// # Arguments
    /// * `strategy_id` - 策略ID
    /// 
    /// # Returns
    /// 返回指定策略的订单列表
    async fn find_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Vec<Order>>;
    
    /// 根据交易所ID查询订单
    /// 
    /// # Arguments
    /// * `exchange_id` - 交易所ID
    /// 
    /// # Returns
    /// 返回指定交易所的订单列表
    async fn find_by_exchange(&self, exchange_id: ExchangeId) -> SigmaXResult<Vec<Order>>;
    
    /// 根据交易对查询订单
    /// 
    /// # Arguments
    /// * `trading_pair` - 交易对
    /// 
    /// # Returns
    /// 返回指定交易对的订单列表
    async fn find_by_trading_pair(&self, trading_pair: &TradingPair) -> SigmaXResult<Vec<Order>>;
    
    /// 根据时间范围查询订单
    /// 
    /// # Arguments
    /// * `start` - 开始时间
    /// * `end` - 结束时间
    /// 
    /// # Returns
    /// 返回指定时间范围内的订单列表
    async fn find_by_time_range(&self, start: DateTime<Utc>, end: DateTime<Utc>) -> SigmaXResult<Vec<Order>>;
    
    // ============================================================================
    // 复合查询方法 - 支持多条件查询
    // ============================================================================
    
    /// 根据过滤器查询订单
    /// 
    /// # Arguments
    /// * `filter` - 查询过滤器
    /// 
    /// # Returns
    /// 返回符合条件的订单列表
    async fn find_by_filter(&self, filter: &OrderQueryFilter) -> SigmaXResult<Vec<Order>>;
    
    /// 分页查询订单（带过滤和排序）
    /// 
    /// # Arguments
    /// * `filter` - 查询过滤器
    /// * `sort` - 排序配置
    /// * `offset` - 偏移量
    /// * `limit` - 限制数量
    /// 
    /// # Returns
    /// 返回分页查询结果
    async fn find_with_filter_and_pagination(
        &self,
        filter: &OrderQueryFilter,
        sort: &[SortConfig],
        offset: u64,
        limit: u64,
    ) -> SigmaXResult<PaginationResult<Order>>;
    
    // ============================================================================
    // 聚合查询方法 - 统计和分析
    // ============================================================================
    
    /// 获取订单统计信息
    /// 
    /// # Arguments
    /// * `filter` - 查询过滤器（可选）
    /// 
    /// # Returns
    /// 返回订单统计信息
    async fn get_statistics(&self, filter: Option<&OrderQueryFilter>) -> SigmaXResult<OrderStatistics>;
    
    /// 根据状态统计订单数量
    /// 
    /// # Returns
    /// 返回各状态的订单数量统计
    async fn count_by_status(&self) -> SigmaXResult<Vec<(OrderStatus, u64)>>;
    
    /// 根据交易对统计订单数量
    /// 
    /// # Arguments
    /// * `limit` - 返回的交易对数量限制
    /// 
    /// # Returns
    /// 返回热门交易对的订单数量统计
    async fn count_by_trading_pair(&self, limit: u64) -> SigmaXResult<Vec<(TradingPair, u64)>>;
    
    // ============================================================================
    // 性能优化方法
    // ============================================================================
    
    /// 获取活跃订单（未完成的订单）
    /// 
    /// # Returns
    /// 返回所有活跃状态的订单
    async fn find_active_orders(&self) -> SigmaXResult<Vec<Order>>;
    
    /// 获取最近的订单
    /// 
    /// # Arguments
    /// * `limit` - 限制数量
    /// 
    /// # Returns
    /// 返回最近创建的订单列表
    async fn find_recent_orders(&self, limit: u64) -> SigmaXResult<Vec<Order>>;
    
    /// 预加载订单关联数据
    /// 
    /// # Arguments
    /// * `order_ids` - 订单ID列表
    /// 
    /// # Returns
    /// 返回预加载了关联数据的订单列表
    async fn preload_with_relations(&self, order_ids: &[OrderId]) -> SigmaXResult<Vec<Order>>;
}

/// 订单查询过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderQueryFilter {
    /// 订单状态过滤
    pub status: Option<Vec<OrderStatus>>,
    /// 策略ID过滤
    pub strategy_ids: Option<Vec<StrategyId>>,
    /// 交易所ID过滤
    pub exchange_ids: Option<Vec<ExchangeId>>,
    /// 交易对过滤
    pub trading_pairs: Option<Vec<TradingPair>>,
    /// 创建时间范围过滤
    pub created_time_range: Option<(DateTime<Utc>, DateTime<Utc>)>,
    /// 更新时间范围过滤
    pub updated_time_range: Option<(DateTime<Utc>, DateTime<Utc>)>,
    /// 数量范围过滤
    pub quantity_range: Option<(rust_decimal::Decimal, rust_decimal::Decimal)>,
    /// 价格范围过滤
    pub price_range: Option<(rust_decimal::Decimal, rust_decimal::Decimal)>,
}

impl Default for OrderQueryFilter {
    fn default() -> Self {
        Self {
            status: None,
            strategy_ids: None,
            exchange_ids: None,
            trading_pairs: None,
            created_time_range: None,
            updated_time_range: None,
            quantity_range: None,
            price_range: None,
        }
    }
}

impl QueryFilter for OrderQueryFilter {
    fn validate(&self) -> SigmaXResult<()> {
        // 验证时间范围
        if let Some((start, end)) = &self.created_time_range {
            if start >= end {
                return Err(sigmax_core::SigmaXError::validation(
                    sigmax_core::ValidationErrorCode::InvalidRange,
                    "created_time_range",
                    "创建时间范围无效：开始时间必须小于结束时间"
                ));
            }
        }

        if let Some((start, end)) = &self.updated_time_range {
            if start >= end {
                return Err(sigmax_core::SigmaXError::validation(
                    sigmax_core::ValidationErrorCode::InvalidRange,
                    "updated_time_range",
                    "更新时间范围无效：开始时间必须小于结束时间"
                ));
            }
        }
        
        // 验证数量范围
        if let Some((min, max)) = &self.quantity_range {
            if min >= max || *min < rust_decimal::Decimal::ZERO {
                return Err(sigmax_core::SigmaXError::validation(
                    sigmax_core::ValidationErrorCode::InvalidRange,
                    "quantity_range",
                    "数量范围无效"
                ));
            }
        }

        // 验证价格范围
        if let Some((min, max)) = &self.price_range {
            if min >= max || *min < rust_decimal::Decimal::ZERO {
                return Err(sigmax_core::SigmaXError::validation(
                    sigmax_core::ValidationErrorCode::InvalidRange,
                    "price_range",
                    "价格范围无效"
                ));
            }
        }
        
        Ok(())
    }
    
    fn description(&self) -> String {
        let mut parts = Vec::new();
        
        if let Some(status) = &self.status {
            parts.push(format!("状态: {:?}", status));
        }
        if let Some(strategy_ids) = &self.strategy_ids {
            parts.push(format!("策略数量: {}", strategy_ids.len()));
        }
        if let Some(exchange_ids) = &self.exchange_ids {
            parts.push(format!("交易所数量: {}", exchange_ids.len()));
        }
        if let Some(trading_pairs) = &self.trading_pairs {
            parts.push(format!("交易对数量: {}", trading_pairs.len()));
        }
        if self.created_time_range.is_some() {
            parts.push("创建时间范围".to_string());
        }
        if self.updated_time_range.is_some() {
            parts.push("更新时间范围".to_string());
        }
        
        if parts.is_empty() {
            "无过滤条件".to_string()
        } else {
            format!("过滤条件: {}", parts.join(", "))
        }
    }
}

/// 订单统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderStatistics {
    /// 总订单数
    pub total_count: u64,
    /// 各状态订单数量
    pub status_counts: std::collections::HashMap<OrderStatus, u64>,
    /// 总交易量
    pub total_volume: rust_decimal::Decimal,
    /// 平均订单大小
    pub average_order_size: rust_decimal::Decimal,
    /// 最大订单大小
    pub max_order_size: rust_decimal::Decimal,
    /// 最小订单大小
    pub min_order_size: rust_decimal::Decimal,
    /// 统计时间
    pub calculated_at: DateTime<Utc>,
}
