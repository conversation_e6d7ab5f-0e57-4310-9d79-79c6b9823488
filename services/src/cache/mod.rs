//! 缓存服务模块
//! 
//! 提供多种缓存实现：
//! - Redis分布式缓存
//! - 内存缓存 (高性能)
//! - 缓存策略 (智能路由)

pub mod redis;
pub mod memory;
// pub mod strategy; // 暂时禁用策略模块
pub mod config;

pub use redis::RedisCacheService;
pub use memory::MemoryCacheService;
// pub use strategy::CacheStrategy; // 暂时禁用策略模块
pub use config::CacheConfig;

use async_trait::async_trait;
use sigmax_core::traits::CacheService;
use std::time::Duration;
use crate::errors::{ServiceError, ServiceResult};

/// 缓存类型枚举
#[derive(Debug, Clone, PartialEq, serde::Serialize, serde::Deserialize)]
pub enum CacheType {
    Redis,
    Memory,
    Hybrid,
}

/// 缓存统计信息
#[derive(Debug, <PERSON><PERSON>)]
pub struct CacheStats {
    pub hits: u64,
    pub misses: u64,
    pub total_requests: u64,
    pub hit_rate: f64,
    pub avg_response_time_ms: f64,
}

impl CacheStats {
    pub fn new() -> Self {
        Self {
            hits: 0,
            misses: 0,
            total_requests: 0,
            hit_rate: 0.0,
            avg_response_time_ms: 0.0,
        }
    }
    
    pub fn record_hit(&mut self, response_time_ms: f64) {
        self.hits += 1;
        self.total_requests += 1;
        self.update_stats(response_time_ms);
    }
    
    pub fn record_miss(&mut self, response_time_ms: f64) {
        self.misses += 1;
        self.total_requests += 1;
        self.update_stats(response_time_ms);
    }
    
    fn update_stats(&mut self, response_time_ms: f64) {
        self.hit_rate = if self.total_requests > 0 {
            self.hits as f64 / self.total_requests as f64
        } else {
            0.0
        };
        
        // 简单的移动平均
        self.avg_response_time_ms = (self.avg_response_time_ms + response_time_ms) / 2.0;
    }
}

/// 扩展的缓存服务接口
#[async_trait]
pub trait ExtendedCacheService: CacheService {
    /// 获取缓存统计信息
    async fn get_stats(&self) -> ServiceResult<CacheStats>;
    
    /// 重置统计信息
    async fn reset_stats(&self) -> ServiceResult<()>;
    
    /// 获取缓存大小
    async fn size(&self) -> ServiceResult<usize>;
    
    /// 检查键是否存在
    async fn exists(&self, key: &str) -> ServiceResult<bool>;
    
    /// 设置键的过期时间
    async fn expire(&self, key: &str, ttl: Duration) -> ServiceResult<()>;
    
    /// 获取键的剩余TTL
    async fn ttl(&self, key: &str) -> ServiceResult<Option<Duration>>;
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cache_stats() {
        let mut stats = CacheStats::new();
        
        stats.record_hit(10.0);
        assert_eq!(stats.hits, 1);
        assert_eq!(stats.total_requests, 1);
        assert_eq!(stats.hit_rate, 1.0);
        
        stats.record_miss(20.0);
        assert_eq!(stats.misses, 1);
        assert_eq!(stats.total_requests, 2);
        assert_eq!(stats.hit_rate, 0.5);
    }
}
