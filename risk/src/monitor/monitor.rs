//! 风险监控器

use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use sigmax_interfaces::Portfolio;
use crate::core::{RiskMetrics, RiskLevel};
use super::alert::{AlertService, AlertLevel};

/// 监控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitorConfig {
    /// 高风险阈值
    pub high_risk_threshold: Decimal,
    /// 告警阈值
    pub alert_threshold: Decimal,
    /// 监控间隔（秒）
    pub monitor_interval_seconds: u64,
}

impl Default for MonitorConfig {
    fn default() -> Self {
        Self {
            high_risk_threshold: Decimal::from(70),
            alert_threshold: Decimal::from(80),
            monitor_interval_seconds: 60,
        }
    }
}

/// 监控结果
#[derive(Debug, Clone)]
pub struct MonitorResult {
    pub metrics: RiskMetrics,
    pub alerts_sent: usize,
}

/// 风险监控器
pub struct RiskMonitor {
    config: MonitorConfig,
    alert_service: Arc<AlertService>,
}

impl RiskMonitor {
    pub fn new(config: MonitorConfig, alert_service: Arc<AlertService>) -> Self {
        Self {
            config,
            alert_service,
        }
    }
    
    /// 监控投资组合
    pub async fn monitor_portfolio(&self, portfolio: &Portfolio) -> Result<MonitorResult, String> {
        // 计算风险指标
        let metrics = self.calculate_metrics(portfolio);
        let mut alerts_sent = 0;
        
        // 根据风险等级发送告警
        match metrics.risk_level {
            RiskLevel::Critical => {
                self.alert_service.send_alert(
                    AlertLevel::Critical,
                    format!("严重风险警告！风险分数: {}", metrics.risk_score),
                ).await?;
                alerts_sent += 1;
            }
            RiskLevel::High => {
                if metrics.risk_score >= self.config.alert_threshold {
                    self.alert_service.send_alert(
                        AlertLevel::High,
                        format!("高风险警告！风险分数: {}", metrics.risk_score),
                    ).await?;
                    alerts_sent += 1;
                }
            }
            _ => {}
        }
        
        Ok(MonitorResult {
            metrics,
            alerts_sent,
        })
    }
    
    /// 计算风险指标
    fn calculate_metrics(&self, portfolio: &Portfolio) -> RiskMetrics {
        let total_value = portfolio.total_value;
        
        // 简化的风险计算
        let mut risk_score = Decimal::ZERO;
        
        // 根据持仓集中度计算风险
        if !portfolio.balances.is_empty() && total_value > Decimal::ZERO {
            let max_position = portfolio.balances.iter()
                .map(|b| b.total())
                .max()
                .unwrap_or(Decimal::ZERO);
            
            let concentration = max_position / total_value;
            risk_score += concentration * Decimal::from(50); // 集中度贡献50分
        }
        
        // 根据杠杆计算风险
        let leverage = Decimal::ONE; // 简化：暂时假设无杠杆
        if leverage > Decimal::from(2) {
            risk_score += (leverage - Decimal::ONE) * Decimal::from(25);
        }
        
        RiskMetrics::new(risk_score, total_value, leverage)
    }
}
