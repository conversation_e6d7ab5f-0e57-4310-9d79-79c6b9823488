//! SigmaX Core Monitoring Configuration
//!
//! ## 主要职责
//!
//! 1. **监控配置模型** - 定义系统监控相关的配置参数
//!    - 基础监控参数
//!    - 数据收集配置
//!    - 告警阈值设置
//!
//! 2. **配置验证** - 提供监控配置参数的验证规则
//!    - 数值范围验证
//!    - 逻辑一致性验证
//!    - 必填字段验证
//!
//! ## 设计原则
//!
//! - **配置导向**: 专注于配置参数定义，不包含监控实现逻辑
//! - **技术无关**: 不绑定特定的监控技术（如Prometheus、Grafana）
//! - **简洁明确**: 只包含核心的监控配置参数
//! - **验证友好**: 使用validator进行参数验证

use serde::{Deserialize, Serialize};
use validator::Validate;
use crate::SigmaXResult;

/// 监控配置
///
/// 定义系统监控的核心配置参数，专注于配置模型而非具体实现
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Validate)]
pub struct MonitoringConfig {
    /// 配置名称
    #[validate(length(min = 1, max = 100, message = "监控配置名称长度必须在1-100个字符之间"))]
    pub name: String,

    /// 配置描述
    #[validate(length(max = 500, message = "监控配置描述长度不能超过500个字符"))]
    pub description: Option<String>,

    /// 是否启用监控
    pub enabled: bool,

    // ============================================================================
    // 核心监控配置参数
    // ============================================================================

    /// 是否启用指标收集
    pub metrics_enabled: bool,

    /// 健康检查间隔(秒)
    #[validate(range(min = 1, max = 3600, message = "健康检查间隔必须在1-3600秒之间"))]
    pub health_check_interval: u32,

    /// 数据收集间隔(秒)
    #[validate(range(min = 1, max = 3600, message = "数据收集间隔必须在1-3600秒之间"))]
    pub data_collection_interval: u32,

    /// 监控数据保留天数
    #[validate(range(min = 1, max = 365, message = "数据保留天数必须在1-365天之间"))]
    pub data_retention_days: u32,

    // ============================================================================
    // 告警配置参数
    // ============================================================================

    /// 是否启用告警
    pub alerting_enabled: bool,

    /// 告警检查间隔(秒)
    #[validate(range(min = 1, max = 3600, message = "告警检查间隔必须在1-3600秒之间"))]
    pub alert_check_interval: u32,

    /// 告警抑制时间(秒)
    #[validate(range(min = 0, max = 86400, message = "告警抑制时间必须在0-86400秒之间"))]
    pub alert_suppression_seconds: u32,

    // ============================================================================
    // 阈值配置参数
    // ============================================================================

    /// CPU使用率告警阈值(百分比)
    #[validate(range(min = 1, max = 100, message = "CPU告警阈值必须在1-100%之间"))]
    pub cpu_threshold_percentage: u32,

    /// 内存使用率告警阈值(百分比)
    #[validate(range(min = 1, max = 100, message = "内存告警阈值必须在1-100%之间"))]
    pub memory_threshold_percentage: u32,

    /// 磁盘使用率告警阈值(百分比)
    #[validate(range(min = 1, max = 100, message = "磁盘告警阈值必须在1-100%之间"))]
    pub disk_threshold_percentage: u32,

    /// API响应时间告警阈值(毫秒)
    #[validate(range(min = 1, max = 60000, message = "API响应时间阈值必须在1-60000毫秒之间"))]
    pub api_response_time_threshold_ms: u32,
}

// ============================================================================
// 告警级别枚举
// ============================================================================

/// 告警级别
///
/// 定义系统告警的严重程度级别
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AlertSeverity {
    /// 信息级别
    Info,
    /// 警告级别
    Warning,
    /// 严重级别
    Critical,
    /// 紧急级别
    Emergency,
}

// ============================================================================
// 默认实现
// ============================================================================

impl Default for AlertSeverity {
    fn default() -> Self {
        AlertSeverity::Warning
    }
}

impl Default for MonitoringConfig {
    fn default() -> Self {
        Self {
            name: "Default Monitoring Config".to_string(),
            description: Some("默认监控配置".to_string()),
            enabled: true,
            metrics_enabled: true,
            health_check_interval: 30, // 30秒
            data_collection_interval: 60, // 1分钟
            data_retention_days: 7, // 7天
            alerting_enabled: true,
            alert_check_interval: 60, // 1分钟
            alert_suppression_seconds: 300, // 5分钟
            cpu_threshold_percentage: 80, // 80%
            memory_threshold_percentage: 85, // 85%
            disk_threshold_percentage: 90, // 90%
            api_response_time_threshold_ms: 5000, // 5秒
        }
    }
}

// ============================================================================
// 配置验证实现
// ============================================================================

impl MonitoringConfig {
    /// 创建新的监控配置
    pub fn new(name: impl Into<String>) -> Self {
        Self {
            name: name.into(),
            ..Default::default()
        }
    }

    /// 设置是否启用
    pub fn with_enabled(mut self, enabled: bool) -> Self {
        self.enabled = enabled;
        self
    }

    /// 设置指标收集
    pub fn with_metrics_enabled(mut self, metrics_enabled: bool) -> Self {
        self.metrics_enabled = metrics_enabled;
        self
    }

    /// 设置健康检查间隔
    pub fn with_health_check_interval(mut self, interval_seconds: u32) -> Self {
        self.health_check_interval = interval_seconds;
        self
    }

    /// 设置数据保留天数
    pub fn with_data_retention_days(mut self, days: u32) -> Self {
        self.data_retention_days = days;
        self
    }

    /// 设置告警功能
    pub fn with_alerting_enabled(mut self, alerting_enabled: bool) -> Self {
        self.alerting_enabled = alerting_enabled;
        self
    }

    /// 设置CPU阈值
    pub fn with_cpu_threshold(mut self, threshold_percentage: u32) -> Self {
        self.cpu_threshold_percentage = threshold_percentage;
        self
    }

    /// 设置内存阈值
    pub fn with_memory_threshold(mut self, threshold_percentage: u32) -> Self {
        self.memory_threshold_percentage = threshold_percentage;
        self
    }

    /// 执行完整的监控配置验证
    pub fn validate_complete(&self) -> SigmaXResult<()> {
        // 首先执行基础验证（通过validator derive宏）
        if let Err(validation_errors) = <Self as Validate>::validate(self) {
            return Err(crate::SigmaXError::from(validation_errors));
        }

        // 跨字段验证逻辑
        if self.enabled {
            // 如果启用监控，检查关键参数的合理性
            if !self.metrics_enabled && !self.alerting_enabled {
                return Err(crate::SigmaXError::validation(
                    crate::ValidationErrorCode::InvalidRange,
                    "monitoring_features",
                    "启用监控时，至少需要启用指标收集或告警功能之一"
                ));
            }
        }

        // 验证数据收集间隔不能小于健康检查间隔
        if self.data_collection_interval < self.health_check_interval {
            return Err(crate::SigmaXError::validation(
                crate::ValidationErrorCode::InvalidRange,
                "data_collection_interval",
                "数据收集间隔不应小于健康检查间隔"
            ));
        }

        // 验证告警检查间隔的合理性
        if self.alerting_enabled && self.alert_check_interval > self.data_collection_interval * 10 {
            return Err(crate::SigmaXError::validation(
                crate::ValidationErrorCode::InvalidRange,
                "alert_check_interval",
                "告警检查间隔不应过长，建议不超过数据收集间隔的10倍"
            ));
        }

        Ok(())
    }

    /// 检查配置是否有效
    pub fn is_valid(&self) -> bool {
        self.validate_complete().is_ok()
    }

    /// 获取配置摘要信息
    pub fn summary(&self) -> String {
        format!(
            "MonitoringConfig[{}]: enabled={}, metrics={}, alerting={}, health_check={}s, retention={}d",
            self.name,
            self.enabled,
            self.metrics_enabled,
            self.alerting_enabled,
            self.health_check_interval,
            self.data_retention_days
        )
    }
}

