//! 性能监控管理器
//!
//! 提供完整的数据库性能监控、慢查询分析、连接池状态监控

use sigmax_core::{SigmaXResult, SigmaXError, DatabaseErrorCode};
use std::sync::Arc;
use std::time::{Duration, Instant};
use std::collections::HashMap;
use tokio::sync::RwLock;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};
use super::connection_manager::ConnectionManager;

/// 性能监控配置
#[derive(Debug, Clone)]
pub struct PerformanceConfig {
    /// 是否启用性能监控
    pub enabled: bool,
    /// 慢查询阈值（毫秒）
    pub slow_query_threshold_ms: u64,
    /// 监控数据保留时间（秒）考虑整体一致性
    pub retention_seconds: u64,
    /// 是否记录查询详情
    pub log_query_details: bool,
    /// 最大监控记录数
    pub max_records: usize,
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            slow_query_threshold_ms: 1000, // 1秒
            retention_seconds: 3600,        // 1小时
            log_query_details: false,
            max_records: 10000,
        }
    }
}

/// 查询类型枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum QueryType {
    Select,
    Insert,
    Update,
    Delete,
    Create,
    Drop,
    Alter,
    Other,
}

/// 时序查询指标 (用于趋势分析)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimeSeriesQueryMetrics {
    /// 查询名称
    pub query_name: String,
    /// 执行时间（毫秒）
    pub duration_ms: u64,
    /// 执行时间戳
    pub timestamp: DateTime<Utc>,
    /// 是否为慢查询
    pub is_slow: bool,
    /// 查询详情（可选）
    pub query_details: Option<String>,
    /// 查询类型
    pub query_type: QueryType,
    /// 参数数量
    pub param_count: usize,
    /// 错误信息
    pub error: Option<String>,
}

/// 聚合查询指标 (用于统计分析)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AggregatedQueryMetrics {
    /// 查询哈希
    pub query_hash: String,
    /// 查询类型
    pub query_type: QueryType,
    /// 执行次数
    pub execution_count: u64,
    /// 总执行时间（毫秒）
    pub total_duration_ms: u64,
    /// 平均执行时间（毫秒）
    pub avg_duration_ms: f64,
    /// 最小执行时间（毫秒）
    pub min_duration_ms: u64,
    /// 最大执行时间（毫秒）
    pub max_duration_ms: u64,
    /// 最后执行时间
    pub last_executed_at: DateTime<Utc>,
    /// 错误次数
    pub error_count: u64,
}

/// 性能统计信息
#[derive(Debug, Clone)]
pub struct PerformanceStats {
    /// 总查询数
    pub total_queries: u64,
    /// 慢查询数
    pub slow_queries: u64,
    /// 平均查询时间（毫秒）
    pub avg_query_time_ms: f64,
    /// 最大查询时间（毫秒）
    pub max_query_time_ms: u64,
    /// 最小查询时间（毫秒）
    pub min_query_time_ms: u64,
    /// 查询类型统计
    pub query_type_stats: HashMap<String, u64>,
}

/// 慢查询记录
#[derive(Debug, Clone)]
pub struct SlowQuery {
    /// 查询名称
    pub query_name: String,
    /// 执行时间（毫秒）
    pub duration_ms: u64,
    /// 执行时间戳
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// 查询详情
    pub query_details: Option<String>,
}

/// 统一性能监控管理器
///
/// 融合了聚合指标和时序指标，提供完整的性能监控功能
#[derive(Debug)]
pub struct PerformanceManager {
    connection_manager: Arc<ConnectionManager>,
    config: PerformanceConfig,
    // 聚合指标 - 用于统计分析
    aggregated_metrics: Arc<RwLock<HashMap<String, AggregatedQueryMetrics>>>,
    // 时序指标 - 用于趋势分析
    time_series_metrics: Arc<RwLock<Vec<TimeSeriesQueryMetrics>>>,
    // 慢查询记录
    slow_queries: Arc<RwLock<Vec<SlowQuery>>>,
    // 统计信息
    stats: Arc<RwLock<PerformanceStats>>,
}

impl PerformanceManager {
    /// 创建新的统一性能监控管理器
    pub fn new(connection_manager: Arc<ConnectionManager>, config: PerformanceConfig) -> Self {
        Self {
            connection_manager,
            config,
            aggregated_metrics: Arc::new(RwLock::new(HashMap::new())),
            time_series_metrics: Arc::new(RwLock::new(Vec::new())),
            slow_queries: Arc::new(RwLock::new(Vec::new())),
            stats: Arc::new(RwLock::new(PerformanceStats {
                total_queries: 0,
                slow_queries: 0,
                avg_query_time_ms: 0.0,
                max_query_time_ms: 0,
                min_query_time_ms: u64::MAX,
                query_type_stats: HashMap::new(),
            })),
        }
    }

    /// 使用默认配置创建性能监控管理器
    pub fn with_defaults(connection_manager: Arc<ConnectionManager>) -> Self {
        Self::new(connection_manager, PerformanceConfig::default())
    }

    /// 记录查询执行 (统一接口)
    pub async fn record_query_execution(
        &self,
        query: &str,
        duration: Duration,
        error: Option<&str>,
        param_count: usize,
    ) -> SigmaXResult<()> {
        if !self.config.enabled {
            return Ok(());
        }

        let duration_ms = duration.as_millis() as u64;
        let query_hash = self.hash_query(query);
        let query_type = self.detect_query_type(query);
        let now = Utc::now();
        let is_slow = duration_ms >= self.config.slow_query_threshold_ms;

        // 创建时序指标
        let time_series_metric = TimeSeriesQueryMetrics {
            query_name: query.to_string(),
            duration_ms,
            timestamp: now,
            is_slow,
            query_details: if self.config.log_query_details {
                Some(query.to_string())
            } else {
                None
            },
            query_type,
            param_count,
            error: error.map(|e| e.to_string()),
        };

        // 记录时序指标
        {
            let mut metrics = self.time_series_metrics.write().await;
            metrics.push(time_series_metric.clone());

            // 限制记录数量
            if metrics.len() > self.config.max_records {
                metrics.remove(0);
            }
        }

        // 更新聚合指标
        self.update_aggregated_metrics(&query_hash, query_type, duration_ms, now, error.is_some()).await;

        // 记录慢查询
        if is_slow {
            self.record_slow_query(&query_hash, query, duration_ms, now, param_count, error).await;
        }

        // 更新统计信息
        self.update_stats(duration_ms, is_slow, query_type).await;

        Ok(())
    }

    /// 记录查询性能 (兼容接口)
    pub async fn record_query(&self, query_name: &str, duration: Duration, query_details: Option<String>) {
        let _ = self.record_query_execution(
            query_name,
            duration,
            None,
            0,
        ).await;
    }

    /// 执行带监控的操作
    pub async fn execute_with_monitoring<F, Fut, R>(
        &self,
        query_name: &str,
        operation: F,
    ) -> SigmaXResult<R>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = SigmaXResult<R>>,
    {
        let start_time = Instant::now();
        let result = operation().await;
        let duration = start_time.elapsed();

        // 记录性能指标
        let error = if result.is_err() {
            Some("Query execution failed")
        } else {
            None
        };

        let _ = self.record_query_execution(query_name, duration, error, 0).await;

        result
    }

    /// 生成查询哈希
    fn hash_query(&self, query: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let normalized = query.trim().to_lowercase();
        let mut hasher = DefaultHasher::new();
        normalized.hash(&mut hasher);
        format!("query_{:x}", hasher.finish())
    }

    /// 检测查询类型
    fn detect_query_type(&self, query: &str) -> QueryType {
        let query_lower = query.trim().to_lowercase();
        if query_lower.starts_with("select") {
            QueryType::Select
        } else if query_lower.starts_with("insert") {
            QueryType::Insert
        } else if query_lower.starts_with("update") {
            QueryType::Update
        } else if query_lower.starts_with("delete") {
            QueryType::Delete
        } else if query_lower.starts_with("create") {
            QueryType::Create
        } else if query_lower.starts_with("drop") {
            QueryType::Drop
        } else if query_lower.starts_with("alter") {
            QueryType::Alter
        } else {
            QueryType::Other
        }
    }

    /// 更新聚合指标
    async fn update_aggregated_metrics(
        &self,
        query_hash: &str,
        query_type: QueryType,
        duration_ms: u64,
        timestamp: DateTime<Utc>,
        has_error: bool,
    ) {
        let mut metrics = self.aggregated_metrics.write().await;
        let metric = metrics.entry(query_hash.to_string()).or_insert_with(|| AggregatedQueryMetrics {
            query_hash: query_hash.to_string(),
            query_type,
            execution_count: 0,
            total_duration_ms: 0,
            avg_duration_ms: 0.0,
            min_duration_ms: u64::MAX,
            max_duration_ms: 0,
            last_executed_at: timestamp,
            error_count: 0,
        });

        metric.execution_count += 1;
        metric.total_duration_ms += duration_ms;
        metric.avg_duration_ms = metric.total_duration_ms as f64 / metric.execution_count as f64;
        metric.min_duration_ms = metric.min_duration_ms.min(duration_ms);
        metric.max_duration_ms = metric.max_duration_ms.max(duration_ms);
        metric.last_executed_at = timestamp;

        if has_error {
            metric.error_count += 1;
        }
    }

    /// 记录慢查询
    async fn record_slow_query(
        &self,
        query_hash: &str,
        query: &str,
        duration_ms: u64,
        timestamp: DateTime<Utc>,
        param_count: usize,
        error: Option<&str>,
    ) {
        let slow_query = SlowQuery {
            query_name: query_hash.to_string(),
            duration_ms,
            timestamp,
            query_details: if self.config.log_query_details {
                Some(query.to_string())
            } else {
                None
            },
        };

        let mut slow_queries = self.slow_queries.write().await;
        slow_queries.push(slow_query);

        // 限制慢查询记录数量
        if slow_queries.len() > 1000 {
            slow_queries.drain(0..100); // 删除最旧的100条记录
        }

        // 记录日志
        tracing::warn!(
            "Slow query detected: {} took {}ms, params: {}, error: {:?}",
            query_hash,
            duration_ms,
            param_count,
            error
        );
    }

    /// 更新统计信息
    async fn update_stats(&self, duration_ms: u64, is_slow: bool, query_type: QueryType) {
        let mut stats = self.stats.write().await;

        stats.total_queries += 1;
        if is_slow {
            stats.slow_queries += 1;
        }

        // 更新时间统计
        if duration_ms > stats.max_query_time_ms {
            stats.max_query_time_ms = duration_ms;
        }
        if duration_ms < stats.min_query_time_ms {
            stats.min_query_time_ms = duration_ms;
        }

        // 计算平均时间
        let total_time = stats.avg_query_time_ms * (stats.total_queries - 1) as f64 + duration_ms as f64;
        stats.avg_query_time_ms = total_time / stats.total_queries as f64;

        // 更新查询类型统计
        *stats.query_type_stats.entry(format!("{:?}", query_type)).or_insert(0) += 1;
    }

    /// 获取性能统计信息
    pub async fn get_stats(&self) -> PerformanceStats {
        self.stats.read().await.clone()
    }

    /// 获取聚合查询指标
    pub async fn get_aggregated_metrics(&self) -> Vec<AggregatedQueryMetrics> {
        let metrics = self.aggregated_metrics.read().await;
        metrics.values().cloned().collect()
    }

    /// 获取时序查询指标
    pub async fn get_time_series_metrics(&self, limit: Option<usize>) -> Vec<TimeSeriesQueryMetrics> {
        let metrics = self.time_series_metrics.read().await;
        let mut result: Vec<_> = metrics.iter().cloned().collect();
        result.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));

        if let Some(limit) = limit {
            result.truncate(limit);
        }

        result
    }

    /// 获取慢查询列表
    pub async fn get_slow_queries(&self, limit: Option<usize>) -> Vec<SlowQuery> {
        let slow_queries = self.slow_queries.read().await;
        let mut result: Vec<SlowQuery> = slow_queries.iter().cloned().collect();
        result.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));

        if let Some(limit) = limit {
            result.truncate(limit);
        }

        result
    }

    /// 清理过期的监控数据
    pub async fn cleanup_expired_data(&self) -> SigmaXResult<usize> {
        let cutoff_time = chrono::Utc::now() - chrono::Duration::seconds(self.config.retention_seconds as i64);

        // 清理时序指标
        let mut time_series_metrics = self.time_series_metrics.write().await;
        let original_len = time_series_metrics.len();
        time_series_metrics.retain(|m| m.timestamp > cutoff_time);
        let removed_time_series = original_len - time_series_metrics.len();

        // 清理慢查询
        let mut slow_queries = self.slow_queries.write().await;
        let original_slow_len = slow_queries.len();
        slow_queries.retain(|q| q.timestamp > cutoff_time);
        let removed_slow_queries = original_slow_len - slow_queries.len();

        let total_removed = removed_time_series + removed_slow_queries;

        if total_removed > 0 {
            tracing::debug!(
                "Cleaned up {} expired performance metrics ({} time series, {} slow queries)",
                total_removed, removed_time_series, removed_slow_queries
            );
        }

        Ok(total_removed)
    }

    /// 重置统计信息
    pub async fn reset_stats(&self) {
        let mut stats = self.stats.write().await;
        *stats = PerformanceStats {
            total_queries: 0,
            slow_queries: 0,
            avg_query_time_ms: 0.0,
            max_query_time_ms: 0,
            min_query_time_ms: u64::MAX,
            query_type_stats: HashMap::new(),
        };

        // 清理所有指标
        let mut aggregated_metrics = self.aggregated_metrics.write().await;
        aggregated_metrics.clear();

        let mut time_series_metrics = self.time_series_metrics.write().await;
        time_series_metrics.clear();

        let mut slow_queries = self.slow_queries.write().await;
        slow_queries.clear();

        tracing::info!("Performance statistics reset");
    }

    /// 获取配置
    pub fn config(&self) -> &PerformanceConfig {
        &self.config
    }

    /// 检查是否启用
    pub fn is_enabled(&self) -> bool {
        self.config.enabled
    }
}

impl Clone for PerformanceManager {
    fn clone(&self) -> Self {
        Self {
            connection_manager: Arc::clone(&self.connection_manager),
            config: self.config.clone(),
            aggregated_metrics: Arc::clone(&self.aggregated_metrics),
            time_series_metrics: Arc::clone(&self.time_series_metrics),
            slow_queries: Arc::clone(&self.slow_queries),
            stats: Arc::clone(&self.stats),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::managers::connection_manager::ConnectionManager;

    #[test]
    fn test_performance_config_default() {
        let config = PerformanceConfig::default();
        assert!(config.enabled);
        assert_eq!(config.slow_query_threshold_ms, 1000);
        assert_eq!(config.retention_seconds, 3600);
        assert!(!config.log_query_details);
        assert_eq!(config.max_records, 10000);
    }

    #[tokio::test]
    async fn test_performance_manager_creation() {
        let connection_manager = Arc::new(ConnectionManager::new_mock().unwrap());
        let perf_manager = PerformanceManager::with_defaults(connection_manager);
        
        assert!(perf_manager.is_enabled());
        
        // 测试克隆
        let _perf_manager_clone = perf_manager.clone();
    }

    #[tokio::test]
    async fn test_record_query() {
        let connection_manager = Arc::new(ConnectionManager::new_mock().unwrap());
        let perf_manager = PerformanceManager::with_defaults(connection_manager);
        
        // 记录一个查询
        perf_manager.record_query("test_query", Duration::from_millis(500), None).await;
        
        let stats = perf_manager.get_stats().await;
        assert_eq!(stats.total_queries, 1);
        assert_eq!(stats.slow_queries, 0);
        assert_eq!(stats.avg_query_time_ms, 500.0);
    }

    #[tokio::test]
    async fn test_slow_query_detection() {
        let connection_manager = Arc::new(ConnectionManager::new_mock().unwrap());
        let perf_manager = PerformanceManager::with_defaults(connection_manager);

        // 记录一个慢查询
        perf_manager.record_query("slow_query", Duration::from_millis(2000), None).await;

        let stats = perf_manager.get_stats().await;
        assert_eq!(stats.total_queries, 1);
        assert_eq!(stats.slow_queries, 1);

        let slow_queries = perf_manager.get_slow_queries(None).await;
        assert_eq!(slow_queries.len(), 1);
        // 查询名称现在使用哈希值，所以检查是否包含预期的哈希前缀
        assert!(slow_queries[0].query_name.starts_with("query_"));
        assert_eq!(slow_queries[0].duration_ms, 2000);
    }
}
