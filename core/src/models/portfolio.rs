//! SigmaX Core Portfolio Models
//!
//! ## 主要职责
//!
//! 1. **投资组合模型** - 核心投资组合概念
//!    - `Portfolio` - 统一的投资组合模型，管理资产配置和价值
//!    - 支持多资产、多策略的投资组合管理
//!
//! 2. **投资组合性能模型** - 性能分析和统计
//!    - `PortfolioPerformance` - 投资组合性能指标和统计数据
//!    - 收益率、回撤、夏普比率等关键指标
//!
//! 3. **投资组合业务逻辑** - 实用的业务方法
//!    - 资产配置管理和再平衡
//!    - 性能计算和风险评估
//!    - 价值计算和盈亏分析
//!
//! ## 设计原则
//!
//! - **统一模型**: 使用单一Portfolio模型，避免多个分散的定义
//! - **资产聚合**: 支持多资产、多交易所的统一管理
//! - **类型安全**: 使用强类型的ID、Amount等类型
//! - **业务友好**: 提供实用的投资组合管理和分析方法

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use validator::{Validate, ValidationError};
use rust_decimal::Decimal;
use rust_decimal::prelude::ToPrimitive;
use std::collections::HashMap;

use crate::{
    types::*,
    enums::*,
    error::*,
    models::{Balance, Position},
    SigmaXResult,
};

// ============================================================================
// 投资组合模型 - 核心投资组合概念
// ============================================================================

/// 投资组合 - 统一的投资组合模型
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Portfolio {
    pub portfolio_id: PortfolioId,
    
    #[validate(length(min = 1, message = "Portfolio name cannot be empty"))]
    pub name: String,
    
    pub description: Option<String>,
    pub currency: String, // 基准货币
    
    #[validate(custom = "validate_positive_amount")]
    pub initial_capital: Amount,
    pub current_value: Amount,
    
    pub balances: Vec<Balance>,
    pub positions: Vec<Position>,
    
    pub strategy_ids: Vec<StrategyId>,
    pub exchange_ids: Vec<ExchangeId>,
    
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl Portfolio {
    /// 创建新的投资组合
    pub fn new(
        name: String,
        currency: String,
        initial_capital: Amount,
    ) -> Self {
        Self {
            portfolio_id: PortfolioId::new(),
            name,
            description: None,
            currency,
            initial_capital,
            current_value: initial_capital,
            balances: Vec::new(),
            positions: Vec::new(),
            strategy_ids: Vec::new(),
            exchange_ids: Vec::new(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    /// 添加余额
    pub fn add_balance(&mut self, balance: Balance) {
        // 如果已存在相同资产和交易所的余额，则更新
        if let Some(existing) = self.balances.iter_mut()
            .find(|b| b.asset == balance.asset && b.exchange_id == balance.exchange_id) {
            *existing = balance;
        } else {
            self.balances.push(balance);
        }
        self.updated_at = Utc::now();
    }

    /// 添加持仓
    pub fn add_position(&mut self, position: Position) {
        // 如果已存在相同交易对和方向的持仓，则更新
        if let Some(existing) = self.positions.iter_mut()
            .find(|p| p.trading_pair == position.trading_pair 
                   && p.side == position.side 
                   && p.exchange_id == position.exchange_id) {
            *existing = position;
        } else {
            self.positions.push(position);
        }
        self.updated_at = Utc::now();
    }

    /// 获取特定资产的总余额
    pub fn get_total_balance(&self, asset: &str) -> Amount {
        self.balances.iter()
            .filter(|b| b.asset == asset)
            .map(|b| b.total())
            .sum()
    }

    /// 获取特定资产的可用余额
    pub fn get_available_balance(&self, asset: &str) -> Amount {
        self.balances.iter()
            .filter(|b| b.asset == asset)
            .map(|b| b.free)
            .sum()
    }

    /// 计算总盈亏
    pub fn total_pnl(&self) -> Amount {
        self.current_value - self.initial_capital
    }

    /// 计算收益率
    pub fn return_rate(&self) -> Percentage {
        if self.initial_capital.is_zero() {
            return Percentage::ZERO;
        }
        (self.total_pnl() / self.initial_capital) * Percentage::from(100)
    }

    /// 计算已实现盈亏
    pub fn realized_pnl(&self) -> Amount {
        self.positions.iter()
            .map(|p| p.realized_pnl)
            .sum()
    }

    /// 计算未实现盈亏
    pub fn unrealized_pnl(&self) -> Amount {
        self.positions.iter()
            .map(|p| p.unrealized_pnl)
            .sum()
    }

    /// 获取资产分布
    pub fn asset_allocation(&self) -> HashMap<String, Percentage> {
        let mut allocation = HashMap::new();
        
        if self.current_value.is_zero() {
            return allocation;
        }

        for balance in &self.balances {
            let asset_value = balance.total(); // 简化：假设价值等于数量
            let percentage = (asset_value / self.current_value) * Percentage::from(100);
            *allocation.entry(balance.asset.clone()).or_insert(Percentage::ZERO) += percentage;
        }

        allocation
    }

    /// 获取交易所分布
    pub fn exchange_allocation(&self) -> HashMap<ExchangeId, Percentage> {
        let mut allocation = HashMap::new();
        
        if self.current_value.is_zero() {
            return allocation;
        }

        for balance in &self.balances {
            let asset_value = balance.total();
            let percentage = (asset_value / self.current_value) * Percentage::from(100);
            *allocation.entry(balance.exchange_id).or_insert(Percentage::ZERO) += percentage;
        }

        allocation
    }

    /// 添加策略ID
    pub fn add_strategy(&mut self, strategy_id: StrategyId) {
        if !self.strategy_ids.contains(&strategy_id) {
            self.strategy_ids.push(strategy_id);
            self.updated_at = Utc::now();
        }
    }

    /// 移除策略ID
    pub fn remove_strategy(&mut self, strategy_id: &StrategyId) {
        self.strategy_ids.retain(|id| id != strategy_id);
        self.updated_at = Utc::now();
    }

    /// 添加交易所ID
    pub fn add_exchange(&mut self, exchange_id: ExchangeId) {
        if !self.exchange_ids.contains(&exchange_id) {
            self.exchange_ids.push(exchange_id);
            self.updated_at = Utc::now();
        }
    }

    /// 更新投资组合价值
    pub fn update_value(&mut self, new_value: Amount) {
        self.current_value = new_value;
        self.updated_at = Utc::now();
    }

    /// 检查投资组合是否盈利
    pub fn is_profitable(&self) -> bool {
        self.total_pnl() > Amount::ZERO
    }

    /// 获取显示用的投资组合摘要
    pub fn summary(&self) -> String {
        format!(
            "{} - 价值: {:.2} {} (收益率: {:.2}%)",
            self.name,
            self.current_value,
            self.currency,
            self.return_rate()
        )
    }

    /// 执行完整的投资组合验证
    pub fn validate_complete(&self) -> SigmaXResult<()> {
        // 首先执行基础验证（通过validator derive宏）
        if let Err(validation_errors) = <Self as Validate>::validate(self) {
            return Err(SigmaXError::from(validation_errors));
        }

        // 跨字段验证：货币不能为空
        if self.currency.is_empty() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::Required,
                "currency",
                "Portfolio currency cannot be empty"
            ));
        }

        // 跨字段验证：当前价值不能为负数
        if self.current_value < Amount::ZERO {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "current_value",
                "Portfolio current value cannot be negative"
            ));
        }

        Ok(())
    }
}

// ============================================================================
// 投资组合性能模型 - 性能分析和统计
// ============================================================================

/// 投资组合性能统计 - 投资组合表现的关键指标
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct PortfolioPerformance {
    pub portfolio_id: PortfolioId,

    pub total_return: Percentage,
    pub annualized_return: Percentage,
    pub max_drawdown: Percentage,
    pub current_drawdown: Percentage,

    pub sharpe_ratio: Decimal,
    pub sortino_ratio: Decimal,
    pub calmar_ratio: Decimal,

    pub volatility: Percentage,
    pub beta: Decimal,
    pub alpha: Percentage,

    pub total_trades: u64,
    pub winning_trades: u64,
    pub losing_trades: u64,
    pub win_rate: Percentage,

    pub profit_factor: Decimal,
    pub total_profit: Amount,
    pub total_loss: Amount,
    pub largest_win: Amount,
    pub largest_loss: Amount,

    pub start_date: DateTime<Utc>,
    pub end_date: DateTime<Utc>,
    pub last_updated: DateTime<Utc>,
}

impl PortfolioPerformance {
    /// 创建新的投资组合性能统计
    pub fn new(portfolio_id: PortfolioId, start_date: DateTime<Utc>) -> Self {
        Self {
            portfolio_id,
            total_return: Percentage::ZERO,
            annualized_return: Percentage::ZERO,
            max_drawdown: Percentage::ZERO,
            current_drawdown: Percentage::ZERO,
            sharpe_ratio: Decimal::ZERO,
            sortino_ratio: Decimal::ZERO,
            calmar_ratio: Decimal::ZERO,
            volatility: Percentage::ZERO,
            beta: Decimal::ONE,
            alpha: Percentage::ZERO,
            total_trades: 0,
            winning_trades: 0,
            losing_trades: 0,
            win_rate: Percentage::ZERO,
            profit_factor: Decimal::ZERO,
            total_profit: Amount::ZERO,
            total_loss: Amount::ZERO,
            largest_win: Amount::ZERO,
            largest_loss: Amount::ZERO,
            start_date,
            end_date: Utc::now(),
            last_updated: Utc::now(),
        }
    }





}

// ============================================================================
// 验证函数 - 用于模型验证
// ============================================================================

/// 验证金额为正数
fn validate_positive_amount(amount: &Amount) -> Result<(), ValidationError> {
    if *amount <= Amount::ZERO {
        return Err(ValidationError::new("Initial capital must be positive"));
    }
    Ok(())
}

// ============================================================================
// Tests
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal::Decimal;

    #[test]
    fn test_portfolio_creation() {
        let portfolio = Portfolio::new(
            "Test Portfolio".to_string(),
            "USDT".to_string(),
            Decimal::from(10000),
        );

        assert_eq!(portfolio.name, "Test Portfolio");
        assert_eq!(portfolio.currency, "USDT");
        assert_eq!(portfolio.initial_capital, Decimal::from(10000));
        assert_eq!(portfolio.current_value, Decimal::from(10000));
        assert!(portfolio.balances.is_empty());
        assert!(portfolio.positions.is_empty());
    }

    #[test]
    fn test_portfolio_pnl_calculations() {
        let mut portfolio = Portfolio::new(
            "Test Portfolio".to_string(),
            "USDT".to_string(),
            Decimal::from(10000),
        );

        // 更新价值
        portfolio.update_value(Decimal::from(12000));

        // 计算盈亏和收益率
        assert_eq!(portfolio.total_pnl(), Decimal::from(2000));
        assert_eq!(portfolio.return_rate(), Decimal::from(20)); // 20%
        assert!(portfolio.is_profitable());

        // 亏损情况
        portfolio.update_value(Decimal::from(8000));
        assert_eq!(portfolio.total_pnl(), Decimal::from(-2000));
        assert_eq!(portfolio.return_rate(), Decimal::from(-20)); // -20%
        assert!(!portfolio.is_profitable());
    }

    #[test]
    fn test_portfolio_balance_management() {
        let mut portfolio = Portfolio::new(
            "Test Portfolio".to_string(),
            "USDT".to_string(),
            Decimal::from(10000),
        );

        // 添加余额
        let balance1 = Balance::new(
            ExchangeId::Binance,
            "USDT".to_string(),
            Decimal::from(5000),
            Decimal::from(0),
        );
        portfolio.add_balance(balance1);

        let balance2 = Balance::new(
            ExchangeId::Binance,
            "BTC".to_string(),
            Decimal::from(1),
            Decimal::from(0),
        );
        portfolio.add_balance(balance2);

        // 检查余额
        assert_eq!(portfolio.balances.len(), 2);
        assert_eq!(portfolio.get_total_balance("USDT"), Decimal::from(5000));
        assert_eq!(portfolio.get_total_balance("BTC"), Decimal::from(1));
        assert_eq!(portfolio.get_available_balance("USDT"), Decimal::from(5000));
    }

    #[test]
    fn test_portfolio_strategy_management() {
        let mut portfolio = Portfolio::new(
            "Test Portfolio".to_string(),
            "USDT".to_string(),
            Decimal::from(10000),
        );

        let strategy_id = StrategyId::new();

        // 添加策略
        portfolio.add_strategy(strategy_id);
        assert_eq!(portfolio.strategy_ids.len(), 1);
        assert!(portfolio.strategy_ids.contains(&strategy_id));

        // 重复添加不会增加
        portfolio.add_strategy(strategy_id);
        assert_eq!(portfolio.strategy_ids.len(), 1);

        // 移除策略
        portfolio.remove_strategy(&strategy_id);
        assert_eq!(portfolio.strategy_ids.len(), 0);
    }

    #[test]
    fn test_portfolio_summary() {
        let mut portfolio = Portfolio::new(
            "My Portfolio".to_string(),
            "USDT".to_string(),
            Decimal::from(10000),
        );

        portfolio.update_value(Decimal::from(11500));
        let summary = portfolio.summary();
        assert!(summary.contains("My Portfolio"));
        assert!(summary.contains("11500"));
        assert!(summary.contains("15.00%"));
    }

    #[test]
    fn test_portfolio_validation() {
        let portfolio = Portfolio::new(
            "Test Portfolio".to_string(),
            "USDT".to_string(),
            Decimal::from(10000),
        );

        // 正常投资组合应该通过验证
        let result = portfolio.validate_complete();
        assert!(result.is_ok());
    }

    #[test]
    fn test_portfolio_performance_creation() {
        let performance = PortfolioPerformance::new(
            PortfolioId::new(),
            Utc::now(),
        );

        assert_eq!(performance.total_trades, 0);
        assert_eq!(performance.winning_trades, 0);
        assert_eq!(performance.losing_trades, 0);
        assert_eq!(performance.win_rate, Decimal::ZERO);
        assert_eq!(performance.total_profit, Decimal::ZERO);
        assert_eq!(performance.total_loss, Decimal::ZERO);
    }

    #[test]
    fn test_portfolio_performance_updates() {
        let mut performance = PortfolioPerformance::new(
            PortfolioId::new(),
            Utc::now(),
        );

        // 添加盈利交易
        performance.update_trade_stats(Decimal::from(100));
        assert_eq!(performance.total_trades, 1);
        assert_eq!(performance.winning_trades, 1);
        assert_eq!(performance.total_profit, Decimal::from(100));
        assert_eq!(performance.largest_win, Decimal::from(100));

        // 添加亏损交易
        performance.update_trade_stats(Decimal::from(-50));
        assert_eq!(performance.total_trades, 2);
        assert_eq!(performance.losing_trades, 1);
        assert_eq!(performance.total_loss, Decimal::from(50));
        assert_eq!(performance.largest_loss, Decimal::from(50));

        // 检查胜率
        assert_eq!(performance.win_rate, Decimal::from(50)); // 50%

        // 检查盈亏比
        assert_eq!(performance.profit_factor, Decimal::from(2)); // 100/50 = 2
    }

    #[test]
    fn test_portfolio_performance_calculations() {
        let mut performance = PortfolioPerformance::new(
            PortfolioId::new(),
            Utc::now(),
        );

        // 添加多笔交易
        performance.update_trade_stats(Decimal::from(200)); // 盈利
        performance.update_trade_stats(Decimal::from(100)); // 盈利
        performance.update_trade_stats(Decimal::from(-50)); // 亏损

        // 检查平均值
        assert_eq!(performance.average_profit(), Decimal::from(150)); // (200+100)/2
        assert_eq!(performance.average_loss(), Decimal::from(50));    // 50/1

        // 检查净盈亏
        assert_eq!(performance.net_profit(), Decimal::from(250)); // 300-50

        // 检查是否盈利
        assert!(performance.is_profitable());
    }

    #[test]
    fn test_portfolio_performance_grading() {
        let mut performance = PortfolioPerformance::new(
            PortfolioId::new(),
            Utc::now(),
        );

        // 设置优秀的性能指标
        performance.update_returns(Decimal::from(30), Decimal::from(25)); // 30% 总收益, 25% 年化
        performance.win_rate = Decimal::from(75);     // 75% 胜率
        performance.update_risk_metrics(
            Decimal::from(2),      // 夏普比率 2.0
            Decimal::from(2),      // 索提诺比率 2.0
            Decimal::from(15),     // 15% 波动率
            Decimal::from(1),      // Beta 1.0
            Decimal::from(5),      // 5% Alpha
        );
        performance.update_drawdown(Decimal::from(8)); // 8% 最大回撤

        let grade = performance.performance_grade();
        assert!(matches!(grade.as_str(), "A+" | "A" | "B+"));
    }

    #[test]
    fn test_portfolio_performance_summary() {
        let mut performance = PortfolioPerformance::new(
            PortfolioId::new(),
            Utc::now(),
        );

        performance.update_returns(Decimal::from(15), Decimal::from(12));
        performance.update_risk_metrics(
            Decimal::from(1),
            Decimal::from(1),
            Decimal::from(20),
            Decimal::from(1),
            Decimal::from(2),
        );
        performance.update_drawdown(Decimal::from(10));
        performance.win_rate = Decimal::from(60);

        let summary = performance.performance_summary();
        assert!(summary.contains("15.00%"));
        assert!(summary.contains("1.00"));
        assert!(summary.contains("10.00%"));
        assert!(summary.contains("60.0%"));
    }
}
