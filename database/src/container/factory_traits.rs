//! Repository工厂trait定义
//! 
//! 定义抽象的工厂接口，实现真正的依赖倒置

use sigmax_core::{SigmaXResult, OrderId, StrategyId, EventId, TradingPair, EventType};
use sigmax_core::{Order, Trade, StrategyState, StoredEvent, OrderStatus, StrategyInfo};
use crate::repositories::traits::risk_repository::{RiskCheckRecord, RiskRuleRecord};
use uuid::Uuid;

// TradeId 类型别名
pub type TradeId = Uuid;
use std::sync::Arc;
use async_trait::async_trait;

/// 通用Repository工厂trait
/// 
/// 所有Repository工厂都应该实现这个trait
#[async_trait]
pub trait RepositoryFactory: Send + Sync {
    /// 工厂类型名称
    fn factory_type(&self) -> &'static str;
    
    /// 健康检查
    async fn health_check(&self) -> SigmaXResult<()>;
}

/// 订单Repository工厂
#[async_trait]
pub trait OrderRepositoryFactory: RepositoryFactory {
    /// 保存订单
    async fn save_order(&self, order: &Order) -> SigmaXResult<()>;

    /// 根据ID查找订单
    async fn find_order_by_id(&self, order_id: OrderId) -> SigmaXResult<Option<Order>>;

    /// 查找所有订单
    async fn find_all_orders(&self) -> SigmaXResult<Vec<Order>>;

    /// 根据状态查找订单
    async fn find_orders_by_status(&self, status: OrderStatus) -> SigmaXResult<Vec<Order>>;

    /// 根据策略查找订单
    async fn find_orders_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Vec<Order>>;
}

/// 交易Repository工厂
#[async_trait]
pub trait TradeRepositoryFactory: RepositoryFactory {
    /// 保存交易
    async fn save_trade(&self, trade: &Trade) -> SigmaXResult<()>;

    /// 查找所有交易
    async fn find_all_trades(&self) -> SigmaXResult<Vec<Trade>>;

    /// 根据交易对查找交易
    async fn find_trades_by_pair(&self, pair: &TradingPair) -> SigmaXResult<Vec<Trade>>;

    /// 根据时间范围查找交易
    async fn find_trades_by_time_range(
        &self,
        start: chrono::DateTime<chrono::Utc>,
        end: chrono::DateTime<chrono::Utc>
    ) -> SigmaXResult<Vec<Trade>>;
}

/// 策略Repository工厂
#[async_trait]
pub trait StrategyRepositoryFactory: RepositoryFactory {
    /// 保存策略状态
    async fn save_strategy_state(&self, strategy_id: StrategyId, state: &StrategyState) -> SigmaXResult<()>;

    /// 根据ID查找策略状态
    async fn find_strategy_state_by_id(&self, strategy_id: StrategyId) -> SigmaXResult<Option<StrategyState>>;

    /// 查找所有策略
    async fn find_all_strategies(&self) -> SigmaXResult<Vec<StrategyInfo>>;
}

/// 事件Repository工厂
#[async_trait]
pub trait EventRepositoryFactory: RepositoryFactory {
    /// 保存事件
    async fn save_event(&self, event: &StoredEvent) -> SigmaXResult<EventId>;
    
    /// 获取事件
    async fn get_event(&self, event_id: EventId) -> SigmaXResult<Option<StoredEvent>>;
    
    /// 获取事件列表
    async fn get_events(&self, offset: usize, limit: usize) -> SigmaXResult<Vec<StoredEvent>>;
    
    /// 根据类型获取事件
    async fn get_events_by_type(&self, event_type: EventType, limit: usize) -> SigmaXResult<Vec<StoredEvent>>;
    
    /// 标记事件已处理
    async fn mark_event_processed(&self, event_id: EventId) -> SigmaXResult<()>;
}

/// 风控Repository工厂
#[async_trait]
pub trait RiskRepositoryFactory: RepositoryFactory {
    /// 保存风控检查记录
    async fn save_risk_check(&self, record: &RiskCheckRecord) -> SigmaXResult<()>;

    /// 获取风控检查记录
    async fn get_risk_checks(&self, limit: usize) -> SigmaXResult<Vec<RiskCheckRecord>>;

    /// 保存风控规则
    async fn save_risk_rule(&self, rule: &RiskRuleRecord) -> SigmaXResult<()>;

    /// 获取风控规则
    async fn get_risk_rules(&self) -> SigmaXResult<Vec<RiskRuleRecord>>;
}

/// 组合Repository工厂
/// 
/// 提供所有Repository的统一访问接口
#[async_trait]
pub trait CompositeRepositoryFactory: Send + Sync {
    /// 获取订单Repository工厂
    fn order_factory(&self) -> Arc<dyn OrderRepositoryFactory>;
    
    /// 获取交易Repository工厂
    fn trade_factory(&self) -> Arc<dyn TradeRepositoryFactory>;
    
    /// 获取策略Repository工厂
    fn strategy_factory(&self) -> Arc<dyn StrategyRepositoryFactory>;
    
    /// 获取事件Repository工厂
    fn event_factory(&self) -> Arc<dyn EventRepositoryFactory>;
    
    /// 获取风控Repository工厂
    fn risk_factory(&self) -> Arc<dyn RiskRepositoryFactory>;
    
    /// 健康检查所有工厂
    async fn health_check_all(&self) -> SigmaXResult<()> {
        self.order_factory().health_check().await?;
        self.trade_factory().health_check().await?;
        self.strategy_factory().health_check().await?;
        self.event_factory().health_check().await?;
        self.risk_factory().health_check().await?;
        Ok(())
    }
}

/// Repository工厂构建器trait
pub trait RepositoryFactoryBuilder: Send + Sync {
    type Factory: CompositeRepositoryFactory;
    
    /// 构建工厂
    fn build(self) -> SigmaXResult<Self::Factory>;
}

/// 工厂配置trait
pub trait FactoryConfig: Send + Sync + Clone {
    /// 验证配置
    fn validate(&self) -> SigmaXResult<()>;
    
    /// 获取工厂类型
    fn factory_type(&self) -> &'static str;
}

/// Mock工厂配置
#[derive(Debug, Clone)]
pub struct MockFactoryConfig {
    /// 是否模拟错误
    pub simulate_errors: bool,
    /// 错误概率 (0.0 - 1.0)
    pub error_probability: f64,
    /// 延迟模拟 (毫秒)
    pub delay_ms: u64,
    /// 是否启用详细日志
    pub verbose_logging: bool,
}

impl Default for MockFactoryConfig {
    fn default() -> Self {
        Self {
            simulate_errors: false,
            error_probability: 0.0,
            delay_ms: 0,
            verbose_logging: false,
        }
    }
}

impl FactoryConfig for MockFactoryConfig {
    fn validate(&self) -> SigmaXResult<()> {
        if self.error_probability < 0.0 || self.error_probability > 1.0 {
            return Err(sigmax_core::SigmaXError::validation(
                sigmax_core::ValidationErrorCode::OutOfRange,
                "error_probability",
                "Error probability must be between 0.0 and 1.0"
            ));
        }
        Ok(())
    }
    
    fn factory_type(&self) -> &'static str {
        "mock"
    }
}

/// SQLx工厂配置
#[derive(Debug, Clone)]
pub struct SqlxFactoryConfig {
    /// 数据库管理器
    pub database_manager: Option<Arc<crate::managers::DatabaseManager>>,
    /// 是否启用事务
    pub enable_transactions: bool,
    /// 查询超时时间（秒）
    pub query_timeout_seconds: u64,
}

impl Default for SqlxFactoryConfig {
    fn default() -> Self {
        Self {
            database_manager: None,
            enable_transactions: true,
            query_timeout_seconds: 30,
        }
    }
}

impl FactoryConfig for SqlxFactoryConfig {
    fn validate(&self) -> SigmaXResult<()> {
        if self.database_manager.is_none() {
            return Err(sigmax_core::SigmaXError::validation(
                sigmax_core::ValidationErrorCode::Required,
                "database_manager",
                "Database manager is required for SQLx factory"
            ));
        }
        Ok(())
    }
    
    fn factory_type(&self) -> &'static str {
        "sqlx"
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_mock_factory_config() {
        let config = MockFactoryConfig::default();
        assert!(config.validate().is_ok());
        assert_eq!(config.factory_type(), "mock");
        
        let invalid_config = MockFactoryConfig {
            error_probability: 1.5,
            ..Default::default()
        };
        assert!(invalid_config.validate().is_err());
    }

    #[test]
    fn test_sqlx_factory_config() {
        let config = SqlxFactoryConfig::default();
        assert!(config.validate().is_err()); // 因为没有database_manager
        assert_eq!(config.factory_type(), "sqlx");
    }
}
