//! 统一Repository基础接口
//!
//! 定义所有Repository的统一基础接口，解决当前接口不一致问题

use async_trait::async_trait;
use sigmax_core::SigmaXResult;
use std::fmt::Debug;
use serde::{Serialize, Deserialize};

/// Repository基础接口
///
/// 所有Repository都实现这个基础接口，提供标准的CRUD操作
#[async_trait]
pub trait Repository<T, ID>: Send + Sync
where
    T: Send + Sync + Clone + Debug,
    ID: Send + Sync + Clone + Debug + PartialEq,
{
    // ============================================================================
    // 基础CRUD操作 - 统一命名规范
    // ============================================================================
    
    /// 保存实体（插入或更新）
    /// 
    /// # Arguments
    /// * `entity` - 要保存的实体
    /// 
    /// # Returns
    /// 返回保存后的实体ID
    async fn save(&self, entity: &T) -> SigmaXResult<ID>;
    
    /// 根据ID查找实体
    /// 
    /// # Arguments
    /// * `id` - 实体ID
    /// 
    /// # Returns
    /// 返回找到的实体，如果不存在则返回None
    async fn find_by_id(&self, id: ID) -> SigmaXResult<Option<T>>;
    
    /// 查找所有实体
    /// 
    /// # Returns
    /// 返回所有实体的列表
    async fn find_all(&self) -> SigmaXResult<Vec<T>>;
    
    /// 更新实体
    /// 
    /// # Arguments
    /// * `entity` - 要更新的实体
    async fn update(&self, entity: &T) -> SigmaXResult<()>;
    
    /// 删除实体
    /// 
    /// # Arguments
    /// * `id` - 要删除的实体ID
    async fn delete(&self, id: ID) -> SigmaXResult<()>;
    
    /// 检查实体是否存在
    /// 
    /// # Arguments
    /// * `id` - 实体ID
    /// 
    /// # Returns
    /// 如果实体存在返回true，否则返回false
    async fn exists(&self, id: ID) -> SigmaXResult<bool>;
    
    // ============================================================================
    // 批量操作
    // ============================================================================
    
    /// 批量保存实体
    /// 
    /// # Arguments
    /// * `entities` - 要保存的实体列表
    /// 
    /// # Returns
    /// 返回保存后的实体ID列表
    async fn save_batch(&self, entities: &[T]) -> SigmaXResult<Vec<ID>>;
    
    /// 批量删除实体
    /// 
    /// # Arguments
    /// * `ids` - 要删除的实体ID列表
    async fn delete_batch(&self, ids: &[ID]) -> SigmaXResult<()>;
    
    // ============================================================================
    // 查询统计
    // ============================================================================
    
    /// 获取实体总数
    /// 
    /// # Returns
    /// 返回实体总数
    async fn count(&self) -> SigmaXResult<u64>;
    
    /// 分页查询实体
    /// 
    /// # Arguments
    /// * `offset` - 偏移量
    /// * `limit` - 限制数量
    /// 
    /// # Returns
    /// 返回分页查询结果
    async fn find_with_pagination(&self, offset: u64, limit: u64) -> SigmaXResult<PaginationResult<T>>;
}

/// 分页查询结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationResult<T> {
    /// 数据列表
    pub items: Vec<T>,
    /// 总数量
    pub total: u64,
    /// 偏移量
    pub offset: u64,
    /// 限制数量
    pub limit: u64,
    /// 是否有下一页
    pub has_next: bool,
}

impl<T> PaginationResult<T> {
    pub fn new(items: Vec<T>, total: u64, offset: u64, limit: u64) -> Self {
        let has_next = offset + limit < total;
        Self {
            items,
            total,
            offset,
            limit,
            has_next,
        }
    }
}

/// 查询过滤器基础接口
pub trait QueryFilter: Send + Sync + Debug + Clone {
    /// 验证过滤器参数
    fn validate(&self) -> SigmaXResult<()>;
    
    /// 获取过滤器描述
    fn description(&self) -> String;
}

/// 排序配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SortConfig {
    /// 排序字段
    pub field: String,
    /// 排序方向
    pub direction: SortDirection,
}

/// 排序方向
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SortDirection {
    /// 升序
    Asc,
    /// 降序
    Desc,
}

impl Default for SortDirection {
    fn default() -> Self {
        SortDirection::Asc
    }
}
