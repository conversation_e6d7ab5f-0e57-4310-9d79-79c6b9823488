//! 错误处理服务实现

use std::sync::Arc;
use axum::http::StatusCode;
use axum::response::{Response, IntoResponse};
use axum::body::Body;
use serde_json::json;
use tracing::{error, warn, info};

use crate::gateway::RequestTracer;
use crate::error::ApiError;

/// 错误处理服务
pub struct ErrorHandler {
    request_tracer: Arc<dyn RequestTracer>,
}

impl ErrorHandler {
    /// 创建新的错误处理器
    pub fn new(request_tracer: Arc<dyn RequestTracer>) -> Self {
        Self { request_tracer }
    }
    
    /// 处理业务错误
    pub async fn handle_business_error(&self, error: &dyn std::error::Error) -> Response<Body> {
        error!("💥 业务错误: {}", error);
        
        let status = StatusCode::BAD_REQUEST;
        let error_response = json!({
            "error": {
                "type": "BusinessError",
                "message": error.to_string(),
                "timestamp": chrono::Utc::now().to_rfc3339(),
            }
        });
        
        Response::builder()
            .status(status)
            .header("Content-Type", "application/json")
            .body(Body::from(error_response.to_string()))
            .unwrap()
    }
    
    /// 处理验证错误
    pub async fn handle_validation_error(&self, error: &dyn std::error::Error) -> Response<Body> {
        warn!("⚠️ 验证错误: {}", error);
        
        let status = StatusCode::UNPROCESSABLE_ENTITY;
        let error_response = json!({
            "error": {
                "type": "ValidationError",
                "message": error.to_string(),
                "timestamp": chrono::Utc::now().to_rfc3339(),
            }
        });
        
        Response::builder()
            .status(status)
            .header("Content-Type", "application/json")
            .body(Body::from(error_response.to_string()))
            .unwrap()
    }
    
    /// 处理系统错误
    pub async fn handle_system_error(&self, error: &dyn std::error::Error) -> Response<Body> {
        error!("💥 系统错误: {}", error);
        
        let status = StatusCode::INTERNAL_SERVER_ERROR;
        let error_response = json!({
            "error": {
                "type": "SystemError",
                "message": "系统内部错误，请稍后重试",
                "timestamp": chrono::Utc::now().to_rfc3339(),
            }
        });
        
        Response::builder()
            .status(status)
            .header("Content-Type", "application/json")
            .body(Body::from(error_response.to_string()))
            .unwrap()
    }
    
    /// 处理认证错误
    pub async fn handle_auth_error(&self, error: &dyn std::error::Error) -> Response<Body> {
        warn!("🔐 认证错误: {}", error);
        
        let status = StatusCode::UNAUTHORIZED;
        let error_response = json!({
            "error": {
                "type": "AuthenticationError",
                "message": error.to_string(),
                "timestamp": chrono::Utc::now().to_rfc3339(),
            }
        });
        
        Response::builder()
            .status(status)
            .header("Content-Type", "application/json")
            .body(Body::from(error_response.to_string()))
            .unwrap()
    }
    
    /// 处理限流错误
    pub async fn handle_rate_limit_error(&self, error: &dyn std::error::Error) -> Response<Body> {
        info!("🚦 限流错误: {}", error);
        
        let status = StatusCode::TOO_MANY_REQUESTS;
        let error_response = json!({
            "error": {
                "type": "RateLimitError",
                "message": "请求过于频繁，请稍后重试",
                "timestamp": chrono::Utc::now().to_rfc3339(),
            }
        });
        
        Response::builder()
            .status(status)
            .header("Content-Type", "application/json")
            .body(Body::from(error_response.to_string()))
            .unwrap()
    }
    
    /// 统一错误处理入口
    pub async fn handle_error(&self, error: &dyn std::error::Error) -> Response<Body> {
        // 根据错误类型分发到不同的处理器
        if error.to_string().contains("validation") || error.to_string().contains("Validation") {
            self.handle_validation_error(error).await
        } else if error.to_string().contains("auth") || error.to_string().contains("Auth") {
            self.handle_auth_error(error).await
        } else if error.to_string().contains("rate limit") || error.to_string().contains("RateLimit") {
            self.handle_rate_limit_error(error).await
        } else if error.to_string().contains("business") || error.to_string().contains("Business") {
            self.handle_business_error(error).await
        } else {
            // 默认作为系统错误处理
            self.handle_system_error(error).await
        }
    }
}

/// 错误类型枚举
#[derive(Debug, Clone, PartialEq)]
pub enum ErrorType {
    Validation,
    Authentication,
    Authorization,
    Business,
    System,
    RateLimit,
    NotFound,
    Unknown,
}

/// 错误严重程度
#[derive(Debug, Clone, PartialEq)]
pub enum ErrorSeverity {
    Low,      // 低严重程度，如验证错误
    Medium,   // 中等严重程度，如业务错误
    High,     // 高严重程度，如系统错误
    Critical, // 严重错误，如认证失败
}
