//! 风险分析子模块
//!
//! 专注风险分析和计算工具，包括：
//! - 指标计算器
//! - 压力测试
//! - 蒙特卡洛模拟
//! - 相关性模型
//! - 波动率模型
//! - 风险报告

// ============================================================================
// 计算器层 - 分析计算工具
// ============================================================================

/// 分析计算器
pub mod calculators;

// ============================================================================
// 模型层 - 分析模型
// ============================================================================

/// 分析模型
pub mod models;

// ============================================================================
// 报告层 - 分析报告
// ============================================================================

/// 分析报告
pub mod reports;

// ============================================================================
// 公共导出
// ============================================================================

// 计算器导出
pub use calculators::{
    MetricsCalculator, MetricsCalculatorConfig, MetricsCalculationResult,
    StressTester, StressTesterConfig, StressTestResult,
    MonteCarloSimulator, MonteCarloConfig, MonteCarloResult,
};

// 模型导出
pub use models::{
    CorrelationModel, CorrelationResult,
    VolatilityModel, VolatilityResult,
};

// 报告导出
pub use reports::{
    RiskReporter, RiskReporterConfig, RiskReport, ReportFormat,
};

// ============================================================================
// 风险分析器实现
// ============================================================================

/// 风险分析器实现
pub struct RiskAnalyzerImpl {
    metrics_calculator: MetricsCalculator,
    stress_tester: StressTester,
    monte_carlo_simulator: MonteCarloSimulator,
    risk_reporter: RiskReporter,
}

impl RiskAnalyzerImpl {
    /// 创建新的风险分析器
    pub fn new(
        metrics_calculator: MetricsCalculator,
        stress_tester: StressTester,
        monte_carlo_simulator: MonteCarloSimulator,
        risk_reporter: RiskReporter,
    ) -> Self {
        Self {
            metrics_calculator,
            stress_tester,
            monte_carlo_simulator,
            risk_reporter,
        }
    }
    
    /// 计算风险指标
    pub async fn calculate_metrics(&self, context: &sigmax_interfaces::risk::RiskContext) -> MetricsCalculationResult {
        // 直接使用接口的 RiskContext
        self.metrics_calculator.calculate_basic_metrics(context).await
    }

    /// 计算高级指标
    pub async fn calculate_advanced_metrics(&self, context: &sigmax_interfaces::risk::RiskContext) -> MetricsCalculationResult {
        self.metrics_calculator.calculate_advanced_metrics(context).await
    }

    /// 运行压力测试
    pub async fn run_stress_test(&self, portfolio: &sigmax_interfaces::Portfolio) -> StressTestResult {
        self.stress_tester.run_stress_test(portfolio, None).await
    }

    /// 运行蒙特卡洛模拟
    pub async fn run_monte_carlo(&mut self, portfolio: &sigmax_interfaces::Portfolio) -> MonteCarloResult {
        self.monte_carlo_simulator.run_simulation(portfolio, None).await
    }

    /// 生成风险报告
    pub async fn generate_report(
        &self,
        portfolio: &sigmax_interfaces::Portfolio,
        context: &sigmax_interfaces::risk::RiskContext,
        alerts: &[sigmax_interfaces::risk::RiskAlert],
        _format: ReportFormat,
    ) -> RiskReport {
        // 收集所有分析结果
        let metrics_result = self.metrics_calculator.calculate_basic_metrics(context).await;
        let stress_test_result = self.stress_tester.run_stress_test(portfolio, None).await;

        // 生成综合报告
        self.risk_reporter.generate_comprehensive_report(
            portfolio,
            context,
            Some(&metrics_result),
            Some(&stress_test_result),
            None, // monte_carlo_result - 需要可变引用
            None, // correlation_result
            None, // volatility_result
            alerts,
        ).await
    }

    /// 生成快速报告
    pub async fn generate_quick_report(
        &self,
        portfolio: &sigmax_interfaces::Portfolio,
        context: &sigmax_interfaces::risk::RiskContext,
        alerts: &[sigmax_interfaces::risk::RiskAlert],
    ) -> RiskReport {
        self.risk_reporter.generate_quick_report(
            portfolio,
            context,
            alerts,
        ).await
    }

    /// 计算相关性分析
    pub fn calculate_correlation(&self, portfolio: &sigmax_interfaces::risk::Portfolio) -> CorrelationResult {
        let correlation_model = models::CorrelationModel::with_default_config();
        correlation_model.calculate_portfolio_correlation(portfolio)
    }

    /// 计算波动率分析
    pub fn calculate_volatility(&self, portfolio: &sigmax_interfaces::risk::Portfolio) -> VolatilityResult {
        let mut volatility_model = models::VolatilityModel::with_default_config();
        volatility_model.calculate_portfolio_volatility(portfolio)
    }
}

// 不再需要类型转换，直接使用接口类型