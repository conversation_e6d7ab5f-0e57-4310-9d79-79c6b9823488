//! 数据库事务包装器
//! 
//! 提供类型安全的事务操作，确保事务的正确提交或回滚

use sigmax_core::{SigmaXResult, SigmaXError, DatabaseErrorCode};
use sqlx::{Transaction, Postgres};
use chrono::{DateTime, Utc};

/// 数据库事务包装器
///
/// 提供类型安全的事务操作，确保事务的正确提交或回滚
pub struct DatabaseTransaction<'a> {
    transaction: Option<Transaction<'a, Postgres>>,
    is_committed: bool,
}

impl<'a> DatabaseTransaction<'a> {
    /// 创建新的数据库事务
    pub fn new(transaction: Transaction<'a, Postgres>) -> Self {
        Self {
            transaction: Some(transaction),
            is_committed: false,
        }
    }

    /// 获取事务的可变引用（用于执行查询）
    pub fn as_mut(&mut self) -> Option<&mut Transaction<'a, Postgres>> {
        self.transaction.as_mut()
    }

    /// 提交事务
    pub async fn commit(mut self) -> SigmaXResult<()> {
        if let Some(tx) = self.transaction.take() {
            tx.commit().await
                .map_err(|e| SigmaXError::database(
                    DatabaseErrorCode::TransactionFailed, 
                    format!("Failed to commit transaction: {}", e)
                ))?;
            self.is_committed = true;
        }
        Ok(())
    }

    /// 回滚事务
    pub async fn rollback(mut self) -> SigmaXResult<()> {
        if let Some(tx) = self.transaction.take() {
            tx.rollback().await
                .map_err(|e| SigmaXError::database(
                    DatabaseErrorCode::TransactionFailed, 
                    format!("Failed to rollback transaction: {}", e)
                ))?;
        }
        Ok(())
    }

    /// 检查事务是否已提交
    pub fn is_committed(&self) -> bool {
        self.is_committed
    }
}

impl<'a> Drop for DatabaseTransaction<'a> {
    fn drop(&mut self) {
        if let Some(_tx) = self.transaction.take() {
            if !self.is_committed {
                // 在Drop时记录警告，事务将在连接返回池时自动回滚
                tracing::warn!("Transaction dropped without explicit commit or rollback");
            }
        }
    }
}

/// 事务统计信息
#[derive(Debug, Clone, Default)]
pub struct TransactionStats {
    pub total_transactions: u64,
    pub committed_transactions: u64,
    pub rolled_back_transactions: u64,
    pub active_transactions: u64,
    pub avg_transaction_duration_ms: u64,
}

/// 长时间运行的事务信息
#[derive(Debug, Clone)]
pub struct LongRunningTransaction {
    pub transaction_id: String,
    pub start_time: DateTime<Utc>,
    pub duration_seconds: u64,
    pub query: Option<String>,
}
