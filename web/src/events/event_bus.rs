//! Web事件总线实现
//!
//! 基于core模块的EventBus，提供Web模块需要的事件发布和订阅功能

use std::sync::Arc;
use tokio::sync::RwLock;
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use sigmax_core::events::{EventBus, SystemEvent, EventType, EventHandler};
use crate::events::event_types::WebEvent;
use crate::error::ApiError;

/// Web事件总线配置
#[derive(Debug, Clone)]
pub struct WebEventBusConfig {
    pub buffer_size: usize,
    pub max_retries: usize,
    pub retry_delay_ms: u64,
    pub enable_web_events: bool,
    pub enable_system_events: bool,
    pub event_persistence: bool,
}

impl Default for WebEventBusConfig {
    fn default() -> Self {
        Self {
            buffer_size: 1000,
            max_retries: 3,
            retry_delay_ms: 1000,
            enable_web_events: true,
            enable_system_events: true,
            event_persistence: true,
        }
    }
}

/// Web事件总线
///
/// 扩展core模块的EventBus，添加Web模块特有的事件处理功能
pub struct WebEventBus {
    /// 核心事件总线
    core_event_bus: Arc<EventBus>,
    
    /// Web事件处理器
    web_event_handlers: Arc<RwLock<HashMap<String, Arc<dyn WebEventHandler>>>>,
    
    /// 事件统计
    event_stats: Arc<RwLock<EventStats>>,
    
    /// 配置
    config: WebEventBusConfig,
}

/// Web事件处理器trait
#[async_trait::async_trait]
pub trait WebEventHandler: Send + Sync {
    /// 处理Web事件
    async fn handle_web_event(&self, event: &WebEvent) -> Result<(), ApiError>;
    
    /// 获取处理器名称
    fn name(&self) -> &str;
    
    /// 获取处理器关注的事件类型
    fn event_types(&self) -> Vec<String>;
    
    /// 处理器是否启用
    fn is_enabled(&self) -> bool {
        true
    }
}

/// 事件统计信息
#[derive(Debug, Clone)]
pub struct EventStats {
    pub total_events: u64,
    pub web_events: u64,
    pub system_events: u64,
    pub failed_events: u64,
    pub last_event_time: Option<DateTime<Utc>>,
    pub event_types: HashMap<String, u64>,
}

impl Default for EventStats {
    fn default() -> Self {
        Self {
            total_events: 0,
            web_events: 0,
            system_events: 0,
            failed_events: 0,
            last_event_time: None,
            event_types: HashMap::new(),
        }
    }
}

impl WebEventBus {
    /// 创建新的Web事件总线
    pub fn new() -> Self {
        Self::with_config(WebEventBusConfig::default())
    }
    
    /// 使用配置创建Web事件总线
    pub fn with_config(config: WebEventBusConfig) -> Self {
        let core_event_bus = Arc::new(EventBus::new());
        
        Self {
            core_event_bus,
            web_event_handlers: Arc::new(RwLock::new(HashMap::new())),
            event_stats: Arc::new(RwLock::new(EventStats::default())),
            config,
        }
    }
    
    /// 启动事件总线
    pub async fn start(&mut self) -> Result<(), ApiError> {
        // 启动核心事件总线
        self.core_event_bus.start().await
            .map_err(|e| ApiError::SystemError(format!("启动核心事件总线失败: {}", e)))?;
        
        info!("🚀 Web事件总线启动成功");
        Ok(())
    }
    
    /// 停止事件总线
    pub async fn stop(&self) -> Result<(), ApiError> {
        // 停止核心事件总线
        self.core_event_bus.stop().await
            .map_err(|e| ApiError::SystemError(format!("停止核心事件总线失败: {}", e)))?;
        
        info!("🛑 Web事件总线停止成功");
        Ok(())
    }
    
    /// 订阅Web事件处理器
    pub async fn subscribe_web_handler<H: WebEventHandler + 'static>(
        &self,
        handler: H,
    ) -> Result<(), ApiError> {
        let handler: Arc<dyn WebEventHandler> = Arc::new(handler);
        let mut handlers = self.web_event_handlers.write().await;
        
        let handler_name = handler.name().to_string();
        handlers.insert(handler_name, handler);
        
        info!("📡 订阅Web事件处理器: {}", handler_name);
        Ok(())
    }
    
    /// 发布Web事件
    pub async fn publish_web_event(&self, event: WebEvent) -> Result<(), ApiError> {
        // 更新统计信息
        self.update_event_stats(&event).await;
        
        // 通知Web事件处理器
        self.notify_web_handlers(&event).await?;
        
        // 转换为系统事件并发布到核心事件总线
        if self.config.enable_system_events {
            if let Some(system_event) = self.web_event_to_system_event(&event) {
                self.core_event_bus.publish(system_event).await
                    .map_err(|e| ApiError::SystemError(format!("发布系统事件失败: {}", e)))?;
            }
        }
        
        info!("📤 发布Web事件: {:?}", event);
        Ok(())
    }
    
    /// 发布系统事件（委托给核心事件总线）
    pub async fn publish_system_event(&self, event: SystemEvent) -> Result<(), ApiError> {
        // 更新统计信息
        self.update_system_event_stats(&event).await;
        
        // 发布到核心事件总线
        self.core_event_bus.publish(event).await
            .map_err(|e| ApiError::SystemError(format!("发布系统事件失败: {}", e)))?;
        
        Ok(())
    }
    
    /// 获取事件统计信息
    pub async fn get_event_stats(&self) -> EventStats {
        self.event_stats.read().await.clone()
    }
    
    /// 获取核心事件总线引用
    pub fn core_event_bus(&self) -> &Arc<EventBus> {
        &self.core_event_bus
    }
    
    /// 更新事件统计信息
    async fn update_event_stats(&self, event: &WebEvent) {
        let mut stats = self.event_stats.write().await;
        stats.total_events += 1;
        stats.web_events += 1;
        stats.last_event_time = Some(Utc::now());
        
        let event_type = match event {
            WebEvent::StrategyCreated(_) => "strategy_created",
            WebEvent::StrategyUpdated(_) => "strategy_updated",
            WebEvent::StrategyDeleted(_) => "strategy_deleted",
            WebEvent::StrategyStarted(_) => "strategy_started",
            WebEvent::StrategyStopped(_) => "strategy_stopped",
            WebEvent::PortfolioCreated(_) => "portfolio_created",
            WebEvent::PortfolioUpdated(_) => "portfolio_updated",
            WebEvent::BalanceChanged(_) => "balance_changed",
            WebEvent::RiskCheckCompleted(_) => "risk_check_completed",
            WebEvent::RiskAlertTriggered(_) => "risk_alert_triggered",
            WebEvent::RiskLimitExceeded(_) => "risk_limit_exceeded",
            WebEvent::OrderCreated(_) => "order_created",
            WebEvent::OrderUpdated(_) => "order_updated",
            WebEvent::OrderExecuted(_) => "order_executed",
            WebEvent::UserLogin(_) => "user_login",
            WebEvent::UserLogout(_) => "user_logout",
            WebEvent::ApiRequest(_) => "api_request",
            WebEvent::ErrorOccurred(_) => "error_occurred",
        };
        
        *stats.event_types.entry(event_type.to_string()).or_insert(0) += 1;
    }
    
    /// 更新系统事件统计信息
    async fn update_system_event_stats(&self, _event: &SystemEvent) {
        let mut stats = self.event_stats.write().await;
        stats.total_events += 1;
        stats.system_events += 1;
        stats.last_event_time = Some(Utc::now());
    }
    
    /// 通知Web事件处理器
    async fn notify_web_handlers(&self, event: &WebEvent) -> Result<(), ApiError> {
        let handlers = self.web_event_handlers.read().await;
        
        for (name, handler) in handlers.iter() {
            if handler.is_enabled() {
                if let Err(e) = handler.handle_web_event(event).await {
                    error!("Web事件处理器 {} 处理事件失败: {}", name, e);
                    // 更新失败统计
                    let mut stats = self.event_stats.write().await;
                    stats.failed_events += 1;
                }
            }
        }
        
        Ok(())
    }
    
    /// 将Web事件转换为系统事件
    fn web_event_to_system_event(&self, event: &WebEvent) -> Option<SystemEvent> {
        match event {
            WebEvent::StrategyCreated(web_event) => Some(SystemEvent::StrategyStarted(web_event.strategy_id)),
            WebEvent::StrategyStarted(web_event) => Some(SystemEvent::StrategyStarted(web_event.strategy_id)),
            WebEvent::StrategyStopped(web_event) => Some(SystemEvent::StrategyStoped(web_event.strategy_id)),
            WebEvent::OrderCreated(web_event) => {
                // 这里需要从web_event.order_data中提取Order信息
                // 简化实现，返回None
                None
            }
            WebEvent::OrderUpdated(web_event) => {
                // 这里需要从web_event.order_data中提取Order信息
                // 简化实现，返回None
                None
            }
            _ => None, // 其他事件类型暂时不转换为系统事件
        }
    }
}

impl Default for WebEventBus {
    fn default() -> Self {
        Self::new()
    }
}
