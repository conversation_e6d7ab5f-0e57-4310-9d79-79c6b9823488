//! Controllers层 - 纯HTTP处理
//!
//! 负责HTTP请求/响应处理，不包含业务逻辑
//! 所有业务逻辑委托给Application Services层

pub mod strategy_controller;
pub mod risk_controller;
pub mod portfolio_controller;
pub mod report_controller;

// 重新导出控制器
pub use strategy_controller::StrategyController;
pub use risk_controller::RiskController;
pub use portfolio_controller::PortfolioController;
pub use report_controller::ReportController;

/// 控制器基类
pub trait BaseController {
    /// 获取控制器名称
    fn name(&self) -> &str;
    
    /// 健康检查
    async fn health_check(&self) -> bool;
}
