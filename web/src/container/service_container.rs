//! Web服务容器
//!
//! 负责管理所有服务的依赖关系，提供服务的创建和生命周期管理

use std::sync::Arc;
use crate::domain::{
    StrategyDomainService, RiskDomainService, PortfolioDomainService, ExecutionDomainService,
};
use crate::application::{
    StrategyApplicationService, RiskApplicationService, PortfolioApplicationService, ReportApplicationService,
};
use crate::events::WebEventBus;
use crate::events::event_handlers::{LoggingEventHandler, DatabaseEventHandler, NotificationEventHandler, PerformanceEventHandler};
use crate::integration::{
    EnginesIntegration, DatabaseIntegration, StrategiesIntegration, RiskIntegration,
};
use crate::domain::repositories::{
    RepositoryFactory, RepositoryConfig, RepositoryType,
    StrategyRepository, RiskRepository, EnhancedOrderRepository,
};
use tracing::info;

/// Web服务容器
///
/// 管理所有Web模块服务的依赖关系，提供统一的服务访问入口
pub struct WebServiceContainer {
    // 领域服务
    pub strategy_domain_service: Arc<StrategyDomainService>,
    pub risk_domain_service: Arc<RiskDomainService>,
    pub portfolio_domain_service: Arc<PortfolioDomainService>,
    pub execution_domain_service: Arc<ExecutionDomainService>,
    
    // 应用服务
    pub strategy_application_service: Arc<StrategyApplicationService>,
    pub risk_application_service: Arc<RiskApplicationService>,
    pub portfolio_application_service: Arc<PortfolioApplicationService>,
    pub report_application_service: Arc<ReportApplicationService>,
    
    // 集成服务
    pub engines_integration: Arc<EnginesIntegration>,
    pub database_integration: Arc<DatabaseIntegration>,
    pub strategies_integration: Arc<StrategiesIntegration>,
    pub risk_integration: Arc<RiskIntegration>,
    
    // 事件总线
    pub event_bus: Arc<WebEventBus>,
    
    // Repository工厂
    pub repository_factory: Arc<RepositoryFactory>,
}

impl WebServiceContainer {
    /// 创建新的Web服务容器
    pub async fn new() -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        info!("🚀 初始化Web服务容器...");
        
        // 1. 创建事件总线
        let event_bus = Arc::new(WebEventBus::new());
        
        // 2. 注册事件处理器
        Self::register_event_handlers(&event_bus).await?;
        
        // 3. 创建Repository工厂
        let repository_config = RepositoryConfig::default();
        let repository_factory = Arc::new(RepositoryFactory::new(repository_config));
        
        // 4. 创建Repository实例（使用工厂）
        let strategy_repository = repository_factory.create_strategy_repository().await?;
        let risk_repository = repository_factory.create_risk_repository().await?;
        let order_repository = repository_factory.create_order_repository().await?;
        
        // 5. 创建领域服务
        let strategy_domain_service = Arc::new(StrategyDomainService::new(strategy_repository.clone()));
        let risk_domain_service = Arc::new(RiskDomainService::new(risk_repository.clone()));
        let portfolio_domain_service = Arc::new(PortfolioDomainService::new());
        let execution_domain_service = Arc::new(ExecutionDomainService::new(order_repository.clone()));
        
        // 6. 创建应用服务
        let strategy_application_service = Arc::new(StrategyApplicationService::new(
            strategy_domain_service.clone(),
            event_bus.clone(),
        ));
        let risk_application_service = Arc::new(RiskApplicationService::new(
            risk_domain_service.clone(),
            event_bus.clone(),
        ));
        let portfolio_application_service = Arc::new(PortfolioApplicationService::new(
            portfolio_domain_service.clone(),
            event_bus.clone(),
        ));
        let report_application_service = Arc::new(ReportApplicationService::new(
            strategy_domain_service.clone(),
            portfolio_domain_service.clone(),
            risk_domain_service.clone(),
            event_bus.clone(),
        ));
        
        // 7. 创建集成服务
        let engines_integration = Arc::new(EnginesIntegration::new());
        let database_integration = Arc::new(DatabaseIntegration::new());
        let strategies_integration = Arc::new(StrategiesIntegration::new());
        let risk_integration = Arc::new(RiskIntegration::new());
        
        info!("✅ Web服务容器初始化完成");
        
        Ok(Self {
            strategy_domain_service,
            risk_domain_service,
            portfolio_domain_service,
            execution_domain_service,
            strategy_application_service,
            risk_application_service,
            portfolio_application_service,
            report_application_service,
            engines_integration,
            database_integration,
            strategies_integration,
            risk_integration,
            event_bus,
            repository_factory,
        })
    }
    
    /// 使用自定义配置创建服务容器
    pub async fn with_config(config: RepositoryConfig) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        info!("🚀 使用自定义配置初始化Web服务容器...");
        
        // 1. 创建事件总线
        let event_bus = Arc::new(WebEventBus::new());
        
        // 2. 注册事件处理器
        Self::register_event_handlers(&event_bus).await?;
        
        // 3. 创建Repository工厂
        let repository_factory = Arc::new(RepositoryFactory::new(config));
        
        // 4. 创建Repository实例（使用工厂）
        let strategy_repository = repository_factory.create_strategy_repository().await?;
        let risk_repository = repository_factory.create_risk_repository().await?;
        let order_repository = repository_factory.create_order_repository().await?;
        
        // 5. 创建领域服务
        let strategy_domain_service = Arc::new(StrategyDomainService::new(strategy_repository.clone()));
        let risk_domain_service = Arc::new(RiskDomainService::new(risk_repository.clone()));
        let portfolio_domain_service = Arc::new(PortfolioDomainService::new());
        let execution_domain_service = Arc::new(ExecutionDomainService::new(order_repository.clone()));
        
        // 6. 创建应用服务
        let strategy_application_service = Arc::new(StrategyApplicationService::new(
            strategy_domain_service.clone(),
            event_bus.clone(),
        ));
        let risk_application_service = Arc::new(RiskApplicationService::new(
            risk_domain_service.clone(),
            event_bus.clone(),
        ));
        let portfolio_application_service = Arc::new(PortfolioApplicationService::new(
            portfolio_domain_service.clone(),
            event_bus.clone(),
        ));
        let report_application_service = Arc::new(ReportApplicationService::new(
            strategy_domain_service.clone(),
            portfolio_domain_service.clone(),
            risk_domain_service.clone(),
            event_bus.clone(),
        ));
        
        // 7. 创建集成服务
        let engines_integration = Arc::new(EnginesIntegration::new());
        let database_integration = Arc::new(DatabaseIntegration::new());
        let strategies_integration = Arc::new(StrategiesIntegration::new());
        let risk_integration = Arc::new(RiskIntegration::new());
        
        info!("✅ Web服务容器初始化完成");
        
        Ok(Self {
            strategy_domain_service,
            risk_domain_service,
            portfolio_domain_service,
            execution_domain_service,
            strategy_application_service,
            risk_application_service,
            portfolio_application_service,
            report_application_service,
            engines_integration,
            database_integration,
            strategies_integration,
            risk_integration,
            event_bus,
            repository_factory,
        })
    }
    
    /// 注册事件处理器
    async fn register_event_handlers(event_bus: &WebEventBus) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 注册日志事件处理器
        let logging_handler = Arc::new(LoggingEventHandler::new());
        event_bus.register_handler(logging_handler).await?;
        
        // 注册数据库事件处理器
        let database_handler = Arc::new(DatabaseEventHandler::new());
        event_bus.register_handler(database_handler).await?;
        
        // 注册通知事件处理器
        let notification_handler = Arc::new(NotificationEventHandler::new());
        event_bus.register_handler(notification_handler).await?;
        
        // 注册性能事件处理器
        let performance_handler = Arc::new(PerformanceEventHandler::new());
        event_bus.register_handler(performance_handler).await?;
        
        info!("✅ 事件处理器注册完成");
        Ok(())
    }
    
    /// 切换Repository类型
    pub async fn switch_repository_type(&mut self, new_type: RepositoryType) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        info!("🔄 切换Repository类型到: {:?}", new_type);
        
        // 更新工厂配置
        let mut config = self.repository_factory.get_config().clone();
        config.repository_type = new_type;
        
        // 重新创建Repository实例
        let strategy_repository = self.repository_factory.create_strategy_repository().await?;
        let risk_repository = self.repository_factory.create_risk_repository().await?;
        let order_repository = self.repository_factory.create_order_repository().await?;
        
        // 重新创建领域服务
        let strategy_domain_service = Arc::new(StrategyDomainService::new(strategy_repository.clone()));
        let risk_domain_service = Arc::new(RiskDomainService::new(risk_repository.clone()));
        let portfolio_domain_service = Arc::new(PortfolioDomainService::new());
        let execution_domain_service = Arc::new(ExecutionDomainService::new(order_repository.clone()));
        
        // 更新服务容器
        self.strategy_domain_service = strategy_domain_service.clone();
        self.risk_domain_service = risk_domain_service.clone();
        self.portfolio_domain_service = portfolio_domain_service.clone();
        self.execution_domain_service = execution_domain_service.clone();
        
        // 重新创建应用服务
        self.strategy_application_service = Arc::new(StrategyApplicationService::new(
            strategy_domain_service.clone(),
            self.event_bus.clone(),
        ));
        self.risk_application_service = Arc::new(RiskApplicationService::new(
            risk_domain_service.clone(),
            self.event_bus.clone(),
        ));
        self.portfolio_application_service = Arc::new(PortfolioApplicationService::new(
            portfolio_domain_service.clone(),
            self.event_bus.clone(),
        ));
        self.report_application_service = Arc::new(ReportApplicationService::new(
            strategy_domain_service.clone(),
            portfolio_domain_service.clone(),
            risk_domain_service.clone(),
            self.event_bus.clone(),
        ));
        
        info!("✅ Repository类型切换完成");
        Ok(())
    }
    
    /// 获取当前Repository类型
    pub fn get_current_repository_type(&self) -> RepositoryType {
        self.repository_factory.get_config().repository_type
    }
    
    /// 获取Repository工厂配置
    pub fn get_repository_config(&self) -> &RepositoryConfig {
        self.repository_factory.get_config()
    }
}
