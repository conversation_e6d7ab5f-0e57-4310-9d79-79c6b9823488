//! 指标聚合器实现

use async_trait::async_trait;
use sigmax_core::{traits::MetricsCollector, SigmaXResult};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tracing::{debug, error};

/// 指标聚合器
/// 将指标发送到多个收集器
pub struct MetricsAggregator {
    collectors: Vec<Arc<dyn MetricsCollector + Send + Sync>>,
}

impl MetricsAggregator {
    /// 创建新的指标聚合器
    pub fn new() -> Self {
        Self {
            collectors: Vec::new(),
        }
    }

    /// 添加指标收集器
    pub fn add_collector(&mut self, collector: Arc<dyn MetricsCollector + Send + Sync>) {
        self.collectors.push(collector);
    }

    /// 从多个收集器创建聚合器
    pub fn from_collectors(collectors: Vec<Arc<dyn MetricsCollector + Send + Sync>>) -> Self {
        Self { collectors }
    }
}

#[async_trait]
impl MetricsCollector for MetricsAggregator {
    async fn record_risk_check(&self, passed: bool) {
        for collector in &self.collectors {
            collector.record_risk_check(passed).await;
        }
        debug!("Recorded risk check {} across {} collectors", passed, self.collectors.len());
    }

    async fn record_cache_hit(&self) {
        for collector in &self.collectors {
            collector.record_cache_hit().await;
        }
        debug!("Recorded cache hit across {} collectors", self.collectors.len());
    }

    async fn record_latency(&self, operation: &str, duration: Duration) {
        for collector in &self.collectors {
            collector.record_latency(operation, duration).await;
        }
        debug!("Recorded latency for {} across {} collectors", operation, self.collectors.len());
    }

    async fn get_metrics(&self) -> SigmaXResult<HashMap<String, f64>> {
        let mut combined_metrics = HashMap::new();

        for (i, collector) in self.collectors.iter().enumerate() {
            match collector.get_metrics().await {
                Ok(metrics) => {
                    for (key, value) in metrics {
                        // 为每个收集器的指标添加前缀以避免冲突
                        let prefixed_key = format!("collector_{}_{}", i + 1, key);
                        combined_metrics.insert(prefixed_key, value);
                    }
                }
                Err(e) => {
                    error!("Failed to get metrics from collector {}: {}", i + 1, e);
                    // 记录错误但不中断其他收集器
                    combined_metrics.insert(format!("collector_{}_error", i + 1), 1.0);
                }
            }
        }

        Ok(combined_metrics)
    }
}

impl Default for MetricsAggregator {
    fn default() -> Self {
        Self::new()
    }
}
