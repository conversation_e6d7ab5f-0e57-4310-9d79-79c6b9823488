//! 订单路由器

use sigmax_core::{ExchangeId, Order, SigmaXResult};
use sigmax_interfaces::ExchangeConnector;
use std::collections::HashMap;
use std::sync::Arc;

/// 订单路由器
pub struct OrderRouter {
    exchanges: HashMap<ExchangeId, Arc<dyn ExchangeConnector>>,
}

impl OrderRouter {
    pub fn new() -> Self {
        Self {
            exchanges: HashMap::new(),
        }
    }

    /// 添加交易所
    pub fn add_exchange(&mut self, exchange: Arc<dyn ExchangeConnector>) {
        let exchange_id = exchange.id();
        self.exchanges.insert(exchange_id, exchange);
    }

    /// 路由订单到指定交易所
    pub async fn route_order(&self, order: &Order) -> SigmaXResult<()> {
        let exchange = self.exchanges.get(&order.exchange_id)
            .ok_or_else(|| sigmax_core::SigmaXError::exchange_api(
                sigmax_core::ExchangeErrorCode::MarketNotAvailable,
                format!("Exchange {} not found", order.exchange_id),
                order.exchange_id.to_string()
            ))?;

        exchange.place_order(order).await?;
        Ok(())
    }

    /// 获取最佳交易所（基于流动性、费用等）
    pub async fn get_best_exchange(&self, trading_pair: &sigmax_core::TradingPair) -> SigmaXResult<ExchangeId> {
        // 简单实现：返回第一个可用的交易所
        // TODO: 实现更复杂的选择逻辑
        for (exchange_id, exchange) in &self.exchanges {
            // 检查交易所是否支持该交易对
            if let Ok(_) = exchange.get_order_book(trading_pair).await {
                return Ok(exchange_id.clone());
            }
        }
        
        Err(sigmax_core::SigmaXError::exchange_api(
            sigmax_core::ExchangeErrorCode::MarketNotAvailable,
            "No suitable exchange found".to_string(),
            "unknown".to_string()
        ))
    }

    /// 获取所有支持的交易所
    pub fn get_supported_exchanges(&self) -> Vec<ExchangeId> {
        self.exchanges.keys().cloned().collect()
    }

    /// 检查交易所是否可用
    pub fn is_exchange_available(&self, exchange_id: &ExchangeId) -> bool {
        self.exchanges.contains_key(exchange_id)
    }
}
