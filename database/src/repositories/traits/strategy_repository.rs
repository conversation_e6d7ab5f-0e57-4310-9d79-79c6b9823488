//! 策略Repository接口
//!
//! 基于Repository的策略数据访问接口

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use sigmax_core::{StrategyId, StrategyState, StrategyInfo, StrategyStatus, SigmaXResult};
use super::base_repository::{Repository, QueryFilter, PaginationResult, SortConfig};
use serde::{Serialize, Deserialize};

/// 策略Repository接口
/// 
/// 继承Repository的基础CRUD操作，并添加策略特有的业务查询方法
#[async_trait]
pub trait StrategyRepository: Repository<StrategyState, StrategyId> {
    /// 按状态查找策略
    async fn find_by_status(&self, status: StrategyStatus) -> SigmaXResult<Vec<StrategyInfo>>;
    
    /// 获取所有策略信息
    async fn get_all_strategies(&self) -> SigmaXResult<Vec<StrategyInfo>>;
    
    /// 保存策略状态
    async fn save_strategy_state(&self, strategy_id: StrategyId, state: &StrategyState) -> SigmaXResult<()>;
    
    /// 加载策略状态
    async fn load_strategy_state(&self, strategy_id: StrategyId) -> SigmaXResult<Option<StrategyState>>;
    
    /// 按过滤器查找策略
    async fn find_by_filter(&self, filter: &StrategyQueryFilter) -> SigmaXResult<Vec<StrategyInfo>>;
    
    /// 获取策略统计信息
    async fn get_statistics(&self, filter: Option<&StrategyQueryFilter>) -> SigmaXResult<StrategyStatistics>;
    
    /// 分页查询策略
    async fn find_with_pagination(&self, offset: u64, limit: u64) -> SigmaXResult<PaginationResult<StrategyInfo>>;
    
    /// 按状态统计策略数量
    async fn count_by_status(&self) -> SigmaXResult<Vec<(StrategyStatus, u64)>>;
}

/// 策略查询过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyQueryFilter {
    /// 策略状态过滤
    pub statuses: Option<Vec<StrategyStatus>>,
    
    /// 策略ID过滤
    pub strategy_ids: Option<Vec<StrategyId>>,
    
    /// 创建时间范围
    pub created_time_range: Option<(DateTime<Utc>, DateTime<Utc>)>,
    
    /// 更新时间范围
    pub updated_time_range: Option<(DateTime<Utc>, DateTime<Utc>)>,
    
    /// 策略名称模糊匹配
    pub name_pattern: Option<String>,
}

impl StrategyQueryFilter {
    /// 创建新的过滤器
    pub fn new() -> Self {
        Self {
            statuses: None,
            strategy_ids: None,
            created_time_range: None,
            updated_time_range: None,
            name_pattern: None,
        }
    }
    
    /// 验证过滤器参数
    pub fn validate(&self) -> SigmaXResult<()> {
        // 验证时间范围
        if let Some((start, end)) = &self.created_time_range {
            if start >= end {
                return Err(sigmax_core::SigmaXError::validation(
                    sigmax_core::ValidationErrorCode::InvalidRange,
                    "created_time_range",
                    "创建时间范围无效：开始时间必须小于结束时间"
                ));
            }
        }
        
        if let Some((start, end)) = &self.updated_time_range {
            if start >= end {
                return Err(sigmax_core::SigmaXError::validation(
                    sigmax_core::ValidationErrorCode::InvalidRange,
                    "updated_time_range",
                    "更新时间范围无效：开始时间必须小于结束时间"
                ));
            }
        }
        
        Ok(())
    }
}

impl Default for StrategyQueryFilter {
    fn default() -> Self {
        Self::new()
    }
}

/// 策略统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyStatistics {
    /// 策略总数
    pub total_count: u64,
    
    /// 各状态策略数量
    pub status_counts: Vec<(StrategyStatus, u64)>,
    
    /// 平均运行时间（秒）
    pub average_runtime_seconds: f64,
    
    /// 最早创建时间
    pub earliest_created: Option<DateTime<Utc>>,
    
    /// 最近更新时间
    pub latest_updated: Option<DateTime<Utc>>,
    
    /// 活跃策略数量
    pub active_count: u64,
    
    /// 错误策略数量
    pub error_count: u64,
}

impl Default for StrategyStatistics {
    fn default() -> Self {
        Self {
            total_count: 0,
            status_counts: Vec::new(),
            average_runtime_seconds: 0.0,
            earliest_created: None,
            latest_updated: None,
            active_count: 0,
            error_count: 0,
        }
    }
}
