//! 风控策略接口定义

use async_trait::async_trait;
use super::{RiskResult, RiskContext};

/// 风控策略接口 - 简单清晰
#[async_trait]
pub trait RiskPolicy: Send + Sync {
    /// 评估风险
    async fn evaluate(&self, context: &RiskContext) -> RiskResult;
    
    /// 策略名称
    fn name(&self) -> &str;
    
    /// 策略描述
    fn description(&self) -> &str {
        "风控策略"
    }
    
    /// 优先级（数字越大优先级越高）
    fn priority(&self) -> i32 {
        50
    }
    
    /// 是否启用
    fn is_enabled(&self) -> bool {
        true
    }
}
