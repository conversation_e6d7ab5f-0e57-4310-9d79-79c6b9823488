//! SigmaX Services Module
//! 
//! 横切关注点服务层，提供：
//! - 缓存服务 (Cache Services)
//! - 指标服务 (Metrics Services) 
//! - 配置服务 (Config Services)
//! 
//! 设计原则：
//! - 高内聚，低耦合：每个服务专注单一职责
//! - 关注点分离：横切关注点独立于业务逻辑
//! - 面向接口设计：所有服务都有抽象接口
//! - 可测试性设计：支持Mock和依赖注入
//! - 简洁与可演化性：可插拔的服务实现

pub mod cache;
pub mod metrics;
pub mod config;
pub mod errors;

// 重新导出核心接口
pub use sigmax_core::traits::{CacheService, MetricsCollector, ConfigService, ConfigChangeEvent};

// 重新导出具体实现
pub use cache::{
    RedisCacheService, MemoryCacheService, CacheConfig
};
pub use metrics::{
    PrometheusMetricsCollector, LogMetricsCollector, MetricsAggregator, MetricsConfig
};
pub use config::{
    ConfigServiceImpl
};
pub use errors::{ServiceError, ServiceResult};

/// 服务模块版本
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// 服务模块初始化
pub async fn init_services() -> ServiceResult<()> {
    tracing::info!("Initializing SigmaX Services v{}", VERSION);
    
    // 初始化日志
    tracing_subscriber::fmt()
        .with_env_filter(tracing_subscriber::EnvFilter::from_default_env())
        .init();
    
    tracing::info!("Services initialized successfully");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_services_init() {
        let result = init_services().await;
        assert!(result.is_ok());
    }
}
