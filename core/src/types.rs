//! SigmaX Core Types
//!
//! ## 主要职责
//!
//! 1. **类型安全的标识符** - 提供强类型的UUID newtype，避免ID混用
//!    - `OrderId`, `TradeId`, `AuditId` - 核心交易概念
//!    - `StrategyId`, `EngineId` - 暂时保留的业务ID (将来迁移)
//!
//! 2. **金融精度计算类型** - 使用Decimal避免浮点精度问题
//!    - `Amount`, `Price`, `Quantity` - 金融数值类型
//!    - `Percentage`, `Fee` - 比率和费用类型
//!
//! 3. **基础交易类型** - 核心交易概念的数据结构
//!    - `TradingPair` - 交易对定义
//!    - `ExchangeId` - 支持的交易所枚举
//!
//! 4. **市场数据类型** - 统一的市场数据结构
//!    - `MarketData` - K线数据结构
//!    - `PortfolioBalance` - 投资组合余额
//!    - `RiskMetrics` - 基础风险指标
//!
//! 5. **验证函数** - 为模型验证提供通用验证逻辑
//!    - 金额、数量、价格的有效性验证
//!    - 百分比范围验证
//!
//! ## 设计原则
//!
//! - **类型安全**: 使用newtype模式避免原始类型混用
//! - **金融精度**: 所有金融计算使用Decimal类型
//! - **数据库兼容**: 为ID类型提供sqlx trait实现
//! - **序列化支持**: 所有类型支持serde序列化
//! - **显式转换**: 避免隐式类型转换，保证数据安全

use uuid::Uuid;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::fmt;

#[cfg(feature = "sqlx")]
use sqlx::{Type, Encode, Decode, Postgres, postgres::{PgTypeInfo, PgArgumentBuffer, PgValueRef}};

// ============================================================================
// Macros for ID Types - 简化ID类型定义的宏
// ============================================================================

/// 定义UUID newtype ID的宏
macro_rules! define_id {
    ($name:ident, $doc:expr) => {
        #[doc = $doc]
        #[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
        pub struct $name(pub Uuid);

        impl $name {
            pub fn new() -> Self {
                Self(Uuid::new_v4())
            }
        }

        impl Default for $name {
            fn default() -> Self {
                Self::new()
            }
        }

        impl fmt::Display for $name {
            fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
                write!(f, "{}", self.0)
            }
        }

        #[cfg(feature = "sqlx")]
        impl Type<Postgres> for $name {
            fn type_info() -> PgTypeInfo {
                <Uuid as Type<Postgres>>::type_info()
            }
        }

        #[cfg(feature = "sqlx")]
        impl<'q> Encode<'q, Postgres> for $name {
            fn encode_by_ref(&self, buf: &mut PgArgumentBuffer) -> sqlx::encode::IsNull {
                <Uuid as Encode<Postgres>>::encode_by_ref(&self.0, buf)
            }
        }

        #[cfg(feature = "sqlx")]
        impl<'r> Decode<'r, Postgres> for $name {
            fn decode(value: PgValueRef<'r>) -> Result<Self, sqlx::error::BoxDynError> {
                let uuid = <Uuid as Decode<Postgres>>::decode(value)?;
                Ok($name(uuid))
            }
        }
    };
}

// ============================================================================
// ID Types (UUID newtype) - 类型安全的标识符
// ============================================================================

// 使用宏定义所有ID类型
define_id!(OrderId, "订单ID - 核心交易概念");
define_id!(TradeId, "交易ID - 核心交易概念");
define_id!(AuditId, "审计ID - 基础设施概念");
define_id!(RiskId, "风险ID - 风险管理概念");
define_id!(RiskEventId, "风险事件ID - 风险事件概念");
define_id!(PortfolioId, "投资组合ID - 投资组合管理概念");
define_id!(NotificationId, "通知ID - 通知管理概念");
define_id!(NotificationTemplateId, "通知模板ID - 通知模板概念");
define_id!(SystemConfigId, "系统配置ID - 系统配置概念");
define_id!(ConfigHistoryId, "配置历史ID - 配置历史概念");
define_id!(EventId, "事件ID - 事件管理概念");
define_id!(EventMetadataId, "事件元数据ID - 事件元数据概念");

// 暂时保留以兼容现有代码 (将来应移至对应模块)
define_id!(StrategyId, "策略ID - 暂时保留以兼容现有代码 (将来应移至strategies模块)");
define_id!(EngineId, "引擎ID - 暂时保留以兼容现有代码 (将来应移至engines模块)");

// ============================================================================
// Numeric Types (Decimal only for financial calculations) - 金融精度保证
// ============================================================================

/// 金额类型 - 用于资金、余额等
pub type Amount = Decimal;

/// 价格类型 - 用于交易价格
pub type Price = Decimal;

/// 数量类型 - 用于交易数量
pub type Quantity = Decimal;

/// 百分比类型 - 用于比率计算
pub type Percentage = Decimal;

/// 手续费类型 - 用于交易费用
pub type Fee = Decimal;

// ============================================================================
// Trading Types - 交易相关类型
// ============================================================================

/// 交易对 - 基础交易概念
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct TradingPair {
    pub base: String,
    pub quote: String,
}

impl TradingPair {
    /// 创建新的交易对
    pub fn new(base: impl Into<String>, quote: impl Into<String>) -> Self {
        Self {
            base: base.into(),
            quote: quote.into(),
        }
    }

    /// 获取交易对符号 (如 "BTC/USDT")
    pub fn symbol(&self) -> String {
        format!("{}/{}", self.base, self.quote)
    }

    /// 从符号字符串解析交易对
    pub fn from_symbol(symbol: &str) -> Result<Self, String> {
        let parts: Vec<&str> = symbol.split('/').collect();
        if parts.len() != 2 {
            return Err(format!("Invalid trading pair symbol: {}", symbol));
        }
        Ok(Self::new(parts[0], parts[1]))
    }
}

impl fmt::Display for TradingPair {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}/{}", self.base, self.quote)
    }
}

/// 交易所ID - 支持的交易所枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ExchangeId {
    Binance,
    Coinbase,
    Kraken,
    OKX,
    Simulator,
}

impl fmt::Display for ExchangeId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ExchangeId::Binance => write!(f, "binance"),
            ExchangeId::Coinbase => write!(f, "coinbase"),
            ExchangeId::Kraken => write!(f, "kraken"),
            ExchangeId::OKX => write!(f, "okx"),
            ExchangeId::Simulator => write!(f, "simulator"),
        }
    }
}

impl From<String> for ExchangeId {
    fn from(s: String) -> Self {
        match s.to_lowercase().as_str() {
            "binance" => ExchangeId::Binance,
            "coinbase" => ExchangeId::Coinbase,
            "kraken" => ExchangeId::Kraken,
            "okx" => ExchangeId::OKX,
            "simulator" => ExchangeId::Simulator,
            _ => ExchangeId::Simulator, // 默认使用模拟器
        }
    }
}

impl From<&str> for ExchangeId {
    fn from(s: &str) -> Self {
        Self::from(s.to_string())
    }
}

impl std::str::FromStr for ExchangeId {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "binance" => Ok(ExchangeId::Binance),
            "coinbase" => Ok(ExchangeId::Coinbase),
            "kraken" => Ok(ExchangeId::Kraken),
            "okx" => Ok(ExchangeId::OKX),
            "simulator" => Ok(ExchangeId::Simulator),
            _ => Err(format!("Invalid ExchangeId: {}", s)),
        }
    }
}

// ============================================================================
// Market Data Types - 市场数据类型
// ============================================================================

/// 市场数据 - 统一的市场数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketData {
    pub trading_pair: TradingPair,
    pub timestamp: DateTime<Utc>,
    pub open: Price,
    pub high: Price,
    pub low: Price,
    pub close: Price,
    pub volume: Quantity,
}

impl MarketData {
    /// 创建新的市场数据
    pub fn new(
        trading_pair: TradingPair,
        open: Price,
        high: Price,
        low: Price,
        close: Price,
        volume: Quantity,
    ) -> Self {
        Self {
            trading_pair,
            timestamp: chrono::Utc::now(),
            open,
            high,
            low,
            close,
            volume,
        }
    }

    /// 获取价格变化百分比
    pub fn price_change_pct(&self) -> Percentage {
        if self.open.is_zero() {
            return Decimal::ZERO;
        }
        ((self.close - self.open) / self.open) * Decimal::from(100)
    }

    /// 检查是否为上涨
    pub fn is_rising(&self) -> bool {
        self.close > self.open
    }
}

/// 投资组合余额 - 基础金融概念
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortfolioBalance {
    pub asset: String,
    pub free: Amount,
    pub locked: Amount,
    pub total: Amount,
}

impl PortfolioBalance {
    /// 创建新的投资组合余额
    pub fn new(asset: String, free: Amount, locked: Amount) -> Self {
        let total = free + locked;
        Self {
            asset,
            free,
            locked,
            total,
        }
    }

    /// 检查余额是否充足
    pub fn has_sufficient_balance(&self, required: Amount) -> bool {
        self.free >= required
    }

    /// 锁定资金
    pub fn lock_funds(&mut self, amount: Amount) -> Result<(), String> {
        if self.free < amount {
            return Err("Insufficient free balance".to_string());
        }
        self.free -= amount;
        self.locked += amount;
        Ok(())
    }

    /// 解锁资金
    pub fn unlock_funds(&mut self, amount: Amount) -> Result<(), String> {
        if self.locked < amount {
            return Err("Insufficient locked balance".to_string());
        }
        self.locked -= amount;
        self.free += amount;
        Ok(())
    }
}

/// 风险指标 - 基础风险概念
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskMetrics {
    pub max_drawdown: Percentage,
    pub sharpe_ratio: Option<Decimal>,
    pub var_95: Amount,
    pub exposure: Amount,
    pub calculated_at: DateTime<Utc>,
}

impl RiskMetrics {
    /// 创建新的风险指标
    pub fn new(
        max_drawdown: Percentage,
        var_95: Amount,
        exposure: Amount,
    ) -> Self {
        Self {
            max_drawdown,
            sharpe_ratio: None,
            var_95,
            exposure,
            calculated_at: chrono::Utc::now(),
        }
    }

    /// 检查风险是否在可接受范围内
    pub fn is_risk_acceptable(&self, max_drawdown_limit: Percentage) -> bool {
        self.max_drawdown <= max_drawdown_limit
    }
}

// ============================================================================
// Validation Functions - 验证函数 (用于模型验证)
// ============================================================================

/// 验证金额为非负数
pub fn validate_non_negative_amount(amount: &Amount) -> Result<(), validator::ValidationError> {
    if *amount < Decimal::ZERO {
        return Err(validator::ValidationError::new("Amount must be non-negative"));
    }
    Ok(())
}

/// 验证数量为正数
pub fn validate_positive_quantity(quantity: &Quantity) -> Result<(), validator::ValidationError> {
    if *quantity <= Decimal::ZERO {
        return Err(validator::ValidationError::new("Quantity must be positive"));
    }
    Ok(())
}

/// 验证价格为正数
pub fn validate_positive_price(price: &Price) -> Result<(), validator::ValidationError> {
    if *price <= Decimal::ZERO {
        return Err(validator::ValidationError::new("Price must be positive"));
    }
    Ok(())
}

/// 验证百分比在有效范围内 (0-100)
pub fn validate_percentage(percentage: &Percentage) -> Result<(), validator::ValidationError> {
    if *percentage < Decimal::ZERO || *percentage > Decimal::from(100) {
        return Err(validator::ValidationError::new("Percentage must be between 0 and 100"));
    }
    Ok(())
}

/// 验证已成交数量不超过总数量
pub fn validate_filled_quantity(filled: &Quantity, total: &Quantity) -> Result<(), validator::ValidationError> {
    if *filled > *total {
        return Err(validator::ValidationError::new("Filled quantity cannot exceed total quantity"));
    }
    Ok(())
}

// ============================================================================
// Tests
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_order_id_creation() {
        let id1 = OrderId::new();
        let id2 = OrderId::new();
        assert_ne!(id1, id2);
    }

    #[test]
    fn test_trading_pair() {
        let pair = TradingPair::new("BTC", "USDT");
        assert_eq!(pair.symbol(), "BTC/USDT");

        let parsed = TradingPair::from_symbol("ETH/BTC").unwrap();
        assert_eq!(parsed.base, "ETH");
        assert_eq!(parsed.quote, "BTC");
    }

    #[test]
    fn test_exchange_id_conversion() {
        let exchange: ExchangeId = "binance".into();
        assert_eq!(exchange, ExchangeId::Binance);
        assert_eq!(exchange.to_string(), "binance");
    }

    #[test]
    fn test_portfolio_balance() {
        let mut balance = PortfolioBalance::new("BTC".to_string(), Decimal::from(10), Decimal::from(5));
        assert_eq!(balance.total, Decimal::from(15));

        balance.lock_funds(Decimal::from(3)).unwrap();
        assert_eq!(balance.free, Decimal::from(7));
        assert_eq!(balance.locked, Decimal::from(8));
    }

    #[test]
    fn test_market_data() {
        let pair = TradingPair::new("BTC", "USDT");
        let data = MarketData::new(
            pair,
            Decimal::from(50000),
            Decimal::from(51000),
            Decimal::from(49000),
            Decimal::from(50500),
            Decimal::from(100),
        );

        assert!(data.is_rising());
        assert_eq!(data.price_change_pct(), Decimal::from(1)); // 1% increase
    }
}
