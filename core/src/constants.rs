//! SigmaX Core Constants
//!
//! ## 主要职责
//!
//! 1. **核心业务常量** - 交易、风险管理等核心业务相关的常量
//! 2. **配置验证常量** - 为配置验证提供边界值和默认值
//! 3. **数据模型常量** - 数据模型验证和处理相关的常量
//! 4. **时间处理常量** - 时间格式和转换相关的常量
//!
//! ## 设计原则
//!
//! - **业务导向**: 只包含核心业务相关的常量
//! - **避免魔法数字**: 统一管理常量，提高代码可维护性
//! - **模块化组织**: 按业务领域组织常量
//! - **类型安全**: 使用合适的数据类型定义常量

/// 配置验证常量
pub mod config {
    /// 数据库连接池配置
    pub mod database {
        /// 最小连接数
        pub const MIN_CONNECTIONS: u32 = 1;
        
        /// 最大连接数
        pub const MAX_CONNECTIONS: u32 = 100;
        
        /// 默认连接数
        pub const DEFAULT_CONNECTIONS: u32 = 10;
        
        /// 连接超时时间 (秒)
        pub const CONNECTION_TIMEOUT_SECONDS: u64 = 30;
        
        /// 空闲超时时间 (秒)
        pub const IDLE_TIMEOUT_SECONDS: u64 = 600;
    }
    
    /// Web服务器配置
    pub mod web {
        /// 最小端口号
        pub const MIN_PORT: u16 = 1024;
        
        /// 最大端口号
        pub const MAX_PORT: u16 = 65535;
        
        /// 默认端口号
        pub const DEFAULT_PORT: u16 = 8080;
        
        /// 默认主机地址
        pub const DEFAULT_HOST: &str = "0.0.0.0";
        
        /// 请求超时时间 (秒)
        pub const REQUEST_TIMEOUT_SECONDS: u64 = 30;
        
        /// 最大请求体大小 (字节)
        pub const MAX_REQUEST_SIZE_BYTES: usize = 16 * 1024 * 1024; // 16MB
    }
    
    /// 缓存配置
    pub mod cache {
        /// 默认TTL (秒)
        pub const DEFAULT_TTL_SECONDS: u64 = 300;
        
        /// 最大TTL (秒)
        pub const MAX_TTL_SECONDS: u64 = 86400; // 24小时
        
        /// 默认容量
        pub const DEFAULT_CAPACITY: usize = 1000;
        
        /// 最大容量
        pub const MAX_CAPACITY: usize = 1000000;
        
        /// 清理间隔 (毫秒)
        pub const CLEANUP_INTERVAL_MS: u64 = 60000;
        
        /// 内存使用阈值 (百分比)
        pub const MEMORY_THRESHOLD_PERCENT: f64 = 80.0;
    }
    
    /// 监控配置
    pub mod monitoring {
        /// 默认指标收集间隔 (秒)
        pub const DEFAULT_METRICS_INTERVAL_SECONDS: u64 = 60;
        
        /// 最小指标收集间隔 (秒)
        pub const MIN_METRICS_INTERVAL_SECONDS: u64 = 1;
        
        /// 最大指标收集间隔 (秒)
        pub const MAX_METRICS_INTERVAL_SECONDS: u64 = 3600;
        
        /// 默认健康检查间隔 (秒)
        pub const DEFAULT_HEALTH_CHECK_INTERVAL_SECONDS: u64 = 30;
        
        /// 默认日志级别
        pub const DEFAULT_LOG_LEVEL: &str = "info";
        
        /// 最大日志文件大小 (MB)
        pub const MAX_LOG_FILE_SIZE_MB: u64 = 100;
        
        /// 最大日志文件数量
        pub const MAX_LOG_FILES: u32 = 10;
    }
}

/// 交易相关常量
pub mod trading {
    /// 订单配置
    pub mod order {
        /// 最小订单金额
        pub const MIN_ORDER_AMOUNT: f64 = 0.001;
        
        /// 最大订单金额
        pub const MAX_ORDER_AMOUNT: f64 = 1000000.0;
        
        /// 默认订单超时时间 (秒)
        pub const DEFAULT_ORDER_TIMEOUT_SECONDS: u64 = 300;
        
        /// 最大订单超时时间 (秒)
        pub const MAX_ORDER_TIMEOUT_SECONDS: u64 = 3600;
    }
    
    /// 风险管理配置
    pub mod risk {
        /// 默认最大持仓比例 (百分比)
        pub const DEFAULT_MAX_POSITION_PERCENT: f64 = 10.0;
        
        /// 最大持仓比例上限 (百分比)
        pub const MAX_POSITION_PERCENT_LIMIT: f64 = 50.0;
        
        /// 默认止损比例 (百分比)
        pub const DEFAULT_STOP_LOSS_PERCENT: f64 = 5.0;
        
        /// 最大止损比例 (百分比)
        pub const MAX_STOP_LOSS_PERCENT: f64 = 20.0;
        
        /// 默认最大日交易次数
        pub const DEFAULT_MAX_DAILY_TRADES: u32 = 100;
        
        /// 最大日交易次数上限
        pub const MAX_DAILY_TRADES_LIMIT: u32 = 1000;
    }
}



/// 时间相关常量
pub mod time {
    /// 时间格式
    pub const ISO8601_FORMAT: &str = "%Y-%m-%dT%H:%M:%S%.3fZ";
    
    /// 默认时区
    pub const DEFAULT_TIMEZONE: &str = "UTC";
    
    /// 一秒的毫秒数
    pub const MILLISECONDS_PER_SECOND: u64 = 1000;
    
    /// 一分钟的秒数
    pub const SECONDS_PER_MINUTE: u64 = 60;
    
    /// 一小时的秒数
    pub const SECONDS_PER_HOUR: u64 = 3600;
    
    /// 一天的秒数
    pub const SECONDS_PER_DAY: u64 = 86400;
}

/// 数据模型相关常量
pub mod models {
    /// 字符串验证
    pub mod validation {
        /// 最小字符串长度
        pub const MIN_STRING_LENGTH: usize = 1;

        /// 最大字符串长度
        pub const MAX_STRING_LENGTH: usize = 255;

        /// 最大描述长度
        pub const MAX_DESCRIPTION_LENGTH: usize = 1000;

        /// 最大名称长度
        pub const MAX_NAME_LENGTH: usize = 100;

        /// 最小密码长度
        pub const MIN_PASSWORD_LENGTH: usize = 8;

        /// 最大密码长度
        pub const MAX_PASSWORD_LENGTH: usize = 128;
    }

    /// 数值验证
    pub mod numeric {
        /// 最小正数值
        pub const MIN_POSITIVE_VALUE: f64 = 0.000001;

        /// 最大百分比值
        pub const MAX_PERCENTAGE: f64 = 100.0;

        /// 最小百分比值
        pub const MIN_PERCENTAGE: f64 = 0.0;

        /// 精度位数
        pub const DECIMAL_PRECISION: u32 = 8;
    }
}

/// 事件系统相关常量
pub mod events {
    /// 最大重试次数
    pub const MAX_RETRY_COUNT: u32 = 3;

    /// 默认重试次数
    pub const DEFAULT_RETRY_COUNT: u32 = 3;

    /// 事件批处理大小
    pub const BATCH_SIZE: usize = 100;

    /// 最大批处理大小
    pub const MAX_BATCH_SIZE: usize = 1000;

    /// 事件名称最大长度
    pub const MAX_EVENT_NAME_LENGTH: usize = 100;

    /// 事件描述最大长度
    pub const MAX_EVENT_DESCRIPTION_LENGTH: usize = 500;

    /// 关联ID最大长度
    pub const MAX_CORRELATION_ID_LENGTH: usize = 64;
}

/// 通知系统相关常量
pub mod notifications {
    /// 最大通知标题长度
    pub const MAX_TITLE_LENGTH: usize = 200;

    /// 最大通知内容长度
    pub const MAX_CONTENT_LENGTH: usize = 2000;

    /// 默认重试间隔（秒）
    pub const DEFAULT_RETRY_INTERVAL_SECONDS: u64 = 60;

    /// 最大重试间隔（秒）
    pub const MAX_RETRY_INTERVAL_SECONDS: u64 = 3600;

    /// 默认重试次数
    pub const DEFAULT_RETRY_COUNT: u32 = 3;

    /// 最大重试次数
    pub const MAX_RETRY_COUNT: u32 = 10;

    /// 收件人地址最大长度
    pub const MAX_RECIPIENT_LENGTH: usize = 255;
}

/// 系统配置相关常量
pub mod system_config {
    /// 配置键最大长度
    pub const MAX_CONFIG_KEY_LENGTH: usize = 100;

    /// 配置值最大长度
    pub const MAX_CONFIG_VALUE_LENGTH: usize = 1000;

    /// 配置描述最大长度
    pub const MAX_CONFIG_DESCRIPTION_LENGTH: usize = 500;

    /// 默认配置版本
    pub const DEFAULT_CONFIG_VERSION: u32 = 1;

    /// 最大配置版本
    pub const MAX_CONFIG_VERSION: u32 = 999999;

    /// 变更原因最大长度
    pub const MAX_CHANGE_REASON_LENGTH: usize = 200;
}


