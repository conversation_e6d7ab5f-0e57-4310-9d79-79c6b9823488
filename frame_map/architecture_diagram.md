# SigmaX 系统架构图

## 整体架构概览

```mermaid
graph TB
    %% 外部接口层
    subgraph "外部接口层 (External Interface Layer)"
        WebUI[Web UI]
        RestAPI[REST API]
        WebSocket[WebSocket API]
        CLI[CLI Interface]
    end

    %% 表现层
    subgraph "表现层 (Presentation Layer)"
        WebServer[Web Server<br/>Axum Framework]
        APIGateway[API Gateway<br/>Route Handler]
        WSHandler[WebSocket Handler<br/>Real-time Events]
    end

    %% 业务服务层
    subgraph "业务服务层 (Business Service Layer)"
        EngineManager[Engine Manager<br/>引擎管理器]
        StrategyService[Strategy Service<br/>策略服务]
        RiskService[Risk Service<br/>风险服务]
        PortfolioService[Portfolio Service<br/>投资组合服务]
        OrderService[Order Service<br/>订单服务]
        ReportService[Report Service<br/>报告服务]
    end

    %% 核心引擎层
    subgraph "核心引擎层 (Core Engine Layer)"
        subgraph "Trading Engines"
            LiveEngine[Live Engine<br/>实盘引擎]
            PaperEngine[Paper Engine<br/>模拟引擎]
            BacktestEngine[Backtest Engine<br/>回测引擎]
        end
        
        subgraph "Strategy Engines"
            GridStrategy[Grid Strategy<br/>网格策略]
            AsymmetricGrid[Asymmetric Grid<br/>非对称网格]
            DCAStrategy[DCA Strategy<br/>定投策略]
        end
        
        subgraph "Risk Engines"
            RiskEngine[Risk Engine<br/>风险引擎]
            OrderValidator[Order Validator<br/>订单验证器]
            PositionMonitor[Position Monitor<br/>持仓监控]
        end
    end

    %% 核心组件层
    subgraph "核心组件层 (Core Component Layer)"
        EventBus[Event Bus<br/>事件总线]
        OrderManager[Order Manager<br/>订单管理器]
        ExecutionEngine[Execution Engine<br/>执行引擎]
        DataProcessor[Data Processor<br/>数据处理器]
    end

    %% 数据服务层
    subgraph "数据服务层 (Data Service Layer)"
        MarketDataProvider[Market Data Provider<br/>市场数据提供商]
        CacheService[Cache Service<br/>缓存服务]
        ConfigService[Config Service<br/>配置服务]
        MetricsService[Metrics Service<br/>监控服务]
    end

    %% 数据持久层
    subgraph "数据持久层 (Data Persistence Layer)"
        PostgreSQL[(PostgreSQL<br/>主数据库)]
        Redis[(Redis<br/>缓存数据库)]
        FileStorage[File Storage<br/>文件存储]
    end

    %% 外部系统
    subgraph "外部系统 (External Systems)"
        Binance[Binance API]
        Kraken[Kraken API]  
        Coinbase[Coinbase API]
        DataFeeds[Market Data Feeds]
    end

    %% 连接关系
    WebUI --> WebServer
    RestAPI --> APIGateway
    WebSocket --> WSHandler
    CLI --> APIGateway

    WebServer --> EngineManager
    APIGateway --> StrategyService
    APIGateway --> RiskService
    APIGateway --> PortfolioService
    APIGateway --> OrderService
    WSHandler --> EventBus

    EngineManager --> LiveEngine
    EngineManager --> PaperEngine
    EngineManager --> BacktestEngine

    StrategyService --> GridStrategy
    StrategyService --> AsymmetricGrid
    StrategyService --> DCAStrategy

    RiskService --> RiskEngine
    RiskService --> OrderValidator
    RiskService --> PositionMonitor

    LiveEngine --> ExecutionEngine
    PaperEngine --> ExecutionEngine
    BacktestEngine --> ExecutionEngine

    GridStrategy --> EventBus
    AsymmetricGrid --> EventBus
    DCAStrategy --> EventBus

    RiskEngine --> EventBus
    OrderValidator --> EventBus
    PositionMonitor --> EventBus

    EventBus --> OrderManager
    OrderManager --> ExecutionEngine
    ExecutionEngine --> MarketDataProvider

    MarketDataProvider --> CacheService
    CacheService --> Redis
    
    ConfigService --> PostgreSQL
    MetricsService --> PostgreSQL
    
    OrderManager --> PostgreSQL
    ExecutionEngine --> PostgreSQL

    MarketDataProvider --> Binance
    MarketDataProvider --> Kraken
    MarketDataProvider --> Coinbase
    MarketDataProvider --> DataFeeds

    %% 样式定义
    classDef externalInterface fill:#e1f5fe
    classDef presentation fill:#f3e5f5
    classDef businessService fill:#e8f5e8
    classDef coreEngine fill:#fff3e0
    classDef coreComponent fill:#fce4ec
    classDef dataService fill:#f1f8e9
    classDef dataPersistence fill:#e0f2f1
    classDef externalSystem fill:#fafafa

    class WebUI,RestAPI,WebSocket,CLI externalInterface
    class WebServer,APIGateway,WSHandler presentation
    class EngineManager,StrategyService,RiskService,PortfolioService,OrderService,ReportService businessService
    class LiveEngine,PaperEngine,BacktestEngine,GridStrategy,AsymmetricGrid,DCAStrategy,RiskEngine,OrderValidator,PositionMonitor coreEngine
    class EventBus,OrderManager,ExecutionEngine,DataProcessor coreComponent
    class MarketDataProvider,CacheService,ConfigService,MetricsService dataService
    class PostgreSQL,Redis,FileStorage dataPersistence
    class Binance,Kraken,Coinbase,DataFeeds externalSystem
```

## 模块依赖关系图

```mermaid
graph LR
    %% 核心模块
    Core[sigmax-core<br/>核心类型和特征]
    
    %% 数据层模块
    Database[sigmax-database<br/>数据库访问]
    Data[sigmax-data<br/>数据处理]
    
    %% 业务逻辑模块
    Engines[sigmax-engines<br/>交易引擎]
    Strategies[sigmax-strategies<br/>策略模块]
    Risk[sigmax-risk<br/>风险管理]
    Portfolio[sigmax-portfolio<br/>投资组合]
    Execution[sigmax-execution<br/>订单执行]
    
    %% 外部接口模块
    Exchange[sigmax-exchange<br/>交易所接口]
    Web[sigmax-web<br/>Web服务]
    
    %% 辅助模块
    Reporting[sigmax-reporting<br/>报告生成]
    Monitor[sigmax-performance-monitor<br/>性能监控]
    Analysis[sigmax-trading-analysis<br/>交易分析]
    
    %% 新架构模块
    Interfaces[sigmax-interfaces<br/>接口定义]
    Services[sigmax-services<br/>共享服务]

    %% 依赖关系
    Database --> Core
    Data --> Core
    Data --> Database
    
    Engines --> Core
    Engines --> Interfaces
    Engines --> Risk
    
    Strategies --> Core
    Strategies --> Interfaces
    Strategies --> Risk
    
    Risk --> Core
    Risk --> Database
    
    Portfolio --> Core
    Portfolio --> Database
    
    Execution --> Core
    Execution --> Database
    
    Exchange --> Core
    
    Web --> Core
    Web --> Engines
    Web --> Strategies
    Web --> Risk
    Web --> Portfolio
    Web --> Services
    
    Reporting --> Core
    Reporting --> Database
    
    Monitor --> Core
    
    Analysis --> Core
    
    Services --> Core
    Services --> Database
    
    Interfaces --> Core

    %% 样式
    classDef core fill:#ff9999
    classDef data fill:#99ccff
    classDef business fill:#99ff99
    classDef interface fill:#ffcc99
    classDef auxiliary fill:#cc99ff
    classDef newArch fill:#ffff99

    class Core core
    class Database,Data data
    class Engines,Strategies,Risk,Portfolio,Execution business
    class Exchange,Web interface
    class Reporting,Monitor,Analysis auxiliary
    class Interfaces,Services newArch
```

## 事件驱动架构图

```mermaid
graph TB
    %% 事件发布者
    subgraph "Event Publishers (事件发布者)"
        EnginesPub[Trading Engines<br/>交易引擎]
        StrategiesPub[Strategies<br/>策略模块]
        RiskPub[Risk Manager<br/>风险管理器]
        OrderPub[Order Manager<br/>订单管理器]
        MarketPub[Market Data<br/>市场数据]
    end

    %% 事件总线
    subgraph "Event Bus (事件总线)"
        EventBusCore[Event Bus Core<br/>事件总线核心]
        EventStore[Event Store<br/>事件存储]
        EventHandlers[Event Handlers<br/>事件处理器]
    end

    %% 事件订阅者
    subgraph "Event Subscribers (事件订阅者)"
        WebSocketSub[WebSocket Handler<br/>实时推送]
        DatabaseSub[Database Writer<br/>数据库写入]
        RiskSub[Risk Monitor<br/>风险监控]
        ReportSub[Report Generator<br/>报告生成]
        AlertSub[Alert Manager<br/>警报管理]
    end

    %% 事件类型
    subgraph "Event Types (事件类型)"
        OrderEvents[Order Events<br/>• OrderCreated<br/>• OrderUpdated<br/>• OrderCancelled]
        TradeEvents[Trade Events<br/>• TradeExecuted<br/>• TradeSettled]
        StrategyEvents[Strategy Events<br/>• StrategyStarted<br/>• StrategyStopped]
        RiskEvents[Risk Events<br/>• RiskAlert<br/>• ThresholdTriggered]
        SystemEvents[System Events<br/>• EngineStarted<br/>• SystemError]
    end

    %% 连接关系
    EnginesPub --> EventBusCore
    StrategiesPub --> EventBusCore
    RiskPub --> EventBusCore
    OrderPub --> EventBusCore
    MarketPub --> EventBusCore

    EventBusCore --> EventStore
    EventBusCore --> EventHandlers

    EventHandlers --> WebSocketSub
    EventHandlers --> DatabaseSub
    EventHandlers --> RiskSub
    EventHandlers --> ReportSub
    EventHandlers --> AlertSub

    EventBusCore -.-> OrderEvents
    EventBusCore -.-> TradeEvents
    EventBusCore -.-> StrategyEvents
    EventBusCore -.-> RiskEvents
    EventBusCore -.-> SystemEvents

    %% 样式
    classDef publisher fill:#e3f2fd
    classDef eventBus fill:#f3e5f5
    classDef subscriber fill:#e8f5e8
    classDef eventType fill:#fff8e1

    class EnginesPub,StrategiesPub,RiskPub,OrderPub,MarketPub publisher
    class EventBusCore,EventStore,EventHandlers eventBus
    class WebSocketSub,DatabaseSub,RiskSub,ReportSub,AlertSub subscriber
    class OrderEvents,TradeEvents,StrategyEvents,RiskEvents,SystemEvents eventType
```

## 数据流架构图

```mermaid
graph TD
    %% 数据输入
    subgraph "Data Input (数据输入)"
        MarketAPI[Market Data APIs<br/>市场数据接口]
        UserInput[User Input<br/>用户输入]
        ConfigFiles[Configuration Files<br/>配置文件]
    end

    %% 数据处理管道
    subgraph "Data Processing Pipeline (数据处理管道)"
        DataIngestion[Data Ingestion<br/>数据摄取]
        DataValidation[Data Validation<br/>数据验证]
        DataTransformation[Data Transformation<br/>数据转换]
        DataEnrichment[Data Enrichment<br/>数据丰富化]
    end

    %% 核心处理
    subgraph "Core Processing (核心处理)"
        StrategyEngine[Strategy Engine<br/>策略引擎]
        RiskEngine[Risk Engine<br/>风险引擎]
        OrderEngine[Order Engine<br/>订单引擎]
        ExecutionEngine[Execution Engine<br/>执行引擎]
    end

    %% 数据输出
    subgraph "Data Output (数据输出)"
        DatabaseWrite[Database Write<br/>数据库写入]
        CacheWrite[Cache Write<br/>缓存写入]
        APIResponse[API Response<br/>API响应]
        RealtimePush[Real-time Push<br/>实时推送]
        Reports[Reports<br/>报告输出]
    end

    %% 数据流向
    MarketAPI --> DataIngestion
    UserInput --> DataIngestion
    ConfigFiles --> DataIngestion

    DataIngestion --> DataValidation
    DataValidation --> DataTransformation
    DataTransformation --> DataEnrichment

    DataEnrichment --> StrategyEngine
    DataEnrichment --> RiskEngine
    
    StrategyEngine --> OrderEngine
    RiskEngine --> OrderEngine
    OrderEngine --> ExecutionEngine

    StrategyEngine --> DatabaseWrite
    RiskEngine --> DatabaseWrite
    OrderEngine --> DatabaseWrite
    ExecutionEngine --> DatabaseWrite

    ExecutionEngine --> CacheWrite
    OrderEngine --> APIResponse
    
    StrategyEngine --> RealtimePush
    RiskEngine --> RealtimePush
    OrderEngine --> RealtimePush

    DatabaseWrite --> Reports

    %% 样式
    classDef input fill:#e3f2fd
    classDef processing fill:#f3e5f5
    classDef core fill:#e8f5e8
    classDef output fill:#fff3e0

    class MarketAPI,UserInput,ConfigFiles input
    class DataIngestion,DataValidation,DataTransformation,DataEnrichment processing
    class StrategyEngine,RiskEngine,OrderEngine,ExecutionEngine core
    class DatabaseWrite,CacheWrite,APIResponse,RealtimePush,Reports output
```


这个架构图全面展示了 SigmaX 项目的多层架构设计，包括：

1. **分层架构** - 清晰的职责分离
2. **模块化设计** - 松耦合的组件结构  
3. **事件驱动** - 异步消息通信
4. **数据管道** - 完整的数据处理流程
5. **可扩展部署** - 支持集群和高可用

这种设计很好地体现了《软件架构基础》中推荐的架构模式和最佳实践。