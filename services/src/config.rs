//! 配置服务实现

use async_trait::async_trait;
use sigmax_core::{traits::{ConfigService, ConfigChangeEvent}, SigmaXResult, SigmaXError};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

use chrono::Utc;

/// 配置服务实现
pub struct ConfigServiceImpl {
    config_data: Arc<RwLock<HashMap<String, serde_json::Value>>>,
    change_listeners: Arc<RwLock<Vec<Box<dyn Fn(ConfigChangeEvent) + Send + Sync>>>>,
}

impl ConfigServiceImpl {
    /// 创建新的配置服务
    pub fn new() -> Self {
        Self {
            config_data: Arc::new(RwLock::new(HashMap::new())),
            change_listeners: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// 从文件加载配置
    pub async fn load_from_file(&self, file_path: &str) -> SigmaXResult<()> {
        let content = tokio::fs::read_to_string(file_path).await
            .map_err(|e| SigmaXError::internal(
                sigmax_core::InternalErrorCode::ConfigurationError,
                format!("Failed to read config file: {}", e)
            ))?;

        let config: HashMap<String, serde_json::Value> = if file_path.ends_with(".json") {
            serde_json::from_str(&content)
                .map_err(|e| SigmaXError::internal(
                    sigmax_core::InternalErrorCode::ConfigurationError,
                    format!("Failed to parse JSON config: {}", e)
                ))?
        } else {
            // 对于非 JSON 文件，尝试作为 JSON 解析
            serde_json::from_str(&content)
                .map_err(|e| SigmaXError::internal(
                    sigmax_core::InternalErrorCode::ConfigurationError,
                    format!("Failed to parse config file: {}", e)
                ))?
        };

        let mut data = self.config_data.write().await;
        *data = config;

        info!("Loaded configuration from file: {}", file_path);
        Ok(())
    }

    /// 添加配置变更监听器
    pub async fn add_change_listener<F>(&self, listener: F)
    where
        F: Fn(ConfigChangeEvent) + Send + Sync + 'static,
    {
        let mut listeners = self.change_listeners.write().await;
        listeners.push(Box::new(listener));
    }

    /// 通知配置变更
    async fn notify_change(&self, event: ConfigChangeEvent) {
        let listeners = self.change_listeners.read().await;
        for listener in listeners.iter() {
            listener(event.clone());
        }
    }
}

#[async_trait]
impl ConfigService for ConfigServiceImpl {
    async fn get_config<T>(&self, key: &str) -> SigmaXResult<Option<T>>
    where
        T: serde::de::DeserializeOwned + Send,
    {
        let data = self.config_data.read().await;

        if let Some(value) = data.get(key) {
            let config: T = serde_json::from_value(value.clone())
                .map_err(|e| SigmaXError::internal(
                    sigmax_core::InternalErrorCode::ConfigurationError,
                    format!("Failed to deserialize config for key '{}': {}", key, e)
                ))?;

            debug!("Retrieved config for key: {}", key);
            Ok(Some(config))
        } else {
            debug!("Config not found for key: {}", key);
            Ok(None)
        }
    }

    async fn reload_config(&self) -> SigmaXResult<()> {
        warn!("Reload not implemented for in-memory config service");
        Ok(())
    }

    async fn validate_config(&self, _config: &serde_json::Value) -> SigmaXResult<()> {
        // 简单实现：总是验证通过
        Ok(())
    }

    async fn watch_changes(&self) -> SigmaXResult<tokio::sync::mpsc::Receiver<ConfigChangeEvent>> {
        // 创建一个通道用于配置变更通知
        let (tx, rx) = tokio::sync::mpsc::channel(100);

        // 在实际实现中，这里应该设置监听器
        // 现在只是返回接收器
        warn!("Config change watching not fully implemented");

        Ok(rx)
    }
}

impl Default for ConfigServiceImpl {
    fn default() -> Self {
        Self::new()
    }
}
