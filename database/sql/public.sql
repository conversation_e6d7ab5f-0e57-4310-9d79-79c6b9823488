/*
 Navicat Premium Data Transfer

 Source Server         : wx
 Source Server Type    : PostgreSQL
 Source Server Version : 170005 (170005)
 Source Host           : *************:55432
 Source Catalog        : mx
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170005 (170005)
 File Encoding         : 65001

 Date: 07/08/2025 11:38:14
*/


-- ----------------------------
-- Type structure for check_result
-- ----------------------------
DROP TYPE IF EXISTS "public"."check_result";
CREATE TYPE "public"."check_result" AS ENUM (
  'passed',
  'failed',
  'warning',
  'error'
);
ALTER TYPE "public"."check_result" OWNER TO "mx";

-- ----------------------------
-- Type structure for exchange_status
-- ----------------------------
DROP TYPE IF EXISTS "public"."exchange_status";
CREATE TYPE "public"."exchange_status" AS ENUM (
  'Active',
  'Inactive',
  'Delisted'
);
ALTER TYPE "public"."exchange_status" OWNER TO "mx";

-- ----------------------------
-- Type structure for order_side
-- ----------------------------
DROP TYPE IF EXISTS "public"."order_side";
CREATE TYPE "public"."order_side" AS ENUM (
  'Buy',
  'Sell'
);
ALTER TYPE "public"."order_side" OWNER TO "mx";

-- ----------------------------
-- Type structure for order_status
-- ----------------------------
DROP TYPE IF EXISTS "public"."order_status";
CREATE TYPE "public"."order_status" AS ENUM (
  'Pending',
  'Open',
  'PartiallyFilled',
  'Filled',
  'Cancelled',
  'Rejected',
  'Expired'
);
ALTER TYPE "public"."order_status" OWNER TO "mx";

-- ----------------------------
-- Type structure for order_type
-- ----------------------------
DROP TYPE IF EXISTS "public"."order_type";
CREATE TYPE "public"."order_type" AS ENUM (
  'Market',
  'Limit',
  'StopLoss',
  'StopLimit',
  'TakeProfit',
  'TakeProfitLimit'
);
ALTER TYPE "public"."order_type" OWNER TO "mx";

-- ----------------------------
-- Type structure for strategy_status
-- ----------------------------
DROP TYPE IF EXISTS "public"."strategy_status";
CREATE TYPE "public"."strategy_status" AS ENUM (
  'Created',
  'Running',
  'Paused',
  'Stopped',
  'Error'
);
ALTER TYPE "public"."strategy_status" OWNER TO "mx";

-- ----------------------------
-- Type structure for violation_severity
-- ----------------------------
DROP TYPE IF EXISTS "public"."violation_severity";
CREATE TYPE "public"."violation_severity" AS ENUM (
  'low',
  'medium',
  'high',
  'critical'
);
ALTER TYPE "public"."violation_severity" OWNER TO "mx";

-- ----------------------------
-- Type structure for violation_status
-- ----------------------------
DROP TYPE IF EXISTS "public"."violation_status";
CREATE TYPE "public"."violation_status" AS ENUM (
  'active',
  'resolved',
  'ignored',
  'escalated'
);
ALTER TYPE "public"."violation_status" OWNER TO "mx";

-- ----------------------------
-- Sequence structure for audit_logs_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."audit_logs_id_seq";
CREATE SEQUENCE "public"."audit_logs_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;
ALTER SEQUENCE "public"."audit_logs_id_seq" OWNER TO "mx";

-- ----------------------------
-- Sequence structure for backtest_files_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."backtest_files_id_seq";
CREATE SEQUENCE "public"."backtest_files_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;
ALTER SEQUENCE "public"."backtest_files_id_seq" OWNER TO "mx";

-- ----------------------------
-- Sequence structure for candles_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."candles_id_seq";
CREATE SEQUENCE "public"."candles_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;
ALTER SEQUENCE "public"."candles_id_seq" OWNER TO "mx";

-- ----------------------------
-- Sequence structure for events_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."events_id_seq";
CREATE SEQUENCE "public"."events_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;
ALTER SEQUENCE "public"."events_id_seq" OWNER TO "mx";

-- ----------------------------
-- Sequence structure for exchanges_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."exchanges_id_seq";
CREATE SEQUENCE "public"."exchanges_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;
ALTER SEQUENCE "public"."exchanges_id_seq" OWNER TO "mx";

-- ----------------------------
-- Sequence structure for strategy_config_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."strategy_config_id_seq";
CREATE SEQUENCE "public"."strategy_config_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;
ALTER SEQUENCE "public"."strategy_config_id_seq" OWNER TO "mx";

-- ----------------------------
-- Sequence structure for system_config_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."system_config_id_seq";
CREATE SEQUENCE "public"."system_config_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;
ALTER SEQUENCE "public"."system_config_id_seq" OWNER TO "mx";

-- ----------------------------
-- Sequence structure for trading_config_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."trading_config_id_seq";
CREATE SEQUENCE "public"."trading_config_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;
ALTER SEQUENCE "public"."trading_config_id_seq" OWNER TO "mx";

-- ----------------------------
-- Sequence structure for trading_pairs_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."trading_pairs_id_seq";
CREATE SEQUENCE "public"."trading_pairs_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;
ALTER SEQUENCE "public"."trading_pairs_id_seq" OWNER TO "mx";

-- ----------------------------
-- Table structure for audit_logs
-- ----------------------------
DROP TABLE IF EXISTS "public"."audit_logs";
CREATE TABLE "public"."audit_logs" (
  "id" int8 NOT NULL DEFAULT nextval('audit_logs_id_seq'::regclass),
  "table_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "record_id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "operation" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "old_values" jsonb,
  "new_values" jsonb,
  "changed_by" varchar(100) COLLATE "pg_catalog"."default",
  "changed_at" timestamptz(6) NOT NULL DEFAULT now(),
  "ip_address" inet,
  "user_agent" text COLLATE "pg_catalog"."default"
)
;
ALTER TABLE "public"."audit_logs" OWNER TO "mx";

-- ----------------------------
-- Table structure for backtest_files
-- ----------------------------
DROP TABLE IF EXISTS "public"."backtest_files";
CREATE TABLE "public"."backtest_files" (
  "id" int4 NOT NULL DEFAULT nextval('backtest_files_id_seq'::regclass),
  "filename" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "trading_pair" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "timeframe" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "candle_count" int4 NOT NULL DEFAULT 0,
  "start_time" timestamptz(6),
  "end_time" timestamptz(6),
  "file_size" int8,
  "file_hash" varchar(64) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now()
)
;
ALTER TABLE "public"."backtest_files" OWNER TO "mx";

-- ----------------------------
-- Table structure for backtest_portfolio_snapshots
-- ----------------------------
DROP TABLE IF EXISTS "public"."backtest_portfolio_snapshots";
CREATE TABLE "public"."backtest_portfolio_snapshots" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "backtest_id" uuid NOT NULL,
  "timestamp" timestamptz(6) NOT NULL,
  "total_value" numeric(20,8) NOT NULL,
  "cash_balance" numeric(20,8) NOT NULL,
  "positions" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "unrealized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "realized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "created_at" timestamptz(6) DEFAULT now()
)
;
ALTER TABLE "public"."backtest_portfolio_snapshots" OWNER TO "mx";

-- ----------------------------
-- Table structure for backtest_results
-- ----------------------------
DROP TABLE IF EXISTS "public"."backtest_results";
CREATE TABLE "public"."backtest_results" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "engine_id" uuid NOT NULL,
  "config" jsonb NOT NULL,
  "start_time" timestamptz(6) NOT NULL,
  "end_time" timestamptz(6) NOT NULL,
  "initial_capital" numeric(20,8) NOT NULL,
  "final_capital" numeric(20,8) NOT NULL,
  "total_return" numeric(20,8) NOT NULL,
  "total_return_percentage" numeric(10,4) NOT NULL,
  "max_drawdown" numeric(20,8) NOT NULL,
  "max_drawdown_percentage" numeric(10,4) NOT NULL,
  "sharpe_ratio" numeric(10,4),
  "profit_factor" numeric(10,4),
  "win_rate" numeric(10,4),
  "total_trades" int4 NOT NULL DEFAULT 0,
  "winning_trades" int4 NOT NULL DEFAULT 0,
  "losing_trades" int4 NOT NULL DEFAULT 0,
  "average_win" numeric(20,8),
  "average_loss" numeric(20,8),
  "largest_win" numeric(20,8),
  "largest_loss" numeric(20,8),
  "status" varchar(50) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'Created'::character varying,
  "created_at" timestamptz(6) DEFAULT now(),
  "completed_at" timestamptz(6)
)
;
ALTER TABLE "public"."backtest_results" OWNER TO "mx";

-- ----------------------------
-- Table structure for backtest_trades
-- ----------------------------
DROP TABLE IF EXISTS "public"."backtest_trades";
CREATE TABLE "public"."backtest_trades" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "backtest_id" uuid NOT NULL,
  "trade_id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "timestamp" timestamptz(6) NOT NULL,
  "trading_pair" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "side" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "price" numeric(20,8) NOT NULL,
  "value" numeric(20,8) NOT NULL,
  "fee" numeric(20,8) NOT NULL DEFAULT 0,
  "strategy_id" varchar(100) COLLATE "pg_catalog"."default",
  "pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "cumulative_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "created_at" timestamptz(6) DEFAULT now()
)
;
ALTER TABLE "public"."backtest_trades" OWNER TO "mx";

-- ----------------------------
-- Table structure for cache_alerts
-- ----------------------------
DROP TABLE IF EXISTS "public"."cache_alerts";
CREATE TABLE "public"."cache_alerts" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "rule_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "severity" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "condition" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "current_value" numeric(15,4) NOT NULL,
  "threshold_value" numeric(15,4) NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "resolved" bool NOT NULL DEFAULT false,
  "resolved_at" timestamptz(6),
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
ALTER TABLE "public"."cache_alerts" OWNER TO "mx";
COMMENT ON COLUMN "public"."cache_alerts"."severity" IS '告警级别：info, warning, critical, emergency';
COMMENT ON COLUMN "public"."cache_alerts"."resolved" IS '是否已解决';
COMMENT ON TABLE "public"."cache_alerts" IS '缓存告警记录表';

-- ----------------------------
-- Table structure for cache_health
-- ----------------------------
DROP TABLE IF EXISTS "public"."cache_health";
CREATE TABLE "public"."cache_health" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "status" varchar(20) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'unknown'::character varying,
  "memory_pressure_level" varchar(20) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'normal'::character varying,
  "memory_usage_percentage" numeric(5,2) NOT NULL DEFAULT 0,
  "eviction_rate" numeric(5,4) NOT NULL DEFAULT 0,
  "fragmentation_ratio" numeric(5,4) NOT NULL DEFAULT 0,
  "issues" jsonb,
  "recommendations" jsonb,
  "created_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
ALTER TABLE "public"."cache_health" OWNER TO "mx";
COMMENT ON COLUMN "public"."cache_health"."status" IS '健康状态：healthy, warning, critical, unknown';
COMMENT ON COLUMN "public"."cache_health"."memory_pressure_level" IS '内存压力级别：low, normal, high, critical';
COMMENT ON TABLE "public"."cache_health" IS '缓存健康检查数据表';

-- ----------------------------
-- Table structure for cache_performance
-- ----------------------------
DROP TABLE IF EXISTS "public"."cache_performance";
CREATE TABLE "public"."cache_performance" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "cache_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "average_response_time_ms" numeric(10,3) NOT NULL DEFAULT 0,
  "p95_response_time_ms" numeric(10,3) NOT NULL DEFAULT 0,
  "p99_response_time_ms" numeric(10,3) NOT NULL DEFAULT 0,
  "throughput_ops_per_second" numeric(10,2) NOT NULL DEFAULT 0,
  "error_rate" numeric(5,4) NOT NULL DEFAULT 0,
  "created_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
ALTER TABLE "public"."cache_performance" OWNER TO "mx";
COMMENT ON COLUMN "public"."cache_performance"."average_response_time_ms" IS '平均响应时间（毫秒）';
COMMENT ON COLUMN "public"."cache_performance"."p95_response_time_ms" IS 'P95响应时间（毫秒）';
COMMENT ON COLUMN "public"."cache_performance"."p99_response_time_ms" IS 'P99响应时间（毫秒）';
COMMENT ON TABLE "public"."cache_performance" IS '缓存性能监控数据表';

-- ----------------------------
-- Table structure for cache_stats
-- ----------------------------
DROP TABLE IF EXISTS "public"."cache_stats";
CREATE TABLE "public"."cache_stats" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "cache_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "total_keys" int8 NOT NULL DEFAULT 0,
  "memory_usage_mb" numeric(10,2) NOT NULL DEFAULT 0,
  "memory_limit_mb" numeric(10,2) NOT NULL DEFAULT 0,
  "memory_usage_percentage" numeric(5,2) NOT NULL DEFAULT 0,
  "hit_rate" numeric(5,4) NOT NULL DEFAULT 0,
  "miss_rate" numeric(5,4) NOT NULL DEFAULT 0,
  "total_operations" int8 NOT NULL DEFAULT 0,
  "hits" int8 NOT NULL DEFAULT 0,
  "misses" int8 NOT NULL DEFAULT 0,
  "evictions" int8 NOT NULL DEFAULT 0,
  "expired_keys" int8 NOT NULL DEFAULT 0,
  "average_ttl_seconds" numeric(10,2) NOT NULL DEFAULT 0,
  "uptime_seconds" int8 NOT NULL DEFAULT 0,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
ALTER TABLE "public"."cache_stats" OWNER TO "mx";
COMMENT ON COLUMN "public"."cache_stats"."cache_type" IS '缓存类型（如：redis, memory, disk等）';
COMMENT ON COLUMN "public"."cache_stats"."hit_rate" IS '命中率（0-1之间）';
COMMENT ON COLUMN "public"."cache_stats"."miss_rate" IS '未命中率（0-1之间）';
COMMENT ON TABLE "public"."cache_stats" IS '缓存统计数据表';

-- ----------------------------
-- Table structure for candles
-- ----------------------------
DROP TABLE IF EXISTS "public"."candles";
CREATE TABLE "public"."candles" (
  "id" int8 NOT NULL DEFAULT nextval('candles_id_seq'::regclass),
  "trading_pair_id" int4 NOT NULL,
  "timeframe" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "open_time" timestamptz(6) NOT NULL,
  "close_time" timestamptz(6) NOT NULL,
  "open_price" numeric(20,8) NOT NULL,
  "high_price" numeric(20,8) NOT NULL,
  "low_price" numeric(20,8) NOT NULL,
  "close_price" numeric(20,8) NOT NULL,
  "volume" numeric(20,8) NOT NULL,
  "quote_volume" numeric(20,8) NOT NULL,
  "trades_count" int4 NOT NULL DEFAULT 0,
  "created_at" timestamptz(6) DEFAULT now()
)
;
ALTER TABLE "public"."candles" OWNER TO "mx";

-- ----------------------------
-- Table structure for engines
-- ----------------------------
DROP TABLE IF EXISTS "public"."engines";
CREATE TABLE "public"."engines" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "engine_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "status" varchar(50) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'Created'::character varying,
  "config" jsonb NOT NULL DEFAULT '{}'::jsonb,
  "trading_pairs" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "initial_capital" numeric(20,8),
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now()
)
;
ALTER TABLE "public"."engines" OWNER TO "mx";

-- ----------------------------
-- Table structure for events
-- ----------------------------
DROP TABLE IF EXISTS "public"."events";
CREATE TABLE "public"."events" (
  "id" int8 NOT NULL DEFAULT nextval('events_id_seq'::regclass),
  "event_type" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "event_data" jsonb NOT NULL,
  "aggregate_id" varchar(100) COLLATE "pg_catalog"."default",
  "aggregate_type" varchar(50) COLLATE "pg_catalog"."default",
  "version" int4 NOT NULL DEFAULT 1,
  "correlation_id" uuid,
  "causation_id" uuid,
  "metadata" jsonb DEFAULT '{}'::jsonb,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "processed_at" timestamptz(6)
)
;
ALTER TABLE "public"."events" OWNER TO "mx";

-- ----------------------------
-- Table structure for exchanges
-- ----------------------------
DROP TABLE IF EXISTS "public"."exchanges";
CREATE TABLE "public"."exchanges" (
  "id" int4 NOT NULL DEFAULT nextval('exchanges_id_seq'::regclass),
  "name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "display_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "status" "public"."exchange_status" NOT NULL DEFAULT 'Active'::exchange_status,
  "api_endpoint" varchar(255) COLLATE "pg_catalog"."default",
  "websocket_endpoint" varchar(255) COLLATE "pg_catalog"."default",
  "trading_fee_rate" numeric(10,6) DEFAULT 0.001,
  "withdrawal_fee_rate" numeric(10,6) DEFAULT 0.0005,
  "min_order_amount" numeric(20,8),
  "max_order_amount" numeric(20,8),
  "supported_order_types" "public"."order_type"[],
  "config" jsonb DEFAULT '{}'::jsonb,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
ALTER TABLE "public"."exchanges" OWNER TO "mx";

-- ----------------------------
-- Table structure for monitoring_alerts
-- ----------------------------
DROP TABLE IF EXISTS "public"."monitoring_alerts";
CREATE TABLE "public"."monitoring_alerts" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "rule_id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "rule_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "severity" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "message" text COLLATE "pg_catalog"."default" NOT NULL,
  "metric_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "triggered_value" float8 NOT NULL,
  "threshold_value" float8 NOT NULL,
  "condition" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "status" varchar(20) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'triggered'::character varying,
  "triggered_at" timestamptz(6) NOT NULL DEFAULT now(),
  "acknowledged_at" timestamptz(6),
  "resolved_at" timestamptz(6),
  "acknowledged_by" varchar(100) COLLATE "pg_catalog"."default",
  "resolution_notes" text COLLATE "pg_catalog"."default",
  "alert_data" jsonb DEFAULT '{}'::jsonb,
  "notification_sent" bool DEFAULT false,
  "date_partition" date NOT NULL DEFAULT CURRENT_DATE
)
;
ALTER TABLE "public"."monitoring_alerts" OWNER TO "mx";
COMMENT ON COLUMN "public"."monitoring_alerts"."id" IS '告警唯一标识符';
COMMENT ON COLUMN "public"."monitoring_alerts"."rule_id" IS '告警规则ID';
COMMENT ON COLUMN "public"."monitoring_alerts"."severity" IS '告警级别';
COMMENT ON COLUMN "public"."monitoring_alerts"."message" IS '告警消息';
COMMENT ON COLUMN "public"."monitoring_alerts"."status" IS '告警状态';
COMMENT ON COLUMN "public"."monitoring_alerts"."triggered_at" IS '告警触发时间';
COMMENT ON TABLE "public"."monitoring_alerts" IS '监控告警表 - 存储系统监控告警信息';

-- ----------------------------
-- Table structure for monitoring_config
-- ----------------------------
DROP TABLE IF EXISTS "public"."monitoring_config";
CREATE TABLE "public"."monitoring_config" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'default'::character varying,
  "description" text COLLATE "pg_catalog"."default" DEFAULT '默认监控配置'::text,
  "enabled" bool NOT NULL DEFAULT true,
  "monitoring_parameters" jsonb NOT NULL,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_by" varchar(100) COLLATE "pg_catalog"."default" DEFAULT 'system'::character varying
)
;
ALTER TABLE "public"."monitoring_config" OWNER TO "mx";
COMMENT ON COLUMN "public"."monitoring_config"."id" IS '配置唯一标识符';
COMMENT ON COLUMN "public"."monitoring_config"."name" IS '配置名称';
COMMENT ON COLUMN "public"."monitoring_config"."description" IS '配置描述';
COMMENT ON COLUMN "public"."monitoring_config"."enabled" IS '是否启用此配置';
COMMENT ON COLUMN "public"."monitoring_config"."monitoring_parameters" IS '监控参数JSON配置';
COMMENT ON COLUMN "public"."monitoring_config"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."monitoring_config"."updated_at" IS '最后更新时间';
COMMENT ON COLUMN "public"."monitoring_config"."created_by" IS '创建者';
COMMENT ON TABLE "public"."monitoring_config" IS '监控配置表 - 存储系统监控相关配置参数';

-- ----------------------------
-- Table structure for monitoring_metrics
-- ----------------------------
DROP TABLE IF EXISTS "public"."monitoring_metrics";
CREATE TABLE "public"."monitoring_metrics" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "metric_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "metric_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "metric_value" float8 NOT NULL,
  "unit" varchar(20) COLLATE "pg_catalog"."default",
  "labels" jsonb DEFAULT '{}'::jsonb,
  "metadata" jsonb DEFAULT '{}'::jsonb,
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "source" varchar(50) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'system'::character varying,
  "date_partition" date NOT NULL DEFAULT CURRENT_DATE
)
;
ALTER TABLE "public"."monitoring_metrics" OWNER TO "mx";
COMMENT ON COLUMN "public"."monitoring_metrics"."id" IS '指标记录唯一标识符';
COMMENT ON COLUMN "public"."monitoring_metrics"."metric_type" IS '指标类型';
COMMENT ON COLUMN "public"."monitoring_metrics"."metric_name" IS '指标名称';
COMMENT ON COLUMN "public"."monitoring_metrics"."metric_value" IS '指标值';
COMMENT ON COLUMN "public"."monitoring_metrics"."unit" IS '指标单位';
COMMENT ON COLUMN "public"."monitoring_metrics"."labels" IS '指标标签JSON';
COMMENT ON COLUMN "public"."monitoring_metrics"."timestamp" IS '指标记录时间';
COMMENT ON COLUMN "public"."monitoring_metrics"."source" IS '数据来源';
COMMENT ON TABLE "public"."monitoring_metrics" IS '监控指标数据表 - 存储系统运行时的各种监控指标';

-- ----------------------------
-- Table structure for orders
-- ----------------------------
DROP TABLE IF EXISTS "public"."orders";
CREATE TABLE "public"."orders" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "strategy_id" uuid,
  "trading_pair_id" int4 NOT NULL,
  "exchange_id" int4 NOT NULL,
  "exchange_order_id" varchar(100) COLLATE "pg_catalog"."default",
  "parent_order_id" uuid,
  "order_type" "public"."order_type" NOT NULL,
  "side" "public"."order_side" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "price" numeric(20,8),
  "stop_price" numeric(20,8),
  "status" "public"."order_status" NOT NULL DEFAULT 'Pending'::order_status,
  "filled_quantity" numeric(20,8) NOT NULL DEFAULT 0,
  "remaining_quantity" numeric(20,8) GENERATED ALWAYS AS (
(quantity - filled_quantity)
) STORED,
  "average_price" numeric(20,8),
  "total_fee" numeric(20,8) NOT NULL DEFAULT 0,
  "fee_asset" varchar(10) COLLATE "pg_catalog"."default",
  "time_in_force" varchar(10) COLLATE "pg_catalog"."default" DEFAULT 'GTC'::character varying,
  "client_order_id" varchar(100) COLLATE "pg_catalog"."default",
  "tags" jsonb DEFAULT '{}'::jsonb,
  "metadata" jsonb DEFAULT '{}'::jsonb,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "submitted_at" timestamptz(6),
  "filled_at" timestamptz(6),
  "cancelled_at" timestamptz(6)
)
;
ALTER TABLE "public"."orders" OWNER TO "mx";

-- ----------------------------
-- Table structure for portfolios
-- ----------------------------
DROP TABLE IF EXISTS "public"."portfolios";
CREATE TABLE "public"."portfolios" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "initial_capital" numeric(20,8) NOT NULL,
  "current_capital" numeric(20,8) NOT NULL DEFAULT 0,
  "total_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "total_fees" numeric(20,8) NOT NULL DEFAULT 0,
  "total_trades" int4 NOT NULL DEFAULT 0,
  "win_rate" numeric(5,4) DEFAULT 0,
  "sharpe_ratio" numeric(10,6),
  "max_drawdown" numeric(10,6),
  "config" jsonb DEFAULT '{}'::jsonb,
  "risk_metrics" jsonb DEFAULT '{}'::jsonb,
  "performance_metrics" jsonb DEFAULT '{}'::jsonb,
  "is_active" bool NOT NULL DEFAULT true,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
ALTER TABLE "public"."portfolios" OWNER TO "mx";

-- ----------------------------
-- Table structure for positions
-- ----------------------------
DROP TABLE IF EXISTS "public"."positions";
CREATE TABLE "public"."positions" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "portfolio_id" uuid NOT NULL,
  "trading_pair_id" int4 NOT NULL,
  "exchange_id" int4 NOT NULL,
  "side" "public"."order_side" NOT NULL,
  "quantity" numeric(20,8) NOT NULL DEFAULT 0,
  "average_price" numeric(20,8) NOT NULL DEFAULT 0,
  "market_price" numeric(20,8),
  "unrealized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "realized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "total_fees" numeric(20,8) NOT NULL DEFAULT 0,
  "cost_basis" numeric(20,8) GENERATED ALWAYS AS (
(quantity * average_price)
) STORED,
  "market_value" numeric(20,8),
  "last_updated_price_at" timestamptz(6),
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
ALTER TABLE "public"."positions" OWNER TO "mx";

-- ----------------------------
-- Table structure for risk_checks_2024_12
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_checks_2024_12";
CREATE TABLE "public"."risk_checks_2024_12" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "trading_pair" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "side" "public"."order_side" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "price" numeric(20,8),
  "order_type" "public"."order_type" NOT NULL,
  "order_value" numeric(20,8) GENERATED ALWAYS AS (
(quantity * COALESCE(price, (0)::numeric))
) STORED,
  "result" "public"."check_result" NOT NULL,
  "risk_score" numeric(5,2) NOT NULL DEFAULT 0,
  "confidence_level" numeric(5,2) DEFAULT 95.0,
  "violations" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "warnings" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "recommendations" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "applied_rules" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "max_allowed_quantity" numeric(20,8),
  "suggested_price_min" numeric(20,8),
  "suggested_price_max" numeric(20,8),
  "alternative_suggestions" jsonb DEFAULT '[]'::jsonb,
  "processing_time_ms" int4,
  "rules_evaluated" int4 DEFAULT 0,
  "cache_hit_rate" numeric(5,2) DEFAULT 0,
  "strategy_id" uuid,
  "portfolio_id" uuid,
  "engine_id" uuid,
  "session_id" varchar(100) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_by" varchar(255) COLLATE "pg_catalog"."default",
  "check_date" date NOT NULL DEFAULT CURRENT_DATE
)
;
ALTER TABLE "public"."risk_checks_2024_12" OWNER TO "mx";

-- ----------------------------
-- Table structure for risk_checks_2025_01
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_checks_2025_01";
CREATE TABLE "public"."risk_checks_2025_01" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "trading_pair" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "side" "public"."order_side" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "price" numeric(20,8),
  "order_type" "public"."order_type" NOT NULL,
  "order_value" numeric(20,8) GENERATED ALWAYS AS (
(quantity * COALESCE(price, (0)::numeric))
) STORED,
  "result" "public"."check_result" NOT NULL,
  "risk_score" numeric(5,2) NOT NULL DEFAULT 0,
  "confidence_level" numeric(5,2) DEFAULT 95.0,
  "violations" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "warnings" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "recommendations" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "applied_rules" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "max_allowed_quantity" numeric(20,8),
  "suggested_price_min" numeric(20,8),
  "suggested_price_max" numeric(20,8),
  "alternative_suggestions" jsonb DEFAULT '[]'::jsonb,
  "processing_time_ms" int4,
  "rules_evaluated" int4 DEFAULT 0,
  "cache_hit_rate" numeric(5,2) DEFAULT 0,
  "strategy_id" uuid,
  "portfolio_id" uuid,
  "engine_id" uuid,
  "session_id" varchar(100) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_by" varchar(255) COLLATE "pg_catalog"."default",
  "check_date" date NOT NULL DEFAULT CURRENT_DATE
)
;
ALTER TABLE "public"."risk_checks_2025_01" OWNER TO "mx";

-- ----------------------------
-- Table structure for risk_checks_2025_02
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_checks_2025_02";
CREATE TABLE "public"."risk_checks_2025_02" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "trading_pair" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "side" "public"."order_side" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "price" numeric(20,8),
  "order_type" "public"."order_type" NOT NULL,
  "order_value" numeric(20,8) GENERATED ALWAYS AS (
(quantity * COALESCE(price, (0)::numeric))
) STORED,
  "result" "public"."check_result" NOT NULL,
  "risk_score" numeric(5,2) NOT NULL DEFAULT 0,
  "confidence_level" numeric(5,2) DEFAULT 95.0,
  "violations" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "warnings" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "recommendations" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "applied_rules" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "max_allowed_quantity" numeric(20,8),
  "suggested_price_min" numeric(20,8),
  "suggested_price_max" numeric(20,8),
  "alternative_suggestions" jsonb DEFAULT '[]'::jsonb,
  "processing_time_ms" int4,
  "rules_evaluated" int4 DEFAULT 0,
  "cache_hit_rate" numeric(5,2) DEFAULT 0,
  "strategy_id" uuid,
  "portfolio_id" uuid,
  "engine_id" uuid,
  "session_id" varchar(100) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_by" varchar(255) COLLATE "pg_catalog"."default",
  "check_date" date NOT NULL DEFAULT CURRENT_DATE
)
;
ALTER TABLE "public"."risk_checks_2025_02" OWNER TO "mx";

-- ----------------------------
-- Table structure for risk_checks_default
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_checks_default";
CREATE TABLE "public"."risk_checks_default" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "trading_pair" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "side" "public"."order_side" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "price" numeric(20,8),
  "order_type" "public"."order_type" NOT NULL,
  "order_value" numeric(20,8) GENERATED ALWAYS AS (
(quantity * COALESCE(price, (0)::numeric))
) STORED,
  "result" "public"."check_result" NOT NULL,
  "risk_score" numeric(5,2) NOT NULL DEFAULT 0,
  "confidence_level" numeric(5,2) DEFAULT 95.0,
  "violations" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "warnings" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "recommendations" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "applied_rules" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "max_allowed_quantity" numeric(20,8),
  "suggested_price_min" numeric(20,8),
  "suggested_price_max" numeric(20,8),
  "alternative_suggestions" jsonb DEFAULT '[]'::jsonb,
  "processing_time_ms" int4,
  "rules_evaluated" int4 DEFAULT 0,
  "cache_hit_rate" numeric(5,2) DEFAULT 0,
  "strategy_id" uuid,
  "portfolio_id" uuid,
  "engine_id" uuid,
  "session_id" varchar(100) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_by" varchar(255) COLLATE "pg_catalog"."default",
  "check_date" date NOT NULL DEFAULT CURRENT_DATE
)
;
ALTER TABLE "public"."risk_checks_default" OWNER TO "mx";

-- ----------------------------
-- Table structure for risk_rules
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_rules";
CREATE TABLE "public"."risk_rules" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "category" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "rule_type" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "parameters" jsonb NOT NULL DEFAULT '{}'::jsonb,
  "conditions" jsonb DEFAULT '{}'::jsonb,
  "enabled" bool NOT NULL DEFAULT true,
  "priority" int4 NOT NULL DEFAULT 100,
  "strategy_type" varchar(100) COLLATE "pg_catalog"."default",
  "trading_pairs" jsonb DEFAULT '[]'::jsonb,
  "execution_count" int8 DEFAULT 0,
  "success_count" int8 DEFAULT 0,
  "failure_count" int8 DEFAULT 0,
  "last_executed_at" timestamptz(6),
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_by" varchar(255) COLLATE "pg_catalog"."default",
  "updated_by" varchar(255) COLLATE "pg_catalog"."default",
  "version" int4 NOT NULL DEFAULT 1,
  "is_active" bool NOT NULL DEFAULT true
)
;
ALTER TABLE "public"."risk_rules" OWNER TO "mx";
COMMENT ON COLUMN "public"."risk_rules"."category" IS '规则分类：basic, position, trading, market, time_based, advanced';
COMMENT ON COLUMN "public"."risk_rules"."rule_type" IS '具体规则类型：order_size_limit, trading_frequency等';
COMMENT ON COLUMN "public"."risk_rules"."parameters" IS '规则参数：包含所有阈值和配置';
COMMENT ON COLUMN "public"."risk_rules"."conditions" IS '执行条件：规则何时生效的条件';
COMMENT ON COLUMN "public"."risk_rules"."priority" IS '执行优先级：数值越大越先执行';
COMMENT ON COLUMN "public"."risk_rules"."strategy_type" IS '适用策略类型：NULL表示全局规则';
COMMENT ON COLUMN "public"."risk_rules"."trading_pairs" IS '适用交易对：空数组表示所有交易对';
COMMENT ON TABLE "public"."risk_rules" IS '统一风控规则表 - 包含所有风控规则的定义和参数';

-- ----------------------------
-- Table structure for risk_violations
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_violations";
CREATE TABLE "public"."risk_violations" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "risk_check_id" uuid NOT NULL,
  "rule_id" uuid NOT NULL,
  "rule_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "rule_version" int4 DEFAULT 1,
  "severity" "public"."violation_severity" NOT NULL DEFAULT 'medium'::violation_severity,
  "status" "public"."violation_status" NOT NULL DEFAULT 'active'::violation_status,
  "message" text COLLATE "pg_catalog"."default" NOT NULL,
  "detailed_message" text COLLATE "pg_catalog"."default",
  "current_value" varchar(255) COLLATE "pg_catalog"."default",
  "limit_value" varchar(255) COLLATE "pg_catalog"."default",
  "threshold_exceeded_by" numeric(20,8),
  "threshold_exceeded_percent" numeric(10,4),
  "trading_pair" varchar(50) COLLATE "pg_catalog"."default",
  "strategy_id" uuid,
  "portfolio_id" uuid,
  "order_context" jsonb DEFAULT '{}'::jsonb,
  "potential_loss" numeric(20,8),
  "risk_contribution" numeric(10,4),
  "impact_score" numeric(5,2) DEFAULT 0,
  "auto_resolved" bool DEFAULT false,
  "resolution_action" varchar(100) COLLATE "pg_catalog"."default",
  "resolution_notes" text COLLATE "pg_catalog"."default",
  "resolved_at" timestamptz(6),
  "resolved_by" varchar(255) COLLATE "pg_catalog"."default",
  "notification_sent" bool DEFAULT false,
  "notification_sent_at" timestamptz(6),
  "escalation_level" int4 DEFAULT 0,
  "escalated_at" timestamptz(6),
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_by" varchar(255) COLLATE "pg_catalog"."default"
)
;
ALTER TABLE "public"."risk_violations" OWNER TO "mx";
COMMENT ON COLUMN "public"."risk_violations"."rule_version" IS '触发违规的规则版本号';
COMMENT ON COLUMN "public"."risk_violations"."severity" IS '违规严重程度：low, medium, high, critical';
COMMENT ON COLUMN "public"."risk_violations"."status" IS '违规状态：active, resolved, ignored, escalated';
COMMENT ON COLUMN "public"."risk_violations"."threshold_exceeded_by" IS '超出阈值的绝对数值';
COMMENT ON COLUMN "public"."risk_violations"."threshold_exceeded_percent" IS '超出阈值的百分比';
COMMENT ON COLUMN "public"."risk_violations"."potential_loss" IS '潜在损失金额';
COMMENT ON COLUMN "public"."risk_violations"."risk_contribution" IS '对总体风险的贡献度百分比';
COMMENT ON COLUMN "public"."risk_violations"."impact_score" IS '影响评分：0-100，数值越高影响越大';
COMMENT ON COLUMN "public"."risk_violations"."escalation_level" IS '升级级别：0=无升级，1=一级升级，2=二级升级等';
COMMENT ON TABLE "public"."risk_violations" IS '优化后的风控违规表 - 包含完整的违规信息、处理状态和影响评估';

-- ----------------------------
-- Table structure for strategies
-- ----------------------------
DROP TABLE IF EXISTS "public"."strategies";
CREATE TABLE "public"."strategies" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "strategy_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "config" jsonb NOT NULL DEFAULT '{}'::jsonb,
  "state_data" jsonb NOT NULL DEFAULT '{}'::jsonb,
  "status" "public"."strategy_status" NOT NULL DEFAULT 'Created'::strategy_status,
  "trading_pair_id" int4,
  "portfolio_id" uuid,
  "risk_config" jsonb DEFAULT '{}'::jsonb,
  "performance_metrics" jsonb DEFAULT '{}'::jsonb,
  "created_by" varchar(100) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "started_at" timestamptz(6),
  "stopped_at" timestamptz(6)
)
;
ALTER TABLE "public"."strategies" OWNER TO "mx";

-- ----------------------------
-- Table structure for strategy_config
-- ----------------------------
DROP TABLE IF EXISTS "public"."strategy_config";
CREATE TABLE "public"."strategy_config" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'default'::character varying,
  "description" text COLLATE "pg_catalog"."default" DEFAULT '默认策略配置'::text,
  "enabled" bool NOT NULL DEFAULT true,
  "strategy_parameters" jsonb NOT NULL,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_by" varchar(100) COLLATE "pg_catalog"."default" DEFAULT 'system'::character varying
)
;
ALTER TABLE "public"."strategy_config" OWNER TO "mx";
COMMENT ON COLUMN "public"."strategy_config"."id" IS '配置唯一标识符';
COMMENT ON COLUMN "public"."strategy_config"."name" IS '配置名称';
COMMENT ON COLUMN "public"."strategy_config"."description" IS '配置描述';
COMMENT ON COLUMN "public"."strategy_config"."enabled" IS '是否启用此配置';
COMMENT ON COLUMN "public"."strategy_config"."strategy_parameters" IS '策略参数JSON配置';
COMMENT ON COLUMN "public"."strategy_config"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."strategy_config"."updated_at" IS '最后更新时间';
COMMENT ON COLUMN "public"."strategy_config"."created_by" IS '创建者';
COMMENT ON TABLE "public"."strategy_config" IS '策略配置表 - 存储系统策略相关配置参数';

-- ----------------------------
-- Table structure for strategy_performance
-- ----------------------------
DROP TABLE IF EXISTS "public"."strategy_performance";
CREATE TABLE "public"."strategy_performance" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "strategy_id" uuid NOT NULL,
  "total_trades" int4 NOT NULL DEFAULT 0,
  "winning_trades" int4 NOT NULL DEFAULT 0,
  "losing_trades" int4 NOT NULL DEFAULT 0,
  "total_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "realized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "unrealized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "win_rate" numeric(10,4) NOT NULL DEFAULT 0,
  "sharpe_ratio" numeric(10,4),
  "max_drawdown" numeric(20,8) NOT NULL DEFAULT 0,
  "max_drawdown_percentage" numeric(10,4) NOT NULL DEFAULT 0,
  "current_positions" int4 NOT NULL DEFAULT 0,
  "active_orders" int4 NOT NULL DEFAULT 0,
  "last_signal_time" timestamptz(6),
  "uptime_seconds" int8 NOT NULL DEFAULT 0,
  "error_count" int4 NOT NULL DEFAULT 0,
  "last_error" text COLLATE "pg_catalog"."default",
  "updated_at" timestamptz(6) DEFAULT now()
)
;
ALTER TABLE "public"."strategy_performance" OWNER TO "mx";

-- ----------------------------
-- Table structure for strategy_positions
-- ----------------------------
DROP TABLE IF EXISTS "public"."strategy_positions";
CREATE TABLE "public"."strategy_positions" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "strategy_id" uuid NOT NULL,
  "trading_pair" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "average_price" numeric(20,8) NOT NULL,
  "current_price" numeric(20,8),
  "market_value" numeric(20,8),
  "unrealized_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "unrealized_pnl_percentage" numeric(10,4) NOT NULL DEFAULT 0,
  "opened_at" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) DEFAULT now()
)
;
ALTER TABLE "public"."strategy_positions" OWNER TO "mx";

-- ----------------------------
-- Table structure for strategy_trades
-- ----------------------------
DROP TABLE IF EXISTS "public"."strategy_trades";
CREATE TABLE "public"."strategy_trades" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "strategy_id" uuid NOT NULL,
  "trade_id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "timestamp" timestamptz(6) NOT NULL,
  "trading_pair" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "side" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "price" numeric(20,8) NOT NULL,
  "value" numeric(20,8) NOT NULL,
  "fee" numeric(20,8) NOT NULL DEFAULT 0,
  "pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "cumulative_pnl" numeric(20,8) NOT NULL DEFAULT 0,
  "signal_type" varchar(50) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT now()
)
;
ALTER TABLE "public"."strategy_trades" OWNER TO "mx";

-- ----------------------------
-- Table structure for system_config
-- ----------------------------
DROP TABLE IF EXISTS "public"."system_config";
CREATE TABLE "public"."system_config" (
  "id" int4 NOT NULL DEFAULT nextval('system_config_id_seq'::regclass),
  "key" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "value" jsonb NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "is_encrypted" bool NOT NULL DEFAULT false,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
ALTER TABLE "public"."system_config" OWNER TO "mx";

-- ----------------------------
-- Table structure for trades
-- ----------------------------
DROP TABLE IF EXISTS "public"."trades";
CREATE TABLE "public"."trades" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "order_id" uuid NOT NULL,
  "trading_pair_id" int4 NOT NULL,
  "exchange_id" int4 NOT NULL,
  "exchange_trade_id" varchar(100) COLLATE "pg_catalog"."default",
  "side" "public"."order_side" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "price" numeric(20,8) NOT NULL,
  "quote_quantity" numeric(20,8) GENERATED ALWAYS AS (
(quantity * price)
) STORED,
  "fee" numeric(20,8) NOT NULL DEFAULT 0,
  "fee_asset" varchar(10) COLLATE "pg_catalog"."default",
  "commission_rate" numeric(10,6),
  "is_maker" bool,
  "trade_time" timestamptz(6),
  "executed_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "metadata" jsonb DEFAULT '{}'::jsonb
)
;
ALTER TABLE "public"."trades" OWNER TO "mx";

-- ----------------------------
-- Table structure for trading_config
-- ----------------------------
DROP TABLE IF EXISTS "public"."trading_config";
CREATE TABLE "public"."trading_config" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'default'::character varying,
  "description" text COLLATE "pg_catalog"."default" DEFAULT '默认交易配置'::text,
  "enabled" bool NOT NULL DEFAULT true,
  "trading_parameters" jsonb NOT NULL,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_by" varchar(100) COLLATE "pg_catalog"."default" DEFAULT 'system'::character varying
)
;
ALTER TABLE "public"."trading_config" OWNER TO "mx";
COMMENT ON COLUMN "public"."trading_config"."id" IS '配置唯一标识符';
COMMENT ON COLUMN "public"."trading_config"."name" IS '配置名称';
COMMENT ON COLUMN "public"."trading_config"."description" IS '配置描述';
COMMENT ON COLUMN "public"."trading_config"."enabled" IS '是否启用此配置';
COMMENT ON COLUMN "public"."trading_config"."trading_parameters" IS '交易参数JSON配置';
COMMENT ON COLUMN "public"."trading_config"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."trading_config"."updated_at" IS '最后更新时间';
COMMENT ON COLUMN "public"."trading_config"."created_by" IS '创建者';
COMMENT ON TABLE "public"."trading_config" IS '交易配置表 - 存储系统交易相关配置参数';

-- ----------------------------
-- Table structure for trading_pairs
-- ----------------------------
DROP TABLE IF EXISTS "public"."trading_pairs";
CREATE TABLE "public"."trading_pairs" (
  "id" int4 NOT NULL DEFAULT nextval('trading_pairs_id_seq'::regclass),
  "symbol" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "base_asset" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "quote_asset" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "exchange_id" int4,
  "is_active" bool NOT NULL DEFAULT true,
  "min_quantity" numeric(20,8) NOT NULL DEFAULT 0.00000001,
  "max_quantity" numeric(20,8),
  "quantity_precision" int4 NOT NULL DEFAULT 8,
  "price_precision" int4 NOT NULL DEFAULT 8,
  "min_price" numeric(20,8),
  "max_price" numeric(20,8),
  "tick_size" numeric(20,8) NOT NULL DEFAULT 0.00000001,
  "min_notional" numeric(20,8) NOT NULL DEFAULT 0.001,
  "trading_fee_rate" numeric(10,6) DEFAULT 0.001,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
ALTER TABLE "public"."trading_pairs" OWNER TO "mx";

-- ----------------------------
-- Table structure for risk_checks
-- ----------------------------
DROP TABLE IF EXISTS "public"."risk_checks";
CREATE TABLE "public"."risk_checks" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "trading_pair" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "side" "public"."order_side" NOT NULL,
  "quantity" numeric(20,8) NOT NULL,
  "price" numeric(20,8),
  "order_type" "public"."order_type" NOT NULL,
  "order_value" numeric(20,8) GENERATED ALWAYS AS (
(quantity * COALESCE(price, (0)::numeric))
) STORED,
  "result" "public"."check_result" NOT NULL,
  "risk_score" numeric(5,2) NOT NULL DEFAULT 0,
  "confidence_level" numeric(5,2) DEFAULT 95.0,
  "violations" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "warnings" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "recommendations" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "applied_rules" jsonb NOT NULL DEFAULT '[]'::jsonb,
  "max_allowed_quantity" numeric(20,8),
  "suggested_price_min" numeric(20,8),
  "suggested_price_max" numeric(20,8),
  "alternative_suggestions" jsonb DEFAULT '[]'::jsonb,
  "processing_time_ms" int4,
  "rules_evaluated" int4 DEFAULT 0,
  "cache_hit_rate" numeric(5,2) DEFAULT 0,
  "strategy_id" uuid,
  "portfolio_id" uuid,
  "engine_id" uuid,
  "session_id" varchar(100) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_by" varchar(255) COLLATE "pg_catalog"."default",
  "check_date" date NOT NULL DEFAULT CURRENT_DATE
)
PARTITION BY  (
)
;
ALTER TABLE "public"."risk_checks" OWNER TO "mx";
ALTER TABLE "public"."risk_checks" ATTACH PARTITION "public"."risk_checks_2024_12" DEFAULT;
ALTER TABLE "public"."risk_checks" ATTACH PARTITION "public"."risk_checks_2025_01" DEFAULT;
ALTER TABLE "public"."risk_checks" ATTACH PARTITION "public"."risk_checks_2025_02" DEFAULT;
ALTER TABLE "public"."risk_checks" ATTACH PARTITION "public"."risk_checks_default" DEFAULT;
COMMENT ON COLUMN "public"."risk_checks"."order_value" IS '订单价值：自动计算的订单总价值';
COMMENT ON COLUMN "public"."risk_checks"."result" IS '检查结果：passed, failed, warning, error';
COMMENT ON COLUMN "public"."risk_checks"."risk_score" IS '风险评分：0-100，数值越高风险越大';
COMMENT ON COLUMN "public"."risk_checks"."confidence_level" IS '置信度：检查结果的可信度百分比';
COMMENT ON COLUMN "public"."risk_checks"."applied_rules" IS '应用的规则列表：记录哪些规则参与了检查';
COMMENT ON COLUMN "public"."risk_checks"."cache_hit_rate" IS '缓存命中率：本次检查的缓存使用效率';
COMMENT ON TABLE "public"."risk_checks" IS '优化后的风控检查表 - 按日期分区，包含完整的检查结果和性能指标';

-- ----------------------------
-- Function structure for update_strategy_config_updated_at
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."update_strategy_config_updated_at"();
CREATE OR REPLACE FUNCTION "public"."update_strategy_config_updated_at"()
  RETURNS "pg_catalog"."trigger" AS $BODY$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;
ALTER FUNCTION "public"."update_strategy_config_updated_at"() OWNER TO "mx";

-- ----------------------------
-- Function structure for update_trading_config_updated_at
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."update_trading_config_updated_at"();
CREATE OR REPLACE FUNCTION "public"."update_trading_config_updated_at"()
  RETURNS "pg_catalog"."trigger" AS $BODY$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;
ALTER FUNCTION "public"."update_trading_config_updated_at"() OWNER TO "mx";

-- ----------------------------
-- Function structure for uuid_generate_v1
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_generate_v1"();
CREATE OR REPLACE FUNCTION "public"."uuid_generate_v1"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_generate_v1'
  LANGUAGE c VOLATILE STRICT
  COST 1;
ALTER FUNCTION "public"."uuid_generate_v1"() OWNER TO "mx";

-- ----------------------------
-- Function structure for uuid_generate_v1mc
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_generate_v1mc"();
CREATE OR REPLACE FUNCTION "public"."uuid_generate_v1mc"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_generate_v1mc'
  LANGUAGE c VOLATILE STRICT
  COST 1;
ALTER FUNCTION "public"."uuid_generate_v1mc"() OWNER TO "mx";

-- ----------------------------
-- Function structure for uuid_generate_v3
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_generate_v3"("namespace" uuid, "name" text);
CREATE OR REPLACE FUNCTION "public"."uuid_generate_v3"("namespace" uuid, "name" text)
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_generate_v3'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;
ALTER FUNCTION "public"."uuid_generate_v3"("namespace" uuid, "name" text) OWNER TO "mx";

-- ----------------------------
-- Function structure for uuid_generate_v4
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_generate_v4"();
CREATE OR REPLACE FUNCTION "public"."uuid_generate_v4"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_generate_v4'
  LANGUAGE c VOLATILE STRICT
  COST 1;
ALTER FUNCTION "public"."uuid_generate_v4"() OWNER TO "mx";

-- ----------------------------
-- Function structure for uuid_generate_v5
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_generate_v5"("namespace" uuid, "name" text);
CREATE OR REPLACE FUNCTION "public"."uuid_generate_v5"("namespace" uuid, "name" text)
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_generate_v5'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;
ALTER FUNCTION "public"."uuid_generate_v5"("namespace" uuid, "name" text) OWNER TO "mx";

-- ----------------------------
-- Function structure for uuid_nil
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_nil"();
CREATE OR REPLACE FUNCTION "public"."uuid_nil"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_nil'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;
ALTER FUNCTION "public"."uuid_nil"() OWNER TO "mx";

-- ----------------------------
-- Function structure for uuid_ns_dns
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_ns_dns"();
CREATE OR REPLACE FUNCTION "public"."uuid_ns_dns"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_ns_dns'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;
ALTER FUNCTION "public"."uuid_ns_dns"() OWNER TO "mx";

-- ----------------------------
-- Function structure for uuid_ns_oid
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_ns_oid"();
CREATE OR REPLACE FUNCTION "public"."uuid_ns_oid"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_ns_oid'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;
ALTER FUNCTION "public"."uuid_ns_oid"() OWNER TO "mx";

-- ----------------------------
-- Function structure for uuid_ns_url
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_ns_url"();
CREATE OR REPLACE FUNCTION "public"."uuid_ns_url"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_ns_url'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;
ALTER FUNCTION "public"."uuid_ns_url"() OWNER TO "mx";

-- ----------------------------
-- Function structure for uuid_ns_x500
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."uuid_ns_x500"();
CREATE OR REPLACE FUNCTION "public"."uuid_ns_x500"()
  RETURNS "pg_catalog"."uuid" AS '$libdir/uuid-ossp', 'uuid_ns_x500'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;
ALTER FUNCTION "public"."uuid_ns_x500"() OWNER TO "mx";

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."audit_logs_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."backtest_files_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."candles_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."events_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."exchanges_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."strategy_config_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."system_config_id_seq"', 10, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."trading_config_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."trading_pairs_id_seq"', 1, false);

-- ----------------------------
-- Indexes structure for table audit_logs
-- ----------------------------
CREATE INDEX "idx_audit_logs_changed_at" ON "public"."audit_logs" USING btree (
  "changed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_audit_logs_operation" ON "public"."audit_logs" USING btree (
  "operation" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_audit_logs_table_record" ON "public"."audit_logs" USING btree (
  "table_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "record_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Checks structure for table audit_logs
-- ----------------------------
ALTER TABLE "public"."audit_logs" ADD CONSTRAINT "audit_logs_operation_check" CHECK (operation::text = ANY (ARRAY['INSERT'::character varying::text, 'UPDATE'::character varying::text, 'DELETE'::character varying::text]));

-- ----------------------------
-- Primary Key structure for table audit_logs
-- ----------------------------
ALTER TABLE "public"."audit_logs" ADD CONSTRAINT "audit_logs_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table backtest_files
-- ----------------------------
CREATE INDEX "idx_backtest_files_timeframe" ON "public"."backtest_files" USING btree (
  "timeframe" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_backtest_files_trading_pair" ON "public"."backtest_files" USING btree (
  "trading_pair" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Uniques structure for table backtest_files
-- ----------------------------
ALTER TABLE "public"."backtest_files" ADD CONSTRAINT "backtest_files_filename_key" UNIQUE ("filename");

-- ----------------------------
-- Primary Key structure for table backtest_files
-- ----------------------------
ALTER TABLE "public"."backtest_files" ADD CONSTRAINT "backtest_files_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table backtest_portfolio_snapshots
-- ----------------------------
CREATE INDEX "idx_backtest_portfolio_snapshots_backtest_id" ON "public"."backtest_portfolio_snapshots" USING btree (
  "backtest_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_backtest_portfolio_snapshots_timestamp" ON "public"."backtest_portfolio_snapshots" USING btree (
  "timestamp" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table backtest_portfolio_snapshots
-- ----------------------------
ALTER TABLE "public"."backtest_portfolio_snapshots" ADD CONSTRAINT "backtest_portfolio_snapshots_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table backtest_results
-- ----------------------------
CREATE INDEX "idx_backtest_results_created_at" ON "public"."backtest_results" USING btree (
  "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_backtest_results_engine_id" ON "public"."backtest_results" USING btree (
  "engine_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_backtest_results_status" ON "public"."backtest_results" USING btree (
  "status" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table backtest_results
-- ----------------------------
ALTER TABLE "public"."backtest_results" ADD CONSTRAINT "backtest_results_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table backtest_trades
-- ----------------------------
CREATE INDEX "idx_backtest_trades_backtest_id" ON "public"."backtest_trades" USING btree (
  "backtest_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_backtest_trades_timestamp" ON "public"."backtest_trades" USING btree (
  "timestamp" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_backtest_trades_trading_pair" ON "public"."backtest_trades" USING btree (
  "trading_pair" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table backtest_trades
-- ----------------------------
ALTER TABLE "public"."backtest_trades" ADD CONSTRAINT "backtest_trades_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table cache_alerts
-- ----------------------------
CREATE INDEX "idx_cache_alerts_resolved" ON "public"."cache_alerts" USING btree (
  "resolved" "pg_catalog"."bool_ops" ASC NULLS LAST
);
CREATE INDEX "idx_cache_alerts_rule_name" ON "public"."cache_alerts" USING btree (
  "rule_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_cache_alerts_severity" ON "public"."cache_alerts" USING btree (
  "severity" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_cache_alerts_timestamp" ON "public"."cache_alerts" USING btree (
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS LAST
);

-- ----------------------------
-- Checks structure for table cache_alerts
-- ----------------------------
ALTER TABLE "public"."cache_alerts" ADD CONSTRAINT "cache_alerts_severity_check" CHECK (severity::text = ANY (ARRAY['info'::character varying, 'warning'::character varying, 'critical'::character varying, 'emergency'::character varying]::text[]));

-- ----------------------------
-- Primary Key structure for table cache_alerts
-- ----------------------------
ALTER TABLE "public"."cache_alerts" ADD CONSTRAINT "cache_alerts_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table cache_health
-- ----------------------------
CREATE INDEX "idx_cache_health_status" ON "public"."cache_health" USING btree (
  "status" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_cache_health_timestamp" ON "public"."cache_health" USING btree (
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS LAST
);

-- ----------------------------
-- Checks structure for table cache_health
-- ----------------------------
ALTER TABLE "public"."cache_health" ADD CONSTRAINT "cache_health_memory_usage_check" CHECK (memory_usage_percentage >= 0::numeric AND memory_usage_percentage <= 100::numeric);
ALTER TABLE "public"."cache_health" ADD CONSTRAINT "cache_health_eviction_rate_check" CHECK (eviction_rate >= 0::numeric AND eviction_rate <= 1::numeric);
ALTER TABLE "public"."cache_health" ADD CONSTRAINT "cache_health_fragmentation_check" CHECK (fragmentation_ratio >= 0::numeric AND fragmentation_ratio <= 1::numeric);
ALTER TABLE "public"."cache_health" ADD CONSTRAINT "cache_health_status_check" CHECK (status::text = ANY (ARRAY['healthy'::character varying, 'warning'::character varying, 'critical'::character varying, 'unknown'::character varying]::text[]));
ALTER TABLE "public"."cache_health" ADD CONSTRAINT "cache_health_memory_pressure_check" CHECK (memory_pressure_level::text = ANY (ARRAY['low'::character varying, 'normal'::character varying, 'high'::character varying, 'critical'::character varying]::text[]));

-- ----------------------------
-- Primary Key structure for table cache_health
-- ----------------------------
ALTER TABLE "public"."cache_health" ADD CONSTRAINT "cache_health_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table cache_performance
-- ----------------------------
CREATE INDEX "idx_cache_performance_cache_type" ON "public"."cache_performance" USING btree (
  "cache_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_cache_performance_cache_type_timestamp" ON "public"."cache_performance" USING btree (
  "cache_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS LAST
);
CREATE INDEX "idx_cache_performance_timestamp" ON "public"."cache_performance" USING btree (
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS LAST
);

-- ----------------------------
-- Checks structure for table cache_performance
-- ----------------------------
ALTER TABLE "public"."cache_performance" ADD CONSTRAINT "cache_performance_error_rate_check" CHECK (error_rate >= 0::numeric AND error_rate <= 1::numeric);

-- ----------------------------
-- Primary Key structure for table cache_performance
-- ----------------------------
ALTER TABLE "public"."cache_performance" ADD CONSTRAINT "cache_performance_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table cache_stats
-- ----------------------------
CREATE INDEX "idx_cache_stats_cache_type" ON "public"."cache_stats" USING btree (
  "cache_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_cache_stats_cache_type_timestamp" ON "public"."cache_stats" USING btree (
  "cache_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS LAST
);
CREATE INDEX "idx_cache_stats_timestamp" ON "public"."cache_stats" USING btree (
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS LAST
);

-- ----------------------------
-- Checks structure for table cache_stats
-- ----------------------------
ALTER TABLE "public"."cache_stats" ADD CONSTRAINT "cache_stats_memory_usage_check" CHECK (memory_usage_percentage >= 0::numeric AND memory_usage_percentage <= 100::numeric);
ALTER TABLE "public"."cache_stats" ADD CONSTRAINT "cache_stats_hit_rate_check" CHECK (hit_rate >= 0::numeric AND hit_rate <= 1::numeric);
ALTER TABLE "public"."cache_stats" ADD CONSTRAINT "cache_stats_miss_rate_check" CHECK (miss_rate >= 0::numeric AND miss_rate <= 1::numeric);

-- ----------------------------
-- Primary Key structure for table cache_stats
-- ----------------------------
ALTER TABLE "public"."cache_stats" ADD CONSTRAINT "cache_stats_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table candles
-- ----------------------------
CREATE INDEX "idx_candles_trading_pair_timeframe_time" ON "public"."candles" USING btree (
  "trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "timeframe" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "open_time" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);

-- ----------------------------
-- Uniques structure for table candles
-- ----------------------------
ALTER TABLE "public"."candles" ADD CONSTRAINT "candles_trading_pair_id_timeframe_open_time_key" UNIQUE ("trading_pair_id", "timeframe", "open_time");

-- ----------------------------
-- Primary Key structure for table candles
-- ----------------------------
ALTER TABLE "public"."candles" ADD CONSTRAINT "candles_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table engines
-- ----------------------------
CREATE INDEX "idx_engines_engine_type" ON "public"."engines" USING btree (
  "engine_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_engines_status" ON "public"."engines" USING btree (
  "status" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table engines
-- ----------------------------
ALTER TABLE "public"."engines" ADD CONSTRAINT "engines_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table events
-- ----------------------------
CREATE INDEX "idx_events_type" ON "public"."events" USING btree (
  "event_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table events
-- ----------------------------
ALTER TABLE "public"."events" ADD CONSTRAINT "events_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table exchanges
-- ----------------------------
CREATE INDEX "idx_exchanges_name" ON "public"."exchanges" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_exchanges_status" ON "public"."exchanges" USING btree (
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST
);

-- ----------------------------
-- Uniques structure for table exchanges
-- ----------------------------
ALTER TABLE "public"."exchanges" ADD CONSTRAINT "exchanges_name_key" UNIQUE ("name");

-- ----------------------------
-- Primary Key structure for table exchanges
-- ----------------------------
ALTER TABLE "public"."exchanges" ADD CONSTRAINT "exchanges_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table monitoring_alerts
-- ----------------------------
CREATE INDEX "idx_monitoring_alerts_date_partition" ON "public"."monitoring_alerts" USING btree (
  "date_partition" "pg_catalog"."date_ops" ASC NULLS LAST
);
CREATE INDEX "idx_monitoring_alerts_rule_id" ON "public"."monitoring_alerts" USING btree (
  "rule_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_monitoring_alerts_severity" ON "public"."monitoring_alerts" USING btree (
  "severity" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_monitoring_alerts_severity_time" ON "public"."monitoring_alerts" USING btree (
  "severity" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "triggered_at" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
);
CREATE INDEX "idx_monitoring_alerts_status" ON "public"."monitoring_alerts" USING btree (
  "status" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_monitoring_alerts_status_time" ON "public"."monitoring_alerts" USING btree (
  "status" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "triggered_at" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
);
CREATE INDEX "idx_monitoring_alerts_triggered_at" ON "public"."monitoring_alerts" USING btree (
  "triggered_at" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
);

-- ----------------------------
-- Checks structure for table monitoring_alerts
-- ----------------------------
ALTER TABLE "public"."monitoring_alerts" ADD CONSTRAINT "chk_monitoring_alerts_severity" CHECK (severity::text = ANY (ARRAY['info'::character varying, 'warning'::character varying, 'critical'::character varying, 'emergency'::character varying]::text[]));
ALTER TABLE "public"."monitoring_alerts" ADD CONSTRAINT "chk_monitoring_alerts_status" CHECK (status::text = ANY (ARRAY['triggered'::character varying, 'acknowledged'::character varying, 'resolved'::character varying, 'suppressed'::character varying]::text[]));
ALTER TABLE "public"."monitoring_alerts" ADD CONSTRAINT "chk_monitoring_alerts_condition" CHECK (condition::text = ANY (ARRAY['>'::character varying, '<'::character varying, '='::character varying, '>='::character varying, '<='::character varying, '!='::character varying]::text[]));

-- ----------------------------
-- Primary Key structure for table monitoring_alerts
-- ----------------------------
ALTER TABLE "public"."monitoring_alerts" ADD CONSTRAINT "monitoring_alerts_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table monitoring_config
-- ----------------------------
CREATE INDEX "idx_monitoring_config_created_at" ON "public"."monitoring_config" USING btree (
  "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_monitoring_config_enabled" ON "public"."monitoring_config" USING btree (
  "enabled" "pg_catalog"."bool_ops" ASC NULLS LAST
);
CREATE INDEX "idx_monitoring_config_name" ON "public"."monitoring_config" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Checks structure for table monitoring_config
-- ----------------------------
ALTER TABLE "public"."monitoring_config" ADD CONSTRAINT "chk_monitoring_config_name_not_empty" CHECK (length(TRIM(BOTH FROM name)) > 0);

-- ----------------------------
-- Primary Key structure for table monitoring_config
-- ----------------------------
ALTER TABLE "public"."monitoring_config" ADD CONSTRAINT "monitoring_config_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table monitoring_metrics
-- ----------------------------
CREATE INDEX "idx_monitoring_metrics_date_partition" ON "public"."monitoring_metrics" USING btree (
  "date_partition" "pg_catalog"."date_ops" ASC NULLS LAST
);
CREATE INDEX "idx_monitoring_metrics_name_time" ON "public"."monitoring_metrics" USING btree (
  "metric_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
);
CREATE INDEX "idx_monitoring_metrics_source" ON "public"."monitoring_metrics" USING btree (
  "source" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_monitoring_metrics_timestamp" ON "public"."monitoring_metrics" USING btree (
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
);
CREATE INDEX "idx_monitoring_metrics_type_name" ON "public"."monitoring_metrics" USING btree (
  "metric_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "metric_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_monitoring_metrics_type_time" ON "public"."monitoring_metrics" USING btree (
  "metric_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
);

-- ----------------------------
-- Checks structure for table monitoring_metrics
-- ----------------------------
ALTER TABLE "public"."monitoring_metrics" ADD CONSTRAINT "chk_monitoring_metrics_type_not_empty" CHECK (length(TRIM(BOTH FROM metric_type)) > 0);
ALTER TABLE "public"."monitoring_metrics" ADD CONSTRAINT "chk_monitoring_metrics_name_not_empty" CHECK (length(TRIM(BOTH FROM metric_name)) > 0);

-- ----------------------------
-- Primary Key structure for table monitoring_metrics
-- ----------------------------
ALTER TABLE "public"."monitoring_metrics" ADD CONSTRAINT "monitoring_metrics_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table orders
-- ----------------------------
CREATE INDEX "idx_orders_active_status" ON "public"."orders" USING btree (
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
) WHERE status = ANY (ARRAY['Pending'::order_status, 'PartiallyFilled'::order_status]);
CREATE INDEX "idx_orders_client_order_id" ON "public"."orders" USING btree (
  "client_order_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_created_at" ON "public"."orders" USING btree (
  "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_created_status" ON "public"."orders" USING btree (
  "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST,
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_exchange" ON "public"."orders" USING btree (
  "exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_exchange_fk" ON "public"."orders" USING btree (
  "exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_exchange_order_id" ON "public"."orders" USING btree (
  "exchange_order_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_exchange_status" ON "public"."orders" USING btree (
  "exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_metadata_gin" ON "public"."orders" USING gin (
  "metadata" "pg_catalog"."jsonb_ops"
);
CREATE INDEX "idx_orders_parent" ON "public"."orders" USING btree (
  "parent_order_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_side" ON "public"."orders" USING btree (
  "side" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_stats" ON "public"."orders" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST,
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "side" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_status" ON "public"."orders" USING btree (
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_strategy" ON "public"."orders" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_strategy_fk" ON "public"."orders" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_strategy_status" ON "public"."orders" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST,
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_time_range" ON "public"."orders" USING btree (
  "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST,
  "updated_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_trading_pair" ON "public"."orders" USING btree (
  "trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_trading_pair_fk" ON "public"."orders" USING btree (
  "trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_orders_trading_pair_status" ON "public"."orders" USING btree (
  "trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST
);

-- ----------------------------
-- Checks structure for table orders
-- ----------------------------
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_price_check" CHECK (price > 0::numeric);
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_stop_price_check" CHECK (stop_price > 0::numeric);
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_filled_quantity_check" CHECK (filled_quantity >= 0::numeric);
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_quantity_check" CHECK (quantity > 0::numeric);
ALTER TABLE "public"."orders" ADD CONSTRAINT "chk_filled_quantity" CHECK (filled_quantity <= quantity);
ALTER TABLE "public"."orders" ADD CONSTRAINT "chk_price_for_limit" CHECK (order_type = 'Market'::order_type OR (order_type = ANY (ARRAY['Limit'::order_type, 'StopLimit'::order_type])) AND price IS NOT NULL);
ALTER TABLE "public"."orders" ADD CONSTRAINT "chk_stop_price_for_stop" CHECK ((order_type <> ALL (ARRAY['StopLoss'::order_type, 'StopLimit'::order_type])) OR (order_type = ANY (ARRAY['StopLoss'::order_type, 'StopLimit'::order_type])) AND stop_price IS NOT NULL);
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_average_price_check" CHECK (average_price > 0::numeric);

-- ----------------------------
-- Primary Key structure for table orders
-- ----------------------------
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table portfolios
-- ----------------------------
CREATE INDEX "idx_portfolios_active" ON "public"."portfolios" USING btree (
  "is_active" "pg_catalog"."bool_ops" ASC NULLS LAST
);
CREATE INDEX "idx_portfolios_created_at" ON "public"."portfolios" USING btree (
  "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_portfolios_name" ON "public"."portfolios" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_portfolios_name_gin" ON "public"."portfolios" USING gin (
  to_tsvector('english'::regconfig, name::text) "pg_catalog"."tsvector_ops"
);

-- ----------------------------
-- Checks structure for table portfolios
-- ----------------------------
ALTER TABLE "public"."portfolios" ADD CONSTRAINT "portfolios_initial_capital_check" CHECK (initial_capital > 0::numeric);

-- ----------------------------
-- Primary Key structure for table portfolios
-- ----------------------------
ALTER TABLE "public"."portfolios" ADD CONSTRAINT "portfolios_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table positions
-- ----------------------------
CREATE INDEX "idx_positions_exchange" ON "public"."positions" USING btree (
  "exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_positions_portfolio" ON "public"."positions" USING btree (
  "portfolio_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_positions_portfolio_fk" ON "public"."positions" USING btree (
  "portfolio_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_positions_portfolio_id" ON "public"."positions" USING btree (
  "portfolio_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_positions_trading_pair" ON "public"."positions" USING btree (
  "trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_positions_updated_at" ON "public"."positions" USING btree (
  "updated_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);

-- ----------------------------
-- Uniques structure for table positions
-- ----------------------------
ALTER TABLE "public"."positions" ADD CONSTRAINT "positions_portfolio_id_trading_pair_id_exchange_id_side_key" UNIQUE ("portfolio_id", "trading_pair_id", "exchange_id", "side");

-- ----------------------------
-- Checks structure for table positions
-- ----------------------------
ALTER TABLE "public"."positions" ADD CONSTRAINT "positions_average_price_check" CHECK (average_price >= 0::numeric);

-- ----------------------------
-- Primary Key structure for table positions
-- ----------------------------
ALTER TABLE "public"."positions" ADD CONSTRAINT "positions_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table risk_checks_2024_12
-- ----------------------------
CREATE INDEX "risk_checks_2024_12_applied_rules_idx" ON "public"."risk_checks_2024_12" USING gin (
  "applied_rules" "pg_catalog"."jsonb_ops"
);
CREATE INDEX "risk_checks_2024_12_check_date_idx" ON "public"."risk_checks_2024_12" USING btree (
  "check_date" "pg_catalog"."date_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_2024_12_processing_time_ms_idx" ON "public"."risk_checks_2024_12" USING btree (
  "processing_time_ms" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_2024_12_processing_time_ms_timestamp_idx" ON "public"."risk_checks_2024_12" USING btree (
  "processing_time_ms" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE processing_time_ms > 1000;
CREATE INDEX "risk_checks_2024_12_result_idx" ON "public"."risk_checks_2024_12" USING btree (
  "result" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_2024_12_result_timestamp_idx" ON "public"."risk_checks_2024_12" USING btree (
  "result" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE result = 'failed'::check_result;
CREATE INDEX "risk_checks_2024_12_risk_score_idx" ON "public"."risk_checks_2024_12" USING btree (
  "risk_score" "pg_catalog"."numeric_ops" DESC NULLS FIRST
);
CREATE INDEX "risk_checks_2024_12_risk_score_timestamp_idx" ON "public"."risk_checks_2024_12" USING btree (
  "risk_score" "pg_catalog"."numeric_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE risk_score >= 70::numeric;
CREATE INDEX "risk_checks_2024_12_strategy_id_idx" ON "public"."risk_checks_2024_12" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_2024_12_strategy_id_timestamp_idx" ON "public"."risk_checks_2024_12" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE strategy_id IS NOT NULL;
CREATE INDEX "risk_checks_2024_12_timestamp_idx" ON "public"."risk_checks_2024_12" USING btree (
  "timestamp" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_2024_12_trading_pair_idx" ON "public"."risk_checks_2024_12" USING btree (
  "trading_pair" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_2024_12_trading_pair_result_timestamp_idx" ON "public"."risk_checks_2024_12" USING btree (
  "trading_pair" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "result" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
);
CREATE INDEX "risk_checks_2024_12_violations_idx" ON "public"."risk_checks_2024_12" USING gin (
  "violations" "pg_catalog"."jsonb_ops"
);
CREATE INDEX "risk_checks_2024_12_warnings_idx" ON "public"."risk_checks_2024_12" USING gin (
  "warnings" "pg_catalog"."jsonb_ops"
);

-- ----------------------------
-- Checks structure for table risk_checks_2024_12
-- ----------------------------
ALTER TABLE "public"."risk_checks_2024_12" ADD CONSTRAINT "check_price_positive" CHECK (price IS NULL OR price > 0::numeric);
ALTER TABLE "public"."risk_checks_2024_12" ADD CONSTRAINT "check_processing_time_positive" CHECK (processing_time_ms >= 0);
ALTER TABLE "public"."risk_checks_2024_12" ADD CONSTRAINT "check_rules_evaluated_positive" CHECK (rules_evaluated >= 0);
ALTER TABLE "public"."risk_checks_2024_12" ADD CONSTRAINT "check_cache_hit_rate_range" CHECK (cache_hit_rate >= 0::numeric AND cache_hit_rate <= 100::numeric);
ALTER TABLE "public"."risk_checks_2024_12" ADD CONSTRAINT "check_risk_score_range" CHECK (risk_score >= 0::numeric AND risk_score <= 100::numeric);
ALTER TABLE "public"."risk_checks_2024_12" ADD CONSTRAINT "check_confidence_level_range" CHECK (confidence_level >= 0::numeric AND confidence_level <= 100::numeric);
ALTER TABLE "public"."risk_checks_2024_12" ADD CONSTRAINT "check_max_allowed_quantity_positive" CHECK (max_allowed_quantity IS NULL OR max_allowed_quantity > 0::numeric);
ALTER TABLE "public"."risk_checks_2024_12" ADD CONSTRAINT "check_quantity_positive" CHECK (quantity > 0::numeric);
ALTER TABLE "public"."risk_checks_2024_12" ADD CONSTRAINT "check_suggested_prices_positive" CHECK ((suggested_price_min IS NULL OR suggested_price_min > 0::numeric) AND (suggested_price_max IS NULL OR suggested_price_max > 0::numeric) AND (suggested_price_min IS NULL OR suggested_price_max IS NULL OR suggested_price_min <= suggested_price_max));

-- ----------------------------
-- Primary Key structure for table risk_checks_2024_12
-- ----------------------------
ALTER TABLE "public"."risk_checks_2024_12" ADD CONSTRAINT "risk_checks_2024_12_pkey" PRIMARY KEY ("id", "check_date");

-- ----------------------------
-- Indexes structure for table risk_checks_2025_01
-- ----------------------------
CREATE INDEX "risk_checks_2025_01_applied_rules_idx" ON "public"."risk_checks_2025_01" USING gin (
  "applied_rules" "pg_catalog"."jsonb_ops"
);
CREATE INDEX "risk_checks_2025_01_check_date_idx" ON "public"."risk_checks_2025_01" USING btree (
  "check_date" "pg_catalog"."date_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_2025_01_processing_time_ms_idx" ON "public"."risk_checks_2025_01" USING btree (
  "processing_time_ms" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_2025_01_processing_time_ms_timestamp_idx" ON "public"."risk_checks_2025_01" USING btree (
  "processing_time_ms" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE processing_time_ms > 1000;
CREATE INDEX "risk_checks_2025_01_result_idx" ON "public"."risk_checks_2025_01" USING btree (
  "result" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_2025_01_result_timestamp_idx" ON "public"."risk_checks_2025_01" USING btree (
  "result" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE result = 'failed'::check_result;
CREATE INDEX "risk_checks_2025_01_risk_score_idx" ON "public"."risk_checks_2025_01" USING btree (
  "risk_score" "pg_catalog"."numeric_ops" DESC NULLS FIRST
);
CREATE INDEX "risk_checks_2025_01_risk_score_timestamp_idx" ON "public"."risk_checks_2025_01" USING btree (
  "risk_score" "pg_catalog"."numeric_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE risk_score >= 70::numeric;
CREATE INDEX "risk_checks_2025_01_strategy_id_idx" ON "public"."risk_checks_2025_01" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_2025_01_strategy_id_timestamp_idx" ON "public"."risk_checks_2025_01" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE strategy_id IS NOT NULL;
CREATE INDEX "risk_checks_2025_01_timestamp_idx" ON "public"."risk_checks_2025_01" USING btree (
  "timestamp" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_2025_01_trading_pair_idx" ON "public"."risk_checks_2025_01" USING btree (
  "trading_pair" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_2025_01_trading_pair_result_timestamp_idx" ON "public"."risk_checks_2025_01" USING btree (
  "trading_pair" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "result" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
);
CREATE INDEX "risk_checks_2025_01_violations_idx" ON "public"."risk_checks_2025_01" USING gin (
  "violations" "pg_catalog"."jsonb_ops"
);
CREATE INDEX "risk_checks_2025_01_warnings_idx" ON "public"."risk_checks_2025_01" USING gin (
  "warnings" "pg_catalog"."jsonb_ops"
);

-- ----------------------------
-- Checks structure for table risk_checks_2025_01
-- ----------------------------
ALTER TABLE "public"."risk_checks_2025_01" ADD CONSTRAINT "check_processing_time_positive" CHECK (processing_time_ms >= 0);
ALTER TABLE "public"."risk_checks_2025_01" ADD CONSTRAINT "check_rules_evaluated_positive" CHECK (rules_evaluated >= 0);
ALTER TABLE "public"."risk_checks_2025_01" ADD CONSTRAINT "check_price_positive" CHECK (price IS NULL OR price > 0::numeric);
ALTER TABLE "public"."risk_checks_2025_01" ADD CONSTRAINT "check_risk_score_range" CHECK (risk_score >= 0::numeric AND risk_score <= 100::numeric);
ALTER TABLE "public"."risk_checks_2025_01" ADD CONSTRAINT "check_confidence_level_range" CHECK (confidence_level >= 0::numeric AND confidence_level <= 100::numeric);
ALTER TABLE "public"."risk_checks_2025_01" ADD CONSTRAINT "check_cache_hit_rate_range" CHECK (cache_hit_rate >= 0::numeric AND cache_hit_rate <= 100::numeric);
ALTER TABLE "public"."risk_checks_2025_01" ADD CONSTRAINT "check_max_allowed_quantity_positive" CHECK (max_allowed_quantity IS NULL OR max_allowed_quantity > 0::numeric);
ALTER TABLE "public"."risk_checks_2025_01" ADD CONSTRAINT "check_quantity_positive" CHECK (quantity > 0::numeric);
ALTER TABLE "public"."risk_checks_2025_01" ADD CONSTRAINT "check_suggested_prices_positive" CHECK ((suggested_price_min IS NULL OR suggested_price_min > 0::numeric) AND (suggested_price_max IS NULL OR suggested_price_max > 0::numeric) AND (suggested_price_min IS NULL OR suggested_price_max IS NULL OR suggested_price_min <= suggested_price_max));

-- ----------------------------
-- Primary Key structure for table risk_checks_2025_01
-- ----------------------------
ALTER TABLE "public"."risk_checks_2025_01" ADD CONSTRAINT "risk_checks_2025_01_pkey" PRIMARY KEY ("id", "check_date");

-- ----------------------------
-- Indexes structure for table risk_checks_2025_02
-- ----------------------------
CREATE INDEX "risk_checks_2025_02_applied_rules_idx" ON "public"."risk_checks_2025_02" USING gin (
  "applied_rules" "pg_catalog"."jsonb_ops"
);
CREATE INDEX "risk_checks_2025_02_check_date_idx" ON "public"."risk_checks_2025_02" USING btree (
  "check_date" "pg_catalog"."date_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_2025_02_processing_time_ms_idx" ON "public"."risk_checks_2025_02" USING btree (
  "processing_time_ms" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_2025_02_processing_time_ms_timestamp_idx" ON "public"."risk_checks_2025_02" USING btree (
  "processing_time_ms" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE processing_time_ms > 1000;
CREATE INDEX "risk_checks_2025_02_result_idx" ON "public"."risk_checks_2025_02" USING btree (
  "result" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_2025_02_result_timestamp_idx" ON "public"."risk_checks_2025_02" USING btree (
  "result" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE result = 'failed'::check_result;
CREATE INDEX "risk_checks_2025_02_risk_score_idx" ON "public"."risk_checks_2025_02" USING btree (
  "risk_score" "pg_catalog"."numeric_ops" DESC NULLS FIRST
);
CREATE INDEX "risk_checks_2025_02_risk_score_timestamp_idx" ON "public"."risk_checks_2025_02" USING btree (
  "risk_score" "pg_catalog"."numeric_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE risk_score >= 70::numeric;
CREATE INDEX "risk_checks_2025_02_strategy_id_idx" ON "public"."risk_checks_2025_02" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_2025_02_strategy_id_timestamp_idx" ON "public"."risk_checks_2025_02" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE strategy_id IS NOT NULL;
CREATE INDEX "risk_checks_2025_02_timestamp_idx" ON "public"."risk_checks_2025_02" USING btree (
  "timestamp" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_2025_02_trading_pair_idx" ON "public"."risk_checks_2025_02" USING btree (
  "trading_pair" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_2025_02_trading_pair_result_timestamp_idx" ON "public"."risk_checks_2025_02" USING btree (
  "trading_pair" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "result" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
);
CREATE INDEX "risk_checks_2025_02_violations_idx" ON "public"."risk_checks_2025_02" USING gin (
  "violations" "pg_catalog"."jsonb_ops"
);
CREATE INDEX "risk_checks_2025_02_warnings_idx" ON "public"."risk_checks_2025_02" USING gin (
  "warnings" "pg_catalog"."jsonb_ops"
);

-- ----------------------------
-- Checks structure for table risk_checks_2025_02
-- ----------------------------
ALTER TABLE "public"."risk_checks_2025_02" ADD CONSTRAINT "check_quantity_positive" CHECK (quantity > 0::numeric);
ALTER TABLE "public"."risk_checks_2025_02" ADD CONSTRAINT "check_processing_time_positive" CHECK (processing_time_ms >= 0);
ALTER TABLE "public"."risk_checks_2025_02" ADD CONSTRAINT "check_rules_evaluated_positive" CHECK (rules_evaluated >= 0);
ALTER TABLE "public"."risk_checks_2025_02" ADD CONSTRAINT "check_price_positive" CHECK (price IS NULL OR price > 0::numeric);
ALTER TABLE "public"."risk_checks_2025_02" ADD CONSTRAINT "check_risk_score_range" CHECK (risk_score >= 0::numeric AND risk_score <= 100::numeric);
ALTER TABLE "public"."risk_checks_2025_02" ADD CONSTRAINT "check_confidence_level_range" CHECK (confidence_level >= 0::numeric AND confidence_level <= 100::numeric);
ALTER TABLE "public"."risk_checks_2025_02" ADD CONSTRAINT "check_cache_hit_rate_range" CHECK (cache_hit_rate >= 0::numeric AND cache_hit_rate <= 100::numeric);
ALTER TABLE "public"."risk_checks_2025_02" ADD CONSTRAINT "check_max_allowed_quantity_positive" CHECK (max_allowed_quantity IS NULL OR max_allowed_quantity > 0::numeric);
ALTER TABLE "public"."risk_checks_2025_02" ADD CONSTRAINT "check_suggested_prices_positive" CHECK ((suggested_price_min IS NULL OR suggested_price_min > 0::numeric) AND (suggested_price_max IS NULL OR suggested_price_max > 0::numeric) AND (suggested_price_min IS NULL OR suggested_price_max IS NULL OR suggested_price_min <= suggested_price_max));

-- ----------------------------
-- Primary Key structure for table risk_checks_2025_02
-- ----------------------------
ALTER TABLE "public"."risk_checks_2025_02" ADD CONSTRAINT "risk_checks_2025_02_pkey" PRIMARY KEY ("id", "check_date");

-- ----------------------------
-- Indexes structure for table risk_checks_default
-- ----------------------------
CREATE INDEX "risk_checks_default_applied_rules_idx" ON "public"."risk_checks_default" USING gin (
  "applied_rules" "pg_catalog"."jsonb_ops"
);
CREATE INDEX "risk_checks_default_check_date_idx" ON "public"."risk_checks_default" USING btree (
  "check_date" "pg_catalog"."date_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_default_processing_time_ms_idx" ON "public"."risk_checks_default" USING btree (
  "processing_time_ms" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_default_processing_time_ms_timestamp_idx" ON "public"."risk_checks_default" USING btree (
  "processing_time_ms" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE processing_time_ms > 1000;
CREATE INDEX "risk_checks_default_result_idx" ON "public"."risk_checks_default" USING btree (
  "result" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_default_result_timestamp_idx" ON "public"."risk_checks_default" USING btree (
  "result" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE result = 'failed'::check_result;
CREATE INDEX "risk_checks_default_risk_score_idx" ON "public"."risk_checks_default" USING btree (
  "risk_score" "pg_catalog"."numeric_ops" DESC NULLS FIRST
);
CREATE INDEX "risk_checks_default_risk_score_timestamp_idx" ON "public"."risk_checks_default" USING btree (
  "risk_score" "pg_catalog"."numeric_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE risk_score >= 70::numeric;
CREATE INDEX "risk_checks_default_strategy_id_idx" ON "public"."risk_checks_default" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_default_strategy_id_timestamp_idx" ON "public"."risk_checks_default" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE strategy_id IS NOT NULL;
CREATE INDEX "risk_checks_default_timestamp_idx" ON "public"."risk_checks_default" USING btree (
  "timestamp" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_default_trading_pair_idx" ON "public"."risk_checks_default" USING btree (
  "trading_pair" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "risk_checks_default_trading_pair_result_timestamp_idx" ON "public"."risk_checks_default" USING btree (
  "trading_pair" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "result" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
);
CREATE INDEX "risk_checks_default_violations_idx" ON "public"."risk_checks_default" USING gin (
  "violations" "pg_catalog"."jsonb_ops"
);
CREATE INDEX "risk_checks_default_warnings_idx" ON "public"."risk_checks_default" USING gin (
  "warnings" "pg_catalog"."jsonb_ops"
);

-- ----------------------------
-- Checks structure for table risk_checks_default
-- ----------------------------
ALTER TABLE "public"."risk_checks_default" ADD CONSTRAINT "check_quantity_positive" CHECK (quantity > 0::numeric);
ALTER TABLE "public"."risk_checks_default" ADD CONSTRAINT "check_processing_time_positive" CHECK (processing_time_ms >= 0);
ALTER TABLE "public"."risk_checks_default" ADD CONSTRAINT "check_rules_evaluated_positive" CHECK (rules_evaluated >= 0);
ALTER TABLE "public"."risk_checks_default" ADD CONSTRAINT "check_price_positive" CHECK (price IS NULL OR price > 0::numeric);
ALTER TABLE "public"."risk_checks_default" ADD CONSTRAINT "check_risk_score_range" CHECK (risk_score >= 0::numeric AND risk_score <= 100::numeric);
ALTER TABLE "public"."risk_checks_default" ADD CONSTRAINT "check_confidence_level_range" CHECK (confidence_level >= 0::numeric AND confidence_level <= 100::numeric);
ALTER TABLE "public"."risk_checks_default" ADD CONSTRAINT "check_cache_hit_rate_range" CHECK (cache_hit_rate >= 0::numeric AND cache_hit_rate <= 100::numeric);
ALTER TABLE "public"."risk_checks_default" ADD CONSTRAINT "check_max_allowed_quantity_positive" CHECK (max_allowed_quantity IS NULL OR max_allowed_quantity > 0::numeric);
ALTER TABLE "public"."risk_checks_default" ADD CONSTRAINT "check_suggested_prices_positive" CHECK ((suggested_price_min IS NULL OR suggested_price_min > 0::numeric) AND (suggested_price_max IS NULL OR suggested_price_max > 0::numeric) AND (suggested_price_min IS NULL OR suggested_price_max IS NULL OR suggested_price_min <= suggested_price_max));

-- ----------------------------
-- Primary Key structure for table risk_checks_default
-- ----------------------------
ALTER TABLE "public"."risk_checks_default" ADD CONSTRAINT "risk_checks_default_pkey" PRIMARY KEY ("id", "check_date");

-- ----------------------------
-- Indexes structure for table risk_rules
-- ----------------------------
CREATE INDEX "idx_unified_risk_rules_active" ON "public"."risk_rules" USING btree (
  "is_active" "pg_catalog"."bool_ops" ASC NULLS LAST,
  "enabled" "pg_catalog"."bool_ops" ASC NULLS LAST
);
CREATE INDEX "idx_unified_risk_rules_category" ON "public"."risk_rules" USING btree (
  "category" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_unified_risk_rules_enabled" ON "public"."risk_rules" USING btree (
  "enabled" "pg_catalog"."bool_ops" ASC NULLS LAST
);
CREATE INDEX "idx_unified_risk_rules_execution" ON "public"."risk_rules" USING btree (
  "enabled" "pg_catalog"."bool_ops" ASC NULLS LAST,
  "is_active" "pg_catalog"."bool_ops" ASC NULLS LAST,
  "priority" "pg_catalog"."int4_ops" DESC NULLS FIRST,
  "category" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_unified_risk_rules_priority" ON "public"."risk_rules" USING btree (
  "priority" "pg_catalog"."int4_ops" DESC NULLS FIRST
);
CREATE INDEX "idx_unified_risk_rules_strategy" ON "public"."risk_rules" USING btree (
  "strategy_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_unified_risk_rules_type" ON "public"."risk_rules" USING btree (
  "rule_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table risk_rules
-- ----------------------------
ALTER TABLE "public"."risk_rules" ADD CONSTRAINT "unified_risk_rules_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table risk_violations
-- ----------------------------
CREATE INDEX "idx_risk_violations_active" ON "public"."risk_violations" USING btree (
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "severity" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "created_at" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE status = 'active'::violation_status;
CREATE INDEX "idx_risk_violations_check_id" ON "public"."risk_violations" USING btree (
  "risk_check_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_risk_violations_created_at" ON "public"."risk_violations" USING btree (
  "created_at" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
);
CREATE INDEX "idx_risk_violations_critical" ON "public"."risk_violations" USING btree (
  "severity" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "created_at" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE severity = 'critical'::violation_severity;
CREATE INDEX "idx_risk_violations_escalation" ON "public"."risk_violations" USING btree (
  "escalation_level" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "escalated_at" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE escalation_level > 0;
CREATE INDEX "idx_risk_violations_impact_score" ON "public"."risk_violations" USING btree (
  "impact_score" "pg_catalog"."numeric_ops" DESC NULLS FIRST
);
CREATE INDEX "idx_risk_violations_notification_pending" ON "public"."risk_violations" USING btree (
  "notification_sent" "pg_catalog"."bool_ops" ASC NULLS LAST,
  "severity" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
) WHERE notification_sent = false;
CREATE INDEX "idx_risk_violations_order_context_gin" ON "public"."risk_violations" USING gin (
  "order_context" "pg_catalog"."jsonb_ops"
);
CREATE INDEX "idx_risk_violations_potential_loss" ON "public"."risk_violations" USING btree (
  "potential_loss" "pg_catalog"."numeric_ops" DESC NULLS FIRST
);
CREATE INDEX "idx_risk_violations_resolved_at" ON "public"."risk_violations" USING btree (
  "resolved_at" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
);
CREATE INDEX "idx_risk_violations_rule_analysis" ON "public"."risk_violations" USING btree (
  "rule_id" "pg_catalog"."uuid_ops" ASC NULLS LAST,
  "rule_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "severity" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "created_at" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
);
CREATE INDEX "idx_risk_violations_rule_id" ON "public"."risk_violations" USING btree (
  "rule_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_risk_violations_rule_version" ON "public"."risk_violations" USING btree (
  "rule_id" "pg_catalog"."uuid_ops" ASC NULLS LAST,
  "rule_version" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "created_at" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
);
CREATE INDEX "idx_risk_violations_severity" ON "public"."risk_violations" USING btree (
  "severity" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "idx_risk_violations_status" ON "public"."risk_violations" USING btree (
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "idx_risk_violations_strategy" ON "public"."risk_violations" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST,
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "created_at" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE strategy_id IS NOT NULL;
CREATE INDEX "idx_risk_violations_trading_pair" ON "public"."risk_violations" USING btree (
  "trading_pair" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "severity" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "created_at" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE trading_pair IS NOT NULL;
CREATE INDEX "idx_risk_violations_unresolved" ON "public"."risk_violations" USING btree (
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "created_at" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE status = ANY (ARRAY['active'::violation_status, 'escalated'::violation_status]);

-- ----------------------------
-- Checks structure for table risk_violations
-- ----------------------------
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_threshold_exceeded_by_positive" CHECK (threshold_exceeded_by IS NULL OR threshold_exceeded_by >= 0::numeric);
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_threshold_exceeded_percent_positive" CHECK (threshold_exceeded_percent IS NULL OR threshold_exceeded_percent >= 0::numeric);
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_potential_loss_positive" CHECK (potential_loss IS NULL OR potential_loss >= 0::numeric);
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_risk_contribution_range" CHECK (risk_contribution IS NULL OR risk_contribution >= 0::numeric AND risk_contribution <= 100::numeric);
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_impact_score_range" CHECK (impact_score >= 0::numeric AND impact_score <= 100::numeric);
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_escalation_level_positive" CHECK (escalation_level >= 0);
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_rule_version_positive" CHECK (rule_version > 0);
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_resolution_consistency" CHECK (status = 'resolved'::violation_status AND resolved_at IS NOT NULL AND resolved_by IS NOT NULL OR status <> 'resolved'::violation_status AND (resolved_at IS NULL OR resolved_by IS NULL));
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_escalation_consistency" CHECK (escalation_level > 0 AND escalated_at IS NOT NULL OR escalation_level = 0 AND escalated_at IS NULL);
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_notification_consistency" CHECK (notification_sent = true AND notification_sent_at IS NOT NULL OR notification_sent = false AND notification_sent_at IS NULL);
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_resolved_after_created" CHECK (resolved_at IS NULL OR resolved_at >= created_at);
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_escalated_after_created" CHECK (escalated_at IS NULL OR escalated_at >= created_at);
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "check_notification_after_created" CHECK (notification_sent_at IS NULL OR notification_sent_at >= created_at);

-- ----------------------------
-- Primary Key structure for table risk_violations
-- ----------------------------
ALTER TABLE "public"."risk_violations" ADD CONSTRAINT "risk_violations_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table strategies
-- ----------------------------
CREATE INDEX "idx_strategies_active" ON "public"."strategies" USING btree (
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "updated_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
) WHERE status = ANY (ARRAY['Running'::strategy_status, 'Paused'::strategy_status]);
CREATE INDEX "idx_strategies_config_gin" ON "public"."strategies" USING gin (
  "config" "pg_catalog"."jsonb_ops"
);
CREATE INDEX "idx_strategies_created_at" ON "public"."strategies" USING btree (
  "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_strategies_name" ON "public"."strategies" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_strategies_name_gin" ON "public"."strategies" USING gin (
  to_tsvector('english'::regconfig, name::text) "pg_catalog"."tsvector_ops"
);
CREATE INDEX "idx_strategies_status" ON "public"."strategies" USING btree (
  "status" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "idx_strategies_strategy_type" ON "public"."strategies" USING btree (
  "strategy_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_strategies_trading_pair" ON "public"."strategies" USING btree (
  "trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_strategies_type" ON "public"."strategies" USING btree (
  "strategy_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table strategies
-- ----------------------------
ALTER TABLE "public"."strategies" ADD CONSTRAINT "strategies_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table strategy_config
-- ----------------------------
CREATE INDEX "idx_strategy_config_enabled" ON "public"."strategy_config" USING btree (
  "enabled" "pg_catalog"."bool_ops" ASC NULLS LAST
);
CREATE INDEX "idx_strategy_config_name" ON "public"."strategy_config" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_strategy_config_parameters_gin" ON "public"."strategy_config" USING gin (
  "strategy_parameters" "pg_catalog"."jsonb_ops"
);

-- ----------------------------
-- Triggers structure for table strategy_config
-- ----------------------------
CREATE TRIGGER "trigger_strategy_config_updated_at" BEFORE UPDATE ON "public"."strategy_config"
FOR EACH ROW
EXECUTE PROCEDURE "public"."update_strategy_config_updated_at"();

-- ----------------------------
-- Primary Key structure for table strategy_config
-- ----------------------------
ALTER TABLE "public"."strategy_config" ADD CONSTRAINT "strategy_config_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table strategy_performance
-- ----------------------------
ALTER TABLE "public"."strategy_performance" ADD CONSTRAINT "strategy_performance_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table strategy_positions
-- ----------------------------
ALTER TABLE "public"."strategy_positions" ADD CONSTRAINT "strategy_positions_strategy_id_trading_pair_key" UNIQUE ("strategy_id", "trading_pair");

-- ----------------------------
-- Primary Key structure for table strategy_positions
-- ----------------------------
ALTER TABLE "public"."strategy_positions" ADD CONSTRAINT "strategy_positions_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table strategy_trades
-- ----------------------------
ALTER TABLE "public"."strategy_trades" ADD CONSTRAINT "strategy_trades_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table system_config
-- ----------------------------
CREATE INDEX "idx_system_config_key" ON "public"."system_config" USING btree (
  "key" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_system_config_value_gin" ON "public"."system_config" USING gin (
  "value" "pg_catalog"."jsonb_ops"
);

-- ----------------------------
-- Uniques structure for table system_config
-- ----------------------------
ALTER TABLE "public"."system_config" ADD CONSTRAINT "system_config_key_key" UNIQUE ("key");

-- ----------------------------
-- Primary Key structure for table system_config
-- ----------------------------
ALTER TABLE "public"."system_config" ADD CONSTRAINT "system_config_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table trades
-- ----------------------------
CREATE INDEX "idx_trades_exchange" ON "public"."trades" USING btree (
  "exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_exchange_time" ON "public"."trades" USING btree (
  "exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "executed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_exchange_trade_id" ON "public"."trades" USING btree (
  "exchange_trade_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_executed_at" ON "public"."trades" USING btree (
  "executed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_order" ON "public"."trades" USING btree (
  "order_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_order_fk" ON "public"."trades" USING btree (
  "order_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_order_id" ON "public"."trades" USING btree (
  "order_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_order_time" ON "public"."trades" USING btree (
  "order_id" "pg_catalog"."uuid_ops" ASC NULLS LAST,
  "executed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_pair_time" ON "public"."trades" USING btree (
  "trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "executed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_side" ON "public"."trades" USING btree (
  "side" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_stats" ON "public"."trades" USING btree (
  "trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "side" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "executed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_time_range" ON "public"."trades" USING btree (
  "executed_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST,
  "created_at" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trades_trading_pair" ON "public"."trades" USING btree (
  "trading_pair_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

-- ----------------------------
-- Checks structure for table trades
-- ----------------------------
ALTER TABLE "public"."trades" ADD CONSTRAINT "trades_quantity_check" CHECK (quantity > 0::numeric);
ALTER TABLE "public"."trades" ADD CONSTRAINT "trades_price_check" CHECK (price > 0::numeric);
ALTER TABLE "public"."trades" ADD CONSTRAINT "trades_fee_check" CHECK (fee >= 0::numeric);

-- ----------------------------
-- Primary Key structure for table trades
-- ----------------------------
ALTER TABLE "public"."trades" ADD CONSTRAINT "trades_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table trading_config
-- ----------------------------
CREATE INDEX "idx_trading_config_enabled" ON "public"."trading_config" USING btree (
  "enabled" "pg_catalog"."bool_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trading_config_name" ON "public"."trading_config" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trading_config_parameters_gin" ON "public"."trading_config" USING gin (
  "trading_parameters" "pg_catalog"."jsonb_ops"
);

-- ----------------------------
-- Triggers structure for table trading_config
-- ----------------------------
CREATE TRIGGER "trigger_trading_config_updated_at" BEFORE UPDATE ON "public"."trading_config"
FOR EACH ROW
EXECUTE PROCEDURE "public"."update_trading_config_updated_at"();

-- ----------------------------
-- Primary Key structure for table trading_config
-- ----------------------------
ALTER TABLE "public"."trading_config" ADD CONSTRAINT "trading_config_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table trading_pairs
-- ----------------------------
CREATE INDEX "idx_trading_pairs_active" ON "public"."trading_pairs" USING btree (
  "is_active" "pg_catalog"."bool_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trading_pairs_base_asset" ON "public"."trading_pairs" USING btree (
  "base_asset" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trading_pairs_exchange" ON "public"."trading_pairs" USING btree (
  "exchange_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trading_pairs_quote_asset" ON "public"."trading_pairs" USING btree (
  "quote_asset" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_trading_pairs_symbol" ON "public"."trading_pairs" USING btree (
  "symbol" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Uniques structure for table trading_pairs
-- ----------------------------
ALTER TABLE "public"."trading_pairs" ADD CONSTRAINT "trading_pairs_symbol_key" UNIQUE ("symbol");

-- ----------------------------
-- Primary Key structure for table trading_pairs
-- ----------------------------
ALTER TABLE "public"."trading_pairs" ADD CONSTRAINT "trading_pairs_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table risk_checks
-- ----------------------------
CREATE INDEX "idx_risk_checks_applied_rules_gin" ON "public"."risk_checks" USING gin (
  "applied_rules" "pg_catalog"."jsonb_ops"
);
CREATE INDEX "idx_risk_checks_date" ON "public"."risk_checks" USING btree (
  "check_date" "pg_catalog"."date_ops" ASC NULLS LAST
);
CREATE INDEX "idx_risk_checks_failed_recent" ON "public"."risk_checks" USING btree (
  "result" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE result = 'failed'::check_result;
CREATE INDEX "idx_risk_checks_high_risk" ON "public"."risk_checks" USING btree (
  "risk_score" "pg_catalog"."numeric_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE risk_score >= 70::numeric;
CREATE INDEX "idx_risk_checks_pair_result_time" ON "public"."risk_checks" USING btree (
  "trading_pair" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "result" "pg_catalog"."enum_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
);
CREATE INDEX "idx_risk_checks_processing_time" ON "public"."risk_checks" USING btree (
  "processing_time_ms" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_risk_checks_result" ON "public"."risk_checks" USING btree (
  "result" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "idx_risk_checks_risk_score" ON "public"."risk_checks" USING btree (
  "risk_score" "pg_catalog"."numeric_ops" DESC NULLS FIRST
);
CREATE INDEX "idx_risk_checks_slow_queries" ON "public"."risk_checks" USING btree (
  "processing_time_ms" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE processing_time_ms > 1000;
CREATE INDEX "idx_risk_checks_strategy" ON "public"."risk_checks" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_risk_checks_strategy_time" ON "public"."risk_checks" USING btree (
  "strategy_id" "pg_catalog"."uuid_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
) WHERE strategy_id IS NOT NULL;
CREATE INDEX "idx_risk_checks_timestamp" ON "public"."risk_checks" USING btree (
  "timestamp" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_risk_checks_trading_pair" ON "public"."risk_checks" USING btree (
  "trading_pair" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_risk_checks_violations_gin" ON "public"."risk_checks" USING gin (
  "violations" "pg_catalog"."jsonb_ops"
);
CREATE INDEX "idx_risk_checks_warnings_gin" ON "public"."risk_checks" USING gin (
  "warnings" "pg_catalog"."jsonb_ops"
);

-- ----------------------------
-- Checks structure for table risk_checks
-- ----------------------------
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_price_positive" CHECK (price IS NULL OR price > 0::numeric);
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_rules_evaluated_positive" CHECK (rules_evaluated >= 0);
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_cache_hit_rate_range" CHECK (cache_hit_rate >= 0::numeric AND cache_hit_rate <= 100::numeric);
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_risk_score_range" CHECK (risk_score >= 0::numeric AND risk_score <= 100::numeric);
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_processing_time_positive" CHECK (processing_time_ms >= 0);
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_confidence_level_range" CHECK (confidence_level >= 0::numeric AND confidence_level <= 100::numeric);
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_max_allowed_quantity_positive" CHECK (max_allowed_quantity IS NULL OR max_allowed_quantity > 0::numeric);
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_quantity_positive" CHECK (quantity > 0::numeric);
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "check_suggested_prices_positive" CHECK ((suggested_price_min IS NULL OR suggested_price_min > 0::numeric) AND (suggested_price_max IS NULL OR suggested_price_max > 0::numeric) AND (suggested_price_min IS NULL OR suggested_price_max IS NULL OR suggested_price_min <= suggested_price_max));

-- ----------------------------
-- Primary Key structure for table risk_checks
-- ----------------------------
ALTER TABLE "public"."risk_checks" ADD CONSTRAINT "risk_checks_pkey" PRIMARY KEY ("id", "check_date");

-- ----------------------------
-- Foreign Keys structure for table backtest_portfolio_snapshots
-- ----------------------------
ALTER TABLE "public"."backtest_portfolio_snapshots" ADD CONSTRAINT "backtest_portfolio_snapshots_backtest_id_fkey" FOREIGN KEY ("backtest_id") REFERENCES "public"."backtest_results" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table backtest_trades
-- ----------------------------
ALTER TABLE "public"."backtest_trades" ADD CONSTRAINT "backtest_trades_backtest_id_fkey" FOREIGN KEY ("backtest_id") REFERENCES "public"."backtest_results" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table candles
-- ----------------------------
ALTER TABLE "public"."candles" ADD CONSTRAINT "candles_trading_pair_id_fkey" FOREIGN KEY ("trading_pair_id") REFERENCES "public"."trading_pairs" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table strategy_performance
-- ----------------------------
ALTER TABLE "public"."strategy_performance" ADD CONSTRAINT "strategy_performance_strategy_id_fkey" FOREIGN KEY ("strategy_id") REFERENCES "public"."strategies" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table strategy_positions
-- ----------------------------
ALTER TABLE "public"."strategy_positions" ADD CONSTRAINT "strategy_positions_strategy_id_fkey" FOREIGN KEY ("strategy_id") REFERENCES "public"."strategies" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table strategy_trades
-- ----------------------------
ALTER TABLE "public"."strategy_trades" ADD CONSTRAINT "strategy_trades_strategy_id_fkey" FOREIGN KEY ("strategy_id") REFERENCES "public"."strategies" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
