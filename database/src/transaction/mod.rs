//! 统一事务管理模块
//!
//! 提供统一的事务管理和工作单元模式，支持跨Repository的事务操作

pub mod transaction_manager;
pub mod unit_of_work;
pub mod transaction_context;
pub mod database_transaction;

// 标准事务管理器
pub use transaction_manager::{TransactionManager, TransactionConfig};

// 高级事务管理功能
pub use unit_of_work::{UnitOfWork, UnitOfWorkBuilder, WorkUnit};
pub use transaction_context::{TransactionContext, TransactionScope, TransactionIsolationLevel};

// 数据库事务包装器
pub use database_transaction::{DatabaseTransaction, TransactionStats, LongRunningTransaction};
