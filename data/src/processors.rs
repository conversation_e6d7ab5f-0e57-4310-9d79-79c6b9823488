//! 数据处理器

use sigmax_core::{Candle, SigmaXResult, SigmaXError};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use std::str::FromStr;

/// 数据处理器trait
pub trait DataProcessor: Send + Sync {
    /// 处理原始数据
    async fn process(&self, raw_data: &[u8]) -> SigmaXResult<Vec<Candle>>;
}

/// 实时数据处理器
pub struct RealTimeProcessor;

impl DataProcessor for RealTimeProcessor {
    async fn process(&self, raw_data: &[u8]) -> SigmaXResult<Vec<Candle>> {
        // 解析实时数据流（通常是JSON格式）
        let data_str = std::str::from_utf8(raw_data)
            .map_err(|e| SigmaXError::validation(
                sigmax_core::ValidationErrorCode::InvalidFormat,
                "data_encoding",
                format!("Invalid UTF-8 data: {}", e)
            ))?;

        // 尝试解析为JSON
        let json_value: serde_json::Value = serde_json::from_str(data_str)
            .map_err(|e| SigmaXError::market_data(
                sigmax_core::MarketDataErrorCode::ParseError,
                format!("Invalid JSON data: {}", e)
            ))?;

        let mut candles = Vec::new();

        // 处理单个K线数据
        if let Some(kline_data) = json_value.as_object() {
            if let Ok(candle) = parse_kline_from_json(kline_data) {
                candles.push(candle);
            }
        }
        // 处理K线数组
        else if let Some(kline_array) = json_value.as_array() {
            for kline_item in kline_array {
                if let Some(kline_data) = kline_item.as_object() {
                    if let Ok(candle) = parse_kline_from_json(kline_data) {
                        candles.push(candle);
                    }
                }
            }
        }

        Ok(candles)
    }
}

/// 回测数据处理器
pub struct BacktestProcessor;

impl DataProcessor for BacktestProcessor {
    async fn process(&self, raw_data: &[u8]) -> SigmaXResult<Vec<Candle>> {
        // 解析回测数据（通常是JSON格式的历史K线数据）
        let data_str = std::str::from_utf8(raw_data)
            .map_err(|e| SigmaXError::validation(
                sigmax_core::ValidationErrorCode::InvalidFormat,
                "data_encoding",
                format!("Invalid UTF-8 data: {}", e)
            ))?;

        // 尝试解析为JSON数组
        let json_array: Vec<serde_json::Value> = serde_json::from_str(data_str)
            .map_err(|e| SigmaXError::market_data(
                sigmax_core::MarketDataErrorCode::ParseError,
                format!("Invalid JSON array: {}", e)
            ))?;

        let mut candles = Vec::new();

        for item in json_array {
            if let Some(kline_data) = item.as_object() {
                match parse_kline_from_json(kline_data) {
                    Ok(candle) => candles.push(candle),
                    Err(e) => {
                        // 记录错误但继续处理其他数据
                        tracing::warn!("Failed to parse candle data: {}", e);
                    }
                }
            }
            // 支持数组格式的K线数据 [timestamp, open, high, low, close, volume]
            else if let Some(kline_array) = item.as_array() {
                match parse_kline_from_array(kline_array) {
                    Ok(candle) => candles.push(candle),
                    Err(e) => {
                        tracing::warn!("Failed to parse candle array: {}", e);
                    }
                }
            }
        }

        // 按时间戳排序
        candles.sort_by(|a, b| a.timestamp.cmp(&b.timestamp));

        Ok(candles)
    }
}

/// 从JSON对象解析K线数据
fn parse_kline_from_json(data: &serde_json::Map<String, serde_json::Value>) -> SigmaXResult<Candle> {
    let timestamp = parse_timestamp(data.get("timestamp").or(data.get("t")))?;
    let open = parse_decimal(data.get("open").or(data.get("o")))?;
    let high = parse_decimal(data.get("high").or(data.get("h")))?;
    let low = parse_decimal(data.get("low").or(data.get("l")))?;
    let close = parse_decimal(data.get("close").or(data.get("c")))?;
    let volume = parse_decimal(data.get("volume").or(data.get("v")))?;

    Ok(Candle {
        timestamp,
        open,
        high,
        low,
        close,
        volume,
    })
}

/// 从数组解析K线数据 [timestamp, open, high, low, close, volume]
fn parse_kline_from_array(data: &[serde_json::Value]) -> SigmaXResult<Candle> {
    if data.len() < 6 {
        return Err(SigmaXError::validation(
            sigmax_core::ValidationErrorCode::InvalidFormat,
            "candle_data",
            "Candle array must have at least 6 elements"
        ));
    }

    let timestamp = parse_timestamp(Some(&data[0]))?;
    let open = parse_decimal(Some(&data[1]))?;
    let high = parse_decimal(Some(&data[2]))?;
    let low = parse_decimal(Some(&data[3]))?;
    let close = parse_decimal(Some(&data[4]))?;
    let volume = parse_decimal(Some(&data[5]))?;

    Ok(Candle {
        timestamp,
        open,
        high,
        low,
        close,
        volume,
    })
}

/// 解析时间戳
fn parse_timestamp(value: Option<&serde_json::Value>) -> SigmaXResult<DateTime<Utc>> {
    let value = value.ok_or_else(|| SigmaXError::validation(
        sigmax_core::ValidationErrorCode::Required,
        "timestamp",
        "Missing timestamp"
    ))?;

    match value {
        serde_json::Value::Number(n) => {
            let timestamp = n.as_i64()
                .ok_or_else(|| SigmaXError::validation(
                    sigmax_core::ValidationErrorCode::InvalidFormat,
                    "timestamp",
                    "Invalid timestamp number"
                ))?;

            // 处理毫秒和秒级时间戳
            let timestamp = if timestamp > 1_000_000_000_000 {
                timestamp / 1000 // 毫秒转秒
            } else {
                timestamp
            };

            DateTime::from_timestamp(timestamp, 0)
                .ok_or_else(|| SigmaXError::validation(
                    sigmax_core::ValidationErrorCode::InvalidFormat,
                    "timestamp",
                    "Invalid timestamp value"
                ))
        }
        serde_json::Value::String(s) => {
            // 尝试解析ISO 8601格式
            DateTime::parse_from_rfc3339(&s)
                .map(|dt| dt.with_timezone(&Utc))
                .map_err(|e| SigmaXError::validation(
                    sigmax_core::ValidationErrorCode::InvalidFormat,
                    "timestamp",
                    format!("Invalid timestamp string: {}", e)
                ))
        }
        _ => Err(SigmaXError::validation(
            sigmax_core::ValidationErrorCode::InvalidFormat,
            "timestamp",
            "Timestamp must be number or string"
        )),
    }
}

/// 解析Decimal值
fn parse_decimal(value: Option<&serde_json::Value>) -> SigmaXResult<Decimal> {
    let value = value.ok_or_else(|| SigmaXError::validation(
        sigmax_core::ValidationErrorCode::Required,
        "decimal_value",
        "Missing decimal value"
    ))?;

    match value {
        serde_json::Value::Number(n) => {
            if let Some(f) = n.as_f64() {
                Decimal::try_from(f)
                    .map_err(|e| SigmaXError::validation(
                        sigmax_core::ValidationErrorCode::InvalidFormat,
                        "decimal_value",
                        format!("Invalid decimal number: {}", e)
                    ))
            } else {
                Err(SigmaXError::validation(
                    sigmax_core::ValidationErrorCode::InvalidFormat,
                    "decimal_value",
                    "Invalid number format"
                ))
            }
        }
        serde_json::Value::String(s) => {
            Decimal::from_str(&s)
                .map_err(|e| SigmaXError::validation(
                    sigmax_core::ValidationErrorCode::InvalidFormat,
                    "decimal_value",
                    format!("Invalid decimal string: {}", e)
                ))
        }
        _ => Err(SigmaXError::validation(
            sigmax_core::ValidationErrorCode::InvalidFormat,
            "decimal_value",
            "Decimal value must be number or string"
        )),
    }
}

#[cfg(test)]
mod tests;
