# 🔄 接口统一化迁移指南

## 📋 概述

本指南帮助您将现有代码迁移到统一的接口系统。**简化版本 - 无需适配器！**

## 🎯 统一化原则

1. **直接替换**: 旧接口已标记为废弃，直接使用新接口
2. **编译时检查**: 编译器会提示需要迁移的地方
3. **最小变更**: 只需要更改 import 和方法名

## 📊 接口映射表

### 风控接口统一化

| 原接口 | 统一接口 | 迁移方式 |
|--------|----------|----------|
| `sigmax_core::RiskEngine` | `sigmax_interfaces::RiskController` | 直接替换 |
| `sigmax_core::RiskManager` | `sigmax_interfaces::RiskController` | 直接替换 |
| `sigmax_risk::control::RiskContext` | `sigmax_interfaces::RiskContext` | 直接替换 |

### 投资组合接口统一化

| 原接口 | 统一接口 | 迁移方式 |
|--------|----------|----------|
| `sigmax_core::PortfolioManager` | `sigmax_interfaces::PortfolioManager` | 直接替换 |
| `sigmax_core::EnhancedPortfolioManager` | `sigmax_interfaces::PortfolioManager` | 已合并到统一接口 |

## 🔧 迁移步骤

### Step 1: 更新依赖

```toml
[dependencies]
sigmax-interfaces = { path = "../interfaces" }
```

### Step 2: 风控模块迁移

#### 原代码:
```rust
use sigmax_core::{RiskEngine, RiskManager};

// 使用 RiskEngine
let risk_engine: Arc<dyn RiskEngine> = create_risk_engine();
let result = risk_engine.check_order_risk(&order, Some("grid")).await?;

// 使用 RiskManager
let risk_manager: Arc<dyn RiskManager> = create_risk_manager();
let passed = risk_manager.check_order_risk(&order).await?;
```

#### 迁移后:
```rust
use sigmax_interfaces::RiskController;

// 统一使用 RiskController
let risk_controller: Arc<dyn RiskController> = create_risk_controller();

// RiskEngine 的方法直接可用
let result = risk_controller.check_order_risk(&order, Some("grid")).await?;

// RiskManager 的方法改名为 _simple 后缀
let passed = risk_controller.check_order_risk_simple(&order).await?;
```

### Step 3: 投资组合模块迁移

#### 原代码:
```rust
use sigmax_core::{PortfolioManager, EnhancedPortfolioManager};

let portfolio: Arc<dyn PortfolioManager> = create_portfolio();
let balances = portfolio.get_balances().await?;

// 如果使用了 EnhancedPortfolioManager
let enhanced: Arc<dyn EnhancedPortfolioManager> = create_enhanced_portfolio();
enhanced.update_from_trade(&trade).await?;
```

#### 迁移后:
```rust
use sigmax_interfaces::PortfolioManager;

// 统一使用 PortfolioManager (包含所有方法)
let portfolio: Arc<dyn PortfolioManager> = create_portfolio_manager();

// 原 PortfolioManager 方法需要改名
let balances = portfolio.get_balances_map().await?; // 注意方法名变化

// 原 EnhancedPortfolioManager 方法直接可用
portfolio.update_from_trade(&trade).await?;
```

### Step 4: 数据结构迁移

#### RiskContext 迁移:
```rust
// 原代码
use sigmax_risk::control::models::RiskContext;

// 新代码
use sigmax_interfaces::RiskContext;

// 字段完全兼容，直接替换即可
```

#### Portfolio 迁移:
```rust
// 原代码
use sigmax_interfaces::trading::Portfolio;

// 新代码
use sigmax_interfaces::Portfolio; // 统一版本

// 字段已合并，包含所有原有字段
```

## 🚀 最佳实践

### 1. 编译器引导迁移

```rust
// 编译器会提示废弃警告，按提示修改即可
warning: use of deprecated trait `sigmax_core::RiskEngine`: 请使用 sigmax_interfaces::RiskController
```

### 2. 方法名映射

| 旧方法 | 新方法 | 说明 |
|--------|--------|------|
| `RiskManager::check_order_risk` | `RiskController::check_order_risk_simple` | 避免冲突 |
| `PortfolioManager::get_balances` | `PortfolioManager::get_balances_map` | 返回类型不同 |

### 3. 错误处理统一

```rust
use sigmax_interfaces::{RiskResult, RiskError};

match risk_controller.validate_order(&order).await {
    RiskResult::Approved => println!("订单通过风控"),
    RiskResult::Rejected(reason) => println!("订单被拒绝: {}", reason),
    RiskResult::RequiresApproval(reason) => println!("需要人工审核: {}", reason),
    RiskResult::Error(error) => println!("风控检查出错: {}", error),
}
```

## ⚠️ 注意事项

### 1. 编译器会提示废弃警告
- 旧接口标记为 `#[deprecated]`
- 按编译器提示修改即可

### 2. 方法名称变更
- `RiskManager::check_order_risk` → `RiskController::check_order_risk_simple`
- `PortfolioManager::get_balances` → `PortfolioManager::get_balances_map`

### 3. 内部可变性
统一接口使用内部可变性，不需要 `&mut self`

## 🧪 测试迁移

```rust
#[tokio::test]
async fn test_unified_interfaces() {
    use sigmax_interfaces::{RiskController, PortfolioManager};

    let risk_controller: Arc<dyn RiskController> = create_risk_controller();
    let portfolio: Arc<dyn PortfolioManager> = create_portfolio_manager();

    // 测试风控
    let order = create_test_order();
    let result = risk_controller.check_order_risk_simple(&order).await;
    assert!(result.is_ok());

    // 测试投资组合
    let balances = portfolio.get_balances_map().await;
    assert!(balances.is_ok());
}
```

## 📈 迁移时间表

| 阶段 | 时间 | 任务 |
|------|------|------|
| Phase 1 | 1 天 | 更新 import 语句 |
| Phase 2 | 2-3 天 | 修改方法调用 |
| Phase 3 | 1 天 | 测试验证 |

**总计：约 1 周完成迁移**

## 🆘 故障排除

### 编译错误
1. **废弃警告**: 按提示替换为新接口
2. **方法未找到**: 检查方法名是否需要添加后缀
3. **类型不匹配**: 检查返回类型是否变化

### 运行时错误
1. **功能缺失**: 确保实现了统一接口的所有方法
2. **性能问题**: 统一接口可能需要适配层，注意性能影响
