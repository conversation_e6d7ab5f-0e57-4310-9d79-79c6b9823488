# SigmaX Contract Map（契约清单 v1）

> 目的：冻结稳定契约（先文档后实现），明确跨模块的“对外承诺”。本清单仅定义接口与模型，不涉及具体实现。

---

## 0. 执行与冲突处理（Contract Map 优先级与歧义清理）

当现有代码与本 Contract Map 冲突或存在歧义时，遵循以下强制策略：

- 优先级：Contract Map > 现有实现代码。
- 处理流程（强制）：
  1) 发现冲突（命名、签名、类型、错误码、事件等）后，立即在 PR 中引用本文件相应章节作为依据；
  2) 若实现与契约不一致，先在实现处加“弃用标记”（Deprecation 注释）并在同 PR 内删除或替换歧义代码；
  3) 禁止引入与 Contract Map 不一致的新代码；
  4) 若确需变更契约，必须先在本文件提出变更（含影响面与迁移方案），评审通过后再改代码；
  5) CI Gate：
     - clippy/deny 规则：禁止通配 re-export、未注释 public API、跨层非法依赖；
     - docs gate：本文件中标注为“冻结”的契约若被修改，CI 必须要求变更说明与签字；
  6) 标签与版本：契约版本以 tag 标识（如 `contracts-v1`），破坏性变更需递增。
- 清理策略：
  - Record/View 命名不一致或混用（如使用 `Strategy` 含混合语义）→ 统一改为 `StrategyRecord`/`StrategyView`；
  - Repository 签名不符合 Record/View 约定 → 统一调整签名；
  - 错误码使用与码表不一致 → 统一映射/替换为码表内编码；
  - 事件定义分散/重复 → 统一收敛至 `core::events` 接口与系统事件类型。

> 目标：通过“文档先行 + CI 护栏 + 立即清理”的策略，确保契约成为唯一可信来源（Single Source of Truth）。

---

## 1. 基础类型（IDs / Numeric）
- IDs（UUID）：
  - `StrategyId`, `OrderId`, `TradeId`, `PortfolioId`
- Numeric：
  - 强制财务数值使用 `rust_decimal::Decimal`（禁止 f64/f32）
  - 金额/价格/数量别名（建议）：`Amount`, `Price`, `Quantity`

命名约定：在 `core::types` 中显式定义并导出，禁止通配导出。

---

## 2. 领域模型（Record / View）

说明：
- Record = 持久化/写模型（面向事务与存储，字段相对稳定）
- View = 查询/展示模型（面向读与聚合，字段可随报表演进）

### 2.1 Strategy
- StrategyRecord（示例字段）
  - `strategy_id: StrategyId`
  - `name: String`
  - `strategy_type: String`
  - `status: StrategyStatus`
  - `config: serde_json::Value`
  - `created_at: DateTime<Utc>`
  - `updated_at: DateTime<Utc>`
- StrategyView（示例字段）
  - `id: StrategyId`
  - `name: String`
  - `strategy_type: String`
  - `status: StrategyStatus`
  - `portfolio_id: Option<PortfolioId>`
  - `created_at: DateTime<Utc>`
  - `updated_at: DateTime<Utc>`

### 2.2 Order
- OrderRecord（示例字段）
  - `order_id: OrderId`
  - `strategy_id: StrategyId`
  - `trading_pair: TradingPair`
  - `side: OrderSide`, `order_type: OrderType`
  - `price: Option<Price>`, `quantity: Quantity`
  - `status: OrderStatus`
  - `created_at`, `updated_at`
- OrderView（示例字段）
  - `id: OrderId`, `strategy_id: StrategyId`, `status: OrderStatus`
  - `trading_pair: TradingPair`, `filled_amount: Amount`
  - 聚合字段（可选）：`avg_fill_price`, `slippage`, `fee`

规则：
- 写入/更新仅使用 Record。
- 列表/报表/筛选使用 View；`find_by_id` 的返回类型需在接口文档明确（Record 或 View）。

---

## 3. 错误契约（Errors）
- 分类：`Validation`, `Business`, `Database`, `External`, `Internal`
- 错误码：在 `core::errors::types` 固定编码表（示例）
  - `Validation`：`Required`, `InvalidFormat`, `InvalidRange`, `Conflict`
  - `Database`：`NotFound`, `UniqueViolation`, `ConnectionFailure`
  - `External`：`Timeout`, `UpstreamError`
  - `Internal`：`Unexpected`
- 映射：
  - `From<SigmaXError> -> ApiError`（web 渲染）；严禁在 Controller 手写业务错误。

---

## 4. 事件契约（Events）
- `EventBus` Trait（核心能力）
  - `publish(event)`, `subscribe(handler)`，异步接口
- 系统事件（示例）：
  - `SystemEvent::StrategyCreated(StrategyId)`, `SystemEvent::OrderFilled(OrderId)`
- 扩展事件：各模块在自身 crate 定义子事件枚举（如 `WebEvent`），但复用统一 `EventBus`。

---

## 5. 事务契约（Transactions）
- `TransactionManager`（抽象）
  - `begin() -> UnitOfWork`
  - `commit(uow)`, `rollback(uow)`
- `UnitOfWork`
  - 暂存变更、收敛提交点；与 Repository 配合使用
- 位置：接口定义于 `core::transaction`；具体实现放 `database`。

---

## 6. Repository 契约（签名统一）

原则：接口定义在 `core`（或 `interfaces`，推荐 `core` 统一导出），实现放 `database`。

### StrategyRepository（示例）
- 写：
  - `save(record: &StrategyRecord) -> Result<StrategyId, SigmaXError>`
  - `update(record: &StrategyRecord) -> Result<(), SigmaXError>`
- 读：
  - `find_by_id(id: StrategyId) -> Result<Option<StrategyRecord>, SigmaXError>`（或 View，需在文档决定）
  - `list(filter: StrategyFilter) -> Result<Vec<StrategyView>, SigmaXError>`
  - `stats(filter: StrategyFilter) -> Result<StrategyStatsView, SigmaXError>`

### OrderRepository（示例）
- 写：`save/update(record)`
- 读：`find_by_id(id)`, `list(filter) -> Vec<OrderView>`，`stats(filter)`

注意：Filter、Stats 统一在 `core::models::view::*` 下定义为只读结构。

---

## 7. Web 分层契约
- Gateway：中间件契约（认证/限流/追踪/错误映射）
- Controllers：仅接受 DTO，调用 Application，用 `ApiError` 渲染
- Application：用例编排接口（注入 TransactionManager/Repositories/EventBus）
- Domain：领域服务接口（无 IO）
- Integration：对外系统/适配层仅定义“调用约定”，不暴露实现细节

---

## 8. 命名与导出约定
- 模型：`StrategyRecord` / `StrategyView` 等后缀固定
- 目录：`core/models/record/*` 与 `core/models/view/*`
- 导出：`core/lib.rs` 显式 `pub use`，禁止 `*` 通配

---

## 9. 验收与冻结
- 本文档为 contracts-v1 的基线；提交 tag：`contracts-v1`
- 后续变更需：
  - 影响面评估（受影响接口/模块）
  - 迁移路径（双写/兼容期/回滚方案）
  - 文档更新与版本号标记
