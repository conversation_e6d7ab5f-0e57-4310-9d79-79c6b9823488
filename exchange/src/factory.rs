//! 交易所工厂

use sigmax_core::{ExchangeId, SigmaXResult};
use sigmax_interfaces::ExchangeConnector;
use std::sync::Arc;

use crate::{binance::BinanceExchange, coinbase::CoinbaseExchange, kraken::<PERSON><PERSON><PERSON>Exchange, simulator::SimulatorExchange};

/// 交易所工厂
pub struct ExchangeFactory;

impl ExchangeFactory {
    /// 创建交易所实例
    pub fn create(exchange_id: ExchangeId) -> SigmaXResult<Arc<dyn ExchangeConnector>> {
        match exchange_id {
            ExchangeId::Binance => Ok(Arc::new(BinanceExchange::new())),
            ExchangeId::Coinbase => Ok(Arc::new(CoinbaseExchange::new())),
            ExchangeId::Kraken => Ok(Arc::new(KrakenExchange::new())),
            ExchangeId::OKX => Ok(Arc::new(SimulatorExchange::new())), // 暂时使用模拟器
            ExchangeId::Simulator => Ok(Arc::new(SimulatorExchange::new())),
        }
    }
}
