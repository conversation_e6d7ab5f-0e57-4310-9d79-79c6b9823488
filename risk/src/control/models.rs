//! 风控模型层
//!
//! 定义风控相关的数据模型和构建器

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use std::collections::HashMap;
use sigmax_core::{Order, Balance};
use sigmax_interfaces::Portfolio;
use sigmax_core::MarketData;
use uuid::Uuid;

// ============================================================================
// 风控上下文
// ============================================================================

/// 风控上下文 - 包含风控决策所需的所有信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskContext {
    /// 订单信息
    pub order: Option<Order>,
    /// 投资组合信息
    pub portfolio: Option<Portfolio>,
    /// 账户余额
    pub balances: Vec<Balance>,
    /// 市场数据
    pub market_data: Option<MarketData>,
    /// 时间戳
    pub timestamp: DateTime<Utc>,
    /// 元数据
    pub metadata: HashMap<String, serde_json::Value>,
}

impl RiskContext {
    /// 创建新的风控上下文
    pub fn new() -> Self {
        Self {
            order: None,
            portfolio: None,
            balances: vec![],
            market_data: None,
            timestamp: Utc::now(),
            metadata: HashMap::new(),
        }
    }

    /// 设置订单
    pub fn with_order(mut self, order: Order) -> Self {
        self.order = Some(order);
        self
    }

    /// 设置投资组合
    pub fn with_portfolio(mut self, portfolio: Portfolio) -> Self {
        self.portfolio = Some(portfolio);
        self
    }

    /// 设置余额
    pub fn with_balances(mut self, balances: Vec<Balance>) -> Self {
        self.balances = balances;
        self
    }

    /// 设置市场数据
    pub fn with_market_data(mut self, market_data: MarketData) -> Self {
        self.market_data = Some(market_data);
        self
    }

    /// 添加元数据
    pub fn with_metadata(mut self, key: String, value: serde_json::Value) -> Self {
        self.metadata.insert(key, value);
        self
    }

    /// 获取交易对字符串
    pub fn get_trading_pair_str(&self) -> Option<String> {
        self.order.as_ref().map(|order| {
            format!("{}_{}", order.trading_pair.base, order.trading_pair.quote)
        })
    }

    /// 获取订单价值
    pub fn get_order_value(&self) -> Option<Decimal> {
        self.order.as_ref().and_then(|order| {
            order.price.map(|price| order.quantity * price)
        })
    }

    /// 检查是否有足够的上下文信息
    pub fn is_complete(&self) -> bool {
        // 至少需要有订单或投资组合信息
        self.order.is_some() || self.portfolio.is_some()
    }
}

impl Default for RiskContext {
    fn default() -> Self {
        Self::new()
    }
}

/// 风控上下文构建器
pub struct RiskContextBuilder {
    context: RiskContext,
}

impl RiskContextBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            context: RiskContext::new(),
        }
    }

    /// 设置订单
    pub fn order(mut self, order: Order) -> Self {
        self.context.order = Some(order);
        self
    }

    /// 设置投资组合
    pub fn portfolio(mut self, portfolio: Portfolio) -> Self {
        self.context.portfolio = Some(portfolio);
        self
    }

    /// 设置余额
    pub fn balances(mut self, balances: Vec<Balance>) -> Self {
        self.context.balances = balances;
        self
    }

    /// 设置市场数据
    pub fn market_data(mut self, market_data: MarketData) -> Self {
        self.context.market_data = Some(market_data);
        self
    }

    /// 添加元数据
    pub fn metadata(mut self, key: String, value: serde_json::Value) -> Self {
        self.context.metadata.insert(key, value);
        self
    }

    /// 设置时间戳
    pub fn timestamp(mut self, timestamp: DateTime<Utc>) -> Self {
        self.context.timestamp = timestamp;
        self
    }

    /// 构建风控上下文
    pub fn build(self) -> RiskContext {
        self.context
    }
}

impl Default for RiskContextBuilder {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// 风险指标
// ============================================================================

/// 风险指标 - 包含各种风险度量
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskMetrics {
    /// 整体风险评分 (0-1)
    pub overall_risk_score: Decimal,
    /// 持仓风险评分
    pub position_risk_score: Decimal,
    /// 市场风险评分
    pub market_risk_score: Decimal,
    /// 流动性风险评分
    pub liquidity_risk_score: Decimal,
    /// 波动率
    pub volatility: Decimal,
    /// VaR (Value at Risk)
    pub var_1d: Option<Decimal>,
    pub var_5d: Option<Decimal>,
    /// CVaR (Conditional Value at Risk)
    pub cvar: Option<Decimal>,
    /// 最大回撤
    pub max_drawdown: Option<Decimal>,
    /// 夏普比率
    pub sharpe_ratio: Option<Decimal>,
    /// 计算时间
    pub calculated_at: DateTime<Utc>,
    /// 详细指标
    pub details: HashMap<String, serde_json::Value>,
}

impl RiskMetrics {
    /// 创建新的风险指标
    pub fn new() -> Self {
        Self {
            overall_risk_score: Decimal::ZERO,
            position_risk_score: Decimal::ZERO,
            market_risk_score: Decimal::ZERO,
            liquidity_risk_score: Decimal::ZERO,
            volatility: Decimal::ZERO,
            var_1d: None,
            var_5d: None,
            cvar: None,
            max_drawdown: None,
            sharpe_ratio: None,
            calculated_at: Utc::now(),
            details: HashMap::new(),
        }
    }

    /// 计算综合风险评分
    pub fn calculate_overall_score(&mut self) {
        // 使用加权平均计算综合风险评分
        let weights = [0.4, 0.3, 0.3]; // 持仓、市场、流动性权重
        let scores = [
            self.position_risk_score,
            self.market_risk_score,
            self.liquidity_risk_score,
        ];

        self.overall_risk_score = weights
            .iter()
            .zip(scores.iter())
            .map(|(weight, score)| Decimal::from_f64_retain(*weight).unwrap() * score)
            .sum::<Decimal>()
            .min(Decimal::ONE)
            .max(Decimal::ZERO);
    }

    /// 获取风险等级
    pub fn get_risk_level(&self) -> RiskLevel {
        if self.overall_risk_score >= Decimal::from_str_exact("0.8").unwrap() {
            RiskLevel::Critical
        } else if self.overall_risk_score >= Decimal::from_str_exact("0.6").unwrap() {
            RiskLevel::High
        } else if self.overall_risk_score >= Decimal::from_str_exact("0.4").unwrap() {
            RiskLevel::Medium
        } else if self.overall_risk_score >= Decimal::from_str_exact("0.2").unwrap() {
            RiskLevel::Low
        } else {
            RiskLevel::Minimal
        }
    }

    /// 添加详细指标
    pub fn add_detail(&mut self, key: String, value: serde_json::Value) {
        self.details.insert(key, value);
    }

    /// 是否为高风险
    pub fn is_high_risk(&self) -> bool {
        matches!(self.get_risk_level(), RiskLevel::Critical | RiskLevel::High)
    }
}

impl Default for RiskMetrics {
    fn default() -> Self {
        Self::new()
    }
}

/// 风险指标构建器
pub struct RiskMetricsBuilder {
    metrics: RiskMetrics,
}

impl RiskMetricsBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            metrics: RiskMetrics::new(),
        }
    }

    /// 设置持仓风险评分
    pub fn position_risk_score(mut self, score: Decimal) -> Self {
        self.metrics.position_risk_score = score;
        self
    }

    /// 设置市场风险评分
    pub fn market_risk_score(mut self, score: Decimal) -> Self {
        self.metrics.market_risk_score = score;
        self
    }

    /// 设置流动性风险评分
    pub fn liquidity_risk_score(mut self, score: Decimal) -> Self {
        self.metrics.liquidity_risk_score = score;
        self
    }

    /// 设置波动率
    pub fn volatility(mut self, volatility: Decimal) -> Self {
        self.metrics.volatility = volatility;
        self
    }

    /// 设置 VaR
    pub fn var(mut self, var_1d: Decimal, var_5d: Decimal) -> Self {
        self.metrics.var_1d = Some(var_1d);
        self.metrics.var_5d = Some(var_5d);
        self
    }

    /// 设置 CVaR
    pub fn cvar(mut self, cvar: Decimal) -> Self {
        self.metrics.cvar = Some(cvar);
        self
    }

    /// 设置最大回撤
    pub fn max_drawdown(mut self, drawdown: Decimal) -> Self {
        self.metrics.max_drawdown = Some(drawdown);
        self
    }

    /// 设置夏普比率
    pub fn sharpe_ratio(mut self, ratio: Decimal) -> Self {
        self.metrics.sharpe_ratio = Some(ratio);
        self
    }

    /// 添加详细指标
    pub fn detail(mut self, key: String, value: serde_json::Value) -> Self {
        self.metrics.details.insert(key, value);
        self
    }

    /// 构建风险指标
    pub fn build(mut self) -> RiskMetrics {
        self.metrics.calculate_overall_score();
        self.metrics.calculated_at = Utc::now();
        self.metrics
    }
}

impl Default for RiskMetricsBuilder {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// 风险等级和相关枚举
// ============================================================================

/// 风险等级
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum RiskLevel {
    /// 极低风险
    Minimal,
    /// 低风险
    Low,
    /// 中等风险
    Medium,
    /// 高风险
    High,
    /// 极高风险
    Critical,
}

impl RiskLevel {
    /// 获取风险等级的数值表示
    pub fn to_score(&self) -> Decimal {
        match self {
            RiskLevel::Minimal => Decimal::from_str_exact("0.1").unwrap(),
            RiskLevel::Low => Decimal::from_str_exact("0.3").unwrap(),
            RiskLevel::Medium => Decimal::from_str_exact("0.5").unwrap(),
            RiskLevel::High => Decimal::from_str_exact("0.7").unwrap(),
            RiskLevel::Critical => Decimal::from_str_exact("0.9").unwrap(),
        }
    }

    /// 从评分获取风险等级
    pub fn from_score(score: Decimal) -> Self {
        if score >= Decimal::from_str_exact("0.8").unwrap() {
            RiskLevel::Critical
        } else if score >= Decimal::from_str_exact("0.6").unwrap() {
            RiskLevel::High
        } else if score >= Decimal::from_str_exact("0.4").unwrap() {
            RiskLevel::Medium
        } else if score >= Decimal::from_str_exact("0.2").unwrap() {
            RiskLevel::Low
        } else {
            RiskLevel::Minimal
        }
    }

    /// 获取风险等级的颜色代码（用于UI显示）
    pub fn color_code(&self) -> &'static str {
        match self {
            RiskLevel::Minimal => "#28a745", // 绿色
            RiskLevel::Low => "#6f42c1",     // 紫色
            RiskLevel::Medium => "#fd7e14",  // 橙色
            RiskLevel::High => "#dc3545",    // 红色
            RiskLevel::Critical => "#721c24", // 深红色
        }
    }

    /// 获取风险等级的描述
    pub fn description(&self) -> &'static str {
        match self {
            RiskLevel::Minimal => "Risk is minimal, safe to proceed",
            RiskLevel::Low => "Low risk detected, monitor closely",
            RiskLevel::Medium => "Medium risk, consider reducing exposure",
            RiskLevel::High => "High risk, immediate attention required",
            RiskLevel::Critical => "Critical risk, stop trading immediately",
        }
    }
}

impl std::fmt::Display for RiskLevel {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RiskLevel::Minimal => write!(f, "Minimal"),
            RiskLevel::Low => write!(f, "Low"),
            RiskLevel::Medium => write!(f, "Medium"),
            RiskLevel::High => write!(f, "High"),
            RiskLevel::Critical => write!(f, "Critical"),
        }
    }
}

// ============================================================================
// 风险事件
// ============================================================================

/// 风险事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskEvent {
    /// 事件ID
    pub id: Uuid,
    /// 事件类型
    pub event_type: RiskEventType,
    /// 风险等级
    pub risk_level: RiskLevel,
    /// 事件描述
    pub description: String,
    /// 相关的交易对
    pub trading_pair: Option<String>,
    /// 相关的订单ID
    pub order_id: Option<Uuid>,
    /// 事件时间
    pub timestamp: DateTime<Utc>,
    /// 事件数据
    pub data: HashMap<String, serde_json::Value>,
}

impl RiskEvent {
    /// 创建新的风险事件
    pub fn new(
        event_type: RiskEventType,
        risk_level: RiskLevel,
        description: String,
    ) -> Self {
        Self {
            id: Uuid::new_v4(),
            event_type,
            risk_level,
            description,
            trading_pair: None,
            order_id: None,
            timestamp: Utc::now(),
            data: HashMap::new(),
        }
    }

    /// 设置交易对
    pub fn with_trading_pair(mut self, trading_pair: String) -> Self {
        self.trading_pair = Some(trading_pair);
        self
    }

    /// 设置订单ID
    pub fn with_order_id(mut self, order_id: Uuid) -> Self {
        self.order_id = Some(order_id);
        self
    }

    /// 添加数据
    pub fn with_data(mut self, key: String, value: serde_json::Value) -> Self {
        self.data.insert(key, value);
        self
    }
}

/// 风险事件类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum RiskEventType {
    /// 订单风险
    OrderRisk,
    /// 持仓风险
    PositionRisk,
    /// 市场风险
    MarketRisk,
    /// 流动性风险
    LiquidityRisk,
    /// 系统风险
    SystemRisk,
    /// 合规风险
    ComplianceRisk,
}

impl std::fmt::Display for RiskEventType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RiskEventType::OrderRisk => write!(f, "Order Risk"),
            RiskEventType::PositionRisk => write!(f, "Position Risk"),
            RiskEventType::MarketRisk => write!(f, "Market Risk"),
            RiskEventType::LiquidityRisk => write!(f, "Liquidity Risk"),
            RiskEventType::SystemRisk => write!(f, "System Risk"),
            RiskEventType::ComplianceRisk => write!(f, "Compliance Risk"),
        }
    }
}


