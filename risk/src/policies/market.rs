//! 市场风控策略

use async_trait::async_trait;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use crate::core::{RiskPolicy, RiskResult, RiskContext};

/// 市场风控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketRiskConfig {
    /// 最大价格偏离度（相对于市场价）
    pub max_price_deviation: Decimal,
    /// 最大滑点
    pub max_slippage: Decimal,
    /// 是否启用
    pub enabled: bool,
}

impl Default for MarketRiskConfig {
    fn default() -> Self {
        Self {
            max_price_deviation: Decimal::from_str_exact("0.05").unwrap(), // 5%
            max_slippage: Decimal::from_str_exact("0.01").unwrap(), // 1%
            enabled: true,
        }
    }
}

/// 市场风控策略
pub struct MarketRiskPolicy {
    config: MarketRiskConfig,
}

impl MarketRiskPolicy {
    pub fn new(config: MarketRiskConfig) -> Self {
        Self { config }
    }
}

impl Default for MarketRiskPolicy {
    fn default() -> Self {
        Self::new(MarketRiskConfig::default())
    }
}

#[async_trait]
impl RiskPolicy for MarketRiskPolicy {
    async fn evaluate(&self, context: &RiskContext) -> RiskResult {
        // 如果不是订单上下文，直接通过
        let order = match &context.order {
            Some(order) => order,
            None => return RiskResult::approved(),
        };
        
        // 这里应该获取实时市场价格进行比较
        // 简化实现：暂时只检查订单价格是否合理
        let price = order.price.unwrap_or(Decimal::ZERO);
        
        if price <= Decimal::ZERO {
            return RiskResult::rejected("订单价格必须大于0");
        }
        
        // TODO: 实际实现时需要：
        // 1. 获取当前市场价格
        // 2. 计算价格偏离度
        // 3. 评估市场深度和潜在滑点
        
        RiskResult::approved()
    }
    
    fn name(&self) -> &str {
        "MarketRiskPolicy"
    }
    
    fn description(&self) -> &str {
        "市场价格和滑点检查"
    }
    
    fn priority(&self) -> i32 {
        80 // 中等优先级
    }
    
    fn is_enabled(&self) -> bool {
        self.config.enabled
    }
}
