//! Repository Module
//!
//! 数据仓储模块，提供统一的数据访问接口和实现
//!
//! 此模块只导出 traits（接口），具体实现通过工厂函数创建

// 公开的子模块
pub mod traits;

// SQLx实现模块
pub mod sqlx;
pub mod mock_order_repository;

// 导出统一的Repository接口
pub use traits::{
    // 基础接口
    base_repository::{Repository, PaginationResult, QueryFilter, SortConfig, SortDirection},

    // 统一的Repository接口
    order_repository::{OrderRepository, OrderQueryFilter, OrderStatistics},
    trade_repository::{TradeRepository, TradeQueryFilter, TradeStatistics},

    // 兼容性接口（逐步废弃）
    StrategyRepository, EventRepository,
    EnhancedOrderRepository, Pagination, OrderSort,
    OrderSortBy, OrderRepositoryBuilder,
    RiskRepository, RiskCheckRecord, RiskRuleRecord,
    RiskViolationRecord, RiskQueryFilter, RiskRuleFilter,
    SystemConfigRepository, ConfigStatistics
};
