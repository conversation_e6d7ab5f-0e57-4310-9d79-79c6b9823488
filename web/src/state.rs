//! 应用状态管理

use std::sync::Arc;
use std::time::Instant;
use sigmax_core::{SigmaXResult, DatabaseConfig, EventBus};
use sigmax_database::{DatabaseManager};
use sigmax_database::{
    OrderRepositoryImpl, TradeRepositoryImpl, StrategyRepositoryImpl,
    OrderRepository, TradeRepository, StrategyRepository
};
// 暂时注释掉不存在的导入
// use sigmax_risk::{SqlUnifiedRiskRepository, unified_engine::UnifiedRiskEngine};

use sigmax_data::BacktestDataManager;
use sigmax_engines::EngineManager;
use crate::{
    websocket::WebSocketServer,
    services::{StrategyService, ConfigManager, UnifiedRiskService},
    execution_engine::StrategyExecutionEngine,
    service_container::WebServiceContainer,
    realtime_progress_handler::RealtimeProgressHandler,
};

/// 应用状态
#[derive(Clone)]
pub struct AppState {
    pub database: Arc<DatabaseManager>,
    pub order_repository: Arc<dyn OrderRepository>,
    pub trade_repository: Arc<dyn TradeRepository>,
    pub strategy_repository: Arc<dyn StrategyRepository>,
    // 暂时注释掉不存在的类型
    // pub unified_risk_repository: Arc<SqlUnifiedRiskRepository>,
    // pub unified_risk_engine: Arc<UnifiedRiskEngine>,
    pub engine_manager: Arc<EngineManager>,
    pub websocket_server: Arc<WebSocketServer>,
    pub backtest_data_manager: Arc<BacktestDataManager>,
    pub strategy_service: StrategyService,
    pub unified_risk_service: UnifiedRiskService,
    pub config_manager: Arc<ConfigManager>,
    pub execution_engine: Arc<StrategyExecutionEngine>,
    pub realtime_progress_handler: Arc<RealtimeProgressHandler>,
    pub start_time: Instant,
}

impl AppState {
    /// 创建新的应用状态
    pub async fn new(database_url: &str) -> SigmaXResult<Self> {
        // 创建数据库配置
        let config = DatabaseConfig {
            url: database_url.to_string(),
            max_connections: 10,
            connection_timeout: 30,
            auto_migrate: false,  // 禁用自动迁移避免错误
        };

        // 初始化数据库
        let database = Arc::new(DatabaseManager::new(config).await?);

        // 创建仓库实例 - 使用新的标准实现
        let order_repository: Arc<dyn OrderRepository> = Arc::new(OrderRepositoryImpl::new(database.clone()));
        let trade_repository: Arc<dyn TradeRepository> = Arc::new(TradeRepositoryImpl::new(database.clone()));
        let strategy_repository: Arc<dyn StrategyRepository> = Arc::new(StrategyRepositoryImpl::new(database.clone()));
        // 暂时注释掉不存在的类型
        // let unified_risk_repository = Arc::new(SqlUnifiedRiskRepository::new(database.clone()));
        // let unified_risk_engine: Arc<UnifiedRiskEngine> = Arc::new(UnifiedRiskEngine::new(unified_risk_repository.clone()).await?);

        // 创建事件总线
        let mut event_bus = EventBus::new();
        event_bus.start().await?;
        let event_bus = Arc::new(event_bus);

        // 创建服务容器（使用统一风控引擎）
        let service_container = Arc::new(
            WebServiceContainer::with_unified_risk_engine(database.clone()).await?
        );

        // 创建引擎管理器
        let engine_manager = Arc::new(EngineManager::new(event_bus, service_container).await?);

        // 创建WebSocket服务器
        let websocket_server = Arc::new(WebSocketServer::new());

        // 创建回测数据管理器 - 尝试多个可能的路径
        let possible_paths = vec![
            "bt_klines",           // 当前目录
            "../bt_klines",        // 上级目录（从web目录运行时）
            "../../bt_klines",     // 上上级目录
        ];

        let mut data_dir = "bt_klines"; // 默认路径
        for path in &possible_paths {
            if std::path::Path::new(path).exists() {
                data_dir = path;
                break;
            }
        }

        let backtest_data_manager = Arc::new(BacktestDataManager::new(data_dir));

        // 创建策略、风控和配置服务
        let mut strategy_service = StrategyService::new(database.pool().clone());
        // 创建统一风控服务
        let unified_risk_service = UnifiedRiskService::new();
        let config_manager = Arc::new(ConfigManager::new(database.clone()).await
            .map_err(|e| sigmax_core::SigmaXError::internal(
                sigmax_core::InternalErrorCode::ConfigurationError,
                e.to_string()
            ))?);

        // 创建策略执行引擎
        let execution_engine = Arc::new(StrategyExecutionEngine::new(
            Arc::new(strategy_service.clone()),
            Arc::new(unified_risk_service.clone()),
        ));

        // 将执行引擎设置到策略服务中
        strategy_service.set_execution_engine(Arc::clone(&execution_engine));

        // 创建实时进度处理器
        let realtime_progress_handler = Arc::new(RealtimeProgressHandler::new(websocket_server.clone()));

        Ok(Self {
            database,
            order_repository,
            trade_repository,
            strategy_repository,
            // 暂时注释掉不存在的类型
            // unified_risk_repository,
            // unified_risk_engine,
            engine_manager,
            websocket_server,
            backtest_data_manager,
            strategy_service,
            unified_risk_service,
            config_manager,
            execution_engine,
            realtime_progress_handler,
            start_time: Instant::now(),
        })
    }

    /// 健康检查
    pub async fn health_check(&self) -> SigmaXResult<AppStatus> {
        // 检查数据库连接
        let db_healthy = self.database.health_check().await.is_ok();

        Ok(AppStatus {
            database: db_healthy,
            overall: db_healthy,
        })
    }
}

/// 健康状态
#[derive(Debug, serde::Serialize)]
pub struct AppStatus {
    pub database: bool,
    pub overall: bool,
}