//! 迁移管理器
//! 
//! 专门负责数据库迁移的管理和执行

use sigmax_core::{SigmaXResult, SigmaXError, DatabaseErrorCode};
use sqlx::Row;
use std::sync::Arc;
use std::path::PathBuf;
use super::connection_manager::ConnectionManager;

/// 迁移配置
#[derive(Debug, Clone)]
pub struct MigrationConfig {
    /// 是否自动执行迁移
    pub auto_migrate: bool,
    /// 迁移脚本目录
    pub migration_dir: PathBuf,
    /// 外部迁移脚本路径
    pub external_script_path: Option<PathBuf>,
    /// 是否在启动时检查迁移状态
    pub check_on_startup: bool,
}

impl Default for MigrationConfig {
    fn default() -> Self {
        Self {
            auto_migrate: false,
            migration_dir: PathBuf::from("database/migrations"),
            external_script_path: Some(PathBuf::from("database/external-db/init_database.sql")),
            check_on_startup: true,
        }
    }
}

/// 迁移状态
#[derive(Debu<PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum MigrationStatus {
    /// 需要迁移
    Required,
    /// 已是最新
    UpToDate,
    /// 迁移失败
    Failed(String),
    /// 未知状态
    Unknown,
}

/// 迁移信息
#[derive(Debug, Clone)]
pub struct MigrationInfo {
    /// 迁移名称
    pub name: String,
    /// 迁移版本
    pub version: String,
    /// 是否已应用
    pub applied: bool,
    /// 应用时间
    pub applied_at: Option<chrono::DateTime<chrono::Utc>>,
}

/// 迁移管理器
/// 
/// 专门负责数据库迁移的检查、执行和管理
#[derive(Debug)]
pub struct MigrationManager {
    connection_manager: Arc<ConnectionManager>,
    config: MigrationConfig,
}

impl MigrationManager {
    /// 创建新的迁移管理器
    pub fn new(connection_manager: Arc<ConnectionManager>, config: MigrationConfig) -> Self {
        Self {
            connection_manager,
            config,
        }
    }

    /// 使用默认配置创建迁移管理器
    pub fn with_defaults(connection_manager: Arc<ConnectionManager>) -> Self {
        Self::new(connection_manager, MigrationConfig::default())
    }

    /// 检查迁移状态
    pub async fn check_migration_status(&self) -> SigmaXResult<MigrationStatus> {
        match self.should_run_migrations().await {
            Ok(true) => Ok(MigrationStatus::Required),
            Ok(false) => Ok(MigrationStatus::UpToDate),
            Err(e) => Ok(MigrationStatus::Failed(e.to_string())),
        }
    }

    /// 运行数据库迁移
    pub async fn run_migrations(&self) -> SigmaXResult<()> {
        tracing::info!("Starting database migrations...");

        // 检查是否需要执行迁移
        if !self.should_run_migrations().await? {
            tracing::info!("Database schema is up to date, skipping migrations");
            return Ok(());
        }

        // 执行迁移
        self.execute_migrations().await?;

        tracing::info!("Database migrations completed successfully");
        Ok(())
    }

    /// 检查是否需要运行迁移
    async fn should_run_migrations(&self) -> SigmaXResult<bool> {
        // 检查关键表是否存在
        let result = sqlx::query(
            r#"
            SELECT COUNT(*) as table_count
            FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_name IN ('exchanges', 'trading_pairs', 'orders', 'trades', 'strategies', 'portfolios')
            "#
        )
        .fetch_one(self.connection_manager.pool())
        .await
        .map_err(|e| SigmaXError::database(
            DatabaseErrorCode::QueryFailed, 
            format!("Failed to check existing tables: {}", e)
        ))?;

        let table_count: i64 = result.get("table_count");

        // 如果核心表不完整，需要运行迁移
        Ok(table_count < 6)
    }

    /// 执行迁移
    async fn execute_migrations(&self) -> SigmaXResult<()> {
        if let Some(external_script) = &self.config.external_script_path {
            self.execute_external_migrations(external_script).await?;
        } else {
            self.execute_internal_migrations().await?;
        }
        Ok(())
    }

    /// 执行外部迁移脚本
    async fn execute_external_migrations(&self, script_path: &PathBuf) -> SigmaXResult<()> {
        tracing::warn!("External SQL migration script execution is not implemented in Rust code");
        tracing::warn!("Please run the following command manually to initialize the database:");
        tracing::warn!("cd database/external-db && ./setup.sh \"{}\"", 
                      self.connection_manager.config().url);
        tracing::warn!("Or execute: psql \"{}\" -f {:?}", 
                      self.connection_manager.config().url, script_path);

        // 对于自动迁移，我们只执行基本的表创建
        // 完整的迁移应该通过外部脚本执行
        self.create_basic_tables().await?;

        Ok(())
    }

    /// 执行内部迁移
    async fn execute_internal_migrations(&self) -> SigmaXResult<()> {
        // 创建迁移历史表
        self.create_migration_history_table().await?;
        
        // 执行基本表创建
        self.create_basic_tables().await?;
        
        // 记录迁移历史
        self.record_migration("initial_schema", "1.0.0").await?;
        
        Ok(())
    }

    /// 创建迁移历史表
    async fn create_migration_history_table(&self) -> SigmaXResult<()> {
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS migration_history (
                id SERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL UNIQUE,
                version VARCHAR(50) NOT NULL,
                applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                checksum VARCHAR(64)
            )
            "#
        )
        .execute(self.connection_manager.pool())
        .await
        .map_err(|e| SigmaXError::database(
            DatabaseErrorCode::QueryFailed, 
            format!("Failed to create migration history table: {}", e)
        ))?;

        Ok(())
    }

    /// 创建基本表结构
    async fn create_basic_tables(&self) -> SigmaXResult<()> {
        // 表创建逻辑已迁移到外部SQL脚本
        // 这里只是一个占位符实现
        tracing::info!("Basic table creation logic has been migrated to external SQL scripts");
        tracing::info!("Please use database/external-db/setup.sh to initialize database schema");

        Ok(())
    }

    /// 记录迁移历史
    async fn record_migration(&self, name: &str, version: &str) -> SigmaXResult<()> {
        sqlx::query(
            "INSERT INTO migration_history (name, version) VALUES ($1, $2) ON CONFLICT (name) DO NOTHING"
        )
        .bind(name)
        .bind(version)
        .execute(self.connection_manager.pool())
        .await
        .map_err(|e| SigmaXError::database(
            DatabaseErrorCode::QueryFailed, 
            format!("Failed to record migration: {}", e)
        ))?;

        Ok(())
    }

    /// 获取迁移历史
    pub async fn get_migration_history(&self) -> SigmaXResult<Vec<MigrationInfo>> {
        let rows = sqlx::query(
            "SELECT name, version, applied_at FROM migration_history ORDER BY applied_at DESC"
        )
        .fetch_all(self.connection_manager.pool())
        .await
        .map_err(|e| SigmaXError::database(
            DatabaseErrorCode::QueryFailed, 
            format!("Failed to get migration history: {}", e)
        ))?;

        let mut migrations = Vec::new();
        for row in rows {
            migrations.push(MigrationInfo {
                name: row.get("name"),
                version: row.get("version"),
                applied: true,
                applied_at: row.get("applied_at"),
            });
        }

        Ok(migrations)
    }

    /// 回滚迁移（占位符实现）
    pub async fn rollback_migration(&self, _migration_name: &str) -> SigmaXResult<()> {
        tracing::warn!("Migration rollback is not implemented yet");
        Err(SigmaXError::database(
            DatabaseErrorCode::QueryFailed,
            "Migration rollback is not implemented"
        ))
    }

    /// 验证数据库架构
    pub async fn validate_schema(&self) -> SigmaXResult<bool> {
        // 检查关键表是否存在且结构正确
        let tables_to_check = vec![
            "exchanges", "trading_pairs", "orders", 
            "trades", "strategies", "portfolios"
        ];

        for table in tables_to_check {
            let exists = self.table_exists(table).await?;
            if !exists {
                tracing::warn!("Required table '{}' does not exist", table);
                return Ok(false);
            }
        }

        Ok(true)
    }

    /// 检查表是否存在
    async fn table_exists(&self, table_name: &str) -> SigmaXResult<bool> {
        let result = sqlx::query(
            r#"
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = $1
            )
            "#
        )
        .bind(table_name)
        .fetch_one(self.connection_manager.pool())
        .await
        .map_err(|e| SigmaXError::database(
            DatabaseErrorCode::QueryFailed, 
            format!("Failed to check table existence: {}", e)
        ))?;

        Ok(result.get::<bool, _>("exists"))
    }
}

impl Clone for MigrationManager {
    fn clone(&self) -> Self {
        Self {
            connection_manager: Arc::clone(&self.connection_manager),
            config: self.config.clone(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::managers::connection_manager::ConnectionManager;

    #[test]
    fn test_migration_config_default() {
        let config = MigrationConfig::default();
        assert!(!config.auto_migrate);
        assert!(config.check_on_startup);
        assert_eq!(config.migration_dir, PathBuf::from("database/migrations"));
    }

    #[test]
    fn test_migration_status() {
        assert_eq!(MigrationStatus::Required, MigrationStatus::Required);
        assert_eq!(MigrationStatus::UpToDate, MigrationStatus::UpToDate);
        assert_ne!(MigrationStatus::Required, MigrationStatus::UpToDate);
    }

    #[tokio::test]
    async fn test_migration_manager_creation() {
        let connection_manager = Arc::new(ConnectionManager::new_mock().unwrap());
        let migration_manager = MigrationManager::with_defaults(connection_manager);
        
        // 测试克隆
        let _migration_manager_clone = migration_manager.clone();
    }
}
