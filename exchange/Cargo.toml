[package]
name = "sigmax-exchange"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true

[dependencies]
sigmax-core.workspace = true
sigmax-interfaces = { path = "../interfaces" }
tokio.workspace = true
serde.workspace = true
serde_json.workspace = true
reqwest.workspace = true
anyhow.workspace = true
tracing.workspace = true
async-trait = "0.1"
