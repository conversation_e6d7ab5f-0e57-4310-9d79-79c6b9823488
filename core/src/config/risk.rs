//! 风险管理配置模型

use chrono::{DateTime, Utc};
use rust_decimal::prelude::ToPrimitive;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use validator::{Validate, ValidationError};

use crate::SigmaXResult;

/// 风险管理配置 - 统一的完整风险配置模型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Validate)]
pub struct RiskManagementConfig {
    /// 配置ID
    pub id: Option<String>,
    /// 配置名称
    #[validate(length(min = 1, max = 100, message = "配置名称长度必须在1-100个字符之间"))]
    pub name: String,
    /// 配置描述
    #[validate(length(max = 500, message = "配置描述长度不能超过500个字符"))]
    pub description: Option<String>,
    /// 策略类型（可选，为空表示全局配置）
    pub strategy_type: Option<String>,
    /// 是否启用
    pub enabled: bool,
    /// 风险参数
    #[validate]
    pub risk_parameters: RiskConfigParameters,
    /// 创建时间
    pub created_at: Option<DateTime<Utc>>,
    /// 更新时间
    pub updated_at: Option<DateTime<Utc>>,
    /// 创建者
    pub created_by: Option<String>,
}

/// 风险参数结构 - 完整的风险参数体系
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Validate)]
pub struct RiskConfigParameters {
    /// 基础风险参数
    #[validate]
    pub basic: BasicRiskParams,
    /// 持仓风险参数
    #[validate]
    pub position: PositionRiskParams,
    /// 交易风险参数
    #[validate]
    pub trading: TradingRiskParams,
    /// 时间风险参数
    #[validate]
    pub time_based: TimeBasedRiskParams,
    /// 市场风险参数
    #[validate]
    pub market: MarketRiskParams,
    /// 高级风险参数
    #[validate]
    pub advanced: AdvancedRiskParams,
}

/// 基础风险参数
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Validate)]
pub struct BasicRiskParams {
    /// 最大回撤百分比
    #[validate(custom = "validate_percentage_0_100")]
    pub max_drawdown_percent: rust_decimal::Decimal,
    /// 最大日损失百分比
    #[validate(custom = "validate_percentage_0_50")]
    pub max_daily_loss_percent: rust_decimal::Decimal,
    /// 最大投资组合风险百分比
    pub max_portfolio_risk_percent: rust_decimal::Decimal,
    /// 单个持仓大小限制百分比
    pub position_size_limit_percent: rust_decimal::Decimal,
    /// 默认止损百分比
    pub stop_loss_percent: rust_decimal::Decimal,
    /// 最大单笔订单金额
    #[validate(custom = "validate_positive_decimal")]
    pub max_order_amount: rust_decimal::Decimal,
    /// 最大总持仓金额
    #[validate(custom = "validate_positive_decimal")]
    pub max_total_position: rust_decimal::Decimal,
}

/// 持仓风险参数
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Validate)]
pub struct PositionRiskParams {
    /// 单个交易对最大持仓比例
    pub max_position_per_symbol: rust_decimal::Decimal,
    /// 最大杠杆倍数
    pub max_leverage: rust_decimal::Decimal,
    /// 持仓集中度限制
    pub concentration_limit: rust_decimal::Decimal,
    /// 相关性限制
    pub correlation_limit: rust_decimal::Decimal,
    /// 行业集中度限制
    pub sector_concentration_limit: rust_decimal::Decimal,
}

/// 交易风险参数
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Validate)]
pub struct TradingRiskParams {
    /// 最大日交易次数
    pub max_daily_trades: u32,
    /// 最大小时交易次数
    pub max_hourly_trades: u32,
    /// 最小订单间隔（秒）
    pub min_order_interval: u32,
    /// 最大滑点容忍度
    pub max_slippage_tolerance: rust_decimal::Decimal,
    /// 最大价格偏离度
    pub max_price_deviation: rust_decimal::Decimal,
}

/// 时间风险参数
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Validate)]
pub struct TimeBasedRiskParams {
    /// 允许交易的时间段
    pub trading_hours: Vec<TradingHour>,
    /// 禁止交易的日期
    pub blackout_dates: Vec<String>,
    /// 市场关闭时的行为
    pub market_close_behavior: MarketCloseBehavior,
    /// 超时设置
    pub timeout_settings: TimeoutSettings,
}

/// 交易时间段
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct TradingHour {
    /// 星期几 (0=周日, 1=周一, ..., 6=周六)
    pub day_of_week: u8,
    /// 开始时间 (HH:MM格式)
    pub start_time: String,
    /// 结束时间 (HH:MM格式)
    pub end_time: String,
    /// 时区
    pub timezone: String,
}

/// 市场关闭行为
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum MarketCloseBehavior {
    StopAll,
    CloseOnly,
    Continue,
}

/// 超时设置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct TimeoutSettings {
    pub order_timeout: u32,
    pub connection_timeout: u32,
    pub response_timeout: u32,
}

/// 市场风险参数
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Validate)]
pub struct MarketRiskParams {
    pub volatility_threshold: rust_decimal::Decimal,
    pub min_liquidity_requirement: rust_decimal::Decimal,
    pub price_impact_limit: rust_decimal::Decimal,
    pub correlation_limit: rust_decimal::Decimal,
    pub var_limit: rust_decimal::Decimal,
    pub confidence_level: rust_decimal::Decimal,
}

/// 高级风险参数
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Validate)]
pub struct AdvancedRiskParams {
    pub dynamic_adjustment: DynamicRiskAdjustment,
    pub stress_test: StressTestParams,
    pub risk_budget: RiskBudgetAllocation,
    pub emergency_measures: EmergencyMeasures,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Validate)]
pub struct DynamicRiskAdjustment {
    pub enabled: bool,
    pub adjustment_frequency: u32,
    pub volatility_factor: rust_decimal::Decimal,
    pub liquidity_factor: rust_decimal::Decimal,
    pub sentiment_factor: rust_decimal::Decimal,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Validate)]
pub struct StressTestParams {
    pub enabled: bool,
    pub test_frequency: u32,
    pub extreme_price_move: rust_decimal::Decimal,
    pub liquidity_shock: rust_decimal::Decimal,
    pub correlation_shock: rust_decimal::Decimal,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Validate)]
pub struct RiskBudgetAllocation {
    pub total_risk_budget: rust_decimal::Decimal,
    pub strategy_allocation: HashMap<String, rust_decimal::Decimal>,
    pub asset_class_allocation: HashMap<String, rust_decimal::Decimal>,
    pub geographic_allocation: HashMap<String, rust_decimal::Decimal>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Validate)]
pub struct EmergencyMeasures {
    pub emergency_stop_threshold: rust_decimal::Decimal,
    pub auto_reduce_threshold: rust_decimal::Decimal,
    pub notification_threshold: rust_decimal::Decimal,
    pub emergency_contacts: Vec<String>,
    pub emergency_actions: Vec<EmergencyAction>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Validate)]
pub struct EmergencyAction {
    pub action_type: EmergencyActionType,
    pub trigger_condition: String,
    pub parameters: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum EmergencyActionType {
    StopAllTrading,
    CloseAllPositions,
    ReducePositions,
    SendNotification,
    AdjustRiskParams,
}

// 默认值实现
impl Default for RiskManagementConfig {
    fn default() -> Self {
        Self {
            id: None,
            name: "默认风险配置".to_string(),
            description: Some("系统默认的风险管理配置".to_string()),
            strategy_type: None,
            enabled: true,
            risk_parameters: RiskConfigParameters::default(),
            created_at: None,
            updated_at: None,
            created_by: Some("system".to_string()),
        }
    }
}

impl Default for RiskConfigParameters {
    fn default() -> Self {
        Self {
            basic: BasicRiskParams::default(),
            position: PositionRiskParams::default(),
            trading: TradingRiskParams::default(),
            time_based: TimeBasedRiskParams::default(),
            market: MarketRiskParams::default(),
            advanced: AdvancedRiskParams::default(),
        }
    }
}

impl Default for BasicRiskParams {
    fn default() -> Self {
        Self {
            max_drawdown_percent: rust_decimal::Decimal::new(10, 2), // 0.10
            max_daily_loss_percent: rust_decimal::Decimal::new(5, 2), // 0.05
            max_portfolio_risk_percent: rust_decimal::Decimal::new(20, 2), // 0.20
            position_size_limit_percent: rust_decimal::Decimal::new(10, 2), // 0.10
            stop_loss_percent: rust_decimal::Decimal::new(5, 2), // 0.05
            max_order_amount: rust_decimal::Decimal::from(1000),
            max_total_position: rust_decimal::Decimal::from(10000),
        }
    }
}

impl Default for PositionRiskParams {
    fn default() -> Self {
        Self {
            max_position_per_symbol: rust_decimal::Decimal::new(3, 1), // 0.3
            max_leverage: rust_decimal::Decimal::from(3),
            concentration_limit: rust_decimal::Decimal::new(5, 1), // 0.5
            correlation_limit: rust_decimal::Decimal::new(7, 1), // 0.7
            sector_concentration_limit: rust_decimal::Decimal::new(4, 1), // 0.4
        }
    }
}

impl Default for TradingRiskParams {
    fn default() -> Self {
        Self {
            max_daily_trades: 100,
            max_hourly_trades: 20,
            min_order_interval: 5,
            max_slippage_tolerance: rust_decimal::Decimal::new(1, 2), // 0.01
            max_price_deviation: rust_decimal::Decimal::new(2, 2), // 0.02
        }
    }
}

impl Default for TimeBasedRiskParams {
    fn default() -> Self {
        Self {
            trading_hours: vec![TradingHour::default()],
            blackout_dates: Vec::new(),
            market_close_behavior: MarketCloseBehavior::default(),
            timeout_settings: TimeoutSettings::default(),
        }
    }
}

impl Default for TradingHour {
    fn default() -> Self {
        Self {
            day_of_week: 1, // Monday
            start_time: "09:00".to_string(),
            end_time: "16:00".to_string(),
            timezone: "UTC".to_string(),
        }
    }
}

impl Default for MarketCloseBehavior {
    fn default() -> Self {
        MarketCloseBehavior::StopAll
    }
}

impl Default for TimeoutSettings {
    fn default() -> Self {
        Self {
            order_timeout: 30,
            connection_timeout: 10,
            response_timeout: 5,
        }
    }
}

impl Default for MarketRiskParams {
    fn default() -> Self {
        Self {
            volatility_threshold: rust_decimal::Decimal::new(2, 1), // 0.2
            min_liquidity_requirement: rust_decimal::Decimal::from(10000),
            price_impact_limit: rust_decimal::Decimal::new(1, 2), // 0.01
            correlation_limit: rust_decimal::Decimal::new(8, 1), // 0.8
            var_limit: rust_decimal::Decimal::new(5, 2), // 0.05
            confidence_level: rust_decimal::Decimal::new(95, 2), // 0.95
        }
    }
}

impl Default for AdvancedRiskParams {
    fn default() -> Self {
        Self {
            dynamic_adjustment: DynamicRiskAdjustment::default(),
            stress_test: StressTestParams::default(),
            risk_budget: RiskBudgetAllocation::default(),
            emergency_measures: EmergencyMeasures::default(),
        }
    }
}

impl Default for DynamicRiskAdjustment {
    fn default() -> Self {
        Self {
            enabled: false,
            adjustment_frequency: 3600,
            volatility_factor: rust_decimal::Decimal::new(1, 1), // 0.1
            liquidity_factor: rust_decimal::Decimal::new(1, 1), // 0.1
            sentiment_factor: rust_decimal::Decimal::new(5, 2), // 0.05
        }
    }
}

impl Default for StressTestParams {
    fn default() -> Self {
        Self {
            enabled: false,
            test_frequency: 86400, // daily
            extreme_price_move: rust_decimal::Decimal::new(20, 2), // 0.20
            liquidity_shock: rust_decimal::Decimal::new(50, 2), // 0.50
            correlation_shock: rust_decimal::Decimal::new(30, 2), // 0.30
        }
    }
}

impl Default for RiskBudgetAllocation {
    fn default() -> Self {
        Self {
            total_risk_budget: rust_decimal::Decimal::from(10000),
            strategy_allocation: HashMap::new(),
            asset_class_allocation: HashMap::new(),
            geographic_allocation: HashMap::new(),
        }
    }
}

impl Default for EmergencyMeasures {
    fn default() -> Self {
        Self {
            emergency_stop_threshold: rust_decimal::Decimal::new(10, 2), // 0.10
            auto_reduce_threshold: rust_decimal::Decimal::new(5, 2), // 0.05
            notification_threshold: rust_decimal::Decimal::new(3, 2), // 0.03
            emergency_contacts: Vec::new(),
            emergency_actions: Vec::new(),
        }
    }
}

// ============================================================================
// 🔥 从 models_old.rs 移植：向后兼容方法
// ============================================================================

impl RiskManagementConfig {
    /// 执行完整的风险配置验证
    pub fn validate_complete(&self) -> SigmaXResult<()> {
        // 首先执行基础验证（通过validator derive宏）
        if let Err(validation_errors) = <Self as Validate>::validate(self) {
            return Err(crate::SigmaXError::from(validation_errors));
        }

        // 可以在这里添加更多跨字段验证逻辑
        Ok(())
    }

    /// 向后兼容：获取最大回撤百分比
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供向后兼容的API访问方式
    pub fn max_drawdown_percent(&self) -> f64 {
        self.risk_parameters.basic.max_drawdown_percent.to_f64().unwrap_or(20.0)
    }

    /// 向后兼容：获取最大日损失百分比
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供向后兼容的API访问方式
    pub fn max_daily_loss_percent(&self) -> f64 {
        self.risk_parameters.basic.max_daily_loss_percent.to_f64().unwrap_or(5.0)
    }

    /// 向后兼容：获取最大持仓比例
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供向后兼容的API访问方式
    pub fn max_position_ratio(&self) -> f64 {
        self.risk_parameters.position.max_position_per_symbol.to_f64().unwrap_or(30.0)
    }

    /// 向后兼容：获取最大订单金额
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供向后兼容的API访问方式
    pub fn max_order_amount(&self) -> f64 {
        self.risk_parameters.basic.max_order_amount.to_f64().unwrap_or(1000.0)
    }

    /// 向后兼容：获取止损百分比
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供向后兼容的API访问方式
    pub fn stop_loss_percent(&self) -> f64 {
        self.risk_parameters.basic.stop_loss_percent.to_f64().unwrap_or(5.0)
    }

    /// 向后兼容：获取检查间隔
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供向后兼容的API访问方式
    pub fn check_interval(&self) -> u32 {
        self.risk_parameters.trading.min_order_interval
    }

    /// 向后兼容：创建简化的风险配置元组
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，返回(max_position_ratio, max_order_amount, max_daily_loss, check_interval, stop_loss_percent)
    pub fn to_simple_config(&self) -> (f64, f64, f64, u32, f64) {
        (
            self.max_position_ratio(),
            self.max_order_amount(),
            self.max_daily_loss_percent(),
            self.check_interval(),
            self.stop_loss_percent(),
        )
    }

    /// 向后兼容：获取最大投资组合风险百分比
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供向后兼容的API访问方式
    pub fn max_portfolio_risk_percent(&self) -> f64 {
        self.risk_parameters.basic.max_portfolio_risk_percent.to_f64().unwrap_or(10.0)
    }

    /// 向后兼容：获取仓位大小限制百分比
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供向后兼容的API访问方式
    pub fn position_size_limit_percent(&self) -> f64 {
        self.risk_parameters.position.max_position_per_symbol.to_f64().unwrap_or(25.0)
    }

    /// 向后兼容：从旧版本参数创建风险配置
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，支持从旧版本的简单参数创建完整的风险配置
    pub fn from_legacy(
        max_drawdown: f64,
        max_daily_loss: f64,
        max_portfolio_risk: f64,
        position_size_limit: f64,
        stop_loss: f64,
    ) -> Self {
        use rust_decimal::Decimal;

        let mut config = Self::default();

        // 更新基础风险参数
        config.risk_parameters.basic.max_drawdown_percent = Decimal::from_f64_retain(max_drawdown).unwrap_or_default();
        config.risk_parameters.basic.max_daily_loss_percent = Decimal::from_f64_retain(max_daily_loss).unwrap_or_default();
        config.risk_parameters.basic.max_portfolio_risk_percent = Decimal::from_f64_retain(max_portfolio_risk).unwrap_or_default();
        config.risk_parameters.basic.position_size_limit_percent = Decimal::from_f64_retain(position_size_limit).unwrap_or_default();
        config.risk_parameters.basic.stop_loss_percent = Decimal::from_f64_retain(stop_loss).unwrap_or_default();

        // 更新持仓风险参数
        config.risk_parameters.position.max_position_per_symbol = Decimal::from_f64_retain(position_size_limit).unwrap_or_default();

        // 更新配置信息
        config.name = "Legacy Risk Config".to_string();
        config.description = Some("Migrated from legacy configuration".to_string());
        config.created_by = Some("system".to_string());

        config
    }

    /// 向后兼容：转换为旧版本格式
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，返回旧版本的参数元组
    pub fn to_legacy(&self) -> (f64, f64, f64, f64, f64) {
        (
            self.max_drawdown_percent(),
            self.max_daily_loss_percent(),
            self.max_portfolio_risk_percent(),
            self.position_size_limit_percent(),
            self.stop_loss_percent(),
        )
    }
}

// ============================================================================
// 🔥 从 models_old.rs 移植：完整的验证逻辑
// ============================================================================

// 注意：验证逻辑已通过 #[derive(Validate)] 宏实现，无需手动实现 Validatable trait


// ============================================================================
// 自定义验证函数 (用于validator derive宏)
// ============================================================================

/// 验证百分比在0-100之间
fn validate_percentage_0_100(value: &rust_decimal::Decimal) -> Result<(), ValidationError> {
    if *value <= rust_decimal::Decimal::ZERO || *value > rust_decimal::Decimal::new(100, 0) {
        return Err(ValidationError::new("百分比必须在0-100之间"));
    }
    Ok(())
}

/// 验证百分比在0-50之间
fn validate_percentage_0_50(value: &rust_decimal::Decimal) -> Result<(), ValidationError> {
    if *value <= rust_decimal::Decimal::ZERO || *value > rust_decimal::Decimal::new(50, 0) {
        return Err(ValidationError::new("百分比必须在0-50之间"));
    }
    Ok(())
}

/// 验证正数decimal
fn validate_positive_decimal(value: &rust_decimal::Decimal) -> Result<(), ValidationError> {
    if *value <= rust_decimal::Decimal::ZERO {
        return Err(ValidationError::new("数值必须大于0"));
    }
    Ok(())
}