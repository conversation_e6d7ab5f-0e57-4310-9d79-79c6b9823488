//! SigmaX Core Validators
//!
//! ## 主要职责
//!
//! 1. **具体验证器实现** - 实现各种业务验证器
//!    - 订单验证器
//!    - 价格验证器
//!    - 数量验证器
//!    - 风险验证器
//!
//! 2. **验证器工厂** - 创建和配置验证器
//!    - 验证器构建器
//!    - 预配置验证器
//!    - 验证器注册
//!
//! 3. **验证服务实现** - 业务验证服务的具体实现
//!    - 默认验证服务
//!    - 可配置验证服务
//!    - 验证缓存

use async_trait::async_trait;
use rust_decimal::Decimal;
use serde_json::Value;
use std::collections::HashMap;

use crate::{
    types::*,
    enums::*,
    error::*,
    constants,
    SigmaXResult,
};

use super::{CustomValidator, ValidationService, OrderRequest, RiskLimits, PortfolioConfig};

// ============================================================================
// 订单相关验证器
// ============================================================================

/// 订单数量验证器
pub struct OrderQuantityValidator {
    min_quantity: Decimal,
    max_quantity: Decimal,
}

impl OrderQuantityValidator {
    pub fn new(min_quantity: Decimal, max_quantity: Decimal) -> Self {
        Self {
            min_quantity,
            max_quantity,
        }
    }
    
    pub fn default() -> Self {
        Self::new(
            Decimal::new(1, 8), // 0.00000001
            Decimal::new(1000000, 0), // 1,000,000
        )
    }
}

#[async_trait]
impl CustomValidator<OrderRequest> for OrderQuantityValidator {
    async fn validate(&self, request: &OrderRequest) -> SigmaXResult<()> {
        if request.quantity < self.min_quantity {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "quantity",
                format!("Order quantity must be at least {}", self.min_quantity)
            ));
        }
        
        if request.quantity > self.max_quantity {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "quantity",
                format!("Order quantity must be at most {}", self.max_quantity)
            ));
        }
        
        Ok(())
    }
    
    fn name(&self) -> &str {
        "OrderQuantityValidator"
    }
    
    fn description(&self) -> Option<&str> {
        Some("Validates order quantity is within acceptable range")
    }
}

/// 订单价格验证器
pub struct OrderPriceValidator {
    allow_zero_price: bool,
}

impl OrderPriceValidator {
    pub fn new(allow_zero_price: bool) -> Self {
        Self { allow_zero_price }
    }
    
    pub fn default() -> Self {
        Self::new(false)
    }
}

#[async_trait]
impl CustomValidator<OrderRequest> for OrderPriceValidator {
    async fn validate(&self, request: &OrderRequest) -> SigmaXResult<()> {
        match request.order_type {
            OrderType::Market => {
                if request.price.is_some() {
                    return Err(SigmaXError::validation(
                        ValidationErrorCode::InvalidFormat,
                        "price",
                        "Market orders should not have a price"
                    ));
                }
            }
            OrderType::Limit | OrderType::StopLimit => {
                match request.price {
                    None => {
                        return Err(SigmaXError::validation(
                            ValidationErrorCode::Required,
                            "price",
                            "Limit orders must have a price"
                        ));
                    }
                    Some(price) => {
                        if !self.allow_zero_price && price <= Decimal::ZERO {
                            return Err(SigmaXError::validation(
                                ValidationErrorCode::OutOfRange,
                                "price",
                                "Order price must be positive"
                            ));
                        }
                    }
                }
            }
            OrderType::StopLoss => {
                // Stop orders may or may not have a price depending on implementation
            }
        }
        
        Ok(())
    }
    
    fn name(&self) -> &str {
        "OrderPriceValidator"
    }
    
    fn description(&self) -> Option<&str> {
        Some("Validates order price based on order type")
    }
}

/// 交易对验证器
pub struct TradingPairValidator {
    allowed_pairs: Option<Vec<TradingPair>>,
}

impl TradingPairValidator {
    pub fn new(allowed_pairs: Option<Vec<TradingPair>>) -> Self {
        Self { allowed_pairs }
    }
    
    pub fn allow_all() -> Self {
        Self::new(None)
    }
    
    pub fn allow_specific(pairs: Vec<TradingPair>) -> Self {
        Self::new(Some(pairs))
    }
}

#[async_trait]
impl CustomValidator<OrderRequest> for TradingPairValidator {
    async fn validate(&self, request: &OrderRequest) -> SigmaXResult<()> {
        if let Some(ref allowed) = self.allowed_pairs {
            if !allowed.contains(&request.trading_pair) {
                return Err(SigmaXError::validation(
                    ValidationErrorCode::InvalidFormat,
                    "trading_pair",
                    format!("Trading pair {} is not allowed", request.trading_pair)
                ));
            }
        }
        
        // 验证交易对格式
        if request.trading_pair.base.is_empty() || request.trading_pair.quote.is_empty() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "trading_pair",
                "Trading pair must have both base and quote currencies"
            ));
        }
        
        Ok(())
    }
    
    fn name(&self) -> &str {
        "TradingPairValidator"
    }
    
    fn description(&self) -> Option<&str> {
        Some("Validates trading pair is allowed and properly formatted")
    }
}

// ============================================================================
// 风险管理验证器
// ============================================================================

/// 风险限制验证器
pub struct RiskLimitsValidator {
    max_position_limit: Decimal,
    max_drawdown_limit: Decimal,
}

impl RiskLimitsValidator {
    pub fn new(max_position_limit: Decimal, max_drawdown_limit: Decimal) -> Self {
        Self {
            max_position_limit,
            max_drawdown_limit,
        }
    }
    
    pub fn default() -> Self {
        Self::new(
            Decimal::new(1000000, 0), // 1,000,000
            Decimal::new(50, 0), // 50%
        )
    }
}

#[async_trait]
impl CustomValidator<RiskLimits> for RiskLimitsValidator {
    async fn validate(&self, limits: &RiskLimits) -> SigmaXResult<()> {
        // 验证最大持仓规模
        if limits.max_position_size <= Decimal::ZERO {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "max_position_size",
                "Maximum position size must be positive"
            ));
        }
        
        if limits.max_position_size > self.max_position_limit {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "max_position_size",
                format!("Maximum position size cannot exceed {}", self.max_position_limit)
            ));
        }
        
        // 验证最大回撤
        if limits.max_drawdown < Decimal::ZERO || limits.max_drawdown > Decimal::new(100, 0) {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "max_drawdown",
                "Maximum drawdown must be between 0 and 100 percent"
            ));
        }
        
        if limits.max_drawdown > self.max_drawdown_limit {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "max_drawdown",
                format!("Maximum drawdown cannot exceed {}%", self.max_drawdown_limit)
            ));
        }
        
        // 验证最大日亏损
        if limits.max_daily_loss <= Decimal::ZERO {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "max_daily_loss",
                "Maximum daily loss must be positive"
            ));
        }
        
        Ok(())
    }
    
    fn name(&self) -> &str {
        "RiskLimitsValidator"
    }
    
    fn description(&self) -> Option<&str> {
        Some("Validates risk limits are within acceptable ranges")
    }
}

// ============================================================================
// 投资组合验证器
// ============================================================================

/// 投资组合配置验证器
pub struct PortfolioConfigValidator {
    min_initial_balance: Decimal,
    max_risk_tolerance: f64,
}

impl PortfolioConfigValidator {
    pub fn new(min_initial_balance: Decimal, max_risk_tolerance: f64) -> Self {
        Self {
            min_initial_balance,
            max_risk_tolerance,
        }
    }
    
    pub fn default() -> Self {
        Self::new(
            Decimal::new(1000, 0), // $1,000
            1.0, // 100%
        )
    }
}

#[async_trait]
impl CustomValidator<PortfolioConfig> for PortfolioConfigValidator {
    async fn validate(&self, config: &PortfolioConfig) -> SigmaXResult<()> {
        // 验证名称
        if config.name.is_empty() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::Required,
                "name",
                "Portfolio name is required"
            ));
        }
        
        if config.name.len() > constants::models::validation::MAX_NAME_LENGTH {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidLength,
                "name",
                format!("Portfolio name must be at most {} characters", constants::models::validation::MAX_NAME_LENGTH)
            ));
        }
        
        // 验证初始余额
        if config.initial_balance < self.min_initial_balance {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "initial_balance",
                format!("Initial balance must be at least {}", self.min_initial_balance)
            ));
        }
        
        // 验证风险容忍度
        if config.risk_tolerance < 0.0 || config.risk_tolerance > self.max_risk_tolerance {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "risk_tolerance",
                format!("Risk tolerance must be between 0 and {}", self.max_risk_tolerance)
            ));
        }
        
        Ok(())
    }
    
    fn name(&self) -> &str {
        "PortfolioConfigValidator"
    }
    
    fn description(&self) -> Option<&str> {
        Some("Validates portfolio configuration parameters")
    }
}

// ============================================================================
// 默认验证服务实现
// ============================================================================

/// 默认验证服务
pub struct DefaultValidationService {
    order_validators: Vec<Box<dyn CustomValidator<OrderRequest>>>,
    risk_validators: Vec<Box<dyn CustomValidator<RiskLimits>>>,
    portfolio_validators: Vec<Box<dyn CustomValidator<PortfolioConfig>>>,
    config_cache: HashMap<String, Value>,
}

impl DefaultValidationService {
    pub fn new() -> Self {
        let mut service = Self {
            order_validators: Vec::new(),
            risk_validators: Vec::new(),
            portfolio_validators: Vec::new(),
            config_cache: HashMap::new(),
        };
        
        // 添加默认验证器
        service.add_default_validators();
        service
    }
    
    fn add_default_validators(&mut self) {
        // 订单验证器
        self.order_validators.push(Box::new(OrderQuantityValidator::default()));
        self.order_validators.push(Box::new(OrderPriceValidator::default()));
        self.order_validators.push(Box::new(TradingPairValidator::allow_all()));
        
        // 风险验证器
        self.risk_validators.push(Box::new(RiskLimitsValidator::default()));
        
        // 投资组合验证器
        self.portfolio_validators.push(Box::new(PortfolioConfigValidator::default()));
    }
    
    pub fn add_order_validator(&mut self, validator: Box<dyn CustomValidator<OrderRequest>>) {
        self.order_validators.push(validator);
    }
    
    pub fn add_risk_validator(&mut self, validator: Box<dyn CustomValidator<RiskLimits>>) {
        self.risk_validators.push(validator);
    }
    
    pub fn add_portfolio_validator(&mut self, validator: Box<dyn CustomValidator<PortfolioConfig>>) {
        self.portfolio_validators.push(validator);
    }
}

impl Default for DefaultValidationService {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl ValidationService for DefaultValidationService {
    async fn validate_strategy_config(&self, config: &Value) -> SigmaXResult<()> {
        // 验证策略配置的基本结构
        if !config.is_object() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "strategy_config",
                "Strategy configuration must be a JSON object"
            ));
        }

        let obj = config.as_object().unwrap();

        // 验证必需字段
        if !obj.contains_key("name") {
            return Err(SigmaXError::validation(
                ValidationErrorCode::Required,
                "name",
                "Strategy name is required"
            ));
        }

        if !obj.contains_key("type") {
            return Err(SigmaXError::validation(
                ValidationErrorCode::Required,
                "type",
                "Strategy type is required"
            ));
        }

        // 验证名称
        if let Some(name) = obj.get("name").and_then(|v| v.as_str()) {
            if name.is_empty() {
                return Err(SigmaXError::validation(
                    ValidationErrorCode::Required,
                    "name",
                    "Strategy name cannot be empty"
                ));
            }

            if name.len() > constants::models::validation::MAX_NAME_LENGTH {
                return Err(SigmaXError::validation(
                    ValidationErrorCode::InvalidLength,
                    "name",
                    format!("Strategy name must be at most {} characters", constants::models::validation::MAX_NAME_LENGTH)
                ));
            }
        }

        Ok(())
    }

    async fn validate_order_request(&self, request: &OrderRequest) -> SigmaXResult<()> {
        // 执行所有订单验证器
        for validator in &self.order_validators {
            if validator.is_enabled() {
                validator.validate(request).await?;
            }
        }

        Ok(())
    }

    async fn validate_risk_limits(&self, limits: &RiskLimits) -> SigmaXResult<()> {
        // 执行所有风险验证器
        for validator in &self.risk_validators {
            if validator.is_enabled() {
                validator.validate(limits).await?;
            }
        }

        Ok(())
    }

    async fn validate_portfolio_config(&self, config: &PortfolioConfig) -> SigmaXResult<()> {
        // 执行所有投资组合验证器
        for validator in &self.portfolio_validators {
            if validator.is_enabled() {
                validator.validate(config).await?;
            }
        }

        Ok(())
    }

    async fn validate_trading_pair(&self, pair: &TradingPair) -> SigmaXResult<()> {
        // 验证交易对格式
        if pair.base.is_empty() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::Required,
                "base_currency",
                "Base currency is required"
            ));
        }

        if pair.quote.is_empty() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::Required,
                "quote_currency",
                "Quote currency is required"
            ));
        }

        // 验证货币代码长度
        if pair.base.len() > 10 {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidLength,
                "base_currency",
                "Base currency code must be at most 10 characters"
            ));
        }

        if pair.quote.len() > 10 {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidLength,
                "quote_currency",
                "Quote currency code must be at most 10 characters"
            ));
        }

        // 验证货币代码格式（只允许字母和数字）
        if !pair.base.chars().all(|c| c.is_alphanumeric()) {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "base_currency",
                "Base currency code must contain only alphanumeric characters"
            ));
        }

        if !pair.quote.chars().all(|c| c.is_alphanumeric()) {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "quote_currency",
                "Quote currency code must contain only alphanumeric characters"
            ));
        }

        Ok(())
    }

    async fn validate_price_range(&self, min_price: Price, max_price: Price) -> SigmaXResult<()> {
        // 验证价格为正数
        if min_price <= Decimal::ZERO {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "min_price",
                "Minimum price must be positive"
            ));
        }

        if max_price <= Decimal::ZERO {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "max_price",
                "Maximum price must be positive"
            ));
        }

        // 验证价格范围逻辑
        if min_price >= max_price {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidRange,
                "price_range",
                "Minimum price must be less than maximum price"
            ));
        }

        // 验证价格范围不能过大
        let price_ratio = max_price / min_price;
        if price_ratio > Decimal::new(1000, 0) {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "price_range",
                "Price range is too wide (max/min ratio cannot exceed 1000)"
            ));
        }

        Ok(())
    }
}
