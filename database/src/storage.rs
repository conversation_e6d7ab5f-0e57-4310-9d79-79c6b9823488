//! 存储实现
//!
//! 提供统一的数据存储接口和内存实现

use sigmax_core::{SigmaXResult, SigmaXError, InternalErrorCode, ValidationErrorCode, DatabaseErrorCode};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use chrono::{DateTime, Utc};

/// 存储接口
pub trait Storage: Send + Sync {
    /// 保存数据
    async fn save(&self, key: &str, data: &[u8]) -> SigmaXResult<()>;
    
    /// 获取数据
    async fn get(&self, key: &str) -> SigmaXResult<Option<Vec<u8>>>;
    
    /// 删除数据
    async fn delete(&self, key: &str) -> SigmaXResult<()>;
    
    /// 检查键是否存在
    async fn exists(&self, key: &str) -> SigmaXResult<bool>;
    
    /// 获取所有键
    async fn keys(&self) -> SigmaXResult<Vec<String>>;
    
    /// 清空所有数据
    async fn clear(&self) -> SigmaXResult<()>;
    
    /// 获取存储统计信息
    async fn stats(&self) -> SigmaXResult<StorageStats>;
}

/// 存储统计信息
#[derive(Debug, Clone)]
pub struct StorageStats {
    /// 总键数
    pub total_keys: usize,
    /// 总数据大小（字节）
    pub total_size_bytes: usize,
    /// 平均数据大小
    pub average_size_bytes: f64,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
}

/// 存储条目
#[derive(Debug, Clone)]
struct StorageEntry {
    /// 数据
    data: Vec<u8>,
    /// 创建时间
    created_at: DateTime<Utc>,
    /// 更新时间
    updated_at: DateTime<Utc>,
    /// 访问次数
    access_count: u64,
    /// 最后访问时间
    last_accessed: DateTime<Utc>,
}

impl StorageEntry {
    fn new(data: Vec<u8>) -> Self {
        let now = Utc::now();
        Self {
            data,
            created_at: now,
            updated_at: now,
            access_count: 0,
            last_accessed: now,
        }
    }

    fn update_data(&mut self, data: Vec<u8>) {
        self.data = data;
        self.updated_at = Utc::now();
    }

    fn access(&mut self) -> &Vec<u8> {
        self.access_count += 1;
        self.last_accessed = Utc::now();
        &self.data
    }
}

/// 内存存储实现
pub struct MemoryStorage {
    /// 数据存储
    storage: Arc<RwLock<HashMap<String, StorageEntry>>>,
    /// 统计信息
    stats: Arc<RwLock<StorageStats>>,
}

impl MemoryStorage {
    /// 创建新的内存存储
    pub fn new() -> Self {
        Self {
            storage: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(RwLock::new(StorageStats {
                total_keys: 0,
                total_size_bytes: 0,
                average_size_bytes: 0.0,
                last_updated: Utc::now(),
            })),
        }
    }

    /// 创建带初始容量的内存存储
    pub fn with_capacity(capacity: usize) -> Self {
        Self {
            storage: Arc::new(RwLock::new(HashMap::with_capacity(capacity))),
            stats: Arc::new(RwLock::new(StorageStats {
                total_keys: 0,
                total_size_bytes: 0,
                average_size_bytes: 0.0,
                last_updated: Utc::now(),
            })),
        }
    }

    /// 更新统计信息
    async fn update_stats(&self) -> SigmaXResult<()> {
        let storage = self.storage.read().await;
        let mut stats = self.stats.write().await;
        
        stats.total_keys = storage.len();
        stats.total_size_bytes = storage.values()
            .map(|entry| entry.data.len())
            .sum();
        
        stats.average_size_bytes = if stats.total_keys > 0 {
            stats.total_size_bytes as f64 / stats.total_keys as f64
        } else {
            0.0
        };
        
        stats.last_updated = Utc::now();
        
        Ok(())
    }

    /// 验证键的有效性
    fn validate_key(&self, key: &str) -> SigmaXResult<()> {
        if key.is_empty() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::Required,
                "key",
                "Storage key cannot be empty"
            ));
        }
        
        if key.len() > 255 {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidLength,
                "key",
                format!("Storage key too long: {} characters (max 255)", key.len())
            ));
        }
        
        Ok(())
    }
}

impl Storage for MemoryStorage {
    async fn save(&self, key: &str, data: &[u8]) -> SigmaXResult<()> {
        self.validate_key(key)?;
        
        let mut storage = self.storage.write().await;
        
        if let Some(entry) = storage.get_mut(key) {
            // 更新现有条目
            entry.update_data(data.to_vec());
            tracing::debug!("📝 Memory存储更新: {} ({} bytes)", key, data.len());
        } else {
            // 创建新条目
            let entry = StorageEntry::new(data.to_vec());
            storage.insert(key.to_string(), entry);
            tracing::debug!("💾 Memory存储保存: {} ({} bytes)", key, data.len());
        }
        
        // 释放锁后更新统计信息
        drop(storage);
        self.update_stats().await?;
        
        Ok(())
    }
    
    async fn get(&self, key: &str) -> SigmaXResult<Option<Vec<u8>>> {
        self.validate_key(key)?;
        
        let mut storage = self.storage.write().await;
        
        if let Some(entry) = storage.get_mut(key) {
            let data = entry.access().clone();
            tracing::debug!("📖 Memory存储读取: {} ({} bytes)", key, data.len());
            Ok(Some(data))
        } else {
            tracing::debug!("❓ Memory存储未找到: {}", key);
            Ok(None)
        }
    }
    
    async fn delete(&self, key: &str) -> SigmaXResult<()> {
        self.validate_key(key)?;
        
        let mut storage = self.storage.write().await;
        
        if storage.remove(key).is_some() {
            tracing::debug!("🗑️ Memory存储删除: {}", key);
            
            // 释放锁后更新统计信息
            drop(storage);
            self.update_stats().await?;
            
            Ok(())
        } else {
            Err(SigmaXError::database(
                DatabaseErrorCode::NotFound,
                format!("Storage key not found: {}", key)
            ))
        }
    }
    
    async fn exists(&self, key: &str) -> SigmaXResult<bool> {
        self.validate_key(key)?;
        
        let storage = self.storage.read().await;
        Ok(storage.contains_key(key))
    }
    
    async fn keys(&self) -> SigmaXResult<Vec<String>> {
        let storage = self.storage.read().await;
        let keys: Vec<String> = storage.keys().cloned().collect();
        
        tracing::debug!("🔑 Memory存储获取所有键: {} 个", keys.len());
        Ok(keys)
    }
    
    async fn clear(&self) -> SigmaXResult<()> {
        let mut storage = self.storage.write().await;
        let count = storage.len();
        storage.clear();
        
        tracing::info!("🧹 Memory存储清空: {} 个条目", count);
        
        // 释放锁后更新统计信息
        drop(storage);
        self.update_stats().await?;
        
        Ok(())
    }
    
    async fn stats(&self) -> SigmaXResult<StorageStats> {
        self.update_stats().await?;
        let stats = self.stats.read().await;
        Ok(stats.clone())
    }
}

impl Default for MemoryStorage {
    fn default() -> Self {
        Self::new()
    }
}
