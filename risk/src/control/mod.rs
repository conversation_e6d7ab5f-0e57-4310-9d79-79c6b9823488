//! 风控控制子模块
//!
//! 专注实时风控业务逻辑，包括：
//! - 订单验证服务
//! - 持仓监控服务
//! - 风险计算服务
//! - 预警管理服务
//! - 风控策略
//! - 风控模型

// ============================================================================
// 服务层 - 核心业务逻辑
// ============================================================================

/// 风控服务层
pub mod services;

// ============================================================================
// 策略层 - 风控策略实现
// ============================================================================

/// 风控策略层
pub mod policies;

// ============================================================================
// 模型层 - 数据模型定义
// ============================================================================

/// 风控模型层
pub mod models;

// ============================================================================
// 公共导出
// ============================================================================

// 服务层导出
pub use services::{
    OrderValidator, OrderValidationResult, OrderValidationError, OrderValidatorConfig,
    PositionMonitor, PositionStatus, PositionRiskLevel, PositionMonitorError, PositionMonitorConfig, PositionSummary,
    RiskCalculator, RiskCalculationResult, RiskCalculationError, RiskCalculatorConfig,
    AlertManager, AlertResult, AlertManagerError, AlertManagerConfig, AlertStatistics,
};

// 策略层导出
pub use policies::{
    BasicRules, BasicRuleConfig,
    PositionLimits, PositionLimitConfig,
    VolatilityControl, VolatilityConfig,
};

// 模型层导出
pub use models::{
    RiskContextBuilder,
    RiskMetrics, RiskMetricsBuilder,
};

// ============================================================================
// 风控控制器实现
// ============================================================================

use sigmax_interfaces::{
    RiskController,
    RiskContext as InterfaceRiskContext,
    Portfolio, RiskResult,
    risk::{RiskAlert}
};
use sigmax_core::{Order, SigmaXResult, Balance};
use async_trait::async_trait;
use models::RiskContext;


/// 风控控制器实现
pub struct RiskControllerImpl {
    order_validator: OrderValidator,
    position_monitor: PositionMonitor,
    risk_calculator: RiskCalculator,
    alert_manager: AlertManager,
}

impl RiskControllerImpl {
    /// 创建新的风控控制器
    pub fn new(
        order_validator: OrderValidator,
        position_monitor: PositionMonitor,
        risk_calculator: RiskCalculator,
        alert_manager: AlertManager,
    ) -> Self {
        Self {
            order_validator,
            position_monitor,
            risk_calculator,
            alert_manager,
        }
    }
    
    /// 验证订单
    pub async fn validate_order(&self, order: &Order) -> SigmaXResult<OrderValidationResult> {
        // 创建风控上下文
        let context = RiskContext::new().with_order(order.clone());

        // 执行订单验证
        let interface_context = self.convert_to_interface_context(&context);
        let result = self.order_validator.validate_order(order, &interface_context).await;
        Ok(result)
    }

    /// 检查持仓状态
    pub async fn check_position_status(&self, portfolio: &sigmax_interfaces::risk::Portfolio) -> SigmaXResult<PositionStatus> {
        let result = self.position_monitor.check_position_status(portfolio).await;
        Ok(result)
    }

    /// 计算风险指标
    pub async fn calculate_risk(&self, context: &RiskContext) -> SigmaXResult<RiskCalculationResult> {
        let interface_context = self.convert_to_interface_context(context);
        let result = self.risk_calculator.calculate_basic_metrics(&interface_context).await;
        Ok(result)
    }

    /// 检查并生成预警
    pub async fn check_alerts(&mut self, context: &RiskContext) -> SigmaXResult<AlertResult> {
        let interface_context = self.convert_to_interface_context(context);
        let result = self.alert_manager.check_and_alert(&interface_context).await;
        Ok(result)
    }
}

#[async_trait]
impl RiskController for RiskControllerImpl {
    async fn validate_order(&self, order: &Order) -> RiskResult {
        let context = RiskContext::new().with_order(order.clone());

        let interface_context = self.convert_to_interface_context(&context);
        match self.order_validator.validate_order(order, &interface_context).await {
            result if result.is_valid => RiskResult::Approved,
            result => RiskResult::Rejected(result.message),
        }
    }

    async fn check_position_limits(&self, portfolio: &sigmax_interfaces::risk::Portfolio) -> RiskResult {
        match self.position_monitor.check_position_status(portfolio).await {
            status if matches!(status.level, PositionRiskLevel::Safe | PositionRiskLevel::Warning) => {
                RiskResult::Approved
            },
            status if matches!(status.level, PositionRiskLevel::Dangerous) => {
                RiskResult::RequiresApproval(status.message)
            },
            status => RiskResult::Rejected(status.message),
        }
    }

    async fn calculate_risk_metrics(&self, context: &InterfaceRiskContext) -> InterfaceRiskMetrics {
        // 转换接口上下文到内部上下文
        let internal_context = self.convert_interface_context(context);

        // 计算风险指标
        let interface_context = self.convert_to_interface_context(&internal_context);
        let calculation_result = self.risk_calculator.calculate_basic_metrics(&interface_context).await;

        // 转换结果到接口格式
        self.convert_to_interface_metrics(&calculation_result)
    }

    async fn monitor_risk(&self, context: &InterfaceRiskContext) -> Vec<RiskAlert> {
        let internal_context = self.convert_interface_context(context);

        // 这里需要一个可变引用，但接口不允许，所以我们创建一个临时的 AlertManager
        let mut temp_alert_manager = AlertManager::with_default_config();
        let interface_context = self.convert_to_interface_context(&internal_context);
        let alert_result = temp_alert_manager.check_and_alert(&interface_context).await;

        alert_result.alerts
    }

    // ========== 来自 RiskEngine 的方法实现 ==========

    /// 核心风控决策 - 返回详细结果
    async fn check_order_risk(&self, order: &Order, context: Option<&InterfaceRiskContext>) -> sigmax_core::SigmaXResult<sigmax_core::RiskCheckResult> {
        // 调用trait方法，返回RiskResult
        let risk_result = RiskController::validate_order(self, order).await;

        let check_result = if risk_result.is_approved() {
            sigmax_core::RiskCheckResult::pass()
        } else {
            let reason = match risk_result {
                RiskResult::Rejected(msg) => msg,
                RiskResult::RequiresApproval(msg) => format!("需要审批: {}", msg),
                RiskResult::Error(msg) => format!("检查错误: {}", msg),
                _ => "未知错误".to_string(),
            };
            sigmax_core::RiskCheckResult::fail(reason)
        };

        Ok(check_result)
    }

    /// 持仓风控检查 - 返回详细结果
    async fn check_position_risk(&self, balances: &[Balance], context: Option<&InterfaceRiskContext>) -> sigmax_core::SigmaXResult<sigmax_core::RiskCheckResult> {
        // 将Balance转换为Portfolio
        let portfolio_balances: Vec<PortfolioBalance> = balances.iter()
            .map(|b| PortfolioBalance::from(b.clone()))
            .collect();

        let portfolio = Portfolio {
            id: None,
            name: Some("临时投资组合".to_string()),
            balances: portfolio_balances,
            total_value: balances.iter().map(|b| b.total()).sum(),
            cash_balance: None,
            positions: None,
            currency: Some("USDT".to_string()),
            created_at: None,
            updated_at: chrono::Utc::now(),
        };

        let risk_result = self.check_position_limits(&portfolio).await;

        let check_result = if risk_result.is_approved() {
            sigmax_core::RiskCheckResult::pass()
        } else {
            let reason = match risk_result {
                RiskResult::Rejected(msg) => msg,
                RiskResult::RequiresApproval(msg) => format!("需要审批: {}", msg),
                RiskResult::Error(msg) => format!("检查错误: {}", msg),
                _ => "未知错误".to_string(),
            };
            sigmax_core::RiskCheckResult::fail(reason)
        };

        Ok(check_result)
    }

    /// 规则管理
    async fn reload_rules(&self) -> sigmax_core::SigmaXResult<()> {
        // TODO: 实现规则重新加载逻辑
        // 这里应该从数据库重新加载风控规则
        log::info!("风控规则重新加载完成");
        Ok(())
    }

    /// 风险指标获取
    async fn get_risk_metrics(&self) -> sigmax_core::SigmaXResult<sigmax_core::RiskMetrics> {
        // 创建一个默认的风险上下文
        let context = InterfaceRiskContext {
            order: None,
            portfolio: Some(Portfolio {
                id: None,
                name: Some("默认投资组合".to_string()),
                balances: vec![],
                portfolio_balances: None,
                total_value: rust_decimal::Decimal::ZERO,
                cash_balance: None,
                positions: None,
                currency: Some("USDT".to_string()),
                created_at: None,
                updated_at: chrono::Utc::now(),
            }),
            balances: vec![],
            strategy_type: None,
            strategy_id: None,
            trading_pair: None,
            market_data: None,
            user_id: None,
            session_id: None,
            timestamp: chrono::Utc::now(),
            metadata: std::collections::HashMap::new(),
        };

        let _risk_metrics = self.calculate_risk_metrics(&context).await;

        // 转换为core的RiskMetrics (使用正确的字段结构)
        let core_metrics = sigmax_core::RiskMetrics {
            exposure: rust_decimal::Decimal::ZERO,
            leverage: rust_decimal::Decimal::ONE,
            var: Some(rust_decimal::Decimal::ZERO),
            max_drawdown: Some(rust_decimal::Decimal::ZERO),
            sharpe_ratio: Some(rust_decimal::Decimal::ZERO),
            calculated_at: chrono::Utc::now(),
        };

        Ok(core_metrics)
    }

    // ========== 来自 RiskManager 的方法实现 ==========

    /// 简化的风控检查
    async fn check_order_risk_simple(&self, order: &Order) -> sigmax_core::SigmaXResult<bool> {
        let result = RiskController::validate_order(self, order).await;
        Ok(result.is_approved())
    }

    /// 简化的持仓检查
    async fn check_position_risk_simple(&self, balances: &[Balance]) -> sigmax_core::SigmaXResult<bool> {
        let result = self.check_position_risk(balances, None).await?;
        Ok(result.failed_rules.is_empty())
    }

    /// 业务辅助方法
    async fn get_max_order_size(&self, _trading_pair: &sigmax_core::TradingPair, _context: Option<&InterfaceRiskContext>) -> sigmax_core::SigmaXResult<sigmax_core::Quantity> {
        // TODO: 实现基于风控规则的最大订单大小计算
        // 这里应该根据当前持仓、风控规则等计算最大允许的订单大小

        // 临时实现：返回一个保守的默认值
        let default_max_size = rust_decimal::Decimal::from(1000); // 默认最大1000单位
        Ok(default_max_size)
    }

    // ========== 新增的批量和性能优化方法 ==========

    /// 批量订单风控检查
    async fn batch_check_orders(&self, orders: &[Order], context: Option<&InterfaceRiskContext>) -> sigmax_core::SigmaXResult<Vec<sigmax_core::RiskCheckResult>> {
        let mut results = Vec::new();
        
        for order in orders {
            let result = self.check_order_risk(order, context).await?;
            results.push(result);
        }
        
        Ok(results)
    }

    /// 并行批量风控检查 - 性能优化版本
    async fn parallel_check_orders(&self, orders: &[Order], context: Option<&InterfaceRiskContext>) -> sigmax_core::SigmaXResult<sigmax_interfaces::BatchRiskResult> {
        use tokio::time::Instant;
        let start_time = Instant::now();
        
        // 简化实现：暂时使用串行处理
        let results = self.batch_check_orders(orders, context).await?;
        
        let processing_time = start_time.elapsed().as_millis() as u64;
        let success_count = results.iter().filter(|r| r.passed).count();
        let failure_count = results.len() - success_count;
        
        let batch_result = sigmax_interfaces::BatchRiskResult {
            results,
            success_count,
            failure_count,
            total_processing_time_ms: processing_time,
            avg_processing_time_ms: if orders.is_empty() { 0.0 } else { processing_time as f64 / orders.len() as f64 },
            parallelism: 1, // 目前为串行处理
            processed_at: chrono::Utc::now(),
        };
        
        Ok(batch_result)
    }

    /// 预检查订单（快速模式）
    async fn pre_check_order(&self, order: &Order, _context: Option<&InterfaceRiskContext>) -> sigmax_core::SigmaXResult<bool> {
        // 快速检查：只检查基本的数量和价格限制
        if order.quantity().value() <= rust_decimal::Decimal::ZERO {
            return Ok(false);
        }
        
        // TODO: 添加更多快速检查规则
        Ok(true)
    }

    /// 缓存风险计算结果
    async fn cache_risk_calculation(&self, _cache_key: &str, _context: &InterfaceRiskContext, _ttl_seconds: Option<u64>) -> sigmax_core::SigmaXResult<()> {
        // TODO: 实现缓存功能
        // 当前为占位实现
        Ok(())
    }

    /// 获取缓存的风险计算结果
    async fn get_cached_risk_calculation(&self, _cache_key: &str) -> sigmax_core::SigmaXResult<Option<sigmax_core::RiskMetrics>> {
        // TODO: 实现缓存查询功能
        // 当前返回 None，表示没有缓存
        Ok(None)
    }
}

impl RiskControllerImpl {
    /// 转换接口上下文到内部上下文
    fn convert_interface_context(&self, context: &InterfaceRiskContext) -> RiskContext {
        RiskContext {
            order: context.order.clone(),
            portfolio: context.portfolio.clone(),
            balances: context.balances.clone(),
            market_data: context.market_data.clone(),
            timestamp: context.timestamp,
            metadata: context.metadata.clone(),
        }
    }
    
    /// 转换内部上下文到接口上下文
    fn convert_to_interface_context(&self, context: &RiskContext) -> InterfaceRiskContext {
        InterfaceRiskContext {
            order: context.order.clone(),
            portfolio: context.portfolio.clone(),
            balances: context.balances.clone(),
            market_data: context.market_data.clone(),
            timestamp: context.timestamp,
            metadata: context.metadata.clone(),
        }
    }

    /// 转换计算结果到接口格式
    fn convert_to_interface_metrics(&self, result: &RiskCalculationResult) -> InterfaceRiskMetrics {
        // 从计算结果中提取基础指标
        let _volatility = result.metrics.get("volatility")
            .and_then(|v| serde_json::from_value::<rust_decimal::Decimal>(v.clone()).ok())
            .unwrap_or_default();

        let leverage = result.metrics.get("leverage")
            .and_then(|v| serde_json::from_value::<rust_decimal::Decimal>(v.clone()).ok())
            .unwrap_or_default();

        let _concentration = result.metrics.get("concentration")
            .and_then(|v| serde_json::from_value::<rust_decimal::Decimal>(v.clone()).ok())
            .unwrap_or_default();

        sigmax_interfaces::risk::RiskMetrics {
            exposure: result.metrics.get("exposure")
                .and_then(|v| serde_json::from_value::<rust_decimal::Decimal>(v.clone()).ok())
                .unwrap_or_default(),
            leverage,
            var: result.metrics.get("var")
                .and_then(|v| serde_json::from_value::<rust_decimal::Decimal>(v.clone()).ok()),
            max_drawdown: result.metrics.get("max_drawdown")
                .and_then(|v| serde_json::from_value::<rust_decimal::Decimal>(v.clone()).ok()),
            sharpe_ratio: result.metrics.get("sharpe_ratio")
                .and_then(|v| serde_json::from_value::<rust_decimal::Decimal>(v.clone()).ok()),
            calculated_at: result.calculated_at,
        }
    }
}