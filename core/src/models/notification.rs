//! SigmaX Core Notification Models
//!
//! ## 主要职责
//!
//! 1. **通知模型** - 核心通知概念
//!    - `Notification` - 统一的通知模型，管理系统通知和告警
//!    - 支持多种通知类型（风险告警、系统通知、交易通知）
//!
//! 2. **通知模板模型** - 通知模板管理
//!    - `NotificationTemplate` - 通知模板定义和管理
//!    - 支持动态内容替换和多语言模板
//!
//! 3. **通知业务逻辑** - 实用的通知管理方法
//!    - 通知创建、发送、状态管理
//!    - 模板渲染和内容生成
//!    - 通知历史和统计分析
//!
//! ## 设计原则
//!
//! - **统一模型**: 使用单一Notification模型，支持多种通知类型
//! - **模板化**: 支持通知模板，提高通知内容的一致性和可维护性
//! - **类型安全**: 使用强类型的ID、枚举等类型
//! - **业务友好**: 提供实用的通知管理和分析方法

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use validator::{Validate, ValidationError};
use rust_decimal::Decimal;
use std::collections::HashMap;

use crate::{
    types::*,
    enums::*,
    error::*,
    SigmaXResult,
};

// ============================================================================
// 通知模型 - 核心通知概念
// ============================================================================

/// 通知 - 统一的通知模型
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Notification {
    pub notification_id: NotificationId,
    
    pub notification_type: NotificationType,
    pub priority: NotificationPriority,
    pub status: NotificationStatus,
    
    #[validate(length(min = 1, message = "Title cannot be empty"))]
    pub title: String,
    
    #[validate(length(min = 1, message = "Content cannot be empty"))]
    pub content: String,
    
    pub recipient: String,
    pub channel: NotificationChannel,
    
    // 关联信息
    pub related_entity_type: Option<String>,
    pub related_entity_id: Option<String>,
    
    // 发送信息
    pub retry_count: u32,
    pub max_retries: u32,
    pub next_retry_at: Option<DateTime<Utc>>,
    
    // 模板信息
    pub template_id: Option<NotificationTemplateId>,
    pub template_variables: serde_json::Value,
    
    pub created_at: DateTime<Utc>,
    pub sent_at: Option<DateTime<Utc>>,
    pub read_at: Option<DateTime<Utc>>,
    pub updated_at: DateTime<Utc>,
}

impl Notification {
    /// 创建新的通知
    pub fn new(
        notification_type: NotificationType,
        priority: NotificationPriority,
        title: String,
        content: String,
        recipient: String,
        channel: NotificationChannel,
    ) -> Self {
        Self {
            notification_id: NotificationId::new(),
            notification_type,
            priority,
            status: NotificationStatus::Pending,
            title,
            content,
            recipient,
            channel,
            related_entity_type: None,
            related_entity_id: None,
            retry_count: 0,
            max_retries: 3,
            next_retry_at: None,
            template_id: None,
            template_variables: serde_json::Value::Object(serde_json::Map::new()),
            created_at: Utc::now(),
            sent_at: None,
            read_at: None,
            updated_at: Utc::now(),
        }
    }

    /// 从模板创建通知
    pub fn from_template(
        template: &NotificationTemplate,
        recipient: String,
        channel: NotificationChannel,
        variables: HashMap<String, String>,
    ) -> SigmaXResult<Self> {
        let rendered_content = template.render(&variables)?;
        let rendered_title = template.render_title(&variables)?;

        Ok(Self {
            notification_id: NotificationId::new(),
            notification_type: template.notification_type,
            priority: template.default_priority,
            status: NotificationStatus::Pending,
            title: rendered_title,
            content: rendered_content,
            recipient,
            channel,
            related_entity_type: None,
            related_entity_id: None,
            retry_count: 0,
            max_retries: template.max_retries,
            next_retry_at: None,
            template_id: Some(template.template_id),
            template_variables: serde_json::to_value(variables).unwrap_or_default(),
            created_at: Utc::now(),
            sent_at: None,
            read_at: None,
            updated_at: Utc::now(),
        })
    }

    /// 标记为已发送
    pub fn mark_as_sent(&mut self) {
        self.status = NotificationStatus::Sent;
        self.sent_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// 标记为发送失败
    pub fn mark_as_failed(&mut self) {
        self.status = NotificationStatus::Failed;
        self.retry_count += 1;
        
        if self.retry_count < self.max_retries {
            // 计算下次重试时间（指数退避）
            let delay_minutes = 2_u64.pow(self.retry_count) * 5; // 5, 10, 20, 40 分钟
            self.next_retry_at = Some(Utc::now() + chrono::Duration::minutes(delay_minutes as i64));
            self.status = NotificationStatus::Pending;
        }
        
        self.updated_at = Utc::now();
    }

    /// 标记为已读
    pub fn mark_as_read(&mut self) {
        self.read_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// 取消通知
    pub fn cancel(&mut self) {
        self.status = NotificationStatus::Cancelled;
        self.updated_at = Utc::now();
    }

    /// 检查是否可以重试
    pub fn can_retry(&self) -> bool {
        matches!(self.status, NotificationStatus::Pending | NotificationStatus::Failed) 
            && self.retry_count < self.max_retries
    }

    /// 检查是否需要重试
    pub fn should_retry(&self) -> bool {
        self.can_retry() && 
        self.next_retry_at.map_or(true, |retry_time| Utc::now() >= retry_time)
    }

    /// 检查是否已发送
    pub fn is_sent(&self) -> bool {
        self.status == NotificationStatus::Sent
    }

    /// 检查是否已读
    pub fn is_read(&self) -> bool {
        self.read_at.is_some()
    }

    /// 设置关联实体
    pub fn set_related_entity(&mut self, entity_type: String, entity_id: String) {
        self.related_entity_type = Some(entity_type);
        self.related_entity_id = Some(entity_id);
        self.updated_at = Utc::now();
    }

    /// 获取优先级数值（用于排序）
    pub fn priority_value(&self) -> u8 {
        match self.priority {
            NotificationPriority::Critical => 1,
            NotificationPriority::High => 2,
            NotificationPriority::Medium => 3,
            NotificationPriority::Low => 4,
        }
    }

    /// 获取显示用的通知摘要
    pub fn summary(&self) -> String {
        format!(
            "{:?} - {} (状态: {:?}, 优先级: {:?})",
            self.notification_type,
            self.title,
            self.status,
            self.priority
        )
    }

    /// 执行完整的通知验证
    pub fn validate_complete(&self) -> SigmaXResult<()> {
        // 首先执行基础验证（通过validator derive宏）
        if let Err(validation_errors) = <Self as Validate>::validate(self) {
            return Err(SigmaXError::from(validation_errors));
        }

        // 跨字段验证：收件人不能为空
        if self.recipient.trim().is_empty() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::Required,
                "recipient",
                "Notification recipient cannot be empty"
            ));
        }

        // 跨字段验证：重试次数不能超过最大重试次数
        if self.retry_count > self.max_retries {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "retry_count",
                "Retry count cannot exceed max retries"
            ));
        }

        Ok(())
    }
}

// ============================================================================
// 通知模板模型 - 通知模板管理
// ============================================================================

/// 通知模板 - 通知模板定义和管理
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct NotificationTemplate {
    pub template_id: NotificationTemplateId,

    #[validate(length(min = 1, message = "Template name cannot be empty"))]
    pub name: String,

    pub description: Option<String>,
    pub notification_type: NotificationType,
    pub default_priority: NotificationPriority,

    #[validate(length(min = 1, message = "Title template cannot be empty"))]
    pub title_template: String,

    #[validate(length(min = 1, message = "Content template cannot be empty"))]
    pub content_template: String,

    pub supported_channels: Vec<NotificationChannel>,
    pub required_variables: Vec<String>,
    pub optional_variables: Vec<String>,

    pub max_retries: u32,
    pub is_active: bool,

    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl NotificationTemplate {
    /// 创建新的通知模板
    pub fn new(
        name: String,
        notification_type: NotificationType,
        title_template: String,
        content_template: String,
    ) -> Self {
        Self {
            template_id: NotificationTemplateId::new(),
            name,
            description: None,
            notification_type,
            default_priority: NotificationPriority::Medium,
            title_template,
            content_template,
            supported_channels: vec![NotificationChannel::Email, NotificationChannel::InApp],
            required_variables: Vec::new(),
            optional_variables: Vec::new(),
            max_retries: 3,
            is_active: true,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    /// 渲染标题
    pub fn render_title(&self, variables: &HashMap<String, String>) -> SigmaXResult<String> {
        self.render_template(&self.title_template, variables)
    }

    /// 渲染内容
    pub fn render(&self, variables: &HashMap<String, String>) -> SigmaXResult<String> {
        self.render_template(&self.content_template, variables)
    }

    /// 渲染模板
    fn render_template(&self, template: &str, variables: &HashMap<String, String>) -> SigmaXResult<String> {
        let mut result = template.to_string();

        // 检查必需变量
        for required_var in &self.required_variables {
            if !variables.contains_key(required_var) {
                return Err(SigmaXError::validation(
                    ValidationErrorCode::Required,
                    "template_variables",
                    &format!("Required variable '{}' is missing", required_var)
                ));
            }
        }

        // 替换变量
        for (key, value) in variables {
            let placeholder = format!("{{{{{}}}}}", key);
            result = result.replace(&placeholder, value);
        }

        // 检查是否还有未替换的必需变量
        for required_var in &self.required_variables {
            let placeholder = format!("{{{{{}}}}}", required_var);
            if result.contains(&placeholder) {
                return Err(SigmaXError::validation(
                    ValidationErrorCode::InvalidFormat,
                    "template_variables",
                    &format!("Variable '{}' was not properly replaced", required_var)
                ));
            }
        }

        Ok(result)
    }

    /// 添加必需变量
    pub fn add_required_variable(&mut self, variable: String) {
        if !self.required_variables.contains(&variable) {
            self.required_variables.push(variable);
            self.updated_at = Utc::now();
        }
    }

    /// 添加可选变量
    pub fn add_optional_variable(&mut self, variable: String) {
        if !self.optional_variables.contains(&variable) {
            self.optional_variables.push(variable);
            self.updated_at = Utc::now();
        }
    }

    /// 添加支持的通道
    pub fn add_supported_channel(&mut self, channel: NotificationChannel) {
        if !self.supported_channels.contains(&channel) {
            self.supported_channels.push(channel);
            self.updated_at = Utc::now();
        }
    }

    /// 检查是否支持指定通道
    pub fn supports_channel(&self, channel: &NotificationChannel) -> bool {
        self.supported_channels.contains(channel)
    }

    /// 激活模板
    pub fn activate(&mut self) {
        self.is_active = true;
        self.updated_at = Utc::now();
    }

    /// 停用模板
    pub fn deactivate(&mut self) {
        self.is_active = false;
        self.updated_at = Utc::now();
    }

    /// 验证变量映射
    pub fn validate_variables(&self, variables: &HashMap<String, String>) -> SigmaXResult<()> {
        // 检查必需变量
        for required_var in &self.required_variables {
            if !variables.contains_key(required_var) {
                return Err(SigmaXError::validation(
                    ValidationErrorCode::Required,
                    "template_variables",
                    &format!("Required variable '{}' is missing", required_var)
                ));
            }
        }

        Ok(())
    }

    /// 获取所有变量列表
    pub fn get_all_variables(&self) -> Vec<String> {
        let mut all_vars = self.required_variables.clone();
        all_vars.extend(self.optional_variables.clone());
        all_vars.sort();
        all_vars.dedup();
        all_vars
    }

    /// 执行完整的模板验证
    pub fn validate_complete(&self) -> SigmaXResult<()> {
        // 首先执行基础验证（通过validator derive宏）
        if let Err(validation_errors) = <Self as Validate>::validate(self) {
            return Err(SigmaXError::from(validation_errors));
        }

        // 跨字段验证：至少支持一个通道
        if self.supported_channels.is_empty() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::Required,
                "supported_channels",
                "Template must support at least one notification channel"
            ));
        }

        Ok(())
    }
}

// ============================================================================
// Tests
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    #[test]
    fn test_notification_creation() {
        let notification = Notification::new(
            NotificationType::RiskAlert,
            NotificationPriority::High,
            "Risk Alert".to_string(),
            "Position risk exceeded threshold".to_string(),
            "<EMAIL>".to_string(),
            NotificationChannel::Email,
        );

        assert_eq!(notification.notification_type, NotificationType::RiskAlert);
        assert_eq!(notification.priority, NotificationPriority::High);
        assert_eq!(notification.status, NotificationStatus::Pending);
        assert_eq!(notification.title, "Risk Alert");
        assert_eq!(notification.retry_count, 0);
        assert!(!notification.is_sent());
        assert!(!notification.is_read());
    }

    #[test]
    fn test_notification_lifecycle() {
        let mut notification = Notification::new(
            NotificationType::SystemAlert,
            NotificationPriority::Medium,
            "System Alert".to_string(),
            "System maintenance scheduled".to_string(),
            "<EMAIL>".to_string(),
            NotificationChannel::Email,
        );

        // 标记为已发送
        notification.mark_as_sent();
        assert!(notification.is_sent());
        assert!(notification.sent_at.is_some());

        // 标记为已读
        notification.mark_as_read();
        assert!(notification.is_read());
        assert!(notification.read_at.is_some());
    }

    #[test]
    fn test_notification_retry_logic() {
        let mut notification = Notification::new(
            NotificationType::TradingAlert,
            NotificationPriority::High,
            "Trading Alert".to_string(),
            "Order execution failed".to_string(),
            "<EMAIL>".to_string(),
            NotificationChannel::Email,
        );

        // 初始状态可以重试
        assert!(notification.can_retry());
        assert!(notification.should_retry());

        // 标记为失败
        notification.mark_as_failed();
        assert_eq!(notification.retry_count, 1);
        assert!(notification.next_retry_at.is_some());

        // 继续失败直到达到最大重试次数
        notification.mark_as_failed();
        notification.mark_as_failed();
        notification.mark_as_failed(); // 第4次失败，超过最大重试次数3

        assert_eq!(notification.status, NotificationStatus::Failed);
        assert!(!notification.can_retry());
    }

    #[test]
    fn test_notification_template_creation() {
        let template = NotificationTemplate::new(
            "Risk Alert Template".to_string(),
            NotificationType::RiskAlert,
            "Risk Alert: {{strategy_name}}".to_string(),
            "Strategy {{strategy_name}} has exceeded risk threshold. Current drawdown: {{drawdown}}%".to_string(),
        );

        assert_eq!(template.name, "Risk Alert Template");
        assert_eq!(template.notification_type, NotificationType::RiskAlert);
        assert!(template.is_active);
        assert_eq!(template.max_retries, 3);
    }

    #[test]
    fn test_template_rendering() {
        let mut template = NotificationTemplate::new(
            "Trading Alert Template".to_string(),
            NotificationType::TradingAlert,
            "Trading Alert: {{symbol}}".to_string(),
            "Order for {{symbol}} failed. Reason: {{reason}}".to_string(),
        );

        // 添加必需变量
        template.add_required_variable("symbol".to_string());
        template.add_required_variable("reason".to_string());

        let mut variables = HashMap::new();
        variables.insert("symbol".to_string(), "BTCUSDT".to_string());
        variables.insert("reason".to_string(), "Insufficient balance".to_string());

        // 渲染标题和内容
        let title = template.render_title(&variables).unwrap();
        let content = template.render(&variables).unwrap();

        assert_eq!(title, "Trading Alert: BTCUSDT");
        assert_eq!(content, "Order for BTCUSDT failed. Reason: Insufficient balance");
    }

    #[test]
    fn test_template_missing_variables() {
        let mut template = NotificationTemplate::new(
            "Test Template".to_string(),
            NotificationType::SystemAlert,
            "Alert: {{title}}".to_string(),
            "Message: {{message}}".to_string(),
        );

        template.add_required_variable("title".to_string());
        template.add_required_variable("message".to_string());

        let mut variables = HashMap::new();
        variables.insert("title".to_string(), "Test".to_string());
        // 缺少 message 变量

        let result = template.render(&variables);
        assert!(result.is_err());
    }

    #[test]
    fn test_notification_from_template() {
        let mut template = NotificationTemplate::new(
            "Welcome Template".to_string(),
            NotificationType::SystemNotification,
            "Welcome {{username}}!".to_string(),
            "Hello {{username}}, welcome to SigmaX trading platform!".to_string(),
        );

        template.add_required_variable("username".to_string());

        let mut variables = HashMap::new();
        variables.insert("username".to_string(), "Alice".to_string());

        let notification = Notification::from_template(
            &template,
            "<EMAIL>".to_string(),
            NotificationChannel::Email,
            variables,
        ).unwrap();

        assert_eq!(notification.title, "Welcome Alice!");
        assert_eq!(notification.content, "Hello Alice, welcome to SigmaX trading platform!");
        assert_eq!(notification.template_id, Some(template.template_id));
    }

    #[test]
    fn test_notification_priority_value() {
        let critical = Notification::new(
            NotificationType::SystemAlert,
            NotificationPriority::Critical,
            "Critical".to_string(),
            "Critical alert".to_string(),
            "<EMAIL>".to_string(),
            NotificationChannel::Email,
        );

        let low = Notification::new(
            NotificationType::SystemNotification,
            NotificationPriority::Low,
            "Info".to_string(),
            "Info message".to_string(),
            "<EMAIL>".to_string(),
            NotificationChannel::InApp,
        );

        assert_eq!(critical.priority_value(), 1);
        assert_eq!(low.priority_value(), 4);
        assert!(critical.priority_value() < low.priority_value());
    }

    #[test]
    fn test_notification_validation() {
        let notification = Notification::new(
            NotificationType::RiskAlert,
            NotificationPriority::High,
            "Test Alert".to_string(),
            "Test content".to_string(),
            "<EMAIL>".to_string(),
            NotificationChannel::Email,
        );

        // 正常通知应该通过验证
        let result = notification.validate_complete();
        assert!(result.is_ok());
    }

    #[test]
    fn test_template_validation() {
        let template = NotificationTemplate::new(
            "Test Template".to_string(),
            NotificationType::SystemAlert,
            "Test Title".to_string(),
            "Test Content".to_string(),
        );

        // 正常模板应该通过验证
        let result = template.validate_complete();
        assert!(result.is_ok());
    }
}
