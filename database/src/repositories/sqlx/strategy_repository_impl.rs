//! 策略Repository实现
//!
//! 基于SQLx的标准策略Repository实现

use std::sync::Arc;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use sqlx::{Row, QueryBuilder, Postgres};
use uuid::Uuid;

use sigmax_core::{
    StrategyId, StrategyState, StrategyInfo, StrategyStatus, TradingPair,
    SigmaXResult, SigmaXError, DatabaseErrorCode
};

use crate::{
    DatabaseManager,
    repositories::traits::{
        Repository, StrategyRepository,
        QueryFilter, PaginationResult, SortConfig, SortDirection,
        StrategyQueryFilter, StrategyStatistics
    }
};

/// 策略Repository实现
pub struct StrategyRepositoryImpl {
    db: Arc<DatabaseManager>,
}

impl StrategyRepositoryImpl {
    /// 创建新的策略Repository实例
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }
    
    /// 获取数据库连接池
    fn pool(&self) -> &sqlx::PgPool {
        self.db.pool()
    }
    
    /// 将数据库行转换为StrategyState对象
    fn row_to_strategy_state(&self, row: &sqlx::postgres::PgRow) -> SigmaXResult<StrategyState> {
        // 简化实现，实际项目中需要根据具体的StrategyState结构来实现
        Ok(StrategyState {
            strategy_id: row.get("id"),
            name: row.get("name"),
            strategy_type: row.get("strategy_type"),
            description: row.get("description"),
            status: row.get::<String, _>("status").parse().unwrap_or(StrategyStatus::Created),
            config: row.get::<serde_json::Value, _>("config"),
            state_data: row.get::<serde_json::Value, _>("state_data"),
            trading_pair: None, // 需要通过JOIN获取
            portfolio_id: row.get("portfolio_id"),
            risk_config: row.get::<serde_json::Value, _>("risk_config"),
            performance_metrics: row.get::<serde_json::Value, _>("performance_metrics"),
            created_by: row.get("created_by"),
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
            started_at: row.get("started_at"),
            stopped_at: row.get("stopped_at"),
        })
    }
    
    /// 将数据库行转换为StrategyInfo对象
    fn row_to_strategy_info(&self, row: &sqlx::postgres::PgRow) -> SigmaXResult<StrategyInfo> {
        Ok(StrategyInfo {
            id: row.get("id"),
            name: row.get("name"),
            strategy_type: row.get("strategy_type"),
            description: row.get("description"),
            status: row.get::<String, _>("status").parse().unwrap_or(StrategyStatus::Created),
            trading_pair: TradingPair::new("BTC".to_string(), "USDT".to_string()), // 默认值，需要通过JOIN获取
            portfolio_id: row.get("portfolio_id"),
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
        })
    }
}

#[async_trait]
impl Repository<StrategyState, StrategyId> for StrategyRepositoryImpl {
    async fn save(&self, entity: &StrategyState) -> SigmaXResult<StrategyId> {
        let query = r#"
            INSERT INTO strategies (
                id, name, status, config, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6)
            ON CONFLICT (id) DO UPDATE SET
                name = EXCLUDED.name,
                status = EXCLUDED.status,
                config = EXCLUDED.config,
                updated_at = EXCLUDED.updated_at
        "#;
        
        sqlx::query(query)
            .bind(entity.strategy_id)
            .bind(&entity.name)
            .bind(entity.status.to_string())
            .bind(serde_json::Value::Null) // config placeholder
            .bind(entity.created_at)
            .bind(entity.updated_at)
            .execute(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::InsertFailed,
                format!("Failed to save strategy: {}", e)
            ))?;
        
        Ok(entity.strategy_id)
    }
    
    async fn find_by_id(&self, id: StrategyId) -> SigmaXResult<Option<StrategyState>> {
        let query = r#"
            SELECT id, name, status, created_at, updated_at
            FROM strategies
            WHERE id = $1
        "#;
        
        let row = sqlx::query(query)
            .bind(id)
            .fetch_optional(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find strategy by ID: {}", e)
            ))?;
        
        match row {
            Some(row) => Ok(Some(self.row_to_strategy_state(&row)?)),
            None => Ok(None),
        }
    }
    
    async fn find_all(&self) -> SigmaXResult<Vec<StrategyState>> {
        let query = r#"
            SELECT id, name, status, created_at, updated_at
            FROM strategies
            ORDER BY created_at DESC
        "#;
        
        let rows = sqlx::query(query)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find all strategies: {}", e)
            ))?;
        
        let mut strategies = Vec::new();
        for row in rows {
            strategies.push(self.row_to_strategy_state(&row)?);
        }
        
        Ok(strategies)
    }
    
    async fn update(&self, entity: &StrategyState) -> SigmaXResult<()> {
        // 对于策略，update和save是相同的操作（使用UPSERT）
        self.save(entity).await?;
        Ok(())
    }
    
    async fn delete(&self, id: StrategyId) -> SigmaXResult<()> {
        let query = "DELETE FROM strategies WHERE id = $1";
        
        let result = sqlx::query(query)
            .bind(id)
            .execute(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::DeleteFailed,
                format!("Failed to delete strategy: {}", e)
            ))?;
        
        if result.rows_affected() == 0 {
            return Err(SigmaXError::database(
                DatabaseErrorCode::NotFound,
                format!("Strategy with ID {} not found", id)
            ));
        }
        
        Ok(())
    }
    
    async fn exists(&self, id: StrategyId) -> SigmaXResult<bool> {
        let query = "SELECT EXISTS(SELECT 1 FROM strategies WHERE id = $1)";
        
        let exists: bool = sqlx::query_scalar(query)
            .bind(id)
            .fetch_one(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to check strategy existence: {}", e)
            ))?;
        
        Ok(exists)
    }
    
    async fn count(&self) -> SigmaXResult<u64> {
        let query = "SELECT COUNT(*) FROM strategies";
        
        let count: i64 = sqlx::query_scalar(query)
            .fetch_one(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to count strategies: {}", e)
            ))?;
        
        Ok(count as u64)
    }

    async fn save_batch(&self, entities: &[StrategyState]) -> SigmaXResult<Vec<StrategyId>> {
        let mut ids = Vec::new();
        for entity in entities {
            let id = self.save(entity).await?;
            ids.push(id);
        }
        Ok(ids)
    }

    async fn delete_batch(&self, ids: &[StrategyId]) -> SigmaXResult<()> {
        for id in ids {
            self.delete(*id).await?;
        }
        Ok(())
    }

    async fn find_with_pagination(&self, offset: u64, limit: u64) -> SigmaXResult<PaginationResult<StrategyState>> {
        let query = r#"
            SELECT s.id, s.name, s.strategy_type, s.description, s.config, s.state_data,
                   s.status, s.portfolio_id, s.risk_config, s.performance_metrics,
                   s.created_by, s.created_at, s.updated_at, s.started_at, s.stopped_at
            FROM strategies s
            ORDER BY s.created_at DESC
            LIMIT $1 OFFSET $2
        "#;

        let rows = sqlx::query(query)
            .bind(limit as i64)
            .bind(offset as i64)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find strategies with pagination: {}", e)
            ))?;

        let strategies: Result<Vec<StrategyState>, _> = rows.iter().map(|row| self.row_to_strategy_state(row)).collect();
        let strategies = strategies?;

        // 获取总数
        let count_query = "SELECT COUNT(*) FROM strategies";
        let total: i64 = sqlx::query_scalar(count_query)
            .fetch_one(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to count strategies: {}", e)
            ))?;

        Ok(PaginationResult::new(
            strategies,
            total as u64,
            offset,
            limit,
        ))
    }
}

#[async_trait]
impl StrategyRepository for StrategyRepositoryImpl {
    async fn find_by_status(&self, status: StrategyStatus) -> SigmaXResult<Vec<StrategyInfo>> {
        let query = r#"
            SELECT id, name, status, created_at, updated_at
            FROM strategies
            WHERE status = $1
            ORDER BY created_at DESC
        "#;

        let rows = sqlx::query(query)
            .bind(status.to_string())
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find strategies by status: {}", e)
            ))?;

        let mut strategies = Vec::new();
        for row in rows {
            strategies.push(self.row_to_strategy_info(&row)?);
        }

        Ok(strategies)
    }

    async fn get_all_strategies(&self) -> SigmaXResult<Vec<StrategyInfo>> {
        let query = r#"
            SELECT id, name, status, created_at, updated_at
            FROM strategies
            ORDER BY created_at DESC
        "#;

        let rows = sqlx::query(query)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to get all strategies: {}", e)
            ))?;

        let mut strategies = Vec::new();
        for row in rows {
            strategies.push(self.row_to_strategy_info(&row)?);
        }

        Ok(strategies)
    }

    async fn save_strategy_state(&self, strategy_id: StrategyId, state: &StrategyState) -> SigmaXResult<()> {
        self.save(state).await?;
        Ok(())
    }

    async fn load_strategy_state(&self, strategy_id: StrategyId) -> SigmaXResult<Option<StrategyState>> {
        self.find_by_id(strategy_id).await
    }

    async fn find_by_filter(&self, filter: &StrategyQueryFilter) -> SigmaXResult<Vec<StrategyInfo>> {
        // 验证过滤器
        filter.validate()?;

        let mut query_builder = QueryBuilder::new(r#"
            SELECT id, name, status, created_at, updated_at
            FROM strategies
            WHERE 1=1
        "#);

        // 添加过滤条件
        if let Some(statuses) = &filter.statuses {
            if !statuses.is_empty() {
                query_builder.push(" AND status = ANY(");
                query_builder.push_bind(statuses.iter().map(|s| s.to_string()).collect::<Vec<_>>());
                query_builder.push(")");
            }
        }

        if let Some(strategy_ids) = &filter.strategy_ids {
            if !strategy_ids.is_empty() {
                query_builder.push(" AND id = ANY(");
                query_builder.push_bind(strategy_ids.clone());
                query_builder.push(")");
            }
        }

        if let Some(pattern) = &filter.name_pattern {
            query_builder.push(" AND name ILIKE ");
            query_builder.push_bind(format!("%{}%", pattern));
        }

        // 添加时间范围过滤
        if let Some((start, end)) = &filter.created_time_range {
            query_builder.push(" AND created_at BETWEEN ");
            query_builder.push_bind(start);
            query_builder.push(" AND ");
            query_builder.push_bind(end);
        }

        // 添加排序
        query_builder.push(" ORDER BY created_at DESC");

        let query = query_builder.build();
        let rows = query.fetch_all(self.pool()).await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find strategies by filter: {}", e)
            ))?;

        let mut strategies = Vec::new();
        for row in rows {
            strategies.push(self.row_to_strategy_info(&row)?);
        }

        Ok(strategies)
    }

    async fn get_statistics(&self, filter: Option<&StrategyQueryFilter>) -> SigmaXResult<StrategyStatistics> {
        let query = r#"
            SELECT
                COUNT(*) as total_count,
                COUNT(CASE WHEN status IN ('Running', 'Initializing') THEN 1 END) as active_count,
                COUNT(CASE WHEN status = 'Error' THEN 1 END) as error_count,
                MIN(created_at) as earliest_created,
                MAX(updated_at) as latest_updated
            FROM strategies
        "#;

        let row = sqlx::query(query)
            .fetch_one(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to get strategy statistics: {}", e)
            ))?;

        Ok(StrategyStatistics {
            total_count: row.get::<i64, _>("total_count") as u64,
            active_count: row.get::<i64, _>("active_count") as u64,
            error_count: row.get::<i64, _>("error_count") as u64,
            earliest_created: row.get("earliest_created"),
            latest_updated: row.get("latest_updated"),
            status_counts: Vec::new(), // 将在后续查询中填充
            average_runtime_seconds: 0.0, // 需要额外计算
        })
    }

    async fn find_with_pagination(&self, offset: u64, limit: u64) -> SigmaXResult<PaginationResult<StrategyInfo>> {
        let query = r#"
            SELECT id, name, status, created_at, updated_at
            FROM strategies
            ORDER BY created_at DESC
            LIMIT $1 OFFSET $2
        "#;

        let rows = sqlx::query(query)
            .bind(limit as i64)
            .bind(offset as i64)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find strategies with pagination: {}", e)
            ))?;

        let mut strategies = Vec::new();
        for row in rows {
            strategies.push(self.row_to_strategy_info(&row)?);
        }

        let total_count = self.count().await?;

        Ok(PaginationResult::new(
            strategies,
            total_count,
            offset,
            limit,
        ))
    }

    async fn count_by_status(&self) -> SigmaXResult<Vec<(StrategyStatus, u64)>> {
        let query = r#"
            SELECT status, COUNT(*) as count
            FROM strategies
            GROUP BY status
            ORDER BY count DESC
        "#;

        let rows = sqlx::query(query)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to count strategies by status: {}", e)
            ))?;

        let mut counts = Vec::new();
        for row in rows {
            let status = row.get::<String, _>("status").parse().unwrap_or(StrategyStatus::Created);
            let count = row.get::<i64, _>("count") as u64;
            counts.push((status, count));
        }

        Ok(counts)
    }
}
