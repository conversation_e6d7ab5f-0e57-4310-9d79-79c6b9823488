//! SigmaX Core Validation System
//!
//! ## 主要职责
//!
//! 1. **验证接口定义** - 统一的验证接口和trait定义
//!    - `Validatable` - 基础验证接口
//!    - `CustomValidator` - 自定义验证器接口
//!    - `ValidationService` - 业务验证服务接口
//!
//! 2. **验证规则实现** - 具体的业务验证规则
//!    - 订单验证规则
//!    - 风险管理验证规则
//!    - 配置验证规则
//!
//! 3. **验证器组合** - 复合验证器和验证链
//!    - 验证器组合
//!    - 验证结果聚合
//!    - 错误信息收集
//!
//! ## 设计原则
//!
//! - **统一接口**: 提供一致的验证接口
//! - **可组合性**: 支持验证器的组合和链式调用
//! - **类型安全**: 使用强类型确保验证的正确性
//! - **业务导向**: 专注于核心业务验证逻辑

pub mod validators;
pub mod rules;
pub mod examples;

#[cfg(test)]
mod tests;

// 重新导出主要类型
pub use validators::*;
pub use rules::*;

use async_trait::async_trait;
use validator::ValidationErrors;
use serde_json::Value;

use crate::{
    types::*,
    enums::*,
    error::*,
    SigmaXResult,
};

// ============================================================================
// 核心验证接口
// ============================================================================

/// 基础验证接口
/// 
/// 为所有可验证的对象提供统一的验证接口
pub trait Validatable {
    /// 执行验证
    fn validate(&self) -> Result<(), ValidationErrors>;
    
    /// 获取验证错误的友好描述
    fn validation_errors(&self) -> Vec<String> {
        match self.validate() {
            Ok(_) => Vec::new(),
            Err(errors) => {
                errors
                    .field_errors()
                    .iter()
                    .flat_map(|(field, errors)| {
                        errors.iter().map(move |error| {
                            format!("{}: {}", field, error.message.as_ref().unwrap_or(&"Invalid value".into()))
                        })
                    })
                    .collect()
            }
        }
    }
    
    /// 检查是否有效
    fn is_valid(&self) -> bool {
        self.validate().is_ok()
    }
}

/// 自定义验证器接口
/// 
/// 用于实现复杂的业务验证逻辑
#[async_trait]
pub trait CustomValidator<T>: Send + Sync {
    /// 执行验证
    async fn validate(&self, value: &T) -> SigmaXResult<()>;
    
    /// 获取验证器名称
    fn name(&self) -> &str;
    
    /// 获取验证器描述
    fn description(&self) -> Option<&str> {
        None
    }
    
    /// 检查是否启用
    fn is_enabled(&self) -> bool {
        true
    }
}

/// 业务验证服务接口
/// 
/// 提供高级的业务验证功能
#[async_trait]
pub trait ValidationService: Send + Sync {
    /// 验证策略配置
    async fn validate_strategy_config(&self, config: &Value) -> SigmaXResult<()>;
    
    /// 验证订单请求
    async fn validate_order_request(&self, request: &OrderRequest) -> SigmaXResult<()>;
    
    /// 验证风险限制
    async fn validate_risk_limits(&self, limits: &RiskLimits) -> SigmaXResult<()>;
    
    /// 验证投资组合配置
    async fn validate_portfolio_config(&self, config: &PortfolioConfig) -> SigmaXResult<()>;
    
    /// 验证交易对
    async fn validate_trading_pair(&self, pair: &TradingPair) -> SigmaXResult<()>;
    
    /// 验证价格范围
    async fn validate_price_range(&self, min_price: Price, max_price: Price) -> SigmaXResult<()>;
}

// ============================================================================
// 验证器组合和工具
// ============================================================================

/// 验证器组合器
/// 
/// 用于组合多个验证器
pub struct ValidatorComposer<T> {
    validators: Vec<Box<dyn CustomValidator<T>>>,
    fail_fast: bool,
}

impl<T> ValidatorComposer<T> {
    /// 创建新的验证器组合器
    pub fn new() -> Self {
        Self {
            validators: Vec::new(),
            fail_fast: true,
        }
    }
    
    /// 设置是否快速失败
    pub fn fail_fast(mut self, fail_fast: bool) -> Self {
        self.fail_fast = fail_fast;
        self
    }
    
    /// 添加验证器
    pub fn add_validator(mut self, validator: Box<dyn CustomValidator<T>>) -> Self {
        self.validators.push(validator);
        self
    }
    
    /// 执行所有验证器
    pub async fn validate_all(&self, value: &T) -> SigmaXResult<()> {
        let mut errors = Vec::new();
        
        for validator in &self.validators {
            if !validator.is_enabled() {
                continue;
            }
            
            match validator.validate(value).await {
                Ok(_) => continue,
                Err(error) => {
                    if self.fail_fast {
                        return Err(error);
                    }
                    errors.push(format!("{}: {}", validator.name(), error));
                }
            }
        }
        
        if !errors.is_empty() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "composite_validation",
                format!("Multiple validation errors: {}", errors.join("; "))
            ));
        }
        
        Ok(())
    }
    
    /// 获取验证器数量
    pub fn validator_count(&self) -> usize {
        self.validators.len()
    }
    
    /// 获取启用的验证器数量
    pub fn enabled_validator_count(&self) -> usize {
        self.validators.iter().filter(|v| v.is_enabled()).count()
    }
}

impl<T> Default for ValidatorComposer<T> {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// 验证工具函数
// ============================================================================

/// 验证工具集
pub struct ValidationUtils;

impl ValidationUtils {
    /// 验证字符串长度
    pub fn validate_string_length(
        value: &str,
        min_length: usize,
        max_length: usize,
        field_name: &str,
    ) -> SigmaXResult<()> {
        if value.len() < min_length {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidLength,
                field_name,
                format!("Value must be at least {} characters long", min_length)
            ));
        }

        if value.len() > max_length {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidLength,
                field_name,
                format!("Value must be at most {} characters long", max_length)
            ));
        }
        
        Ok(())
    }
    
    /// 验证数值范围
    pub fn validate_numeric_range<T>(
        value: T,
        min_value: T,
        max_value: T,
        field_name: &str,
    ) -> SigmaXResult<()>
    where
        T: PartialOrd + std::fmt::Display,
    {
        if value < min_value {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                field_name,
                format!("Value must be at least {}", min_value)
            ));
        }
        
        if value > max_value {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                field_name,
                format!("Value must be at most {}", max_value)
            ));
        }
        
        Ok(())
    }
    
    /// 验证必填字段
    pub fn validate_required<T>(value: &Option<T>, field_name: &str) -> SigmaXResult<()> {
        if value.is_none() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::Required,
                field_name,
                "Field is required"
            ));
        }
        Ok(())
    }
    
    /// 验证正数
    pub fn validate_positive_number<T>(value: T, field_name: &str) -> SigmaXResult<()>
    where
        T: PartialOrd + Default + std::fmt::Display,
    {
        if value <= T::default() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                field_name,
                "Value must be positive"
            ));
        }
        Ok(())
    }
    
    /// 验证百分比（0-100）
    pub fn validate_percentage(value: f64, field_name: &str) -> SigmaXResult<()> {
        Self::validate_numeric_range(value, 0.0, 100.0, field_name)
    }
    
    /// 验证邮箱格式
    pub fn validate_email(email: &str, field_name: &str) -> SigmaXResult<()> {
        if !email.contains('@') || !email.contains('.') {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                field_name,
                "Invalid email format"
            ));
        }
        Ok(())
    }
}

// ============================================================================
// 临时类型定义（这些应该在实际实现中从其他模块导入）
// ============================================================================

/// 订单请求（临时定义）
#[derive(Debug, Clone)]
pub struct OrderRequest {
    pub trading_pair: TradingPair,
    pub side: OrderSide,
    pub order_type: OrderType,
    pub quantity: Quantity,
    pub price: Option<Price>,
}

/// 风险限制（临时定义）
#[derive(Debug, Clone)]
pub struct RiskLimits {
    pub max_position_size: Amount,
    pub max_drawdown: Percentage,
    pub max_daily_loss: Amount,
}

/// 投资组合配置（临时定义）
#[derive(Debug, Clone)]
pub struct PortfolioConfig {
    pub name: String,
    pub initial_balance: Amount,
    pub risk_tolerance: f64,
}
