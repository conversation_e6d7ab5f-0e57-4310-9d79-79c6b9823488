Risk 模块过度复杂 🚨
当前结构：
.
问题：
子模块过多，职责重叠
control 和 analytics 边界模糊
unified_engine 的存在导致职责不清
优化方向：
合并 control 和 analytics 为统一的风险管理模块
移除 unified_engine，功能整合到主模块
简化为：assessment（评估）、policy（策略）、monitor（监控）三个核心子模块

cargo build

Mock实现和空实现

先搭建架构框架，再填充具体实现
先验证整体架构设计，在解决Database模块有编译错误


完成Database模块修复: 解决剩余的2个Decimal类型转换错误
集成真实Repository: 逐步替换Mock实现为真实数据库实现
性能测试: 验证5层架构的性能表现

移除配置驱动的Repository工厂，允许在Mock和真实实现之间切换的代码

实现与sigmax-database的真实连接

使用sigmax_core中实际存在的类型：
StrategyState 而不是 Strategy
Balance 而不是 Portfolio
Position 而不是 RiskMetrics
Order 保持不变

graph TB
    subgraph "最佳实践解决方案"
        S[简化的 Risk 模块架构]
        
        S --> S1[核心层 - Core]
        S1 --> S11[RiskEngine - 风控引擎]
        S1 --> S12[RiskPolicy - 策略接口]
        S1 --> S13[RiskMetrics - 指标计算]
        
        S --> S2[策略层 - Policies]
        S2 --> S21[OrderRiskPolicy - 订单风控]
        S2 --> S22[PositionRiskPolicy - 持仓风控]
        S2 --> S23[MarketRiskPolicy - 市场风控]
        
        S --> S3[监控层 - Monitor]
        S3 --> S31[RiskMonitor - 风险监控]
        S3 --> S32[AlertService - 预警服务]
        
        S --> S4[分析层 - Analysis]
        S4 --> S41[RiskAnalyzer - 风险分析]
        S4 --> S42[ReportGenerator - 报告生成]
    end
    
    style S fill:#9f9,stroke:#333,stroke-width:2px
    style S1 fill:#cfc,stroke:#333,stroke-width:1px
    style S2 fill:#cfc,stroke:#333,stroke-width:1px
    style S3 fill:#cfc,stroke:#333,stroke-width:1px
    style S4 fill:#cfc,stroke:#333,stroke-width:1px


graph TB
    subgraph "🏗️ SigmaX风控系统最佳架构设计"
        subgraph "API层 - 统一入口"
            A1[RiskManager]
            A2["check_order_risk()"]
            A3["check_position_risk()"]
            A4["get_risk_metrics()"]
        end
        
        subgraph "核心层 - 微内核"
            B1[RiskCore]
            B2["规则调度器"]
            B3["结果聚合器"]
            B4["性能监控器"]
            B5["缓存管理器"]
        end
        
        subgraph "规则层 - 可插拔"
            C1[OrderAmountRule]
            C2[DrawdownRule]
            C3[LeverageRule]
            C4[VolatilityRule]
            C5[CustomRule]
        end
        
        subgraph "数据层 - 统一访问"
            D1[RiskRepository]
            D2["批量查询优化"]
            D3["连接池管理"]
            D4["SQL缓存"]
        end
        
        subgraph "事件层 - 异步通知"
            E1[RiskEventBus]
            E2["风险预警事件"]
            E3["违规通知事件"]
            E4["性能指标事件"]
        end
        
        subgraph "存储层"
            F1[(PostgreSQL)]
            F2[(Redis Cache)]
            F3[(Metrics Store)]
        end
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4
    
    B2 --> C1
    B2 --> C2
    B2 --> C3
    B2 --> C4
    B2 --> C5
    
    B3 --> D1
    B4 --> D2
    B5 --> D3
    
    B1 --> E1
    E1 --> E2
    E1 --> E3
    E1 --> E4
    
    D1 --> F1
    D2 --> F2
    D3 --> F3
    
    style B1 fill:#e1f5fe
    style C1 fill:#f3e5f5
    style D1 fill:#e8f5e8
    style E1 fill:#fff3e0