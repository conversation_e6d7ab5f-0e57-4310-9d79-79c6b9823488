//! 交易配置模型
//!
//! 本模块包含了交易相关的配置结构，从 models_old.rs 中移植而来。
//! 主要包括交易参数、订单限制、滑点控制等配置。

use serde::{Deserialize, Serialize};
use validator::{Validate, ValidationError};
use crate::{SigmaXResult, SigmaXError};

// ============================================================================
// 交易配置
// ============================================================================

/// 交易配置
///
/// # 移植说明
/// 从 models_old.rs 移植，定义了交易相关的全局配置参数。
/// 这些参数控制着整个交易系统的行为和限制。
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Validate)]
pub struct TradingConfig {
    /// 每个策略最大订单数
    #[validate(range(min = 1, max = 1000, message = "每个策略最大订单数必须在1-1000之间"))]
    pub max_orders_per_strategy: u32,
    /// 最大持仓金额(USDT)
    #[validate(custom = "validate_positive_decimal")]
    pub max_position_size: rust_decimal::Decimal,
    /// 默认订单超时时间(秒)
    #[validate(range(min = 1, message = "默认订单超时时间必须大于0秒"))]
    pub default_order_timeout: u32,
    /// 最小订单间隔(秒)
    #[validate(range(min = 0, message = "最小订单间隔不能为负数"))]
    pub min_order_interval: u32,
    /// 最大滑点百分比
    #[validate(range(min = 0.0, max = 50.0, message = "最大滑点百分比必须在0-50之间"))]
    pub max_slippage_percent: f64,
}

// ============================================================================
// 交易配置实现
// ============================================================================

impl TradingConfig {
    /// 执行完整的交易配置验证
    pub fn validate_complete(&self) -> SigmaXResult<()> {
        // 首先执行基础验证（通过validator derive宏）
        if let Err(validation_errors) = <Self as Validate>::validate(self) {
            return Err(crate::SigmaXError::from(validation_errors));
        }

        // 可以在这里添加更多跨字段验证逻辑
        Ok(())
    }

    /// 创建新的交易配置
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供自定义交易配置的创建方法
    pub fn new(
        max_orders_per_strategy: u32,
        max_position_size: rust_decimal::Decimal,
        default_order_timeout: u32,
        min_order_interval: u32,
        max_slippage_percent: f64,
    ) -> Self {
        Self {
            max_orders_per_strategy,
            max_position_size,
            default_order_timeout,
            min_order_interval,
            max_slippage_percent,
        }
    }

    /// 获取保守的交易配置
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供保守的交易参数设置
    pub fn conservative() -> Self {
        Self {
            max_orders_per_strategy: 50,
            max_position_size: rust_decimal::Decimal::new(5000, 0), // 5,000 USDT
            default_order_timeout: 1800, // 30分钟
            min_order_interval: 5, // 5秒
            max_slippage_percent: 0.2, // 0.2%
        }
    }

    /// 获取激进的交易配置
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供激进的交易参数设置
    pub fn aggressive() -> Self {
        Self {
            max_orders_per_strategy: 200,
            max_position_size: rust_decimal::Decimal::new(50000, 0), // 50,000 USDT
            default_order_timeout: 300, // 5分钟
            min_order_interval: 1, // 1秒
            max_slippage_percent: 1.0, // 1.0%
        }
    }

    /// 检查订单数量是否超限
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供订单数量检查功能
    pub fn is_order_count_exceeded(&self, current_orders: u32) -> bool {
        current_orders >= self.max_orders_per_strategy
    }

    /// 检查持仓金额是否超限
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供持仓金额检查功能
    pub fn is_position_size_exceeded(&self, current_position: rust_decimal::Decimal) -> bool {
        current_position >= self.max_position_size
    }

    /// 检查滑点是否可接受
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，提供滑点检查功能
    pub fn is_slippage_acceptable(&self, slippage_percent: f64) -> bool {
        slippage_percent <= self.max_slippage_percent
    }

    /// 获取建议的订单超时时间
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，根据订单类型返回建议的超时时间
    pub fn get_order_timeout(&self, order_type: &str) -> u32 {
        match order_type {
            "market" => self.default_order_timeout / 6, // 市价单更快超时
            "limit" => self.default_order_timeout,
            "stop" => self.default_order_timeout * 2, // 止损单更长超时
            _ => self.default_order_timeout,
        }
    }

    /// 计算最大允许的订单金额
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植，根据当前持仓计算最大允许的订单金额
    pub fn max_order_amount(&self, current_position: rust_decimal::Decimal) -> rust_decimal::Decimal {
        if current_position >= self.max_position_size {
            rust_decimal::Decimal::ZERO
        } else {
            self.max_position_size - current_position
        }
    }
}

// ============================================================================
// 验证逻辑实现
// ============================================================================



// ============================================================================
// 默认实现
// ============================================================================

/// 交易配置的默认值
///
/// # 移植说明
/// 从 models_old.rs 移植，提供合理的默认交易配置
impl Default for TradingConfig {
    fn default() -> Self {
        Self {
            max_orders_per_strategy: 100,
            max_position_size: rust_decimal::Decimal::new(10000, 0), // 10,000 USDT
            default_order_timeout: 300, // 5分钟
            min_order_interval: 1, // 1秒
            max_slippage_percent: 0.5, // 0.5%
        }
    }
}

// ============================================================================
// 辅助函数
// ============================================================================

/// 根据风险等级获取交易配置
///
/// # 移植说明
/// 从 models_old.rs 移植，提供基于风险等级的配置选择
pub fn get_trading_config_by_risk_level(risk_level: &str) -> TradingConfig {
    match risk_level.to_lowercase().as_str() {
        "low" | "conservative" => TradingConfig::conservative(),
        "high" | "aggressive" => TradingConfig::aggressive(),
        _ => TradingConfig::default(), // 中等风险
    }
}

/// 验证交易配置的兼容性
///
/// # 移植说明
/// 从 models_old.rs 移植，检查交易配置与其他系统配置的兼容性
pub fn validate_trading_config_compatibility(
    trading_config: &TradingConfig,
    max_system_orders: u32,
) -> SigmaXResult<()> {
    if trading_config.max_orders_per_strategy > max_system_orders {
        return Err(SigmaXError::validation(
            crate::ValidationErrorCode::OutOfRange,
            "max_orders_per_strategy",
            format!(
                "策略最大订单数({})不能超过系统最大订单数({})",
                trading_config.max_orders_per_strategy,
                max_system_orders
            )
        ));
    }
    Ok(())
}

// ============================================================================
// 自定义验证函数 (用于validator derive宏)
// ============================================================================

/// 验证正数decimal
fn validate_positive_decimal(value: &rust_decimal::Decimal) -> Result<(), ValidationError> {
    if *value <= rust_decimal::Decimal::ZERO {
        return Err(ValidationError::new("最大持仓金额必须大于0"));
    }
    if *value > rust_decimal::Decimal::new(10000000, 0) { // 10M USDT
        return Err(ValidationError::new("最大持仓金额不能超过10,000,000 USDT"));
    }
    Ok(())
}
