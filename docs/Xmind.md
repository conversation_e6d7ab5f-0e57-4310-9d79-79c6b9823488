# SigmaX Contract Map

## 执行与冲突处理（摘要）
  - Contract Map 优先于现有实现；冲突立即清理或替换为契约版本
  - PR 必须引用本文件对应条目作为依据；修改冻结契约需变更说明与迁移策略
  - CI Gate：禁止通配导出、未注释 pub API、跨层非法依赖；冻结契约变更需签字

## Core
  - `types.rs`: ID/Decimal 等基础类型与别名（强制财务数值用 `Decimal`）。
    - IDs: `StrategyId`, `OrderId`, `TradeId`, `PortfolioId`
    - Numeric: `Amount`, `Price`, `Quantity`（均为 Decimal）
    - Trading: `TradingPair`, `ExchangeId`
  - `models/*`: 只保留两类模型：
    - StrategyRecord: strategy_id, name, strategy_type, status, config, created_at, updated_at
    - OrderRecord: order_id, strategy_id, trading_pair, side, order_type, price?, quantity, status, created_at, updated_at
    - PortfolioRecord: portfolio_id, owner, balances(json), created_at, updated_at
    - TradeRecord: trade_id, order_id, strategy_id, price, quantity, fee, ts
  - `errors/*`: 错误分类、错误码表、跨层 From/Into 映射。
    - 分类: Validation / Business / Database / External / Internal
    - 码表示例:
      - Validation: Required, InvalidFormat, InvalidRange, Conflict
      - Database: NotFound, UniqueViolation, ConnectionFailure
      - External: Timeout, UpstreamError
      - Internal: Unexpected
    - 映射: `From<SigmaXError> -> ApiError`（web 渲染），Controller 禁止手写业务错误
  - `events/*`: 事件总线接口与基础事件类型。
    - EventBus: publish(event), subscribe(handler)（async）
    - SystemEvent: StrategyCreated(StrategyId), StrategyUpdated(StrategyId), OrderPlaced(OrderId), OrderFilled(OrderId)
    - 模块可扩展子事件（如 WebEvent），统一复用 EventBus
  - `transaction/*`: 事务契约。
    - TransactionManager: begin() -> UnitOfWork, commit(uow), rollback(uow)
    - UnitOfWork: 暂存变更、统一提交；与 Repository 配合
  - `repositories/traits/*`: 仅接口（实现放 database）。
    - StrategyRepository
      - 写: save(&StrategyRecord)->StrategyId, update(&StrategyRecord)->()
      - 读: find_by_id(StrategyId)->Option<StrategyRecord|StrategyView>, list(StrategyFilter)->Vec<StrategyView>, stats(StrategyFilter)->StrategyStatsView
    - OrderRepository
      - 写: save/update(&OrderRecord)
      - 读: find_by_id(OrderId), list(OrderFilter)->Vec<OrderView>, stats(OrderFilter)->OrderStatsView
    - TradeRepository
      - 写: save(&TradeRecord)
      - 读: find_by_id(TradeId)->Option<TradeView>, list(TradeFilter)->Vec<TradeView>
      - 统计: stats(TradeFilter)->TradeStatsView
    - PortfolioRepository
      - 写: save/update(&PortfolioRecord)
      - 读: find_by_id(PortfolioId)->Option<PortfolioRecord|PortfolioView>, list(PortfolioFilter)->Vec<PortfolioView>
      - 统计: stats(PortfolioFilter)->PortfolioStatsView
    - RiskRepository
      - 写: save_metrics(&RiskRecord)
      - 读: latest_by_strategy(StrategyId)->Option<RiskView>, list(RiskFilter)->Vec<RiskView>
      - 统计: stats(RiskFilter)->RiskStatsView
    - SystemConfigRepository（如有）
      - 写: save/update(&ConfigRecord)
      - 读: get(key)->Option<ConfigView>, list(ConfigFilter)->Vec<ConfigView>
    - NotificationRepository（可选）
      - 写: save(&NotificationRecord), update_status(id, status)
      - 读: list(NotificationFilter)->Vec<NotificationView>
    - MonitoringRepository（可选）
      - 写: save_metrics(MonitoringMetricsRecord), save_config(MonitoringConfigRecord)
      - 读: list_metrics(MonitoringFilter)->Vec<MonitoringMetricsView>, list_alerts(AlertFilter)->Vec<AlertView>
    - CacheRepository（可选）
      - 写: set(key, value, ttl?)
      - 读: get(key)->Option<Value>, stats()->CacheStatsView
    - Enums / IDs / Types
      - Enums：
        - OrderSide={Buy,Sell}; OrderType={Market,Limit,Stop,StopLimit}
        - OrderStatus={Pending,PartiallyFilled,Filled,Cancelled,Rejected}
        - StrategyStatus={Active,Paused,Stopped,Error}
        - RiskSeverity={Low,Medium,High,Critical}
        - 其它：NotificationStatus、AlertStatus（可选）
      - IDs：StrategyId, OrderId, TradeId, PortfolioId（UUID newtype）
      - Types：Amount/Price/Quantity（Decimal），TradingPair(base,quote)，ExchangeId（newtype）
      - 导出：显式 `pub use`，禁止通配 re-export
    - Events（细化）
      - EventBus（async）：publish(event), subscribe(handler)
      - SystemEvent：StrategyCreated, StrategyUpdated, OrderPlaced, OrderFilled, PortfolioUpdated
      - EventHandler：handle(&SystemEvent)->Result<(), SigmaXError>
      - 模块子事件：遵循统一 EventBus 注册/发布
    - Transaction（细化）
      - TransactionManager：begin()->UnitOfWork, commit(uow), rollback(uow), with_transaction<F,R>(f)->R
      - UnitOfWork：id(), in_tx()->bool
      - 应用层持有 TM；Repository 通过 UoW 获取上下文（由 database 实现）
