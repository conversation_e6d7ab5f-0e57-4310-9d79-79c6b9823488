//! 认证服务实现

use std::sync::Arc;
use axum::http::{Request, StatusCode};
use axum::body::Body;
use async_trait::async_trait;
use sigmax_core::SigmaXResult;
use crate::gateway::AuthenticationService;

/// 简单认证服务实现
pub struct SimpleAuthService {
    api_keys: Arc<Vec<String>>,
}

impl SimpleAuthService {
    /// 创建新的认证服务
    pub fn new(api_keys: Vec<String>) -> Self {
        Self {
            api_keys: Arc::new(api_keys),
        }
    }
    
    /// 从请求头提取API Key
    fn extract_api_key(&self, request: &Request<Body>) -> Option<String> {
        request.headers()
            .get("X-API-Key")
            .and_then(|h| h.to_str().ok())
            .map(|s| s.to_string())
    }
}

#[async_trait]
impl AuthenticationService for SimpleAuthService {
    async fn authenticate(&self, request: &Request<Body>) -> SigmaXResult<()> {
        // 对于开发环境，暂时跳过认证
        #[cfg(debug_assertions)]
        return Ok(());
        
        // 生产环境需要API Key验证
        #[cfg(not(debug_assertions))]
        {
            let api_key = self.extract_api_key(request)
                .ok_or_else(|| sigmax_core::SigmaXError::internal(
                    sigmax_core::InternalErrorCode::AuthenticationFailed,
                    "缺少API Key"
                ))?;
            
            if !self.api_keys.contains(&api_key) {
                return Err(sigmax_core::SigmaXError::internal(
                    sigmax_core::InternalErrorCode::AuthenticationFailed,
                    "无效的API Key"
                ));
            }
        }
        
        Ok(())
    }
}

/// 无认证服务（用于测试）
pub struct NoAuthService;

#[async_trait]
impl AuthenticationService for NoAuthService {
    async fn authenticate(&self, _request: &Request<Body>) -> SigmaXResult<()> {
        Ok(())
    }
}
