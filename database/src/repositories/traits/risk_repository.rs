//! 风险管理仓储接口定义
//!
//! 基于现有数据库表结构设计的风险管理数据访问接口

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};
use sigmax_core::SigmaXResult;
use uuid::Uuid;

/// 风险检查记录（匹配线上表结构）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskCheckRecord {
    /// 检查ID
    pub id: Uuid,
    /// 检查时间戳
    pub timestamp: DateTime<Utc>,
    /// 交易对
    pub trading_pair: String,
    /// 交易方向（ENUM: buy, sell）
    pub side: String,
    /// 数量
    pub quantity: rust_decimal::Decimal,
    /// 价格
    pub price: Option<rust_decimal::Decimal>,
    /// 订单类型（ENUM: market, limit, stop, etc.）
    pub order_type: String,
    /// 检查结果（ENUM: passed, failed, warning, error）
    pub result: String,
    /// 风险评分
    pub risk_score: rust_decimal::Decimal,
    /// 置信度
    pub confidence_level: Option<rust_decimal::Decimal>,
    /// 违规信息
    pub violations: serde_json::Value,
    /// 警告信息
    pub warnings: serde_json::Value,
    /// 建议信息
    pub recommendations: serde_json::Value,
    /// 应用的规则列表
    pub applied_rules: serde_json::Value,
    /// 最大允许数量
    pub max_allowed_quantity: Option<rust_decimal::Decimal>,
    /// 建议最小价格
    pub suggested_price_min: Option<rust_decimal::Decimal>,
    /// 建议最大价格
    pub suggested_price_max: Option<rust_decimal::Decimal>,
    /// 替代建议
    pub alternative_suggestions: Option<serde_json::Value>,
    /// 处理时间（毫秒）
    pub processing_time_ms: Option<i32>,
    /// 评估的规则数量
    pub rules_evaluated: Option<i32>,
    /// 缓存命中率
    pub cache_hit_rate: Option<rust_decimal::Decimal>,
    /// 策略ID
    pub strategy_id: Option<Uuid>,
    /// 投资组合ID
    pub portfolio_id: Option<Uuid>,
    /// 引擎ID
    pub engine_id: Option<Uuid>,
    /// 会话ID
    pub session_id: Option<String>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 检查日期（分区字段）
    pub check_date: chrono::NaiveDate,
}

/// 风险规则配置（匹配线上表结构）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskRuleRecord {
    /// 规则ID
    pub id: Uuid,
    /// 规则名称
    pub name: String,
    /// 规则描述
    pub description: Option<String>,
    /// 规则分类
    pub category: String,
    /// 规则类型
    pub rule_type: String,
    /// 规则参数
    pub parameters: serde_json::Value,
    /// 执行条件
    pub conditions: Option<serde_json::Value>,
    /// 是否启用
    pub enabled: bool,
    /// 优先级
    pub priority: i32,
    /// 适用策略类型
    pub strategy_type: Option<String>,
    /// 适用交易对
    pub trading_pairs: Option<serde_json::Value>,
    /// 执行次数
    pub execution_count: Option<i64>,
    /// 成功次数
    pub success_count: Option<i64>,
    /// 失败次数
    pub failure_count: Option<i64>,
    /// 最后执行时间
    pub last_executed_at: Option<DateTime<Utc>>,
    /// 平均执行时间（毫秒）
    pub average_execution_time_ms: Option<rust_decimal::Decimal>,
    /// 版本号
    pub version: i32,
    /// 是否激活
    pub is_active: bool,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
    /// 创建者
    pub created_by: Option<String>,
    /// 更新者
    pub updated_by: Option<String>,
}



/// 风险违规记录（匹配线上表结构）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskViolationRecord {
    /// 违规ID
    pub id: Uuid,
    /// 关联的风险检查ID
    pub risk_check_id: Uuid,
    /// 违反的规则ID
    pub rule_id: Uuid,
    /// 规则名称
    pub rule_name: String,
    /// 规则版本
    pub rule_version: Option<i32>,
    /// 严重程度（ENUM: low, medium, high, critical）
    pub severity: String,
    /// 状态（ENUM: active, resolved, ignored, escalated）
    pub status: String,
    /// 违规消息
    pub message: String,
    /// 详细消息
    pub detailed_message: Option<String>,
    /// 当前值
    pub current_value: Option<String>,
    /// 限制值
    pub limit_value: Option<String>,
    /// 超出阈值的绝对值
    pub threshold_exceeded_by: Option<rust_decimal::Decimal>,
    /// 超出阈值的百分比
    pub threshold_exceeded_percent: Option<rust_decimal::Decimal>,
    /// 交易对
    pub trading_pair: Option<String>,
    /// 策略ID
    pub strategy_id: Option<Uuid>,
    /// 投资组合ID
    pub portfolio_id: Option<Uuid>,
    /// 订单上下文
    pub order_context: Option<serde_json::Value>,
    /// 潜在损失
    pub potential_loss: Option<rust_decimal::Decimal>,
    /// 风险贡献度
    pub risk_contribution: Option<rust_decimal::Decimal>,
    /// 影响评分
    pub impact_score: Option<rust_decimal::Decimal>,
    /// 是否自动解决
    pub auto_resolved: Option<bool>,
    /// 解决行动
    pub resolution_action: Option<String>,
    /// 解决备注
    pub resolution_notes: Option<String>,
    /// 解决时间
    pub resolved_at: Option<DateTime<Utc>>,
    /// 解决者
    pub resolved_by: Option<String>,
    /// 是否已发送通知
    pub notification_sent: Option<bool>,
    /// 通知发送时间
    pub notification_sent_at: Option<DateTime<Utc>>,
    /// 升级级别
    pub escalation_level: Option<i32>,
    /// 升级时间
    pub escalated_at: Option<DateTime<Utc>>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
    /// 创建者
    pub created_by: Option<String>,
}

/// 风险查询过滤器
#[derive(Debug, Clone, Default)]
pub struct RiskQueryFilter {
    /// 交易对过滤
    pub trading_pair: Option<String>,
    /// 策略ID过滤
    pub strategy_id: Option<Uuid>,
    /// 引擎ID过滤
    pub engine_id: Option<Uuid>,
    /// 是否通过过滤
    pub passed: Option<bool>,
    /// 开始时间
    pub start_time: Option<DateTime<Utc>>,
    /// 结束时间
    pub end_time: Option<DateTime<Utc>>,
    /// 最小风险评分
    pub min_risk_score: Option<rust_decimal::Decimal>,
    /// 最大风险评分
    pub max_risk_score: Option<rust_decimal::Decimal>,
}

/// 风险规则查询过滤器
#[derive(Debug, Clone, Default)]
pub struct RiskRuleFilter {
    /// 规则类型过滤
    pub rule_type: Option<String>,
    /// 是否启用过滤
    pub enabled: Option<bool>,
    /// 创建者过滤
    pub created_by: Option<String>,
}

/// 分页参数
#[derive(Debug, Clone)]
pub struct Pagination {
    /// 偏移量
    pub offset: usize,
    /// 限制数量
    pub limit: usize,
}

/// 风险仓储接口
#[async_trait]
pub trait RiskRepository: Send + Sync {
    // ============================================================================
    // 风险检查记录管理
    // ============================================================================
    
    /// 保存风险检查记录
    async fn save_risk_check(&self, record: &RiskCheckRecord) -> SigmaXResult<()>;
    
    /// 根据ID获取风险检查记录
    async fn get_risk_check(&self, id: Uuid) -> SigmaXResult<Option<RiskCheckRecord>>;
    
    /// 根据条件查询风险检查记录
    async fn find_risk_checks(
        &self,
        filter: &RiskQueryFilter,
        pagination: Option<&Pagination>
    ) -> SigmaXResult<Vec<RiskCheckRecord>>;
    
    /// 统计风险检查记录数量
    async fn count_risk_checks(&self, filter: &RiskQueryFilter) -> SigmaXResult<u64>;
    
    // ============================================================================
    // 风险规则管理
    // ============================================================================
    
    /// 保存风险规则
    async fn save_risk_rule(&self, rule: &RiskRuleRecord) -> SigmaXResult<()>;
    
    /// 根据ID获取风险规则
    async fn get_risk_rule(&self, id: Uuid) -> SigmaXResult<Option<RiskRuleRecord>>;
    
    /// 获取所有启用的风险规则
    async fn get_enabled_risk_rules(&self) -> SigmaXResult<Vec<RiskRuleRecord>>;
    
    /// 根据类型获取风险规则
    async fn get_risk_rules_by_type(&self, rule_type: &str) -> SigmaXResult<Vec<RiskRuleRecord>>;
    
    /// 根据条件查询风险规则
    async fn find_risk_rules(
        &self,
        filter: &RiskRuleFilter,
        pagination: Option<&Pagination>
    ) -> SigmaXResult<Vec<RiskRuleRecord>>;
    
    /// 更新风险规则状态
    async fn update_risk_rule_status(&self, id: Uuid, enabled: bool) -> SigmaXResult<()>;
    
    /// 删除风险规则
    async fn delete_risk_rule(&self, id: Uuid) -> SigmaXResult<()>;
    

    
    // ============================================================================
    // 风险违规记录管理
    // ============================================================================

    /// 保存风险违规记录
    async fn save_risk_violation(&self, violation: &RiskViolationRecord) -> SigmaXResult<()>;

    /// 根据风险检查ID获取违规记录
    async fn get_violations_by_check_id(&self, check_id: Uuid) -> SigmaXResult<Vec<RiskViolationRecord>>;

    /// 根据规则ID获取违规记录
    async fn get_violations_by_rule_id(&self, rule_id: Uuid) -> SigmaXResult<Vec<RiskViolationRecord>>;

    /// 获取最近的违规记录
    async fn get_recent_violations(&self, limit: usize) -> SigmaXResult<Vec<RiskViolationRecord>>;



}
