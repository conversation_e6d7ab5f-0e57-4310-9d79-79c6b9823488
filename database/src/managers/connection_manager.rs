//! 连接管理器
//! 
//! 专门负责数据库连接池的管理，实现单一职责原则

use sigmax_core::{SigmaXResult, SigmaXError, DatabaseErrorCode};
use sqlx::{PgPool, Row};
use std::time::Duration;
use std::sync::Arc;

/// 连接配置
#[derive(Debug, Clone)]
pub struct ConnectionConfig {
    /// 数据库连接URL
    pub url: String,
    /// 最大连接数
    pub max_connections: u32,
    /// 连接超时时间（秒）
    pub connection_timeout: u64,
    /// 连接空闲超时时间（秒）
    pub idle_timeout: Option<u64>,
    /// 最大连接生命周期（秒）
    pub max_lifetime: Option<u64>,
}

impl Default for ConnectionConfig {
    fn default() -> Self {
        Self {
            url: "postgresql://localhost/sigmax".to_string(),
            max_connections: 10,
            connection_timeout: 30,
            idle_timeout: Some(600), // 10分钟
            max_lifetime: Some(1800), // 30分钟
        }
    }
}

/// 连接池统计信息
#[derive(Debug, <PERSON><PERSON>)]
pub struct ConnectionStats {
    /// 总连接数
    pub total_connections: u32,
    /// 活跃连接数
    pub active_connections: u32,
    /// 空闲连接数
    pub idle_connections: u32,
    /// 等待连接的请求数
    pub waiting_requests: u32,
}

/// 连接管理器
/// 
/// 专门负责数据库连接池的创建、管理和监控
#[derive(Debug)]
pub struct ConnectionManager {
    pool: PgPool,
    config: ConnectionConfig,
}

impl ConnectionManager {
    /// 创建新的连接管理器
    pub async fn new(config: ConnectionConfig) -> SigmaXResult<Self> {
        let pool = Self::create_connection_pool(&config).await?;
        
        Ok(Self {
            pool,
            config,
        })
    }

    /// 创建用于测试的连接管理器
    pub fn new_mock() -> SigmaXResult<Self> {
        let config = ConnectionConfig {
            url: "postgresql://mock".to_string(),
            max_connections: 1,
            connection_timeout: 30,
            idle_timeout: None,
            max_lifetime: None,
        };

        // 创建延迟连接池（不立即连接）
        // 根据编译器错误，connect_lazy 在我们的SQLx版本中返回 Result
        let pool = sqlx::postgres::PgPoolOptions::new()
            .max_connections(1)
            .connect_lazy(&config.url)
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::ConnectionFailed,
                format!("Failed to create mock connection pool: {}", e)
            ))?;

        Ok(Self {
            pool,
            config,
        })
    }

    /// 创建连接池
    async fn create_connection_pool(config: &ConnectionConfig) -> SigmaXResult<PgPool> {
        let mut options = sqlx::postgres::PgPoolOptions::new()
            .max_connections(config.max_connections)
            .acquire_timeout(Duration::from_secs(config.connection_timeout));

        // 设置空闲超时
        if let Some(idle_timeout) = config.idle_timeout {
            options = options.idle_timeout(Duration::from_secs(idle_timeout));
        }

        // 设置最大生命周期
        if let Some(max_lifetime) = config.max_lifetime {
            options = options.max_lifetime(Duration::from_secs(max_lifetime));
        }

        let pool = options
            .connect(&config.url)
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::ConnectionFailed, 
                format!("Failed to create connection pool: {}", e)
            ))?;
        
        Ok(pool)
    }

    /// 获取连接池引用
    pub fn pool(&self) -> &PgPool {
        &self.pool
    }

    /// 获取连接池的Arc引用（用于共享）
    pub fn pool_arc(&self) -> Arc<PgPool> {
        Arc::new(self.pool.clone())
    }

    /// 获取配置
    pub fn config(&self) -> &ConnectionConfig {
        &self.config
    }

    /// 健康检查
    pub async fn health_check(&self) -> SigmaXResult<()> {
        sqlx::query("SELECT 1")
            .fetch_one(&self.pool)
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed, 
                format!("Connection health check failed: {}", e)
            ))?;
        
        Ok(())
    }

    /// 获取连接池统计信息
    pub async fn get_connection_stats(&self) -> SigmaXResult<ConnectionStats> {
        // 查询PostgreSQL的连接统计
        let row = sqlx::query(
            r#"
            SELECT
                (SELECT setting::int FROM pg_settings WHERE name = 'max_connections') as max_connections,
                (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_connections,
                (SELECT count(*) FROM pg_stat_activity WHERE state = 'idle') as idle_connections,
                (SELECT count(*) FROM pg_stat_activity WHERE state = 'idle in transaction') as idle_in_transaction
            "#
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| SigmaXError::database(
            DatabaseErrorCode::QueryFailed, 
            format!("Failed to get connection stats: {}", e)
        ))?;

        Ok(ConnectionStats {
            total_connections: self.config.max_connections,
            active_connections: row.get::<i64, _>("active_connections") as u32,
            idle_connections: row.get::<i64, _>("idle_connections") as u32,
            waiting_requests: 0, // SQLx不直接提供此信息
        })
    }

    /// 关闭连接池
    pub async fn close(&self) {
        self.pool.close().await;
    }

    /// 检查连接池是否已关闭
    pub fn is_closed(&self) -> bool {
        self.pool.is_closed()
    }
}

impl Clone for ConnectionManager {
    fn clone(&self) -> Self {
        Self {
            pool: self.pool.clone(),
            config: self.config.clone(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_connection_config_default() {
        let config = ConnectionConfig::default();
        assert_eq!(config.max_connections, 10);
        assert_eq!(config.connection_timeout, 30);
        assert_eq!(config.idle_timeout, Some(600));
        assert_eq!(config.max_lifetime, Some(1800));
    }

    #[tokio::test]
    async fn test_connection_manager_mock() {
        let manager = ConnectionManager::new_mock().unwrap();
        assert_eq!(manager.config.max_connections, 1);
        assert!(!manager.is_closed());
    }

    #[test]
    fn test_connection_stats() {
        let stats = ConnectionStats {
            total_connections: 10,
            active_connections: 3,
            idle_connections: 7,
            waiting_requests: 0,
        };
        
        assert_eq!(stats.total_connections, 10);
        assert_eq!(stats.active_connections, 3);
        assert_eq!(stats.idle_connections, 7);
    }
}
