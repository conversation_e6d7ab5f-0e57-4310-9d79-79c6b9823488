# SigmaX 整体架构优化方案（契约先行，分层重塑）

> 目标：以“契约先行、实现后置”为核心，重塑清晰的模块边界与依赖方向，统一横切能力（错误、事件、配置、观测），并提供可回滚的分阶段迁移路径。

---

## 1. 核心设计原则
- 契约集中，稳定输出：统一在 `core` 暴露稳定契约（类型/ID、持久化模型 Record、查询/展示模型 View、错误、事件接口）。
- 依赖单向，边界清晰：`web → application → domain → core`，`infrastructure(database/engines/exchange)` 只依赖 `core` 契约；禁止反向或跨层耦合。
- 单一职责，分层协作：每层只做本层职责，禁止“越层”逻辑（如 controller 直接操作事务或 repository）。
- 显式导出，消灭歧义：禁止 `*` 通配 re-export，避免命名冲突与不确定依赖。
- 渐进演进，可回滚：分阶段迁移，阶段结束可编译、可运行、可回滚。

- **高内聚，低耦合 (High Cohesion, Low Coupling)**
   - 模块内部功能紧密相关，模块间依赖最小化
   - 每个模块专注于单一职责

- **关注点分离 (Separation of Concerns, SoC)**
   - 配置管理、业务逻辑、数据访问分离

- **面向接口设计 (Design to an Interface)**
   - 定义清晰的接口契约
   - 依赖抽象而非具体实现

- **可测试性设计 (Design for Testability)**
   - 支持单元测试和集成测试
   - 依赖注入，便于 Mock 测试

- **简洁与可演化性 (Simplicity and Evolvability)**
   - 保持代码简洁易懂
   - 支持功能扩展和维护


---

## 2. 分层与模块边界

### 2.1 Core（稳定契约层）
- 内容：
  - `types.rs`: ID/Decimal 等基础类型与别名（强制财务数值用 `Decimal`）。
  - `models/*`: 只保留两类模型：
    - Record（持久化/写模型，如 `StrategyRecord`）
    - View（查询/展示/聚合模型，如 `StrategyView`）
  - `errors/*`: 错误分类、错误码表、跨层 From/Into 映射。
  - `events/*`: 事件总线接口与基础事件类型。
- 规则：
  - 显式导出：只 `pub use` 稳定契约，禁止 `pub use models::*` 之类通配。
  - 文档：为 Record/View 加注释，明确用途与下游约束。

### 2.2 Domain（领域规则层）
- 内容：纯业务规则与校验；无 IO；依赖注入抽象（如 repository trait）。
- 规则：
  - 输入/输出使用 `core` 稳定契约（Record 用于规则校验与写入；必要时读取 View，但不依赖其聚合语义）。
  - 领域服务不关注事务，事务在 application。

### 2.3 Application（应用编排层）
- 内容：用例编排、事务边界、事件发布。
- 规则：
  - 注入 `TransactionManager` 抽象与各 repository 抽象。
  - 一个用例一种事务策略（显式开始/提交/回滚）。

### 2.4 Infrastructure（基础设施层）
- 内容：
  - `database`: Repository 实现（SQLx）、连接池、事务实现。
  - `engines/exchange`: 行情与执行适配。
- 规则：
  - 仅依赖 `core` 契约，禁止依赖 web、application、domain 实现。
  - Repository 接口的实现与工厂均放此处。

### 2.5 Web（接口与装配层）
- 层内结构：
  - Gateway（中间件/认证/限流/Tracing/统一错误映射）
  - Controllers（HTTP 参数校验与 DTO 绑定）
  - Application Services（编排与事务/事件）
  - Domain Services（注入抽象，执行业务规则）
  - Integration（对外部模块的最薄封装）
  - Container（依赖注入/装配，零业务逻辑）

---

## 3. 横切能力统一

### 3.1 错误体系
- 错误分类：Validation / Business / Database / External / Internal。
- 统一码表：在 `core::errors::types` 定义常量/枚举，映射至 web 的 `ApiError` 渲染。
- 转换策略：提供 `From<SigmaXError> for ApiError` 等桥接；禁止 controller 直接构造业务错误。

### 3.2 事件驱动
- `core::events`：`EventBus` 接口 + 系统级事件类型。
- 各模块扩展自身事件枚举（`WebEvent` 等），通过组合事件总线注册处理器。
- 事件处理器职责：日志、审计、统计、通知、异步任务。

### 3.3 配置与观测
- 配置：以 typed config（环境变量 + dotenv）注入至容器。
- 观测：Tracing（请求/DB/风控/执行耗时）、Metrics（QPS/错误率/延时分布）。

---

## 4. Repository 与事务
- Repository 接口归一到 `core`；实现放在 `database`。
- Record/View 分流：
  - 写：`save/update` 接口接收 Record。
  - 读：列表与报表返回 View；`find_by_id` 可返回 Record 或 View，必须在接口层文档固定。
- 事务：`TransactionManager` + `UnitOfWork` 在 `database`；`application` 注入并持有，禁止 `controller` 开启事务。

---

## 5. 依赖注入（DI）与装配
- `web/container`：
  - 读取配置，创建连接池、事件总线、Repository 实现、领域/应用服务实例。
  - 提供获取应用服务/事件总线等只读方法；不含业务逻辑。
- 禁止在领域/适配层 `new MockXXX`，全部在容器层选择实现。

---

## 6. 目录结构推荐
```
core/
  models/
    record/
    view/
  errors/
  events/
  types.rs

database/
  repositories/
    traits/
    sqlx_impl/
  transaction/

engines/
exchange/

risk/
strategies/
portfolio/

web/
  gateway/
  controllers/
  application/
  domain/
  integration/
  container/
  bin/server.rs
```

---

## 7. 迁移计划（分阶段，可回滚）

### Phase 0（当天）
- core：去除通配导出，显式 `pub use` 稳定契约；为 Record/View 增注释。  
- errors：固化错误码定义与跨层转换；新增最小单元测试。

### Phase 1（1-2 天）
- database：
  - 统一 repository 接口签名：Record/View 区分；补齐文档。
  - 提供 `TransactionManager` 抽象 + SQLx 实现。
- web：
  - Application Service 注入 `TransactionManager` 与 repository 抽象；Controller 去除业务逻辑。
  - Container 统一装配真实 repository。

### Phase 2（2-3 天）
- web：替换旧模块（handlers/routes/old api/old services），迁移到 5 层结构；逐文件替换 + cargo check 保持绿灯。  
- risk/strategies/portfolio：只依赖 core 契约，调整对 Record/View 的使用。

### Phase 3（2 天）
- 观测/性能：Tracing + Metrics、DB/风控/执行耗时采集；路由级别限流。  
- 文档：更新 README、错误码表、事件字典、依赖图；打 tag。

回滚策略：每阶段完成打 tag；若失败立即回滚到前一 tag。

---

## 8. 验收标准
- 构建：`cargo check`、`cargo clippy`（warning 不超过基线）。
- 端到端：策略创建/下单/风控/持久化主路径可跑通。
- 错误：错误码与 HTTP 渲染一致，日志中包含 trace_id。
- 文档：核心模块与接口有注释；根目录文档与依赖图同步更新。

---

## 9. 近期落地的三个“最小且高价值”改动
1) core：去除通配 re-export，显式导出稳定契约（消除歧义警告）。
2) models/strategy.rs：在注释中明确 `StrategyRecord`（写/持久化）与 `StrategyView`（读/查询）用法，并在 repository 接口中统一签名。
3) web/application：引入 `TransactionManager` 抽象（由 database 实现注入），将事务从 controller 移至 application。

> 以上文档为统一裁剪路径与执行准则的“北极星”。如需，我可以按 Phase 0 立即提交对应最小改动的 MR。
