//! 持仓风控策略

use async_trait::async_trait;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use crate::core::{RiskPolicy, RiskResult, RiskContext};

/// 持仓风控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PositionRiskConfig {
    /// 最大单一资产持仓比例
    pub max_single_asset_ratio: Decimal,
    /// 最大总持仓价值
    pub max_total_position_value: Decimal,
    /// 最大持仓数量
    pub max_position_count: usize,
    /// 是否启用
    pub enabled: bool,
}

impl Default for PositionRiskConfig {
    fn default() -> Self {
        Self {
            max_single_asset_ratio: Decimal::from_str_exact("0.3").unwrap(), // 30%
            max_total_position_value: Decimal::from(1000000),
            max_position_count: 20,
            enabled: true,
        }
    }
}

/// 持仓风控策略
pub struct PositionRiskPolicy {
    config: PositionRiskConfig,
}

impl PositionRiskPolicy {
    pub fn new(config: PositionRiskConfig) -> Self {
        Self { config }
    }
}

impl Default for PositionRiskPolicy {
    fn default() -> Self {
        Self::new(PositionRiskConfig::default())
    }
}

#[async_trait]
impl RiskPolicy for PositionRiskPolicy {
    async fn evaluate(&self, context: &RiskContext) -> RiskResult {
        // 如果没有持仓数据，直接通过
        if context.balances.is_empty() {
            return RiskResult::approved();
        }
        
        // 检查持仓数量
        if context.balances.len() > self.config.max_position_count {
            return RiskResult::rejected(format!(
                "持仓数量 {} 超过最大限制 {}",
                context.balances.len(),
                self.config.max_position_count
            ));
        }
        
        // 计算总价值和检查单一资产比例
        let total_value: Decimal = context.balances.iter()
            .map(|b| b.total())
            .sum();
            
        if total_value > self.config.max_total_position_value {
            return RiskResult::rejected(format!(
                "总持仓价值 {} 超过最大限制 {}",
                total_value,
                self.config.max_total_position_value
            ));
        }
        
        // 检查单一资产集中度
        if total_value > Decimal::ZERO {
            for balance in &context.balances {
                let asset_value = balance.total();
                let ratio = asset_value / total_value;
                
                if ratio > self.config.max_single_asset_ratio {
                    return RiskResult::rejected(format!(
                        "资产 {} 占比 {:.2}% 超过最大限制 {:.0}%",
                        balance.asset,
                        ratio * Decimal::from(100),
                        self.config.max_single_asset_ratio * Decimal::from(100)
                    ));
                }
            }
        }
        
        RiskResult::approved()
    }
    
    fn name(&self) -> &str {
        "PositionRiskPolicy"
    }
    
    fn description(&self) -> &str {
        "持仓集中度和规模限制检查"
    }
    
    fn priority(&self) -> i32 {
        90 // 高优先级
    }
    
    fn is_enabled(&self) -> bool {
        self.config.enabled
    }
}
