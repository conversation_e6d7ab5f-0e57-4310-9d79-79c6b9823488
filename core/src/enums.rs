//! SigmaX Core Enums
//!
//! ## 主要职责
//!
//! 1. **交易相关枚举** - 核心交易概念的状态和类型定义
//!    - `OrderSide`, `OrderType`, `OrderStatus` - 订单相关枚举
//!    - 提供字符串转换和显示功能
//!
//! 2. **系统相关枚举** - 基础设施相关的状态枚举
//!    - `TimeFrame` - 时间周期定义
//!    - 提供实用的转换方法
//!
//! 3. **业务枚举** - 暂时保留的业务相关枚举 (将来迁移)
//!    - `StrategyStatus`, `EngineType`, `EngineStatus` - 策略和引擎相关
//!    - 标记为将来迁移至对应模块
//!
//! ## 设计原则
//!
//! - **类型安全**: 使用强类型枚举避免魔法字符串
//! - **序列化支持**: 所有枚举支持serde序列化
//! - **字符串转换**: 提供Display和FromStr实现
//! - **数据库兼容**: 支持数据库存储和查询
//! - **业务语义**: 枚举值反映真实的业务状态

use serde::{Deserialize, Serialize};
use std::fmt;

// ============================================================================
// 交易相关枚举 - 核心交易概念
// ============================================================================

/// 订单方向 - 买入或卖出
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum OrderSide {
    Buy,
    Sell,
}

impl fmt::Display for OrderSide {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            OrderSide::Buy => write!(f, "buy"),
            OrderSide::Sell => write!(f, "sell"),
        }
    }
}

impl std::str::FromStr for OrderSide {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "buy" => Ok(OrderSide::Buy),
            "sell" => Ok(OrderSide::Sell),
            _ => Err(format!("Invalid OrderSide: {}", s)),
        }
    }
}

impl OrderSide {
    /// 获取相反方向
    pub fn opposite(&self) -> Self {
        match self {
            OrderSide::Buy => OrderSide::Sell,
            OrderSide::Sell => OrderSide::Buy,
        }
    }

    /// 检查是否为买入
    pub fn is_buy(&self) -> bool {
        matches!(self, OrderSide::Buy)
    }

    /// 检查是否为卖出
    pub fn is_sell(&self) -> bool {
        matches!(self, OrderSide::Sell)
    }
}

/// 订单类型 - 市价、限价等
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum OrderType {
    Market,
    Limit,
    StopLoss,
    StopLimit,
}

impl fmt::Display for OrderType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            OrderType::Market => write!(f, "market"),
            OrderType::Limit => write!(f, "limit"),
            OrderType::StopLoss => write!(f, "stop_loss"),
            OrderType::StopLimit => write!(f, "stop_limit"),
        }
    }
}

impl std::str::FromStr for OrderType {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "market" => Ok(OrderType::Market),
            "limit" => Ok(OrderType::Limit),
            "stop_loss" | "stoploss" => Ok(OrderType::StopLoss),
            "stop_limit" | "stoplimit" => Ok(OrderType::StopLimit),
            _ => Err(format!("Invalid OrderType: {}", s)),
        }
    }
}

impl OrderType {
    /// 检查是否需要价格参数
    pub fn requires_price(&self) -> bool {
        matches!(self, OrderType::Limit | OrderType::StopLimit)
    }

    /// 检查是否为止损类型
    pub fn is_stop_order(&self) -> bool {
        matches!(self, OrderType::StopLoss | OrderType::StopLimit)
    }

    /// 检查是否为市价单
    pub fn is_market_order(&self) -> bool {
        matches!(self, OrderType::Market)
    }
}


/// 订单状态 - 订单生命周期状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum OrderStatus {
    Pending,
    PartiallyFilled,
    Filled,
    Cancelled,
    Rejected,
    Expired,
}

impl fmt::Display for OrderStatus {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            OrderStatus::Pending => write!(f, "pending"),
            OrderStatus::PartiallyFilled => write!(f, "partially_filled"),
            OrderStatus::Filled => write!(f, "filled"),
            OrderStatus::Cancelled => write!(f, "cancelled"),
            OrderStatus::Rejected => write!(f, "rejected"),
            OrderStatus::Expired => write!(f, "expired"),
        }
    }
}

impl std::str::FromStr for OrderStatus {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "pending" => Ok(OrderStatus::Pending),
            "partially_filled" | "partiallyfilled" | "partial" => Ok(OrderStatus::PartiallyFilled),
            "filled" | "complete" => Ok(OrderStatus::Filled),
            "cancelled" | "canceled" => Ok(OrderStatus::Cancelled),
            "rejected" => Ok(OrderStatus::Rejected),
            "expired" => Ok(OrderStatus::Expired),
            _ => Err(format!("Invalid OrderStatus: {}", s)),
        }
    }
}

impl OrderStatus {
    /// 检查订单是否为活跃状态 (可以被执行或修改)
    pub fn is_active(&self) -> bool {
        matches!(self, OrderStatus::Pending | OrderStatus::PartiallyFilled)
    }

    /// 检查订单是否已完成 (不会再有状态变化)
    pub fn is_final(&self) -> bool {
        matches!(
            self,
            OrderStatus::Filled | OrderStatus::Cancelled | OrderStatus::Rejected | OrderStatus::Expired
        )
    }

    /// 检查订单是否已成交 (完全或部分)
    pub fn is_filled(&self) -> bool {
        matches!(self, OrderStatus::Filled | OrderStatus::PartiallyFilled)
    }

    /// 检查订单是否被取消或拒绝
    pub fn is_cancelled_or_rejected(&self) -> bool {
        matches!(self, OrderStatus::Cancelled | OrderStatus::Rejected)
    }

    /// 获取状态的优先级 (用于排序)
    pub fn priority(&self) -> u8 {
        match self {
            OrderStatus::Pending => 1,
            OrderStatus::PartiallyFilled => 2,
            OrderStatus::Filled => 3,
            OrderStatus::Cancelled => 4,
            OrderStatus::Rejected => 5,
            OrderStatus::Expired => 6,
        }
    }
}

// ============================================================================
// 系统相关枚举 - 基础设施概念
// ============================================================================

/// 时间周期 - K线和数据分析的时间间隔
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum TimeFrame {
    M1,   // 1分钟
    M5,   // 5分钟
    M15,  // 15分钟
    M30,  // 30分钟
    H1,   // 1小时
    H4,   // 4小时
    D1,   // 1天
    W1,   // 1周
    MN1,  // 1月
}

impl fmt::Display for TimeFrame {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            TimeFrame::M1 => write!(f, "1m"),
            TimeFrame::M5 => write!(f, "5m"),
            TimeFrame::M15 => write!(f, "15m"),
            TimeFrame::M30 => write!(f, "30m"),
            TimeFrame::H1 => write!(f, "1h"),
            TimeFrame::H4 => write!(f, "4h"),
            TimeFrame::D1 => write!(f, "1d"),
            TimeFrame::W1 => write!(f, "1w"),
            TimeFrame::MN1 => write!(f, "1M"),
        }
    }
}

impl std::str::FromStr for TimeFrame {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "1m" | "m1" => Ok(TimeFrame::M1),
            "5m" | "m5" => Ok(TimeFrame::M5),
            "15m" | "m15" => Ok(TimeFrame::M15),
            "30m" | "m30" => Ok(TimeFrame::M30),
            "1h" | "h1" => Ok(TimeFrame::H1),
            "4h" | "h4" => Ok(TimeFrame::H4),
            "1d" | "d1" => Ok(TimeFrame::D1),
            "1w" | "w1" => Ok(TimeFrame::W1),
            "1M" | "mn1" => Ok(TimeFrame::MN1),
            _ => Err(format!("Invalid TimeFrame: {}", s)),
        }
    }
}

impl TimeFrame {
    /// 转换为秒数
    pub fn to_seconds(&self) -> u64 {
        match self {
            TimeFrame::M1 => 60,
            TimeFrame::M5 => 300,
            TimeFrame::M15 => 900,
            TimeFrame::M30 => 1800,
            TimeFrame::H1 => 3600,
            TimeFrame::H4 => 14400,
            TimeFrame::D1 => 86400,
            TimeFrame::W1 => 604800,
            TimeFrame::MN1 => 2592000, // 30天近似
        }
    }

    /// 转换为分钟数
    pub fn to_minutes(&self) -> u64 {
        self.to_seconds() / 60
    }

    /// 转换为小时数
    pub fn to_hours(&self) -> f64 {
        self.to_seconds() as f64 / 3600.0
    }

    /// 获取人类可读的描述
    pub fn description(&self) -> &'static str {
        match self {
            TimeFrame::M1 => "1 minute",
            TimeFrame::M5 => "5 minutes",
            TimeFrame::M15 => "15 minutes",
            TimeFrame::M30 => "30 minutes",
            TimeFrame::H1 => "1 hour",
            TimeFrame::H4 => "4 hours",
            TimeFrame::D1 => "1 day",
            TimeFrame::W1 => "1 week",
            TimeFrame::MN1 => "1 month",
        }
    }

    /// 检查是否为分钟级别
    pub fn is_minute_based(&self) -> bool {
        matches!(self, TimeFrame::M1 | TimeFrame::M5 | TimeFrame::M15 | TimeFrame::M30)
    }

    /// 检查是否为小时级别
    pub fn is_hour_based(&self) -> bool {
        matches!(self, TimeFrame::H1 | TimeFrame::H4)
    }

    /// 检查是否为日级别或更长
    pub fn is_daily_or_longer(&self) -> bool {
        matches!(self, TimeFrame::D1 | TimeFrame::W1 | TimeFrame::MN1)
    }
}

// ============================================================================
// 业务相关枚举 - 暂时保留 (将来迁移至对应模块)
// ============================================================================

/// 策略状态 - 暂时保留 (将来应移至strategies模块)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum StrategyStatus {
    Active,
    Paused,
    Stopped,
    Error,
}

impl fmt::Display for StrategyStatus {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            StrategyStatus::Active => write!(f, "active"),
            StrategyStatus::Paused => write!(f, "paused"),
            StrategyStatus::Stopped => write!(f, "stopped"),
            StrategyStatus::Error => write!(f, "error"),
        }
    }
}

impl std::str::FromStr for StrategyStatus {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "active" | "running" => Ok(StrategyStatus::Active),
            "paused" => Ok(StrategyStatus::Paused),
            "stopped" => Ok(StrategyStatus::Stopped),
            "error" => Ok(StrategyStatus::Error),
            _ => Err(format!("Invalid StrategyStatus: {}", s)),
        }
    }
}

/// 引擎类型 - 暂时保留 (将来应移至engines模块)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EngineType {
    Backtest,
    Live,
    Paper,
}

impl fmt::Display for EngineType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            EngineType::Backtest => write!(f, "backtest"),
            EngineType::Live => write!(f, "live"),
            EngineType::Paper => write!(f, "paper"),
        }
    }
}

impl std::str::FromStr for EngineType {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "backtest" => Ok(EngineType::Backtest),
            "live" => Ok(EngineType::Live),
            "paper" => Ok(EngineType::Paper),
            _ => Err(format!("Invalid EngineType: {}", s)),
        }
    }
}

/// 引擎状态 - 暂时保留 (将来应移至engines模块)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EngineStatus {
    Starting,
    Running,
    Paused,
    Stopped,
    Error,
}

impl fmt::Display for EngineStatus {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            EngineStatus::Starting => write!(f, "starting"),
            EngineStatus::Running => write!(f, "running"),
            EngineStatus::Paused => write!(f, "paused"),
            EngineStatus::Stopped => write!(f, "stopped"),
            EngineStatus::Error => write!(f, "error"),
        }
    }
}

impl std::str::FromStr for EngineStatus {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "starting" => Ok(EngineStatus::Starting),
            "running" => Ok(EngineStatus::Running),
            "paused" => Ok(EngineStatus::Paused),
            "stopped" => Ok(EngineStatus::Stopped),
            "error" => Ok(EngineStatus::Error),
            _ => Err(format!("Invalid EngineStatus: {}", s)),
        }
    }
}

// ============================================================================
// Tests
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_order_side() {
        // 基本功能测试
        assert_eq!(OrderSide::Buy.to_string(), "buy");
        assert_eq!(OrderSide::Sell.to_string(), "sell");

        // 字符串解析测试
        assert_eq!("buy".parse::<OrderSide>().unwrap(), OrderSide::Buy);
        assert_eq!("SELL".parse::<OrderSide>().unwrap(), OrderSide::Sell);

        // 业务方法测试
        assert_eq!(OrderSide::Buy.opposite(), OrderSide::Sell);
        assert_eq!(OrderSide::Sell.opposite(), OrderSide::Buy);
        assert!(OrderSide::Buy.is_buy());
        assert!(OrderSide::Sell.is_sell());
    }

    #[test]
    fn test_order_type() {
        // 基本功能测试
        assert_eq!(OrderType::Market.to_string(), "market");
        assert_eq!(OrderType::Limit.to_string(), "limit");

        // 字符串解析测试
        assert_eq!("MARKET".parse::<OrderType>().unwrap(), OrderType::Market);
        assert_eq!("stop_loss".parse::<OrderType>().unwrap(), OrderType::StopLoss);

        // 业务方法测试
        assert!(OrderType::Limit.requires_price());
        assert!(!OrderType::Market.requires_price());
        assert!(OrderType::StopLoss.is_stop_order());
        assert!(OrderType::Market.is_market_order());
    }

    #[test]
    fn test_order_status() {
        // 基本功能测试
        assert_eq!(OrderStatus::Pending.to_string(), "pending");
        assert_eq!(OrderStatus::PartiallyFilled.to_string(), "partially_filled");

        // 字符串解析测试
        assert_eq!("PENDING".parse::<OrderStatus>().unwrap(), OrderStatus::Pending);
        assert_eq!("partial".parse::<OrderStatus>().unwrap(), OrderStatus::PartiallyFilled);

        // 业务方法测试
        assert!(OrderStatus::Pending.is_active());
        assert!(OrderStatus::Filled.is_final());
        assert!(OrderStatus::PartiallyFilled.is_filled());
        assert!(OrderStatus::Cancelled.is_cancelled_or_rejected());

        // 优先级测试
        assert!(OrderStatus::Pending.priority() < OrderStatus::Filled.priority());
    }

    #[test]
    fn test_timeframe() {
        // 基本功能测试
        assert_eq!(TimeFrame::M1.to_string(), "1m");
        assert_eq!(TimeFrame::H4.to_string(), "4h");

        // 字符串解析测试
        assert_eq!("1m".parse::<TimeFrame>().unwrap(), TimeFrame::M1);
        assert_eq!("H4".parse::<TimeFrame>().unwrap(), TimeFrame::H4);

        // 时间转换测试
        assert_eq!(TimeFrame::M1.to_seconds(), 60);
        assert_eq!(TimeFrame::H1.to_minutes(), 60);
        assert_eq!(TimeFrame::H4.to_hours(), 4.0);

        // 分类测试
        assert!(TimeFrame::M5.is_minute_based());
        assert!(TimeFrame::H1.is_hour_based());
        assert!(TimeFrame::D1.is_daily_or_longer());

        // 描述测试
        assert_eq!(TimeFrame::M15.description(), "15 minutes");
    }

    #[test]
    fn test_strategy_status() {
        // 基本功能测试
        assert_eq!(StrategyStatus::Active.to_string(), "active");
        assert_eq!(StrategyStatus::Paused.to_string(), "paused");

        // 字符串解析测试
        assert_eq!("ACTIVE".parse::<StrategyStatus>().unwrap(), StrategyStatus::Active);
        assert_eq!("running".parse::<StrategyStatus>().unwrap(), StrategyStatus::Active);
    }

    #[test]
    fn test_engine_type() {
        // 基本功能测试
        assert_eq!(EngineType::Backtest.to_string(), "backtest");
        assert_eq!(EngineType::Live.to_string(), "live");

        // 字符串解析测试
        assert_eq!("PAPER".parse::<EngineType>().unwrap(), EngineType::Paper);
    }

    #[test]
    fn test_engine_status() {
        // 基本功能测试
        assert_eq!(EngineStatus::Running.to_string(), "running");
        assert_eq!(EngineStatus::Stopped.to_string(), "stopped");

        // 字符串解析测试
        assert_eq!("STARTING".parse::<EngineStatus>().unwrap(), EngineStatus::Starting);
    }

    #[test]
    fn test_serialization() {
        // 测试序列化和反序列化
        let side = OrderSide::Buy;
        let json = serde_json::to_string(&side).unwrap();
        let deserialized: OrderSide = serde_json::from_str(&json).unwrap();
        assert_eq!(side, deserialized);

        let status = OrderStatus::PartiallyFilled;
        let json = serde_json::to_string(&status).unwrap();
        let deserialized: OrderStatus = serde_json::from_str(&json).unwrap();
        assert_eq!(status, deserialized);
    }
}

// ============================================================================
// 风险相关枚举 - 风险管理概念
// ============================================================================

/// 风险等级 - 风险程度分类
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum RiskLevel {
    VeryLow,
    Low,
    Medium,
    High,
}

impl fmt::Display for RiskLevel {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            RiskLevel::VeryLow => write!(f, "very_low"),
            RiskLevel::Low => write!(f, "low"),
            RiskLevel::Medium => write!(f, "medium"),
            RiskLevel::High => write!(f, "high"),
        }
    }
}

/// 风险严重程度 - 风险事件的严重性分类
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum RiskSeverity {
    Low,
    Medium,
    High,
    Critical,
}

impl fmt::Display for RiskSeverity {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            RiskSeverity::Low => write!(f, "low"),
            RiskSeverity::Medium => write!(f, "medium"),
            RiskSeverity::High => write!(f, "high"),
            RiskSeverity::Critical => write!(f, "critical"),
        }
    }
}

/// 风险事件类型 - 风险事件的分类
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum RiskEventType {
    DrawdownExceeded,
    VarExceeded,
    LiquidityLow,
    VolatilityHigh,
    ConcentrationHigh,
    LeverageExceeded,
    PositionSizeExceeded,
    DailyLossExceeded,
}

impl fmt::Display for RiskEventType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            RiskEventType::DrawdownExceeded => write!(f, "drawdown_exceeded"),
            RiskEventType::VarExceeded => write!(f, "var_exceeded"),
            RiskEventType::LiquidityLow => write!(f, "liquidity_low"),
            RiskEventType::VolatilityHigh => write!(f, "volatility_high"),
            RiskEventType::ConcentrationHigh => write!(f, "concentration_high"),
            RiskEventType::LeverageExceeded => write!(f, "leverage_exceeded"),
            RiskEventType::PositionSizeExceeded => write!(f, "position_size_exceeded"),
            RiskEventType::DailyLossExceeded => write!(f, "daily_loss_exceeded"),
        }
    }
}

/// 风险事件状态 - 风险事件的处理状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum RiskEventStatus {
    Active,
    Escalated,
    Resolved,
    Ignored,
}

impl fmt::Display for RiskEventStatus {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            RiskEventStatus::Active => write!(f, "active"),
            RiskEventStatus::Escalated => write!(f, "escalated"),
            RiskEventStatus::Resolved => write!(f, "resolved"),
            RiskEventStatus::Ignored => write!(f, "ignored"),
        }
    }
}

#[cfg(test)]
mod risk_enum_tests {
    use super::*;

    #[test]
    fn test_risk_level_display() {
        assert_eq!(RiskLevel::VeryLow.to_string(), "very_low");
        assert_eq!(RiskLevel::Low.to_string(), "low");
        assert_eq!(RiskLevel::Medium.to_string(), "medium");
        assert_eq!(RiskLevel::High.to_string(), "high");
    }

    #[test]
    fn test_risk_severity_display() {
        assert_eq!(RiskSeverity::Low.to_string(), "low");
        assert_eq!(RiskSeverity::Medium.to_string(), "medium");
        assert_eq!(RiskSeverity::High.to_string(), "high");
        assert_eq!(RiskSeverity::Critical.to_string(), "critical");
    }

    #[test]
    fn test_risk_event_type_display() {
        assert_eq!(RiskEventType::DrawdownExceeded.to_string(), "drawdown_exceeded");
        assert_eq!(RiskEventType::VarExceeded.to_string(), "var_exceeded");
        assert_eq!(RiskEventType::LiquidityLow.to_string(), "liquidity_low");
    }

    #[test]
    fn test_risk_event_status_display() {
        assert_eq!(RiskEventStatus::Active.to_string(), "active");
        assert_eq!(RiskEventStatus::Escalated.to_string(), "escalated");
        assert_eq!(RiskEventStatus::Resolved.to_string(), "resolved");
        assert_eq!(RiskEventStatus::Ignored.to_string(), "ignored");
    }

    #[test]
    fn test_risk_enum_serialization() {
        let level = RiskLevel::High;
        let json = serde_json::to_string(&level).unwrap();
        let deserialized: RiskLevel = serde_json::from_str(&json).unwrap();
        assert_eq!(level, deserialized);

        let severity = RiskSeverity::Critical;
        let json = serde_json::to_string(&severity).unwrap();
        let deserialized: RiskSeverity = serde_json::from_str(&json).unwrap();
        assert_eq!(severity, deserialized);
    }
}

// ============================================================================
// 通知相关枚举 - 通知管理概念
// ============================================================================

/// 通知类型 - 通知的分类
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum NotificationType {
    SystemAlert,
    SystemNotification,
    RiskAlert,
    TradingAlert,
    PerformanceReport,
    MaintenanceNotice,
    SecurityAlert,
}

impl fmt::Display for NotificationType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            NotificationType::SystemAlert => write!(f, "system_alert"),
            NotificationType::SystemNotification => write!(f, "system_notification"),
            NotificationType::RiskAlert => write!(f, "risk_alert"),
            NotificationType::TradingAlert => write!(f, "trading_alert"),
            NotificationType::PerformanceReport => write!(f, "performance_report"),
            NotificationType::MaintenanceNotice => write!(f, "maintenance_notice"),
            NotificationType::SecurityAlert => write!(f, "security_alert"),
        }
    }
}

/// 通知优先级 - 通知的重要程度
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum NotificationPriority {
    Critical,
    High,
    Medium,
    Low,
}

impl fmt::Display for NotificationPriority {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            NotificationPriority::Critical => write!(f, "critical"),
            NotificationPriority::High => write!(f, "high"),
            NotificationPriority::Medium => write!(f, "medium"),
            NotificationPriority::Low => write!(f, "low"),
        }
    }
}

/// 通知状态 - 通知的处理状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum NotificationStatus {
    Pending,
    Sent,
    Failed,
    Cancelled,
}

impl fmt::Display for NotificationStatus {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            NotificationStatus::Pending => write!(f, "pending"),
            NotificationStatus::Sent => write!(f, "sent"),
            NotificationStatus::Failed => write!(f, "failed"),
            NotificationStatus::Cancelled => write!(f, "cancelled"),
        }
    }
}

/// 通知渠道 - 通知发送的渠道
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum NotificationChannel {
    Email,
    Sms,
    InApp,
    Webhook,
    Slack,
    Discord,
}

impl fmt::Display for NotificationChannel {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            NotificationChannel::Email => write!(f, "email"),
            NotificationChannel::Sms => write!(f, "sms"),
            NotificationChannel::InApp => write!(f, "in_app"),
            NotificationChannel::Webhook => write!(f, "webhook"),
            NotificationChannel::Slack => write!(f, "slack"),
            NotificationChannel::Discord => write!(f, "discord"),
        }
    }
}

// ============================================================================
// 系统配置相关枚举 - 系统配置管理概念
// ============================================================================

/// 配置分类 - 配置的业务分类
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ConfigCategory {
    System,
    Database,
    Cache,
    Api,
    Security,
    Trading,
    Risk,
    Monitoring,
    Notification,
}

impl fmt::Display for ConfigCategory {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ConfigCategory::System => write!(f, "system"),
            ConfigCategory::Database => write!(f, "database"),
            ConfigCategory::Cache => write!(f, "cache"),
            ConfigCategory::Api => write!(f, "api"),
            ConfigCategory::Security => write!(f, "security"),
            ConfigCategory::Trading => write!(f, "trading"),
            ConfigCategory::Risk => write!(f, "risk"),
            ConfigCategory::Monitoring => write!(f, "monitoring"),
            ConfigCategory::Notification => write!(f, "notification"),
        }
    }
}

/// 配置类型 - 配置值的数据类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ConfigType {
    String,
    Integer,
    Float,
    Boolean,
    Json,
}

impl fmt::Display for ConfigType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ConfigType::String => write!(f, "string"),
            ConfigType::Integer => write!(f, "integer"),
            ConfigType::Float => write!(f, "float"),
            ConfigType::Boolean => write!(f, "boolean"),
            ConfigType::Json => write!(f, "json"),
        }
    }
}

/// 配置环境 - 配置适用的环境
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ConfigEnvironment {
    Development,
    Testing,
    Staging,
    Production,
}

impl fmt::Display for ConfigEnvironment {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ConfigEnvironment::Development => write!(f, "development"),
            ConfigEnvironment::Testing => write!(f, "testing"),
            ConfigEnvironment::Staging => write!(f, "staging"),
            ConfigEnvironment::Production => write!(f, "production"),
        }
    }
}

/// 配置操作 - 配置的变更操作类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ConfigAction {
    Create,
    Update,
    Delete,
}

impl fmt::Display for ConfigAction {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ConfigAction::Create => write!(f, "create"),
            ConfigAction::Update => write!(f, "update"),
            ConfigAction::Delete => write!(f, "delete"),
        }
    }
}

/// 配置变更来源 - 配置变更的触发来源
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ConfigChangeSource {
    Manual,
    Api,
    System,
    Migration,
    Import,
}

impl fmt::Display for ConfigChangeSource {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ConfigChangeSource::Manual => write!(f, "manual"),
            ConfigChangeSource::Api => write!(f, "api"),
            ConfigChangeSource::System => write!(f, "system"),
            ConfigChangeSource::Migration => write!(f, "migration"),
            ConfigChangeSource::Import => write!(f, "import"),
        }
    }
}

#[cfg(test)]
mod notification_config_enum_tests {
    use super::*;

    #[test]
    fn test_notification_type_display() {
        assert_eq!(NotificationType::SystemAlert.to_string(), "system_alert");
        assert_eq!(NotificationType::RiskAlert.to_string(), "risk_alert");
        assert_eq!(NotificationType::TradingAlert.to_string(), "trading_alert");
    }

    #[test]
    fn test_notification_priority_display() {
        assert_eq!(NotificationPriority::Critical.to_string(), "critical");
        assert_eq!(NotificationPriority::High.to_string(), "high");
        assert_eq!(NotificationPriority::Medium.to_string(), "medium");
        assert_eq!(NotificationPriority::Low.to_string(), "low");
    }

    #[test]
    fn test_notification_status_display() {
        assert_eq!(NotificationStatus::Pending.to_string(), "pending");
        assert_eq!(NotificationStatus::Sent.to_string(), "sent");
        assert_eq!(NotificationStatus::Failed.to_string(), "failed");
        assert_eq!(NotificationStatus::Cancelled.to_string(), "cancelled");
    }

    #[test]
    fn test_config_category_display() {
        assert_eq!(ConfigCategory::System.to_string(), "system");
        assert_eq!(ConfigCategory::Database.to_string(), "database");
        assert_eq!(ConfigCategory::Trading.to_string(), "trading");
    }

    #[test]
    fn test_config_type_display() {
        assert_eq!(ConfigType::String.to_string(), "string");
        assert_eq!(ConfigType::Integer.to_string(), "integer");
        assert_eq!(ConfigType::Boolean.to_string(), "boolean");
        assert_eq!(ConfigType::Json.to_string(), "json");
    }

    #[test]
    fn test_enum_serialization() {
        let notification_type = NotificationType::RiskAlert;
        let json = serde_json::to_string(&notification_type).unwrap();
        let deserialized: NotificationType = serde_json::from_str(&json).unwrap();
        assert_eq!(notification_type, deserialized);

        let config_type = ConfigType::Integer;
        let json = serde_json::to_string(&config_type).unwrap();
        let deserialized: ConfigType = serde_json::from_str(&json).unwrap();
        assert_eq!(config_type, deserialized);
    }
}

// ============================================================================
// 事件相关枚举 - 事件管理概念
// ============================================================================

/// 事件类型 - 事件的分类
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EventType {
    // 交易相关事件
    OrderCreated,
    OrderUpdated,
    OrderCancelled,
    OrderFilled,
    TradeExecuted,

    // 策略相关事件
    StrategyStarted,
    StrategyStopped,
    StrategyPaused,
    StrategyResumed,
    StrategyError,

    // 风险相关事件
    RiskThresholdExceeded,
    RiskLimitBreached,
    RiskAlert,

    // 系统相关事件
    SystemStarted,
    SystemStopped,
    SystemError,
    SystemWarning,

    // 引擎相关事件
    EngineStarted,
    EngineStopped,
    EnginePaused,
    EngineResumed,
    EngineError,

    // 通知相关事件
    NotificationSent,
    NotificationFailed,

    // 配置相关事件
    ConfigUpdated,
    ConfigCreated,
    ConfigDeleted,

    // 用户相关事件
    UserLogin,
    UserLogout,
    UserAction,
}

impl fmt::Display for EventType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            EventType::OrderCreated => write!(f, "order_created"),
            EventType::OrderUpdated => write!(f, "order_updated"),
            EventType::OrderCancelled => write!(f, "order_cancelled"),
            EventType::OrderFilled => write!(f, "order_filled"),
            EventType::TradeExecuted => write!(f, "trade_executed"),
            EventType::StrategyStarted => write!(f, "strategy_started"),
            EventType::StrategyStopped => write!(f, "strategy_stopped"),
            EventType::StrategyPaused => write!(f, "strategy_paused"),
            EventType::StrategyResumed => write!(f, "strategy_resumed"),
            EventType::StrategyError => write!(f, "strategy_error"),
            EventType::RiskThresholdExceeded => write!(f, "risk_threshold_exceeded"),
            EventType::RiskLimitBreached => write!(f, "risk_limit_breached"),
            EventType::RiskAlert => write!(f, "risk_alert"),
            EventType::SystemStarted => write!(f, "system_started"),
            EventType::SystemStopped => write!(f, "system_stopped"),
            EventType::SystemError => write!(f, "system_error"),
            EventType::SystemWarning => write!(f, "system_warning"),
            EventType::EngineStarted => write!(f, "engine_started"),
            EventType::EngineStopped => write!(f, "engine_stopped"),
            EventType::EnginePaused => write!(f, "engine_paused"),
            EventType::EngineResumed => write!(f, "engine_resumed"),
            EventType::EngineError => write!(f, "engine_error"),
            EventType::NotificationSent => write!(f, "notification_sent"),
            EventType::NotificationFailed => write!(f, "notification_failed"),
            EventType::ConfigUpdated => write!(f, "config_updated"),
            EventType::ConfigCreated => write!(f, "config_created"),
            EventType::ConfigDeleted => write!(f, "config_deleted"),
            EventType::UserLogin => write!(f, "user_login"),
            EventType::UserLogout => write!(f, "user_logout"),
            EventType::UserAction => write!(f, "user_action"),
        }
    }
}

/// 事件分类 - 事件的业务分类
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EventCategory {
    Trading,
    Strategy,
    Risk,
    System,
    Engine,
    Notification,
    Configuration,
    User,
    Audit,
}

impl fmt::Display for EventCategory {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            EventCategory::Trading => write!(f, "trading"),
            EventCategory::Strategy => write!(f, "strategy"),
            EventCategory::Risk => write!(f, "risk"),
            EventCategory::System => write!(f, "system"),
            EventCategory::Engine => write!(f, "engine"),
            EventCategory::Notification => write!(f, "notification"),
            EventCategory::Configuration => write!(f, "configuration"),
            EventCategory::User => write!(f, "user"),
            EventCategory::Audit => write!(f, "audit"),
        }
    }
}

/// 事件严重程度 - 事件的重要性级别
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EventSeverity {
    Debug,
    Info,
    Warning,
    Error,
    Critical,
}

impl fmt::Display for EventSeverity {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            EventSeverity::Debug => write!(f, "debug"),
            EventSeverity::Info => write!(f, "info"),
            EventSeverity::Warning => write!(f, "warning"),
            EventSeverity::Error => write!(f, "error"),
            EventSeverity::Critical => write!(f, "critical"),
        }
    }
}

/// 事件状态 - 事件的处理状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EventStatus {
    Pending,
    Processing,
    Processed,
    Failed,
    Cancelled,
}

impl fmt::Display for EventStatus {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            EventStatus::Pending => write!(f, "pending"),
            EventStatus::Processing => write!(f, "processing"),
            EventStatus::Processed => write!(f, "processed"),
            EventStatus::Failed => write!(f, "failed"),
            EventStatus::Cancelled => write!(f, "cancelled"),
        }
    }
}

/// 事件来源 - 事件的产生来源
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EventSource {
    System,
    User,
    Api,
    Strategy,
    Engine,
    External,
    Scheduler,
}

impl fmt::Display for EventSource {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            EventSource::System => write!(f, "system"),
            EventSource::User => write!(f, "user"),
            EventSource::Api => write!(f, "api"),
            EventSource::Strategy => write!(f, "strategy"),
            EventSource::Engine => write!(f, "engine"),
            EventSource::External => write!(f, "external"),
            EventSource::Scheduler => write!(f, "scheduler"),
        }
    }
}

/// 事件元数据类型 - 事件元数据的分类
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EventMetadataType {
    System,
    User,
    Business,
    Technical,
    Audit,
    Performance,
}

impl fmt::Display for EventMetadataType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            EventMetadataType::System => write!(f, "system"),
            EventMetadataType::User => write!(f, "user"),
            EventMetadataType::Business => write!(f, "business"),
            EventMetadataType::Technical => write!(f, "technical"),
            EventMetadataType::Audit => write!(f, "audit"),
            EventMetadataType::Performance => write!(f, "performance"),
        }
    }
}

#[cfg(test)]
mod event_enum_tests {
    use super::*;

    #[test]
    fn test_event_type_display() {
        assert_eq!(EventType::OrderCreated.to_string(), "order_created");
        assert_eq!(EventType::TradeExecuted.to_string(), "trade_executed");
        assert_eq!(EventType::StrategyStarted.to_string(), "strategy_started");
        assert_eq!(EventType::RiskAlert.to_string(), "risk_alert");
        assert_eq!(EventType::SystemError.to_string(), "system_error");
    }

    #[test]
    fn test_event_category_display() {
        assert_eq!(EventCategory::Trading.to_string(), "trading");
        assert_eq!(EventCategory::Strategy.to_string(), "strategy");
        assert_eq!(EventCategory::Risk.to_string(), "risk");
        assert_eq!(EventCategory::System.to_string(), "system");
    }

    #[test]
    fn test_event_severity_display() {
        assert_eq!(EventSeverity::Debug.to_string(), "debug");
        assert_eq!(EventSeverity::Info.to_string(), "info");
        assert_eq!(EventSeverity::Warning.to_string(), "warning");
        assert_eq!(EventSeverity::Error.to_string(), "error");
        assert_eq!(EventSeverity::Critical.to_string(), "critical");
    }

    #[test]
    fn test_event_status_display() {
        assert_eq!(EventStatus::Pending.to_string(), "pending");
        assert_eq!(EventStatus::Processing.to_string(), "processing");
        assert_eq!(EventStatus::Processed.to_string(), "processed");
        assert_eq!(EventStatus::Failed.to_string(), "failed");
        assert_eq!(EventStatus::Cancelled.to_string(), "cancelled");
    }

    #[test]
    fn test_event_source_display() {
        assert_eq!(EventSource::System.to_string(), "system");
        assert_eq!(EventSource::User.to_string(), "user");
        assert_eq!(EventSource::Api.to_string(), "api");
        assert_eq!(EventSource::Strategy.to_string(), "strategy");
    }

    #[test]
    fn test_event_enum_serialization() {
        let event_type = EventType::OrderCreated;
        let json = serde_json::to_string(&event_type).unwrap();
        let deserialized: EventType = serde_json::from_str(&json).unwrap();
        assert_eq!(event_type, deserialized);

        let event_category = EventCategory::Trading;
        let json = serde_json::to_string(&event_category).unwrap();
        let deserialized: EventCategory = serde_json::from_str(&json).unwrap();
        assert_eq!(event_category, deserialized);

        let event_severity = EventSeverity::Critical;
        let json = serde_json::to_string(&event_severity).unwrap();
        let deserialized: EventSeverity = serde_json::from_str(&json).unwrap();
        assert_eq!(event_severity, deserialized);
    }
}
