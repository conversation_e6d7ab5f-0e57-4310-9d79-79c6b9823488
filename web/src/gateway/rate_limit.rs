//! 限流服务实现

use std::sync::Arc;
use std::collections::HashMap;
use std::time::{Duration, Instant};
use axum::http::{Request, StatusCode};
use axum::body::Body;
use async_trait::async_trait;
use tokio::sync::RwLock;
use sigmax_core::SigmaXResult;
use crate::gateway::RateLimiter;

/// 限流记录
#[derive(Debug, Clone)]
struct RateLimitRecord {
    count: u32,
    reset_time: Instant,
}

/// 内存限流器实现
pub struct MemoryRateLimiter {
    limits: Arc<RwLock<HashMap<String, RateLimitRecord>>>,
    max_requests: u32,
    window_duration: Duration,
}

impl MemoryRateLimiter {
    /// 创建新的限流器
    pub fn new(max_requests: u32, window_duration: Duration) -> Self {
        Self {
            limits: Arc::new(RwLock::new(HashMap::new())),
            max_requests,
            window_duration,
        }
    }
    
    /// 获取客户端标识
    fn get_client_id(&self, request: &Request<Body>) -> String {
        // 简单实现：使用IP地址作为客户端标识
        // 生产环境可以使用更复杂的标识策略
        request.headers()
            .get("X-Forwarded-For")
            .or_else(|| request.headers().get("X-Real-IP"))
            .and_then(|h| h.to_str().ok())
            .unwrap_or("unknown")
            .to_string()
    }
    
    /// 清理过期的限流记录
    async fn cleanup_expired_records(&self) {
        let mut limits = self.limits.write().await;
        let now = Instant::now();
        limits.retain(|_, record| now < record.reset_time);
    }
}

#[async_trait]
impl RateLimiter for MemoryRateLimiter {
    async fn check_limit(&self, request: &Request<Body>) -> SigmaXResult<()> {
        // 定期清理过期记录
        self.cleanup_expired_records().await;
        
        let client_id = self.get_client_id(request);
        let now = Instant::now();
        
        let mut limits = self.limits.write().await;
        
        if let Some(record) = limits.get_mut(&client_id) {
            // 检查是否需要重置窗口
            if now >= record.reset_time {
                record.count = 1;
                record.reset_time = now + self.window_duration;
            } else {
                // 检查是否超过限制
                if record.count >= self.max_requests {
                    return Err(sigmax_core::SigmaXError::internal(
                        sigmax_core::InternalErrorCode::RateLimitExceeded,
                        "请求频率超过限制"
                    ));
                }
                record.count += 1;
            }
        } else {
            // 创建新的限流记录
            limits.insert(client_id, RateLimitRecord {
                count: 1,
                reset_time: now + self.window_duration,
            });
        }
        
        Ok(())
    }
}

/// 无限制流器（用于测试）
pub struct NoRateLimiter;

#[async_trait]
impl RateLimiter for NoRateLimiter {
    async fn check_limit(&self, _request: &Request<Body>) -> SigmaXResult<()> {
        Ok(())
    }
}
