//! SigmaX Core Trade Models
//!
//! ## 主要职责
//!
//! 1. **交易记录模型** - 核心成交概念
//!    - `Trade` - 统一的交易记录模型，记录实际成交信息
//!    - 不可变的交易记录，一旦创建不允许修改
//!
//! 2. **交易计算功能** - 实用的计算方法
//!    - 交易价值、净价值计算
//!    - 手续费和佣金计算
//!    - 盈亏分析功能
//!
//! 3. **交易分析功能** - 交易数据分析
//!    - 交易统计和聚合
//!    - 交易模式识别
//!    - 性能指标计算
//!
//! ## 设计原则
//!
//! - **不可变性**: 交易记录一旦创建不允许修改，保证数据完整性
//! - **统一模型**: 使用单一Trade模型，避免Record/View分离
//! - **类型安全**: 使用强类型的ID、Price、Quantity等类型
//! - **业务友好**: 提供实用的计算和分析方法

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use validator::{Validate, ValidationError};

use crate::{
    types::*,
    enums::*,
    error::*,
    SigmaXResult,
};

// ============================================================================
// 交易记录模型 - 核心成交概念
// ============================================================================

/// 交易记录 - 不可变的成交记录
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Trade {
    pub trade_id: TradeId,
    pub order_id: OrderId,
    pub exchange_id: ExchangeId,
    pub trading_pair: TradingPair,
    pub side: OrderSide,

    #[validate(custom = "validate_positive_quantity")]
    pub quantity: Quantity,

    #[validate(custom = "validate_positive_price")]
    pub price: Price,

    #[validate(custom = "validate_non_negative_amount")]
    pub fee: Amount,
    pub fee_asset: Option<String>,

    pub is_maker: Option<bool>,
    pub exchange_trade_id: Option<String>,
    pub executed_at: DateTime<Utc>,
}

impl Trade {
    /// 创建新的交易记录
    pub fn new(
        order_id: OrderId,
        exchange_id: ExchangeId,
        trading_pair: TradingPair,
        side: OrderSide,
        quantity: Quantity,
        price: Price,
        fee: Amount,
        fee_asset: Option<String>,
    ) -> Self {
        Self {
            trade_id: TradeId::new(),
            order_id,
            exchange_id,
            trading_pair,
            side,
            quantity,
            price,
            fee,
            fee_asset,
            is_maker: None,
            exchange_trade_id: None,
            executed_at: Utc::now(),
        }
    }

    /// 计算交易总价值（不含手续费）
    pub fn total_value(&self) -> Amount {
        self.quantity * self.price
    }

    /// 计算净价值（扣除手续费后）
    /// 注意：此计算假设手续费资产与计价资产相同
    pub fn net_value(&self) -> Amount {
        match self.side {
            OrderSide::Buy => self.total_value() + self.fee, // 买入时手续费增加成本
            OrderSide::Sell => self.total_value() - self.fee, // 卖出时手续费减少收入
        }
    }

    /// 计算手续费率
    pub fn fee_rate(&self) -> Percentage {
        let total = self.total_value();
        if total.is_zero() {
            return Percentage::ZERO;
        }
        (self.fee / total) * Percentage::from(100)
    }

    /// 获取显示用的交易名称
    pub fn display_name(&self) -> String {
        format!("{} {} {} @ {}",
            self.side,
            self.quantity,
            self.trading_pair.base,
            self.price
        )
    }

    /// 检查是否为做市商交易
    pub fn is_maker_trade(&self) -> bool {
        self.is_maker.unwrap_or(false)
    }

    /// 检查是否为吃单交易
    pub fn is_taker_trade(&self) -> bool {
        !self.is_maker_trade()
    }

    /// 计算交易对基础资产的变化量
    pub fn base_asset_change(&self) -> Quantity {
        match self.side {
            OrderSide::Buy => self.quantity,   // 买入增加基础资产
            OrderSide::Sell => -self.quantity, // 卖出减少基础资产
        }
    }

    /// 计算交易对计价资产的变化量（含手续费）
    pub fn quote_asset_change(&self) -> Amount {
        match self.side {
            OrderSide::Buy => -(self.total_value() + self.fee), // 买入减少计价资产
            OrderSide::Sell => self.total_value() - self.fee,   // 卖出增加计价资产
        }
    }

    /// 执行完整的交易记录验证
    pub fn validate_complete(&self) -> SigmaXResult<()> {
        // 首先执行基础验证（通过validator derive宏）
        if let Err(validation_errors) = <Self as Validate>::validate(self) {
            return Err(SigmaXError::from(validation_errors));
        }

        // 跨字段验证：交易对不能为空
        if self.trading_pair.base.is_empty() || self.trading_pair.quote.is_empty() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::Required,
                "trading_pair",
                "Trading pair base and quote cannot be empty"
            ));
        }

        Ok(())
    }
}

// ============================================================================
// 验证函数 - 用于模型验证
// ============================================================================

/// 验证数量为正数
fn validate_positive_quantity(quantity: &Quantity) -> Result<(), ValidationError> {
    if *quantity <= Quantity::ZERO {
        return Err(ValidationError::new("Trade quantity must be positive"));
    }
    Ok(())
}

/// 验证价格为正数
fn validate_positive_price(price: &Price) -> Result<(), ValidationError> {
    if *price <= Price::ZERO {
        return Err(ValidationError::new("Trade price must be positive"));
    }
    Ok(())
}

/// 验证金额非负数
fn validate_non_negative_amount(amount: &Amount) -> Result<(), ValidationError> {
    if *amount < Amount::ZERO {
        return Err(ValidationError::new("Trade fee cannot be negative"));
    }
    Ok(())
}

// ============================================================================
// Tests
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal::Decimal;

    #[test]
    fn test_trade_creation() {
        let trade = Trade::new(
            OrderId::new(),
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Buy,
            Decimal::from(1),
            Decimal::from(50000),
            Decimal::from(25), // 0.05% fee
            Some("USDT".to_string()),
        );

        assert_eq!(trade.side, OrderSide::Buy);
        assert_eq!(trade.quantity, Decimal::from(1));
        assert_eq!(trade.price, Decimal::from(50000));
        assert_eq!(trade.fee, Decimal::from(25));
        assert_eq!(trade.fee_asset, Some("USDT".to_string()));
    }

    #[test]
    fn test_trade_value_calculations() {
        let trade = Trade::new(
            OrderId::new(),
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Buy,
            Decimal::from(2),
            Decimal::from(50000),
            Decimal::from(50), // 手续费
            Some("USDT".to_string()),
        );

        // 总价值
        assert_eq!(trade.total_value(), Decimal::from(100000)); // 2 * 50000

        // 净价值（买入时手续费增加成本）
        assert_eq!(trade.net_value(), Decimal::from(100050)); // 100000 + 50

        // 手续费率
        assert_eq!(trade.fee_rate(), Decimal::new(5, 4)); // 0.05%
    }

    #[test]
    fn test_trade_sell_calculations() {
        let trade = Trade::new(
            OrderId::new(),
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Sell,
            Decimal::from(1),
            Decimal::from(50000),
            Decimal::from(25), // 手续费
            Some("USDT".to_string()),
        );

        // 净价值（卖出时手续费减少收入）
        assert_eq!(trade.net_value(), Decimal::from(49975)); // 50000 - 25
    }

    #[test]
    fn test_asset_changes() {
        let buy_trade = Trade::new(
            OrderId::new(),
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Buy,
            Decimal::from(1),
            Decimal::from(50000),
            Decimal::from(25),
            Some("USDT".to_string()),
        );

        // 买入：基础资产增加，计价资产减少
        assert_eq!(buy_trade.base_asset_change(), Decimal::from(1));
        assert_eq!(buy_trade.quote_asset_change(), Decimal::from(-50025)); // -(50000 + 25)

        let sell_trade = Trade::new(
            OrderId::new(),
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Sell,
            Decimal::from(1),
            Decimal::from(50000),
            Decimal::from(25),
            Some("USDT".to_string()),
        );

        // 卖出：基础资产减少，计价资产增加
        assert_eq!(sell_trade.base_asset_change(), Decimal::from(-1));
        assert_eq!(sell_trade.quote_asset_change(), Decimal::from(49975)); // 50000 - 25
    }

    #[test]
    fn test_maker_taker_identification() {
        let mut trade = Trade::new(
            OrderId::new(),
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Buy,
            Decimal::from(1),
            Decimal::from(50000),
            Decimal::from(25),
            Some("USDT".to_string()),
        );

        // 默认情况下不是做市商
        assert!(!trade.is_maker_trade());
        assert!(trade.is_taker_trade());

        // 设置为做市商交易
        trade.is_maker = Some(true);
        assert!(trade.is_maker_trade());
        assert!(!trade.is_taker_trade());
    }

    #[test]
    fn test_trade_display_name() {
        let trade = Trade::new(
            OrderId::new(),
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Buy,
            Decimal::from(1),
            Decimal::from(50000),
            Decimal::from(25),
            Some("USDT".to_string()),
        );

        assert_eq!(trade.display_name(), "buy 1 BTC @ 50000");
    }

    #[test]
    fn test_trade_validation() {
        let trade = Trade::new(
            OrderId::new(),
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Buy,
            Decimal::from(1),
            Decimal::from(50000),
            Decimal::from(25),
            Some("USDT".to_string()),
        );

        // 正常交易应该通过验证
        let result = trade.validate_complete();
        assert!(result.is_ok());
    }

    #[test]
    fn test_fee_rate_calculation() {
        let trade = Trade::new(
            OrderId::new(),
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Buy,
            Decimal::from(1),
            Decimal::from(10000),
            Decimal::from(10), // 0.1% fee
            Some("USDT".to_string()),
        );

        assert_eq!(trade.fee_rate(), Decimal::new(1, 1)); // 0.1%
    }

    #[test]
    fn test_zero_fee_trade() {
        let trade = Trade::new(
            OrderId::new(),
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Buy,
            Decimal::from(1),
            Decimal::from(50000),
            Decimal::ZERO, // 无手续费
            None,
        );

        assert_eq!(trade.fee, Decimal::ZERO);
        assert_eq!(trade.fee_rate(), Decimal::ZERO);
        assert_eq!(trade.net_value(), trade.total_value());
    }
}