//! 请求追踪服务实现

use std::sync::Arc;
use axum::http::{Request, StatusCode};
use axum::body::Body;
use async_trait::async_trait;
use tokio::sync::RwLock;
use std::collections::HashMap;
use std::time::Instant;
use uuid::Uuid;
use tracing::{info, warn, error};

use crate::gateway::RequestTracer;

/// 请求追踪记录
#[derive(Debug, Clone)]
pub struct RequestTrace {
    pub request_id: Uuid,
    pub method: String,
    pub path: String,
    pub client_ip: String,
    pub start_time: Instant,
    pub end_time: Option<Instant>,
    pub status: Option<StatusCode>,
    pub duration_ms: Option<u64>,
}

/// 内存请求追踪器实现
pub struct MemoryRequestTracer {
    traces: Arc<RwLock<HashMap<Uuid, RequestTrace>>>,
    max_traces: usize,
}

impl MemoryRequestTracer {
    /// 创建新的请求追踪器
    pub fn new(max_traces: usize) -> Self {
        Self {
            traces: Arc::new(RwLock::new(HashMap::new())),
            max_traces,
        }
    }
    
    /// 从请求中提取客户端IP
    fn extract_client_ip(&self, request: &Request<Body>) -> String {
        request.headers()
            .get("X-Forwarded-For")
            .or_else(|| request.headers().get("X-Real-IP"))
            .and_then(|h| h.to_str().ok())
            .unwrap_or("unknown")
            .to_string()
    }
    
    /// 清理旧的追踪记录
    async fn cleanup_old_traces(&self) {
        let mut traces = self.traces.write().await;
        if traces.len() > self.max_traces {
            // 简单策略：删除最旧的记录
            let mut sorted_traces: Vec<_> = traces.iter().collect();
            sorted_traces.sort_by_key(|(_, trace)| trace.start_time);
            
            let to_remove = traces.len() - self.max_traces;
            for (id, _) in sorted_traces.iter().take(to_remove) {
                traces.remove(id);
            }
        }
    }
}

#[async_trait]
impl RequestTracer for MemoryRequestTracer {
    async fn start_trace(&self, request_id: Uuid, request: &Request<Body>) {
        let trace = RequestTrace {
            request_id,
            method: request.method().to_string(),
            path: request.uri().path().to_string(),
            client_ip: self.extract_client_ip(request),
            start_time: Instant::now(),
            end_time: None,
            status: None,
            duration_ms: None,
        };
        
        // 记录追踪开始
        info!("🚀 请求开始追踪: {} {} {}", trace.method, trace.path, request_id);
        
        // 存储追踪记录
        let mut traces = self.traces.write().await;
        traces.insert(request_id, trace);
        
        // 清理旧记录
        self.cleanup_old_traces().await;
    }
    
    async fn end_trace(&self, request_id: Uuid, status: StatusCode) {
        let mut traces = self.traces.write().await;
        
        if let Some(trace) = traces.get_mut(&request_id) {
            trace.end_time = Some(Instant::now());
            trace.status = Some(status);
            trace.duration_ms = Some(
                trace.end_time.unwrap()
                    .duration_since(trace.start_time)
                    .as_millis() as u64
            );
            
            // 记录追踪结束
            let duration = trace.duration_ms.unwrap();
            let status_str = if status.is_success() { "✅" } else { "❌" };
            
            info!("🏁 请求追踪完成: {} {} {} ({}ms) - {}", 
                  trace.method, trace.path, request_id, duration, status_str);
            
            // 记录慢请求警告
            if duration > 1000 {
                warn!("🐌 慢请求检测: {} {} 耗时 {}ms", 
                      trace.method, trace.path, duration);
            }
            
            // 记录错误请求
            if status.is_client_error() || status.is_server_error() {
                error!("💥 请求失败: {} {} 状态码 {} 耗时 {}ms", 
                       trace.method, trace.path, status, duration);
            }
        }
    }
}

/// 无操作请求追踪器（用于测试）
pub struct NoOpRequestTracer;

#[async_trait]
impl RequestTracer for NoOpRequestTracer {
    async fn start_trace(&self, _request_id: Uuid, _request: &Request<Body>) {
        // 无操作
    }
    
    async fn end_trace(&self, _request_id: Uuid, _status: StatusCode) {
        // 无操作
    }
}
