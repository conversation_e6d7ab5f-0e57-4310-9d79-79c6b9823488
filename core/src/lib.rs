//! SigmaX Core Module
//!
//! 包含核心接口、数据模型和枚举，是系统稳定性的基石。

pub mod types;
pub mod models;
pub mod enums;
pub mod error;   // 新的错误处理系统
pub mod result;  // 结果类型扩展
pub mod retry;   // 重试策略模块
pub mod events;
pub mod config;
pub mod validation;
pub mod constants;
pub mod traits;  // 核心抽象接口



// 重新导出主要类型 - 明确导出以避免歧义
pub use enums::*;        // 枚举类型优先

// 从 types 模块明确导出基础类型
pub use types::{
    Price, Quantity, Amount, TradingPair, OrderId, TradeId, StrategyId, EngineId, ExchangeId,
    MarketData, PortfolioBalance
};

// 使用 types 模块的 RiskMetrics 作为主要版本
pub use types::RiskMetrics;

pub use models::*;       // 数据模型
pub use models::account::{Balance, Position, PositionSide}; // 明确导出账户相关类型

// 新的错误处理系统导出
pub use error::{SigmaXError, ErrorContext, ErrorSeverity, ErrorCategory};
pub use error::types::*; // 所有错误代码枚举
pub use result::{SigmaXResult, SigmaXResultExt, BatchResult, BatchResultExt};

// 重试策略模块导出
pub use retry::{RetryPolicy, RetryManager, RetryContext, RetryStats};

// 错误适配器导出 - 纯SigmaXError版本
pub use error::adapters::{
    ErrorAdapter, AdapterResultExt as SigmaXAdapterExt,
    adapt_error, adapt_results, adapt_option, from_any_error
};

pub use events::*;       // 事件类型
pub use config::*;       // 配置类型
pub use validation::*;   // 验证相关
pub use traits::*;       // 核心抽象接口
// 常量模块通过 mod constants 已经可用，不需要重新导出
