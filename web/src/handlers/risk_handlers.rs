//! 统一风险管理API处理器
//!
//! 实现企业级风险管理API的具体处理逻辑
//! 整合了所有风控功能，提供完整的风险管理能力

use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
};
use serde::{Deserialize, Serialize};
use rust_decimal::prelude::FromPrimitive;
use uuid::Uuid;
use std::collections::HashMap;
use tracing::{info, debug, warn};

use crate::{AppState, ApiResponse, ApiError};
use crate::services::unified_risk_service::{RuleResponse, UpdateRuleRequest};
use sigmax_core::{Order, Balance, SigmaXResult, RiskCheckResult};
use sigmax_risk::{
    ControlRiskMetrics as RiskMetrics,
};
use sigmax_core::config::risk::RiskConfigParameters;

// ============================================================================
// 风险检查处理器
// ============================================================================

/// 检查订单风险
pub async fn check_order_risk(
    State(state): State<AppState>,
    Json(request): Json<OrderRiskCheckRequest>,
) -> Result<Json<ApiResponse<RiskCheckResult>>, ApiError> {
    info!("收到订单风险检查请求");

    // 这里应该调用实际的风险服务
    // 暂时返回模拟结果
    let result = RiskCheckResult::pass();

    Ok(Json(ApiResponse::success(result, "Order risk check passed")))
}

/// 检查持仓风险
pub async fn check_position_risk(
    State(state): State<AppState>,
    Json(request): Json<PositionRiskCheckRequest>,
) -> Result<Json<ApiResponse<RiskCheckResult>>, ApiError> {
    info!("收到持仓风险检查请求");

    let result = RiskCheckResult::pass();
    Ok(Json(ApiResponse::success(result, "Position risk check passed")))
}

/// 检查投资组合风险
pub async fn check_portfolio_risk(
    State(state): State<AppState>,
    Json(request): Json<PortfolioRiskCheckRequest>,
) -> Result<Json<ApiResponse<RiskCheckResult>>, ApiError> {
    info!("收到投资组合风险检查请求");

    let result = RiskCheckResult::pass();
    Ok(Json(ApiResponse::success(result, "Portfolio risk check passed")))
}

/// 批量风险检查
pub async fn check_batch_risk(
    State(state): State<AppState>,
    Json(requests): Json<Vec<OrderRiskCheckRequest>>,
) -> Result<Json<ApiResponse<Vec<RiskCheckResult>>>, ApiError> {
    info!("收到批量风险检查请求，数量: {}", requests.len());

    let results: Vec<RiskCheckResult> = requests
        .into_iter()
        .map(|_req| RiskCheckResult::pass())
        .collect();

    Ok(Json(ApiResponse::success(results, "Batch risk checks completed")))
}

/// 智能风险评估
pub async fn smart_risk_assessment(
    State(state): State<AppState>,
    Json(request): Json<SmartRiskAssessmentRequest>,
) -> Result<Json<ApiResponse<serde_json::Value>>, ApiError> {
    info!("收到智能风险评估请求");
    
    // 模拟智能评估结果
    let smart_result = serde_json::json!({
        "basic_check": "pass",
        "current_metrics": {
            "overall_risk_score": 0.05,
            "position_risk_score": 0.08,
            "market_risk_score": 0.06,
            "liquidity_risk_score": 0.09,
            "volatility": 0.15,
            "var_1d": 0.15,
            "var_5d": 0.12,
            "cvar": 1.2,
            "max_drawdown": 0.12,
            "sharpe_ratio": 1.2,
            "calculated_at": chrono::Utc::now().to_rfc3339(),
            "details": {}
        },
        "adjustments": [],
        "recommendation": "当前风险水平可接受，可以正常交易"
    });
    
    Ok(Json(ApiResponse::success(smart_result, "Smart risk check completed")))
}

// ============================================================================
// 规则管理处理器
// ============================================================================

/// 列出风险规则
pub async fn list_rules(
    State(state): State<AppState>,
    Query(params): Query<ListRulesQuery>,
) -> Result<Json<ApiResponse<Vec<RiskRule>>>, ApiError> {
    info!("列出风险规则，策略类型: {:?}", params.strategy_type);
    
    // 返回空列表作为示例
    Ok(Json(ApiResponse::success(vec![], "Risk rules retrieved")))
}

/// 创建风险规则
pub async fn create_rule(
    State(state): State<AppState>,
    Json(rule): Json<CreateRuleRequest>,
) -> Result<Json<ApiResponse<Uuid>>, ApiError> {
    info!("创建风险规则: {}", rule.name);
    
    // TODO: 调用服务层方法来实现创建规则的完整数据库逻辑
    let rule_id = Uuid::new_v4();

    // 数据库操作成功后，刷新缓存
    // 暂时注释掉，因为unified_risk_engine不存在
    // if let Err(e) = state.unified_risk_service.reload_rules().await {
    //     warn!("创建规则后刷新缓存失败: {}", e);
    // }
    
    Ok(Json(ApiResponse::success(rule_id, "Risk rule created")))
}

/// 获取风险规则
pub async fn get_rule(
    State(state): State<AppState>,
    Path(rule_id): Path<Uuid>,
) -> Result<Json<ApiResponse<Option<RiskRule>>>, ApiError> {
    info!("获取风险规则: {}", rule_id);
    
    Ok(Json(ApiResponse::success(None, "Risk rule retrieved")))
}

/// 更新风险规则
pub async fn update_rule(
    State(state): State<AppState>,
    Path(rule_id): Path<Uuid>,
    Json(req): Json<UpdateRuleRequest>,
) -> Result<Json<ApiResponse<RuleResponse>>, ApiError> {
    info!("更新风险规则: {}", rule_id);

    // 从服务层调用更新逻辑
    let updated_rule = state.unified_risk_service.update_rule(rule_id, req).await?;

    // 数据库操作成功后，立即刷新缓存
    info!("规则 {} 已更新，正在刷新风控引擎缓存...", rule_id);
    // 暂时注释掉，因为unified_risk_engine不存在
    // if let Err(e) = state.unified_risk_service.reload_rules().await {
    //     warn!("更新规则后刷新缓存失败: {}", e);
    // }
    
    Ok(Json(ApiResponse::success(updated_rule, "Risk rule updated and cache reloaded")))
}

/// 删除风险规则
pub async fn delete_rule(
    State(state): State<AppState>,
    Path(rule_id): Path<Uuid>,
) -> Result<Json<ApiResponse<()>>, ApiError> {
    info!("删除风险规则: {}", rule_id);
    
    // TODO: 实现删除规则的完整数据库逻辑

    // 数据库操作成功后，刷新缓存
    // 暂时注释掉，因为unified_risk_engine不存在
    // if let Err(e) = state.unified_risk_service.reload_rules().await {
    //     warn!("删除规则后刷新缓存失败: {}", e);
    // }

    Ok(Json(ApiResponse::success((), "Risk rule deleted")))
}

/// 启用/禁用规则
pub async fn toggle_rule(
    State(state): State<AppState>,
    Path(rule_id): Path<Uuid>,
    Json(request): Json<ToggleRuleRequest>,
) -> Result<Json<ApiResponse<RuleResponse>>, ApiError> {
    info!("切换规则状态: {} -> {}", rule_id, request.enabled);
    
    // 调用服务层方法
    let updated_rule = state.unified_risk_service.toggle_rule(rule_id, request.enabled).await?;

    // 数据库操作成功后，刷新缓存
    info!("规则 {} 状态已切换，正在刷新风控引擎缓存...", rule_id);
    // 暂时注释掉，因为unified_risk_engine不存在
    // if let Err(e) = state.unified_risk_service.reload_rules().await {
    //     warn!("切换规则状态后刷新缓存失败: {}", e);
    // }
    
    Ok(Json(ApiResponse::success(updated_rule, "Rule status toggled and cache reloaded")))
}

/// 获取规则历史
pub async fn get_rule_history(
    State(state): State<AppState>,
    Path(rule_id): Path<Uuid>,
    Query(params): Query<HistoryQuery>,
) -> Result<Json<ApiResponse<Vec<RuleExecutionRecord>>>, ApiError> {
    info!("获取规则执行历史: {}", rule_id);
    
    Ok(Json(ApiResponse::success(vec![], "Rule execution history retrieved")))
}

/// 获取规则模板
pub async fn get_rule_templates(
    State(state): State<AppState>,
) -> Result<Json<ApiResponse<Vec<RuleTemplate>>>, ApiError> {
    info!("获取规则模板");
    
    Ok(Json(ApiResponse::success(vec![], "Rule templates retrieved")))
}

// ============================================================================
// 监控处理器
// ============================================================================

/// 获取实时风险指标
pub async fn get_real_time_metrics(
    State(state): State<AppState>,
) -> Result<Json<ApiResponse<RiskMetrics>>, ApiError> {
    info!("获取实时风险指标");
    
    let metrics = RiskMetrics {
        overall_risk_score: rust_decimal::Decimal::from_f64(0.05).unwrap_or_default(),
        position_risk_score: rust_decimal::Decimal::from_f64(0.08).unwrap_or_default(),
        market_risk_score: rust_decimal::Decimal::from_f64(0.06).unwrap_or_default(),
        liquidity_risk_score: rust_decimal::Decimal::from_f64(0.09).unwrap_or_default(),
        volatility: rust_decimal::Decimal::from_f64(0.15).unwrap_or_default(),
        var_1d: Some(rust_decimal::Decimal::from_f64(0.15).unwrap_or_default()),
        var_5d: Some(rust_decimal::Decimal::from_f64(0.12).unwrap_or_default()),
        cvar: Some(rust_decimal::Decimal::from_f64(1.2).unwrap_or_default()),
        max_drawdown: Some(rust_decimal::Decimal::from_f64(0.12).unwrap_or_default()),
        sharpe_ratio: Some(rust_decimal::Decimal::from_f64(1.2).unwrap_or_default()),
        calculated_at: chrono::Utc::now(),
        details: std::collections::HashMap::new(),
    };
    
    Ok(Json(ApiResponse::success(metrics, "Real-time risk metrics retrieved")))
}

/// 获取风险趋势
pub async fn get_risk_trends(
    State(state): State<AppState>,
    Query(params): Query<TrendsQuery>,
) -> Result<Json<ApiResponse<Vec<RiskMetrics>>>, ApiError> {
    info!("获取风险趋势，时间段: {:?}", params.period);
    
    Ok(Json(ApiResponse::success(vec![], "Risk trends retrieved")))
}

/// 获取预警信息
pub async fn get_alerts(
    State(state): State<AppState>,
    Query(params): Query<AlertsQuery>,
) -> Result<Json<ApiResponse<Vec<RiskAlert>>>, ApiError> {
    info!("获取预警信息");
    
    Ok(Json(ApiResponse::success(vec![], "Risk alerts retrieved")))
}

/// 创建预警规则
pub async fn create_alert_rule(
    State(state): State<AppState>,
    Json(rule): Json<CreateAlertRuleRequest>,
) -> Result<Json<ApiResponse<Uuid>>, ApiError> {
    info!("创建预警规则");
    
    let alert_id = Uuid::new_v4();
    Ok(Json(ApiResponse::success(alert_id, "Alert rule created")))
}

/// 获取风险仪表板
pub async fn get_risk_dashboard(
    State(state): State<AppState>,
) -> Result<Json<ApiResponse<RiskDashboard>>, ApiError> {
    info!("获取风险仪表板");
    
    let dashboard = RiskDashboard {
        current_metrics: RiskMetrics {
            overall_risk_score: rust_decimal::Decimal::from_f64(0.05).unwrap_or_default(),
            position_risk_score: rust_decimal::Decimal::from_f64(0.08).unwrap_or_default(),
            market_risk_score: rust_decimal::Decimal::from_f64(0.06).unwrap_or_default(),
            liquidity_risk_score: rust_decimal::Decimal::from_f64(0.09).unwrap_or_default(),
            volatility: rust_decimal::Decimal::from_f64(0.15).unwrap_or_default(),
            var_1d: Some(rust_decimal::Decimal::from_f64(0.15).unwrap_or_default()),
            var_5d: Some(rust_decimal::Decimal::from_f64(0.12).unwrap_or_default()),
            cvar: Some(rust_decimal::Decimal::from_f64(1.2).unwrap_or_default()),
            max_drawdown: Some(rust_decimal::Decimal::from_f64(0.12).unwrap_or_default()),
            sharpe_ratio: Some(rust_decimal::Decimal::from_f64(1.2).unwrap_or_default()),
            calculated_at: chrono::Utc::now(),
            details: std::collections::HashMap::new(),
        },
        active_alerts: vec![],
        recent_violations: vec![],
        system_status: "healthy".to_string(),
    };
    
    Ok(Json(ApiResponse::success(dashboard, "Risk dashboard retrieved")))
}

// ============================================================================
// 配置管理处理器
// ============================================================================

/// 获取风险参数
pub async fn get_risk_parameters(
    State(state): State<AppState>,
) -> Result<Json<ApiResponse<RiskConfigParameters>>, ApiError> {
    info!("获取风险参数");

    let parameters = RiskConfigParameters::default();
    Ok(Json(ApiResponse::success(parameters, "Risk parameters retrieved")))
}

/// 更新风险参数
pub async fn update_risk_parameters(
    State(state): State<AppState>,
    Json(parameters): Json<RiskConfigParameters>,
) -> Result<Json<ApiResponse<()>>, ApiError> {
    info!("更新风险参数");

    Ok(Json(ApiResponse::success((), "Risk parameters updated")))
}

/// 获取风险配置
pub async fn get_risk_config(
    State(state): State<AppState>,
) -> Result<Json<ApiResponse<RiskConfig>>, ApiError> {
    info!("获取风险配置");

    let config = RiskConfig {
        parameters: RiskConfigParameters::default(),
        enabled_features: vec!["var_checking".to_string(), "position_limits".to_string()],
        alert_settings: AlertSettings {
            email_enabled: true,
            webhook_url: None,
            notification_threshold: 0.8,
        },
    };

    Ok(Json(ApiResponse::success(config, "Risk configuration retrieved")))
}

/// 更新风险配置
pub async fn update_risk_config(
    State(state): State<AppState>,
    Json(config): Json<UpdateRiskConfigRequest>,
) -> Result<Json<ApiResponse<()>>, ApiError> {
    info!("更新风险配置");

    Ok(Json(ApiResponse::success((), "Risk configuration updated")))
}

// ============================================================================
// 分析工具处理器
// ============================================================================

/// 运行压力测试
pub async fn run_stress_test(
    State(state): State<AppState>,
    Json(request): Json<StressTestRequest>,
) -> Result<Json<ApiResponse<StressTestResult>>, ApiError> {
    info!("运行压力测试: {:?}", request.scenario_type);

    let result = StressTestResult {
        test_id: Uuid::new_v4(),
        scenario_type: request.scenario_type,
        start_time: chrono::Utc::now(),
        end_time: chrono::Utc::now(),
        portfolio_value_before: 100000.0,
        portfolio_value_after: 85000.0,
        max_loss: 15000.0,
        recovery_time_estimate: Some(chrono::Duration::days(30)),
        detailed_results: HashMap::new(),
    };

    Ok(Json(ApiResponse::success(result, "Stress test completed")))
}

/// 运行蒙特卡洛模拟
pub async fn run_monte_carlo_simulation(
    State(state): State<AppState>,
    Json(request): Json<MonteCarloRequest>,
) -> Result<Json<ApiResponse<MonteCarloResult>>, ApiError> {
    info!("运行蒙特卡洛模拟，迭代次数: {}", request.iterations);

    let result = MonteCarloResult {
        simulation_id: Uuid::new_v4(),
        iterations: request.iterations,
        confidence_intervals: HashMap::new(),
        var_estimates: HashMap::new(),
        expected_return: 0.08,
        volatility: 0.15,
        max_drawdown_distribution: vec![],
    };

    Ok(Json(ApiResponse::success(result, "Monte Carlo simulation completed")))
}

/// 运行敏感性分析
pub async fn run_sensitivity_analysis(
    State(state): State<AppState>,
    Json(request): Json<SensitivityRequest>,
) -> Result<Json<ApiResponse<SensitivityResult>>, ApiError> {
    info!("运行敏感性分析");

    let result = SensitivityResult {
        analysis_id: Uuid::new_v4(),
        parameter_sensitivities: HashMap::new(),
        risk_factor_impacts: HashMap::new(),
        scenario_results: HashMap::new(),
    };

    Ok(Json(ApiResponse::success(result, "Sensitivity analysis completed")))
}

/// 获取分析历史
pub async fn get_analysis_history(
    State(state): State<AppState>,
    Query(params): Query<AnalysisHistoryQuery>,
) -> Result<Json<ApiResponse<Vec<AnalysisRecord>>>, ApiError> {
    info!("获取分析历史");

    Ok(Json(ApiResponse::success(vec![], "Analysis history retrieved")))
}

// ============================================================================
// 系统状态处理器
// ============================================================================

/// 健康检查
pub async fn health_check(
    State(state): State<AppState>,
) -> Result<Json<ApiResponse<HealthStatus>>, ApiError> {
    info!("执行健康检查");

    let health = HealthStatus {
        status: "healthy".to_string(),
        version: "2.0.0".to_string(),
        uptime: chrono::Duration::hours(24),
        components: HashMap::from([
            ("risk_engine".to_string(), "healthy".to_string()),
            ("database".to_string(), "healthy".to_string()),
            ("cache".to_string(), "healthy".to_string()),
        ]),
    };

    Ok(Json(ApiResponse::success(health, "Health check completed")))
}

/// 获取系统状态
pub async fn get_system_status(
    State(state): State<AppState>,
) -> Result<Json<ApiResponse<SystemStatus>>, ApiError> {
    info!("获取系统状态");

    let status = SystemStatus {
        risk_engine_status: "running".to_string(),
        active_rules_count: 15,
        processed_checks_today: 1250,
        average_response_time_ms: 25,
        memory_usage_mb: 512,
        cpu_usage_percent: 15.5,
        last_restart: chrono::Utc::now() - chrono::Duration::hours(24),
    };

    Ok(Json(ApiResponse::success(status, "System status retrieved")))
}

// ============================================================================
// 请求/响应类型定义
// ============================================================================

#[derive(Debug, Deserialize)]
pub struct OrderRiskCheckRequest {
    pub order: Order,
    pub strategy_type: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct PositionRiskCheckRequest {
    pub balances: Vec<Balance>,
    pub strategy_type: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct PortfolioRiskCheckRequest {
    pub portfolio: serde_json::Value,
    pub strategy_type: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct SmartRiskAssessmentRequest {
    pub order: Order,
    pub context: serde_json::Value,
}

#[derive(Debug, Deserialize)]
pub struct ListRulesQuery {
    pub strategy_type: Option<String>,
    pub enabled: Option<bool>,
    pub limit: Option<usize>,
    pub offset: Option<usize>,
}

#[derive(Debug, Deserialize)]
pub struct CreateRuleRequest {
    pub name: String,
    pub description: Option<String>,
    pub rule_type: String,
    pub parameters: HashMap<String, serde_json::Value>,
    pub strategy_types: Vec<String>,
    pub trading_pairs: Vec<String>,
    pub priority: Option<i32>,
}


#[derive(Debug, Deserialize)]
pub struct ToggleRuleRequest {
    pub enabled: bool,
}

#[derive(Debug, Deserialize)]
pub struct HistoryQuery {
    pub limit: Option<usize>,
    pub offset: Option<usize>,
    pub start_time: Option<chrono::DateTime<chrono::Utc>>,
    pub end_time: Option<chrono::DateTime<chrono::Utc>>,
}

#[derive(Debug, Deserialize)]
pub struct TrendsQuery {
    pub period: Option<String>, // "1h", "1d", "1w", "1m"
    pub limit: Option<usize>,
}

#[derive(Debug, Deserialize)]
pub struct AlertsQuery {
    pub severity: Option<String>,
    pub resolved: Option<bool>,
    pub limit: Option<usize>,
}

#[derive(Debug, Deserialize)]
pub struct CreateAlertRuleRequest {
    pub name: String,
    pub condition: String,
    pub threshold: f64,
    pub severity: String,
}

// ============================================================================
// 响应类型定义
// ============================================================================

#[derive(Debug, Serialize)]
pub struct RuleTemplate {
    pub id: String,
    pub name: String,
    pub description: String,
    pub rule_type: String,
    pub default_parameters: HashMap<String, serde_json::Value>,
}

// 占位符，因为 risk_handlers.rs 中引用了 RiskRule。
// 注意：在真实的实现中，应该有一个共享的 DTO 或视图模型。
#[derive(Debug, Serialize)]
pub struct RiskRule {}

#[derive(Debug, Serialize)]
pub struct RuleExecutionRecord {
    pub id: Uuid,
    pub rule_id: Uuid,
    pub executed_at: chrono::DateTime<chrono::Utc>,
    pub result: bool,
    pub execution_time_ms: u64,
    pub details: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct RiskAlert {
    pub id: Uuid,
    pub alert_type: String,
    pub severity: String,
    pub message: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub resolved: bool,
}

#[derive(Debug, Serialize)]
pub struct RiskDashboard {
    pub current_metrics: RiskMetrics,
    pub active_alerts: Vec<RiskAlert>,
    pub recent_violations: Vec<String>,
    pub system_status: String,
}

#[derive(Debug, Serialize)]
pub struct RiskConfig {
    pub parameters: RiskConfigParameters,
    pub enabled_features: Vec<String>,
    pub alert_settings: AlertSettings,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AlertSettings {
    pub email_enabled: bool,
    pub webhook_url: Option<String>,
    pub notification_threshold: f64,
}

#[derive(Debug, Deserialize)]
pub struct UpdateRiskConfigRequest {
    pub parameters: Option<RiskConfigParameters>,
    pub enabled_features: Option<Vec<String>>,
    pub alert_settings: Option<AlertSettings>,
}

#[derive(Debug, Deserialize)]
pub struct StressTestRequest {
    pub scenario_type: String,
    pub parameters: HashMap<String, f64>,
}

#[derive(Debug, Serialize)]
pub struct StressTestResult {
    pub test_id: Uuid,
    pub scenario_type: String,
    pub start_time: chrono::DateTime<chrono::Utc>,
    pub end_time: chrono::DateTime<chrono::Utc>,
    pub portfolio_value_before: f64,
    pub portfolio_value_after: f64,
    pub max_loss: f64,
    pub recovery_time_estimate: Option<chrono::Duration>,
    pub detailed_results: HashMap<String, f64>,
}

#[derive(Debug, Deserialize)]
pub struct MonteCarloRequest {
    pub iterations: usize,
    pub time_horizon: usize,
    pub parameters: HashMap<String, f64>,
}

#[derive(Debug, Serialize)]
pub struct MonteCarloResult {
    pub simulation_id: Uuid,
    pub iterations: usize,
    pub confidence_intervals: HashMap<String, (f64, f64)>,
    pub var_estimates: HashMap<String, f64>,
    pub expected_return: f64,
    pub volatility: f64,
    pub max_drawdown_distribution: Vec<f64>,
}

#[derive(Debug, Deserialize)]
pub struct SensitivityRequest {
    pub parameters: Vec<String>,
    pub shock_size: f64,
}

#[derive(Debug, Serialize)]
pub struct SensitivityResult {
    pub analysis_id: Uuid,
    pub parameter_sensitivities: HashMap<String, f64>,
    pub risk_factor_impacts: HashMap<String, f64>,
    pub scenario_results: HashMap<String, f64>,
}

#[derive(Debug, Deserialize)]
pub struct AnalysisHistoryQuery {
    pub analysis_type: Option<String>,
    pub limit: Option<usize>,
    pub start_date: Option<chrono::DateTime<chrono::Utc>>,
    pub end_date: Option<chrono::DateTime<chrono::Utc>>,
}

#[derive(Debug, Serialize)]
pub struct AnalysisRecord {
    pub id: Uuid,
    pub analysis_type: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub status: String,
    pub summary: String,
}

#[derive(Debug, Serialize)]
pub struct HealthStatus {
    pub status: String,
    pub version: String,
    pub uptime: chrono::Duration,
    pub components: HashMap<String, String>,
}

#[derive(Debug, Serialize)]
pub struct SystemStatus {
    pub risk_engine_status: String,
    pub active_rules_count: usize,
    pub processed_checks_today: usize,
    pub average_response_time_ms: u64,
    pub memory_usage_mb: usize,
    pub cpu_usage_percent: f64,
    pub last_restart: chrono::DateTime<chrono::Utc>,
}
