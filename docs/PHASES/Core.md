# Core 模块开发地图

> **Core契约定义** - SigmaX系统基础设施层代码开发指南，这是一个纯粹的代码契约文档，专注于技术实现细节。

## 📁 模块结构
  ### Core模块职责: 专注于数据模型、类型定义、枚举、错误处理等基础概念

```
core/
├── src/
│   ├── lib.rs              # 显式导出 (禁止通配导出)
│   ├── types.rs            # 基础类型定义
│   ├── enums.rs            # 枚举定义
│   ├── models/             # 数据模型
│   │   ├── mod.rs
│   │   ├── strategy.rs
│   │   ├── order.rs
│   │   ├── trade.rs
│   │   ├── portfolio.rs
│   │   └── risk.rs
│   ├── error/              # 错误处理系统
│   │   ├── mod.rs
│   │   ├── types.rs        # 错误分类和错误码
│   │   └── context.rs      # 错误上下文
│   ├── events.rs           # 事件系统
│   ├── traits.rs           # 核心抽象接口
│   ├── config/             # 配置管理
│   │   ├── mod.rs
│   │   ├── app.rs          # 应用配置
│   │   ├── database.rs     # 数据库配置
│   │   ├── cache.rs        # 缓存配置
│   │   ├── risk.rs         # 风险配置
│   │   ├── monitoring.rs   # 监控配置
│   │   └── trading.rs      # 交易配置
│   ├── validation/         # 数据验证
│   │   ├── mod.rs
│   │   ├── validators.rs   # 验证器实现
│   │   └── rules.rs        # 验证规则
└── Cargo.toml
```

## 🔧 基础类型 (`types.rs`)

```rust
use uuid::Uuid;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

// === ID Types (UUID newtype) ===
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct StrategyId(pub Uuid);

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct OrderId(pub Uuid);

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct TradeId(pub Uuid);

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct EngineId(pub Uuid);

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct PortfolioId(pub Uuid);

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct RiskId(pub Uuid);

// === Numeric Types (Decimal only for financial calculations) ===
pub type Amount = Decimal;
pub type Price = Decimal;
pub type Quantity = Decimal;
pub type Percentage = Decimal;
pub type Fee = Decimal;

// === Trading Types ===
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct TradingPair {
    pub base: String,
    pub quote: String,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ExchangeId {
    Binance,
    Coinbase,
    Kraken,
    OKX,
    Simulator,
}

// === Market Data Types ===
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketData {
    pub trading_pair: TradingPair,
    pub timestamp: DateTime<Utc>,
    pub open: Price,
    pub high: Price,
    pub low: Price,
    pub close: Price,
    pub volume: Quantity,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortfolioBalance {
    pub asset: String,
    pub free: Amount,
    pub locked: Amount,
    pub total: Amount,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskMetrics {
    pub strategy_id: StrategyId,
    pub max_drawdown: Percentage,
    pub sharpe_ratio: Option<Decimal>,
    pub var_95: Amount,
    pub exposure: Amount,
    pub calculated_at: DateTime<Utc>,
}
```

## 🏷️ 枚举定义 (`enums.rs`)

```rust
use serde::{Deserialize, Serialize};

// === 交易相关枚举 ===
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum OrderSide {
    Buy,
    Sell,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum OrderType {
    Market,
    Limit,
    StopLoss,
    StopLimit,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum OrderStatus {
    Pending,
    PartiallyFilled,
    Filled,
    Cancelled,
    Rejected,
    Expired,
}

// === 策略相关枚举 ===
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum StrategyStatus {
    Active,
    Paused,
    Stopped,
    Error,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EngineType {
    Backtest,
    Live,
    Paper,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EngineStatus {
    Starting,
    Running,
    Paused,
    Stopped,
    Error,
}

// === 风险相关枚举 ===
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
    Critical,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum RiskSeverity {
    Info,
    Warning,
    Error,
    Critical,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AlertStatus {
    Active,
    Acknowledged,
    Resolved,
    Dismissed,
}

// === 时间相关枚举 ===
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum TimeFrame {
    M1,  // 1 minute
    M5,  // 5 minutes
    M15, // 15 minutes
    M30, // 30 minutes
    H1,  // 1 hour
    H4,  // 4 hours
    D1,  // 1 day
    W1,  // 1 week
    MN1, // 1 month
}

// === 通知相关枚举 ===
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum NotificationStatus {
    Pending,
    Sent,
    Failed,
    Acknowledged,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum NotificationType {
    Info,
    Warning,
    Error,
    Alert,
}
```
## ⚙️ 配置管理 (`config/*`)

### 应用配置 (`config/app.rs`)
```rust
use serde::{Deserialize, Serialize};
use std::time::Duration;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    pub database: DatabaseConfig,
    pub cache: CacheConfig,
    pub risk: RiskConfig,
    pub monitoring: MonitoringConfig,
    pub trading: TradingConfig,
    pub engine: EngineConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub connection_timeout: Duration,
    pub idle_timeout: Duration,
    pub max_lifetime: Duration,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheConfig {
    pub redis_url: String,
    pub max_connections: u32,
    pub connection_timeout: Duration,
    pub default_ttl: Duration,
    pub key_prefix: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskConfig {
    pub max_position_size: Decimal,
    pub max_daily_loss: Decimal,
    pub max_drawdown: Decimal,
    pub var_confidence_level: Decimal,
    pub check_interval: Duration,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringConfig {
    pub metrics_enabled: bool,
    pub metrics_port: u16,
    pub health_check_interval: Duration,
    pub log_level: String,
    pub trace_enabled: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradingConfig {
    pub default_exchange: ExchangeId,
    pub order_timeout: Duration,
    pub max_slippage: Decimal,
    pub min_order_size: Decimal,
    pub fee_rate: Decimal,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineConfig {
    pub backtest_data_path: String,
    pub live_trading_enabled: bool,
    pub paper_trading_enabled: bool,
    pub max_concurrent_strategies: u32,
    pub heartbeat_interval: Duration,
}
```

### 配置管理接口 (`config/mod.rs`)
```rust
use async_trait::async_trait;
use crate::error::SigmaXResult;

#[async_trait]
pub trait ConfigManager: Send + Sync {
    async fn load_config(&self) -> SigmaXResult<AppConfig>;
    async fn get<T>(&self, key: &str) -> SigmaXResult<Option<T>>
    where
        T: serde::de::DeserializeOwned + Send;
    async fn set<T>(&self, key: &str, value: T) -> SigmaXResult<()>
    where
        T: serde::Serialize + Send;
    async fn reload(&self) -> SigmaXResult<()>;
    async fn validate_config(&self, config: &AppConfig) -> SigmaXResult<()>;
}

#[async_trait]
pub trait ConfigWatcher: Send + Sync {
    async fn watch_changes(&self) -> SigmaXResult<()>;
    async fn on_config_changed(&self, key: &str) -> SigmaXResult<()>;
}
```

## 🎯 核心抽象接口 (`traits.rs`)

### 策略抽象
```rust
use async_trait::async_trait;
use crate::{types::*, enums::*, error::SigmaXResult};

#[async_trait]
pub trait Strategy: Send + Sync {
    fn id(&self) -> StrategyId;
    fn name(&self) -> &str;
    fn strategy_type(&self) -> &str;

    async fn initialize(&self, config: serde_json::Value) -> SigmaXResult<()>;
    async fn on_market_data(&self, data: &MarketData) -> SigmaXResult<Vec<OrderRequest>>;
    async fn on_order_update(&self, order: &OrderUpdate) -> SigmaXResult<()>;
    async fn on_trade_update(&self, trade: &TradeUpdate) -> SigmaXResult<()>;
    async fn shutdown(&self) -> SigmaXResult<()>;
}

#[derive(Debug, Clone)]
pub struct OrderRequest {
    pub trading_pair: TradingPair,
    pub side: OrderSide,
    pub order_type: OrderType,
    pub quantity: Quantity,
    pub price: Option<Price>,
    pub stop_price: Option<Price>,
}

#[derive(Debug, Clone)]
pub struct OrderUpdate {
    pub order_id: OrderId,
    pub status: OrderStatus,
    pub filled_quantity: Quantity,
    pub avg_fill_price: Option<Price>,
}

#[derive(Debug, Clone)]
pub struct TradeUpdate {
    pub trade_id: TradeId,
    pub order_id: OrderId,
    pub price: Price,
    pub quantity: Quantity,
    pub fee: Fee,
}
```

### 投资组合管理抽象
```rust
#[async_trait]
pub trait PortfolioManager: Send + Sync {
    async fn get_balance(&self, asset: &str) -> SigmaXResult<PortfolioBalance>;
    async fn get_all_balances(&self) -> SigmaXResult<Vec<PortfolioBalance>>;
    async fn update_balance(&self, asset: &str, amount: Amount) -> SigmaXResult<()>;
    async fn calculate_total_value(&self, base_currency: &str) -> SigmaXResult<Amount>;
    async fn get_positions(&self) -> SigmaXResult<Vec<Position>>;
    async fn update_position(&self, trade: &TradeUpdate) -> SigmaXResult<()>;
}

#[derive(Debug, Clone)]
pub struct Position {
    pub trading_pair: TradingPair,
    pub quantity: Quantity,
    pub avg_price: Price,
    pub unrealized_pnl: Amount,
    pub realized_pnl: Amount,
}
```

### 风险管理抽象
```rust
#[async_trait]
pub trait RiskManager: Send + Sync {
    async fn check_order_risk(&self, order: &OrderRequest) -> SigmaXResult<RiskCheckResult>;
    async fn check_portfolio_risk(&self, portfolio_id: PortfolioId) -> SigmaXResult<RiskCheckResult>;
    async fn calculate_var(&self, portfolio_id: PortfolioId, confidence: Decimal) -> SigmaXResult<Amount>;
    async fn get_risk_metrics(&self, strategy_id: StrategyId) -> SigmaXResult<RiskMetrics>;
    async fn update_risk_limits(&self, strategy_id: StrategyId, limits: RiskLimits) -> SigmaXResult<()>;
}

#[derive(Debug, Clone)]
pub struct RiskCheckResult {
    pub passed: bool,
    pub violations: Vec<RiskViolation>,
    pub warnings: Vec<RiskWarning>,
}

#[derive(Debug, Clone)]
pub struct RiskViolation {
    pub rule_name: String,
    pub severity: RiskSeverity,
    pub message: String,
}

#[derive(Debug, Clone)]
pub struct RiskWarning {
    pub rule_name: String,
    pub message: String,
}

#[derive(Debug, Clone)]
pub struct RiskLimits {
    pub max_position_size: Amount,
    pub max_daily_loss: Amount,
    pub max_drawdown: Percentage,
    pub stop_loss_threshold: Percentage,
}
```

### 服务容器抽象
```rust
#[async_trait]
pub trait ServiceContainer: Send + Sync {
    // 核心服务创建
    async fn create_portfolio_manager(&self) -> SigmaXResult<Arc<dyn PortfolioManager>>;
    async fn create_risk_manager(&self) -> SigmaXResult<Arc<dyn RiskManager>>;
    async fn create_strategy_manager(&self) -> SigmaXResult<Arc<dyn StrategyManager>>;
    async fn create_order_manager(&self) -> SigmaXResult<Arc<dyn OrderManager>>;

    // 配置管理
    async fn get_config_manager(&self) -> SigmaXResult<Arc<dyn ConfigManager>>;

    // 事件系统
    async fn get_event_bus(&self) -> SigmaXResult<Arc<dyn EventBus>>;

    // 事务管理
    async fn get_transaction_manager(&self) -> SigmaXResult<Arc<dyn TransactionManager>>;

    // 生命周期管理
    async fn start_services(&self) -> SigmaXResult<()>;
    async fn stop_services(&self) -> SigmaXResult<()>;
    async fn health_check(&self) -> SigmaXResult<HealthStatus>;
}

#[derive(Debug, Clone)]
pub struct HealthStatus {
    pub overall: ServiceHealth,
    pub services: std::collections::HashMap<String, ServiceHealth>,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ServiceHealth {
    Healthy,
    Degraded,
    Unhealthy,
    Unknown,
}
```

### 策略管理抽象
```rust
#[async_trait]
pub trait StrategyManager: Send + Sync {
    async fn register_strategy(&self, strategy: Arc<dyn Strategy>) -> SigmaXResult<()>;
    async fn unregister_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<()>;
    async fn start_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<()>;
    async fn stop_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<()>;
    async fn get_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Arc<dyn Strategy>>;
    async fn list_strategies(&self) -> SigmaXResult<Vec<StrategyId>>;
    async fn get_strategy_status(&self, strategy_id: StrategyId) -> SigmaXResult<StrategyStatus>;
}
```

### 订单管理抽象
```rust
#[async_trait]
pub trait OrderManager: Send + Sync {
    async fn place_order(&self, request: OrderRequest) -> SigmaXResult<OrderId>;
    async fn cancel_order(&self, order_id: OrderId) -> SigmaXResult<()>;
    async fn get_order(&self, order_id: OrderId) -> SigmaXResult<Option<OrderInfo>>;
    async fn list_orders(&self, filter: OrderFilter) -> SigmaXResult<Vec<OrderInfo>>;
    async fn get_order_status(&self, order_id: OrderId) -> SigmaXResult<OrderStatus>;
}

#[derive(Debug, Clone)]
pub struct OrderInfo {
    pub order_id: OrderId,
    pub strategy_id: StrategyId,
    pub trading_pair: TradingPair,
    pub side: OrderSide,
    pub order_type: OrderType,
    pub quantity: Quantity,
    pub price: Option<Price>,
    pub filled_quantity: Quantity,
    pub avg_fill_price: Option<Price>,
    pub status: OrderStatus,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}
```

## � 数据模型重构方案 (`models/*`)

> **🚨 现状**: 当前models设计存在职责混乱、分散性等问题，需要重构
> **🎯 目标**: Core模块只保留基础设施模型，业务模型移至对应模块

### 📋 当前问题分析

#### 🔴 **存在的问题**:
```
当前Core模块models (需要重构):
├── account.rs          # ✅ 保留 - 基础金融模型
├── order.rs            # ✅ 保留 - 核心交易模型
├── trade.rs            # ✅ 保留 - 核心交易模型
├── market_data.rs      # ✅ 保留 - 通用市场数据
├── engine.rs           # ❌ 移除 - 应移至engines模块
├── strategy.rs         # ❌ 移除 - 应移至strategies模块
└── tests.rs            # ✅ 保留 - 测试辅助
```

#### 🎯 **重构目标结构**:
```
core/models/ (重构后):
├── mod.rs              # 模块导出
├── account.rs          # Balance, Position - 账户持仓
├── order.rs            # Order - 订单模型
├── trade.rs            # Trade - 交易模型
├── market_data.rs      # MarketData, Candle, OrderBook - 市场数据
├── event.rs            # SystemEvent - 系统事件
└── audit.rs            # AuditLog - 审计日志
```

#### 📦 **业务模型迁移计划**:
- `EngineConfig, EngineInfo` → `engines/src/models/`
- `StrategyConfig, StrategyPerformance` → `strategies/src/models/`
- `RiskMetrics, RiskCheckResult` → `risk/src/models/`
- `DataProviderConfig` → `data/src/models/`

### 🔧 核心模型定义 (统一模型设计)

#### 账户模型 (`models/account.rs`) - 基础金融概念
```rust
use serde::{Deserialize, Serialize};
use validator::Validate;
use chrono::{DateTime, Utc};
use crate::{types::*, enums::*, error::*};

/// 账户余额 - 基础金融模型
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Balance {
    pub exchange_id: ExchangeId,

    #[validate(length(min = 1, message = "Asset name cannot be empty"))]
    pub asset: String,

    #[validate(custom = "validate_non_negative_amount")]
    pub free: Amount,

    #[validate(custom = "validate_non_negative_amount")]
    pub locked: Amount,

    pub updated_at: DateTime<Utc>,
}

impl Balance {
    pub fn new(exchange_id: ExchangeId, asset: String, free: Amount, locked: Amount) -> Self;
    pub fn total(&self) -> Amount;
    pub fn lock_funds(&mut self, amount: Amount) -> SigmaXResult<()>;
    pub fn unlock_funds(&mut self, amount: Amount) -> SigmaXResult<()>;
    pub fn is_consistent(&self) -> bool;
    pub fn validate_complete(&self) -> SigmaXResult<()>;
}

/// 持仓信息 - 基础交易模型
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Position {
    pub exchange_id: ExchangeId,
    pub trading_pair: TradingPair,
    pub side: PositionSide,
    #[validate(custom = "validate_quantity")]
    pub quantity: Quantity,
    pub average_price: Price,
    pub unrealized_pnl: Amount,
    pub realized_pnl: Amount,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PositionSide { Long, Short }
```

#### 订单模型 (`models/order.rs`) - 核心交易概念
```rust
/// 订单模型 - 统一的订单数据结构
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Order {
    pub order_id: OrderId,
    pub strategy_id: Option<StrategyId>,
    pub exchange_id: ExchangeId,
    pub trading_pair: TradingPair,
    pub side: OrderSide,
    pub order_type: OrderType,
    pub status: OrderStatus,

    #[validate(custom = "validate_positive_quantity")]
    pub quantity: Quantity,
    pub price: Option<Price>,
    pub stop_price: Option<Price>,

    #[validate(custom = "validate_filled_quantity")]
    pub filled_quantity: Quantity,
    pub average_price: Option<Price>,
    pub total_fee: Amount,

    pub exchange_order_id: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl Order {
    /// 计算订单完成进度百分比
    pub fn progress_pct(&self) -> Percentage {
        if self.quantity.is_zero() {
            return Decimal::ZERO;
        }
        (self.filled_quantity / self.quantity) * Decimal::from(100)
    }

    /// 获取显示用的订单名称
    pub fn display_name(&self) -> String {
        format!("{} {} {}",
            self.side,
            self.quantity,
            self.trading_pair.base
        )
    }

    /// 检查订单是否已完成
    pub fn is_completed(&self) -> bool {
        matches!(self.status, OrderStatus::Filled | OrderStatus::Cancelled)
    }
}
```

#### 交易模型 (`models/trade.rs`) - 核心交易概念
```rust
/// 交易模型 - 统一的交易数据结构
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Trade {
    pub trade_id: TradeId,
    pub order_id: OrderId,
    pub strategy_id: Option<StrategyId>,
    pub exchange_id: ExchangeId,
    pub trading_pair: TradingPair,
    pub side: OrderSide,

    #[validate(range(min = 0.0))]
    pub price: Price,

    #[validate(range(min = 0.0))]
    pub quantity: Quantity,

    #[validate(range(min = 0.0))]
    pub fee: Fee,

    pub fee_asset: String,
    pub exchange_trade_id: Option<String>,
    pub executed_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
}

impl Trade {
    /// 计算交易总价值
    pub fn trade_value(&self) -> Amount {
        self.price * self.quantity
    }

    /// 计算净交易价值 (扣除手续费)
    pub fn net_value(&self) -> Amount {
        self.trade_value() - self.fee
    }

    /// 检查是否为买入交易
    pub fn is_buy(&self) -> bool {
        matches!(self.side, OrderSide::Buy)
    }

    /// 检查是否为卖出交易
    pub fn is_sell(&self) -> bool {
        matches!(self.side, OrderSide::Sell)
    }
}
```

#### 市场数据模型 (`models/market_data.rs`) - 值对象，无需Record/View分离
```rust
/// K线数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Candle {
    pub timestamp: DateTime<Utc>,
    pub open: Price,
    pub high: Price,
    pub low: Price,
    pub close: Price,
    pub volume: Quantity,
}

/// 市场深度数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderBook {
    pub timestamp: DateTime<Utc>,
    pub bids: Vec<(Price, Quantity)>,
    pub asks: Vec<(Price, Quantity)>,
}

/// 统一市场数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketData {
    pub trading_pair: TradingPair,
    pub timestamp: DateTime<Utc>,
    pub price: Price,
    pub volume: Quantity,
    pub source: String,
}
```

### 📋 模型设计原则

#### 🎯 **统一模型设计原则**:
- **单一模型**: 每个实体使用一个统一的模型结构，避免不必要的分离
- **计算字段**: 通过impl方法提供计算字段，而不是单独的View模型
- **按需扩展**: 需要时可以添加格式化方法或计算属性
- **简洁优先**: 避免过度抽象，保持代码简洁易懂

#### 🔧 **验证规则约定**:
```rust
// 金额验证
#[validate(custom = "validate_non_negative_amount")]
pub amount: Amount,

// 数量验证
#[validate(custom = "validate_positive_quantity")]
pub quantity: Quantity,

// 字符串长度验证
#[validate(length(min = 1, max = 100))]
pub name: String,

// 范围验证
#[validate(range(min = 0.0))]
pub price: Price,
```

#### 🏷️ **命名约定**:
- **实体模型**: 直接使用实体名 (`Order`, `Trade`, `Balance`)
- **ID类型**: 使用newtype (`OrderId`, `TradeId`)
- **计算方法**: 使用动词或形容词 (`progress_pct()`, `is_completed()`)
- **格式化方法**: 使用`display_`前缀 (`display_name()`, `display_status()`)

### 🚀 重构实施指南

#### **Phase 1: 模型迁移** (立即执行)
1. **移除业务模型**: 将 `engine.rs`, `strategy.rs` 移至对应模块
2. **保留核心模型**: 只保留 `account.rs`, `order.rs`, `trade.rs`, `market_data.rs`
3. **更新导出**: 修改 `mod.rs` 中的重新导出

#### **Phase 2: Record/View分离** (短期)
1. **重构Order模型**: 分离为 `OrderRecord` 和 `OrderView`
2. **重构Trade模型**: 分离为 `TradeRecord` 和 `TradeView`
3. **更新Repository**: 修改Repository接口使用新的Record/View模型

#### **Phase 3: 验证统一** (中期)
1. **统一验证规则**: 使用统一的验证函数和错误处理
2. **完善测试**: 为所有模型添加完整的单元测试
3. **文档完善**: 为所有public API添加文档注释

### ⚠️ **迁移注意事项**

#### **依赖处理**:
- 其他模块引用Core中业务模型的地方需要更新import路径
- 确保迁移后的模型保持API兼容性
- 使用feature flag进行渐进式迁移

#### **数据库兼容**:
- Record模型的字段变更需要对应的数据库迁移
- 确保现有数据的兼容性
- 考虑使用版本化的模型结构

#### **测试策略**:
- 为每个模型编写完整的单元测试
- 集成测试验证Record/View转换的正确性
- 性能测试确保模型操作的效率

/// 持仓信息 - 基础交易模型
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Position {
    pub exchange_id: ExchangeId,
    pub trading_pair: TradingPair,
    pub side: PositionSide,

    #[validate(custom = "validate_quantity")]
    pub quantity: Quantity,

    pub average_price: Price,
    pub unrealized_pnl: Amount,
    pub realized_pnl: Amount,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PositionSide {
    Long,
    Short,
}
```

/// 持仓信息 - 交易持仓管理
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Position {
    pub exchange_id: ExchangeId,
    pub trading_pair: TradingPair,
    pub side: PositionSide,

    #[validate(custom = "validate_quantity")]
    pub quantity: Quantity,

    pub average_price: Price,
    pub unrealized_pnl: Amount,
    pub realized_pnl: Amount,
    pub margin_used: Amount,
    pub leverage: Decimal,
    pub updated_at: DateTime<Utc>,
}

impl Position {
    pub fn new(exchange_id: ExchangeId, trading_pair: TradingPair, side: PositionSide) -> Self;
    pub fn update_from_trade(&mut self, trade: &Trade) -> SigmaXResult<()>;
    pub fn calculate_unrealized_pnl(&mut self, current_price: Price) -> SigmaXResult<()>;
    pub fn close_position(&mut self, close_price: Price) -> SigmaXResult<Amount>;
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PositionSide {
    Long,
    Short,
}
```

#### OrderRecord (`models/record/order.rs`)
```rust
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct OrderRecord {
    pub order_id: OrderId,
    pub strategy_id: StrategyId,
    pub trading_pair: TradingPair,
    pub side: OrderSide,
    pub order_type: OrderType,
    pub status: OrderStatus,

    #[validate(range(min = 0.0))]
    pub quantity: Quantity,

    pub price: Option<Price>,
    pub stop_price: Option<Price>,

    #[validate(range(min = 0.0))]
    pub filled_quantity: Quantity,

    pub avg_fill_price: Option<Price>,
    pub total_fee: Fee,

    pub exchange_order_id: Option<String>,
    pub exchange_id: ExchangeId,

    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub filled_at: Option<DateTime<Utc>>,
}
```

#### TradeRecord (`models/record/trade.rs`)
```rust
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct TradeRecord {
    pub trade_id: TradeId,
    pub order_id: OrderId,
    pub strategy_id: StrategyId,
    pub trading_pair: TradingPair,
    pub side: OrderSide,

    #[validate(range(min = 0.0))]
    pub price: Price,

    #[validate(range(min = 0.0))]
    pub quantity: Quantity,

    #[validate(range(min = 0.0))]
    pub fee: Fee,

    pub fee_asset: String,
    pub exchange_trade_id: Option<String>,
    pub exchange_id: ExchangeId,

    pub executed_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
}
```

#### PortfolioRecord (`models/record/portfolio.rs`)
```rust
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct PortfolioRecord {
    pub portfolio_id: PortfolioId,

    #[validate(length(min = 1, max = 100))]
    pub name: String,

    #[validate(length(min = 1, max = 100))]
    pub owner: String,

    #[validate(range(min = 0.0))]
    pub initial_capital: Amount,

    #[validate(range(min = 0.0))]
    pub current_capital: Amount,

    pub base_currency: String,
    pub balances: std::collections::HashMap<String, PortfolioBalance>,

    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}
```

#### RiskRecord (`models/record/risk.rs`)
```rust
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct RiskRecord {
    pub risk_id: RiskId,
    pub strategy_id: StrategyId,
    pub portfolio_id: Option<PortfolioId>,

    pub risk_type: String,
    pub metrics: serde_json::Value,

    pub max_drawdown: Percentage,
    pub current_drawdown: Percentage,
    pub var_95: Amount,
    pub exposure: Amount,
    pub leverage: Decimal,

    pub calculated_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
}
```

### View Models (查询/展示模型) - `models/view/`

#### StrategyView (`models/view/strategy.rs`)
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyView {
    pub id: StrategyId,
    pub name: String,
    pub strategy_type: String,
    pub status: StrategyStatus,
    pub portfolio_id: Option<PortfolioId>,
    pub trading_pair: Option<TradingPair>,
    pub performance_summary: Option<PerformanceSummary>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceSummary {
    pub total_return: Percentage,
    pub sharpe_ratio: Option<Decimal>,
    pub max_drawdown: Percentage,
    pub win_rate: Percentage,
    pub total_trades: u64,
}
```

#### OrderView (`models/view/order.rs`)
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderView {
    pub id: OrderId,
    pub strategy_id: StrategyId,
    pub trading_pair: TradingPair,
    pub side: OrderSide,
    pub order_type: OrderType,
    pub status: OrderStatus,
    pub quantity: Quantity,
    pub price: Option<Price>,
    pub filled_amount: Quantity,
    pub avg_fill_price: Option<Price>,
    pub slippage: Option<Percentage>,
    pub total_fee: Fee,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}
```

#### TradeView (`models/view/trade.rs`)
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeView {
    pub id: TradeId,
    pub order_id: OrderId,
    pub strategy_id: StrategyId,
    pub trading_pair: TradingPair,
    pub side: OrderSide,
    pub price: Price,
    pub quantity: Quantity,
    pub fee: Fee,
    pub pnl: Option<Amount>,
    pub pnl_pct: Option<Percentage>,
    pub executed_at: DateTime<Utc>,
}
```

#### PortfolioView (`models/view/portfolio.rs`)
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortfolioView {
    pub id: PortfolioId,
    pub name: String,
    pub owner: String,
    pub total_value: Amount,
    pub base_currency: String,
    pub positions_count: u32,
    pub risk_score: Option<RiskLevel>,
    pub performance_metrics: Option<PortfolioPerformance>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortfolioPerformance {
    pub total_return: Percentage,
    pub daily_return: Percentage,
    pub volatility: Percentage,
    pub sharpe_ratio: Option<Decimal>,
    pub max_drawdown: Percentage,
}
```

#### RiskView (`models/view/risk.rs`)
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskView {
    pub strategy_id: StrategyId,
    pub portfolio_id: Option<PortfolioId>,
    pub current_exposure: Amount,
    pub risk_level: RiskLevel,
    pub alerts_count: u32,
    pub max_drawdown: Percentage,
    pub current_drawdown: Percentage,
    pub var_95: Amount,
    pub last_check_at: DateTime<Utc>,
}
```

## � 验证系统 (`validation/*`)

### 验证接口 (`validation/mod.rs`)
```rust
use async_trait::async_trait;
use validator::ValidationErrors;
use crate::error::SigmaXResult;

pub trait Validatable {
    fn validate(&self) -> Result<(), ValidationErrors>;
}

#[async_trait]
pub trait CustomValidator<T>: Send + Sync {
    async fn validate(&self, value: &T) -> SigmaXResult<()>;
    fn name(&self) -> &str;
}

#[async_trait]
pub trait ValidationService: Send + Sync {
    async fn validate_strategy_config(&self, config: &serde_json::Value) -> SigmaXResult<()>;
    async fn validate_order_request(&self, request: &OrderRequest) -> SigmaXResult<()>;
    async fn validate_risk_limits(&self, limits: &RiskLimits) -> SigmaXResult<()>;
    async fn validate_portfolio_config(&self, config: &PortfolioConfig) -> SigmaXResult<()>;
}
```

### 验证规则 (`validation/rules.rs`)
```rust
use crate::{types::*, enums::*};

pub struct OrderValidationRules;

impl OrderValidationRules {
    pub fn validate_quantity(quantity: Quantity) -> SigmaXResult<()> {
        if quantity <= Decimal::ZERO {
            return Err(SigmaXError::validation("Quantity must be positive"));
        }
        Ok(())
    }

    pub fn validate_price(price: Option<Price>, order_type: OrderType) -> SigmaXResult<()> {
        match order_type {
            OrderType::Market => {
                if price.is_some() {
                    return Err(SigmaXError::validation("Market orders should not have price"));
                }
            }
            OrderType::Limit | OrderType::StopLimit => {
                if price.is_none() {
                    return Err(SigmaXError::validation("Limit orders must have price"));
                }
                if let Some(p) = price {
                    if p <= Decimal::ZERO {
                        return Err(SigmaXError::validation("Price must be positive"));
                    }
                }
            }
            _ => {}
        }
        Ok(())
    }
}

pub struct RiskValidationRules;

impl RiskValidationRules {
    pub fn validate_drawdown(drawdown: Percentage) -> SigmaXResult<()> {
        if drawdown < Decimal::ZERO || drawdown > Decimal::from(100) {
            return Err(SigmaXError::validation("Drawdown must be between 0 and 100"));
        }
        Ok(())
    }

    pub fn validate_position_size(size: Amount, max_size: Amount) -> SigmaXResult<()> {
        if size > max_size {
            return Err(SigmaXError::validation("Position size exceeds maximum allowed"));
        }
        Ok(())
    }
}
```
## 🔍 查询过滤器 (`filters/*`)

### StrategyFilter (`filters/strategy.rs`)
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyFilter {
    pub status: Option<StrategyStatus>,
    pub strategy_type: Option<String>,
    pub portfolio_id: Option<PortfolioId>,
    pub created_after: Option<DateTime<Utc>>,
    pub created_before: Option<DateTime<Utc>>,
    pub limit: Option<usize>,
    pub offset: Option<usize>,
    pub sort_by: Option<StrategySortBy>,
    pub sort_order: Option<SortOrder>,
}

#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum StrategySortBy {
    Name,
    CreatedAt,
    UpdatedAt,
    Status,
    Performance,
}

#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum SortOrder {
    Asc,
    Desc,
}
```

### OrderFilter (`filters/order.rs`)
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderFilter {
    pub strategy_id: Option<StrategyId>,
    pub status: Option<OrderStatus>,
    pub side: Option<OrderSide>,
    pub order_type: Option<OrderType>,
    pub trading_pair: Option<TradingPair>,
    pub created_after: Option<DateTime<Utc>>,
    pub created_before: Option<DateTime<Utc>>,
    pub limit: Option<usize>,
    pub offset: Option<usize>,
    pub sort_by: Option<OrderSortBy>,
    pub sort_order: Option<SortOrder>,
}

#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum OrderSortBy {
    CreatedAt,
    UpdatedAt,
    Status,
    Quantity,
    Price,
}
```

### TradeFilter (`filters/trade.rs`)
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeFilter {
    pub strategy_id: Option<StrategyId>,
    pub order_id: Option<OrderId>,
    pub trading_pair: Option<TradingPair>,
    pub side: Option<OrderSide>,
    pub executed_after: Option<DateTime<Utc>>,
    pub executed_before: Option<DateTime<Utc>>,
    pub min_quantity: Option<Quantity>,
    pub max_quantity: Option<Quantity>,
    pub min_price: Option<Price>,
    pub max_price: Option<Price>,
    pub limit: Option<usize>,
    pub offset: Option<usize>,
    pub sort_by: Option<TradeSortBy>,
    pub sort_order: Option<SortOrder>,
}

#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum TradeSortBy {
    ExecutedAt,
    Price,
    Quantity,
    Fee,
    Pnl,
}
```

### PortfolioFilter (`filters/portfolio.rs`)
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortfolioFilter {
    pub owner: Option<String>,
    pub base_currency: Option<String>,
    pub min_value: Option<Amount>,
    pub max_value: Option<Amount>,
    pub created_after: Option<DateTime<Utc>>,
    pub created_before: Option<DateTime<Utc>>,
    pub limit: Option<usize>,
    pub offset: Option<usize>,
    pub sort_by: Option<PortfolioSortBy>,
    pub sort_order: Option<SortOrder>,
}

#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum PortfolioSortBy {
    Name,
    TotalValue,
    CreatedAt,
    UpdatedAt,
    Performance,
}
```

## �️ Repository接口 (`traits.rs`)

### 核心Repository接口 (统一模型)
```rust
use async_trait::async_trait;
use crate::{types::*, models::*, filters::*, error::SigmaXResult};

#[async_trait]
pub trait OrderRepository: Send + Sync {
    // 基础CRUD操作
    async fn save(&self, order: &Order) -> SigmaXResult<OrderId>;
    async fn update(&self, order: &Order) -> SigmaXResult<()>;
    async fn delete(&self, id: OrderId) -> SigmaXResult<()>;
    async fn find_by_id(&self, id: OrderId) -> SigmaXResult<Option<Order>>;

    // 查询操作
    async fn list(&self, filter: OrderFilter) -> SigmaXResult<Vec<Order>>;
    async fn count(&self, filter: OrderFilter) -> SigmaXResult<u64>;

    // 业务查询
    async fn find_by_exchange(&self, exchange_id: ExchangeId) -> SigmaXResult<Vec<Order>>;
    async fn find_active_orders(&self) -> SigmaXResult<Vec<Order>>;

    // 批量操作
    async fn batch_save(&self, orders: &[Order]) -> SigmaXResult<Vec<OrderId>>;
    async fn batch_update(&self, orders: &[Order]) -> SigmaXResult<()>;
}

#[async_trait]
pub trait TradeRepository: Send + Sync {
    // 基础CRUD操作
    async fn save(&self, trade: &Trade) -> SigmaXResult<TradeId>;
    async fn find_by_id(&self, id: TradeId) -> SigmaXResult<Option<Trade>>;

    // 查询操作
    async fn list(&self, filter: TradeFilter) -> SigmaXResult<Vec<Trade>>;
    async fn count(&self, filter: TradeFilter) -> SigmaXResult<u64>;

    // 业务查询
    async fn find_by_order(&self, order_id: OrderId) -> SigmaXResult<Vec<Trade>>;
    async fn find_by_exchange(&self, exchange_id: ExchangeId) -> SigmaXResult<Vec<Trade>>;
    async fn find_recent_trades(&self, limit: usize) -> SigmaXResult<Vec<Trade>>;
}

#[async_trait]
pub trait PortfolioRepository: Send + Sync {
    async fn save(&self, record: &PortfolioRecord) -> SigmaXResult<PortfolioId>;
    async fn update(&self, record: &PortfolioRecord) -> SigmaXResult<()>;
    async fn find_by_id(&self, id: PortfolioId) -> SigmaXResult<Option<PortfolioRecord>>;
    async fn find_view_by_id(&self, id: PortfolioId) -> SigmaXResult<Option<PortfolioView>>;
    async fn list(&self, filter: PortfolioFilter) -> SigmaXResult<Vec<PortfolioView>>;
    async fn count(&self, filter: PortfolioFilter) -> SigmaXResult<u64>;
    async fn find_by_owner(&self, owner: &str) -> SigmaXResult<Vec<PortfolioView>>;
}

#[async_trait]
pub trait RiskRepository: Send + Sync {
    async fn save_metrics(&self, record: &RiskRecord) -> SigmaXResult<()>;
    async fn latest_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Option<RiskView>>;
    async fn list(&self, filter: RiskFilter) -> SigmaXResult<Vec<RiskView>>;
    async fn find_alerts(&self, strategy_id: StrategyId) -> SigmaXResult<Vec<RiskAlert>>;
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskFilter {
    pub strategy_id: Option<StrategyId>,
    pub portfolio_id: Option<PortfolioId>,
    pub risk_level: Option<RiskLevel>,
    pub calculated_after: Option<DateTime<Utc>>,
    pub limit: Option<usize>,
    pub offset: Option<usize>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAlert {
    pub id: RiskId,
    pub strategy_id: StrategyId,
    pub alert_type: String,
    pub severity: RiskSeverity,
    pub message: String,
    pub triggered_at: DateTime<Utc>,
    pub status: AlertStatus,
}
```

## 📡 事件系统 (`events.rs`)

```rust
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use crate::{types::*, enums::*, error::SigmaXResult};

#[async_trait]
pub trait EventBus: Send + Sync {
    async fn publish(&self, event: SystemEvent) -> SigmaXResult<()>;
    async fn subscribe<H>(&self, handler: H) -> SigmaXResult<()>
    where
        H: EventHandler + 'static;
    async fn unsubscribe(&self, handler_id: &str) -> SigmaXResult<()>;
}

#[async_trait]
pub trait EventHandler: Send + Sync {
    async fn handle(&self, event: &SystemEvent) -> SigmaXResult<()>;
    fn handler_id(&self) -> &str;
    fn event_types(&self) -> Vec<EventType>;
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SystemEvent {
    // 策略事件
    StrategyCreated { strategy_id: StrategyId, timestamp: DateTime<Utc> },
    StrategyStarted { strategy_id: StrategyId, timestamp: DateTime<Utc> },
    StrategyPaused { strategy_id: StrategyId, timestamp: DateTime<Utc> },
    StrategyStopped { strategy_id: StrategyId, timestamp: DateTime<Utc> },
    StrategyError { strategy_id: StrategyId, error: String, timestamp: DateTime<Utc> },

    // 订单事件
    OrderCreated { order_id: OrderId, strategy_id: StrategyId, timestamp: DateTime<Utc> },
    OrderUpdated { order_id: OrderId, status: OrderStatus, timestamp: DateTime<Utc> },
    OrderFilled { order_id: OrderId, filled_quantity: Quantity, timestamp: DateTime<Utc> },
    OrderCancelled { order_id: OrderId, reason: String, timestamp: DateTime<Utc> },

    // 交易事件
    TradeExecuted { trade_id: TradeId, order_id: OrderId, timestamp: DateTime<Utc> },

    // 风险事件
    RiskAlert { strategy_id: StrategyId, alert: RiskAlert, timestamp: DateTime<Utc> },
    RiskLimitExceeded { strategy_id: StrategyId, limit_type: String, timestamp: DateTime<Utc> },

    // 系统事件
    SystemStarted { timestamp: DateTime<Utc> },
    SystemStopped { timestamp: DateTime<Utc> },
    SystemError { error: String, timestamp: DateTime<Utc> },

    // 引擎事件
    EngineCreated { engine_id: EngineId, engine_type: EngineType, timestamp: DateTime<Utc> },
    EngineStarted { engine_id: EngineId, timestamp: DateTime<Utc> },
    EngineStopped { engine_id: EngineId, timestamp: DateTime<Utc> },
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EventType {
    Strategy,
    Order,
    Trade,
    Risk,
    System,
    Engine,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StoredEvent {
    pub id: Uuid,
    pub event: SystemEvent,
    pub event_type: EventType,
    pub published_at: DateTime<Utc>,
    pub processed: bool,
}
```

## 🔄 事务管理 (`transaction.rs`)

```rust
use async_trait::async_trait;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use crate::error::SigmaXResult;

#[async_trait]
pub trait TransactionManager: Send + Sync {
    async fn begin(&self) -> SigmaXResult<UnitOfWork>;
    async fn commit(&self, uow: UnitOfWork) -> SigmaXResult<()>;
    async fn rollback(&self, uow: UnitOfWork) -> SigmaXResult<()>;

    async fn with_transaction<F, R>(&self, f: F) -> SigmaXResult<R>
    where
        F: FnOnce(UnitOfWork) -> std::pin::Pin<Box<dyn std::future::Future<Output = SigmaXResult<R>> + Send>> + Send,
        R: Send;
}

#[derive(Debug, Clone)]
pub struct UnitOfWork {
    pub id: Uuid,
    pub isolation_level: IsolationLevel,
    pub started_at: DateTime<Utc>,
}

impl UnitOfWork {
    pub fn new(isolation_level: IsolationLevel) -> Self {
        Self {
            id: Uuid::new_v4(),
            isolation_level,
            started_at: Utc::now(),
        }
    }

    pub fn in_transaction(&self) -> bool {
        true
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum IsolationLevel {
    ReadUncommitted,
    ReadCommitted,
    RepeatableRead,
    Serializable,
}

#[derive(Debug, Clone)]
pub struct TransactionContext {
    pub uow: UnitOfWork,
    pub metadata: std::collections::HashMap<String, String>,
}
```

## 📤 导出约定 (`lib.rs`)

```rust
//! SigmaX Core - 基础设施层
//!
//! 提供系统基础类型、错误处理、事件系统和数据访问抽象

// 🔧 基础类型 - 显式导出
pub use types::{
    // IDs
    StrategyId, OrderId, TradeId, EngineId, PortfolioId, RiskId,
    // Numeric
    Amount, Price, Quantity, Percentage, Fee,
    // Trading
    TradingPair, ExchangeId,
    // Market Data
    MarketData, RiskMetrics, PortfolioBalance,
};

// 🏷️ 枚举 - 显式导出
pub use enums::{
    // Trading
    OrderSide, OrderType, OrderStatus,
    // Strategy
    StrategyStatus, EngineType, EngineStatus,
    // Risk
    RiskLevel, RiskSeverity, AlertStatus,
    // Time
    TimeFrame,
    // Notification
    NotificationStatus, NotificationType,
};

// 📊 模型 - 显式导出 (统一模型)
pub use models::{
    // 基础设施模型
    Order, Trade, Balance, Position, MarketData, Candle, OrderBook,
    // 系统模型
    SystemEvent, AuditLog,
};

// 🔍 过滤器 - 显式导出
pub use filters::{
    StrategyFilter, OrderFilter, TradeFilter, PortfolioFilter, RiskFilter,
    StrategySortBy, OrderSortBy, TradeSortBy, PortfolioSortBy, SortOrder,
};

// 🎯 核心抽象 - 显式导出
pub use traits::{
    // Strategy
    Strategy, StrategyManager, OrderRequest, OrderUpdate, TradeUpdate,
    // Portfolio
    PortfolioManager, Position,
    // Risk
    RiskManager, RiskCheckResult, RiskViolation, RiskWarning, RiskLimits,
    // Service
    ServiceContainer, HealthStatus, ServiceHealth,
    // Order
    OrderManager, OrderInfo,
    // Repository
    StrategyRepository, OrderRepository, TradeRepository, PortfolioRepository, RiskRepository,
};

// ⚙️ 配置管理 - 显式导出
pub use config::{
    AppConfig, DatabaseConfig, CacheConfig, RiskConfig, MonitoringConfig,
    TradingConfig, EngineConfig, ConfigManager, ConfigWatcher,
};

// 🔍 验证系统 - 显式导出
pub use validation::{
    Validatable, CustomValidator, ValidationService,
    OrderValidationRules, RiskValidationRules,
};

// 📡 事件系统 - 显式导出
pub use events::{
    EventBus, EventHandler, SystemEvent, EventType, StoredEvent,
};

// 🔄 事务管理 - 显式导出
pub use transaction::{
    TransactionManager, UnitOfWork, IsolationLevel, TransactionContext,
};

// ⚠️ 错误处理 - 显式导出
pub use error::{
    SigmaXError, SigmaXResult, ErrorContext, ErrorCategory, ErrorSeverity,
    TradingErrorCode, StrategyErrorCode, ValidationErrorCode, DatabaseErrorCode,
    ExchangeApiErrorCode, InternalErrorCode, RiskErrorCode,
};

// 🚫 禁止事项:
// - 通配导出 (pub use module::*)
// - 未注释的 public API
// - 跨层非法依赖
// - 混合语义的类型名称
```

---

> **📝 Core模块开发地图** - 完整的代码契约定义
> **🎯 用途**: 作为Core模块开发的唯一参考标准
> **📋 状态**: 契约完整，可立即开始实施