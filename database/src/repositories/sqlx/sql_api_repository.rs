//! SQL API仓储实现
//!
//! 基于 SQLx 的API数据访问实现

use std::sync::Arc;
use std::collections::HashMap;
use async_trait::async_trait;
use sqlx::Row;
use chrono::{DateTime, Utc};
use sigmax_core::{ DatabaseErrorCode, SigmaXResult, SigmaXError, ApiConfig};

use crate::DatabaseManager;
use crate::repositories::traits::api_repository::{
    ApiRepository, ApiRequestRecord, ApiRateLimitRecord, ApiRequestStats
};

/// SQL API仓储实现
pub struct SqlApiRepository {
    db: Arc<DatabaseManager>,
}

impl SqlApiRepository {
    /// 创建新的 SQL API仓储实例
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }

    /// 将数据库行转换为API请求记录
    fn row_to_api_request_record(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<ApiRequestRecord> {
        Ok(ApiRequestRecord {
            id: row.get("id"),
            path: row.get("path"),
            method: row.get("method"),
            client_ip: row.get("client_ip"),
            user_agent: row.get("user_agent"),
            status_code: row.get::<i32, _>("status_code") as u16,
            response_time_ms: row.get::<i32, _>("response_time_ms") as u32,
            timestamp: row.get("timestamp"),
        })
    }

    /// 将数据库行转换为限流记录
    fn row_to_rate_limit_record(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<ApiRateLimitRecord> {
        Ok(ApiRateLimitRecord {
            id: row.get("id"),
            client_id: row.get("client_id"),
            endpoint: row.get("endpoint"),
            request_count: row.get::<i32, _>("request_count") as u32,
            window_start: row.get("window_start"),
            window_end: row.get("window_end"),
        })
    }
}

#[async_trait]
impl ApiRepository for SqlApiRepository {
    // ============================================================================
    // API配置管理
    // ============================================================================

    async fn get_api_config(&self) -> SigmaXResult<ApiConfig> {
        let pool = self.db.pool();

        // 从 system_config 表中获取API配置
        let rows = sqlx::query(
            r#"
            SELECT key, value 
            FROM system_config 
            WHERE key LIKE 'api.%'
            "#
        )
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("获取API配置失败: {}", e)))?;

        // 构建API配置对象
        let mut config = ApiConfig::default();

        for row in rows {
            let key: String = row.get("key");
            let value_json: serde_json::Value = row.get("value");

            // 从 JSON 值中提取字符串或数字
            let value_str = match &value_json {
                serde_json::Value::String(s) => s.clone(),
                serde_json::Value::Number(n) => n.to_string(),
                serde_json::Value::Bool(b) => b.to_string(),
                _ => continue, // 跳过无法转换的值
            };

            match key.as_str() {
                "api.rate_limit_per_minute" => {
                    config.rate_limit_per_minute = value_str.parse()
                        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("解析 rate_limit_per_minute 失败: {}", e)))?;
                }
                "api.max_request_size" => {
                    config.max_request_size = value_str.parse()
                        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("解析 max_request_size 失败: {}", e)))?;
                }
                "api.timeout_seconds" => {
                    config.timeout_seconds = value_str.parse()
                        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("解析 timeout_seconds 失败: {}", e)))?;
                }
                "api.cors_enabled" => {
                    config.cors_enabled = value_str.parse()
                        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("解析 cors_enabled 失败: {}", e)))?;
                }
                _ => {} // 忽略未知的配置项
            }
        }

        Ok(config)
    }

    async fn save_api_config(&self, config: &ApiConfig) -> SigmaXResult<()> {
        let pool = self.db.pool();

        // 开始事务
        let mut tx = pool.begin()
            .await
            .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("开始事务失败: {}", e)))?;

        // 保存各个配置项
        let config_items = [
            ("api.rate_limit_per_minute", config.rate_limit_per_minute.to_string(), "每分钟请求限制"),
            ("api.max_request_size", config.max_request_size.to_string(), "最大请求大小(字节)"),
            ("api.timeout_seconds", config.timeout_seconds.to_string(), "请求超时时间(秒)"),
            ("api.cors_enabled", config.cors_enabled.to_string(), "启用CORS"),
        ];

        for (key, value, description) in config_items {
            sqlx::query(
                r#"
                INSERT INTO system_config (key, value, description, is_encrypted, created_at, updated_at)
                VALUES ($1, $2, $3, false, now(), now())
                ON CONFLICT (key) DO UPDATE SET
                    value = EXCLUDED.value,
                    description = EXCLUDED.description,
                    updated_at = now()
                "#
            )
            .bind(key)
            .bind(value)
            .bind(description)
            .execute(&mut *tx)
            .await
            .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("保存API配置项 {} 失败: {}", key, e)))?;
        }

        // 提交事务
        tx.commit()
            .await
            .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("提交API配置事务失败: {}", e)))?;

        Ok(())
    }

    async fn reset_api_config(&self) -> SigmaXResult<()> {
        // 重置为默认配置
        let default_config = ApiConfig::default();
        self.save_api_config(&default_config).await
    }

    // ============================================================================
    // API请求记录管理
    // ============================================================================

    async fn save_api_request(&self, request: &ApiRequestRecord) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            INSERT INTO api_requests (
                id, path, method, client_ip, user_agent, status_code, 
                response_time_ms, timestamp
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8
            )
            "#
        )
        .bind(&request.id)
        .bind(&request.path)
        .bind(&request.method)
        .bind(&request.client_ip)
        .bind(&request.user_agent)
        .bind(request.status_code as i32)
        .bind(request.response_time_ms as i32)
        .bind(&request.timestamp)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("保存API请求记录失败: {}", e)))?;

        Ok(())
    }

    async fn get_api_request_stats(&self, start_time: DateTime<Utc>, end_time: DateTime<Utc>) -> SigmaXResult<ApiRequestStats> {
        let pool = self.db.pool();

        // 获取基本统计信息
        let stats_row = sqlx::query(
            r#"
            SELECT 
                COUNT(*) as total_requests,
                COUNT(CASE WHEN status_code >= 200 AND status_code < 300 THEN 1 END) as successful_requests,
                COUNT(CASE WHEN status_code >= 400 THEN 1 END) as error_requests,
                AVG(response_time_ms) as avg_response_time,
                MAX(response_time_ms) as max_response_time,
                MIN(response_time_ms) as min_response_time
            FROM api_requests
            WHERE timestamp BETWEEN $1 AND $2
            "#
        )
        .bind(&start_time)
        .bind(&end_time)
        .fetch_one(pool)
        .await
        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("获取API统计信息失败: {}", e)))?;

        let total_requests: i64 = stats_row.get("total_requests");
        let successful_requests: i64 = stats_row.get("successful_requests");
        let error_requests: i64 = stats_row.get("error_requests");
        let avg_response_time: Option<f64> = stats_row.get("avg_response_time");
        let max_response_time: Option<i32> = stats_row.get("max_response_time");
        let min_response_time: Option<i32> = stats_row.get("min_response_time");

        // 获取按状态码分组的统计
        let status_rows = sqlx::query(
            r#"
            SELECT status_code, COUNT(*) as count
            FROM api_requests
            WHERE timestamp BETWEEN $1 AND $2
            GROUP BY status_code
            "#
        )
        .bind(&start_time)
        .bind(&end_time)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("获取状态码统计失败: {}", e)))?;

        let mut requests_by_status = HashMap::new();
        for row in status_rows {
            let status_code: i32 = row.get("status_code");
            let count: i64 = row.get("count");
            requests_by_status.insert(status_code as u16, count as u64);
        }

        // 获取按路径分组的统计
        let path_rows = sqlx::query(
            r#"
            SELECT path, COUNT(*) as count
            FROM api_requests
            WHERE timestamp BETWEEN $1 AND $2
            GROUP BY path
            ORDER BY count DESC
            LIMIT 20
            "#
        )
        .bind(&start_time)
        .bind(&end_time)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("获取路径统计失败: {}", e)))?;

        let mut requests_by_path = HashMap::new();
        for row in path_rows {
            let path: String = row.get("path");
            let count: i64 = row.get("count");
            requests_by_path.insert(path, count as u64);
        }

        Ok(ApiRequestStats {
            total_requests: total_requests as u64,
            successful_requests: successful_requests as u64,
            error_requests: error_requests as u64,
            average_response_time_ms: avg_response_time.unwrap_or(0.0),
            max_response_time_ms: max_response_time.unwrap_or(0) as u32,
            min_response_time_ms: min_response_time.unwrap_or(0) as u32,
            requests_by_status,
            requests_by_path,
        })
    }

    async fn get_recent_api_requests(&self, limit: usize) -> SigmaXResult<Vec<ApiRequestRecord>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT id, path, method, client_ip, user_agent, status_code, 
                   response_time_ms, timestamp
            FROM api_requests
            ORDER BY timestamp DESC
            LIMIT $1
            "#
        )
        .bind(limit as i64)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("获取最近API请求失败: {}", e)))?;

        let mut requests = Vec::new();
        for row in rows {
            requests.push(self.row_to_api_request_record(row)?);
        }

        Ok(requests)
    }

    async fn delete_expired_api_requests(&self, before: DateTime<Utc>) -> SigmaXResult<u64> {
        let pool = self.db.pool();

        let result = sqlx::query(
            "DELETE FROM api_requests WHERE timestamp < $1"
        )
        .bind(&before)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("删除过期API请求失败: {}", e)))?;

        Ok(result.rows_affected())
    }

    // ============================================================================
    // API限流管理
    // ============================================================================

    async fn save_rate_limit_record(&self, record: &ApiRateLimitRecord) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            INSERT INTO api_rate_limits (
                id, client_id, endpoint, request_count, window_start, window_end
            ) VALUES (
                $1, $2, $3, $4, $5, $6
            )
            ON CONFLICT (client_id, endpoint, window_start) DO UPDATE SET
                request_count = EXCLUDED.request_count,
                window_end = EXCLUDED.window_end
            "#
        )
        .bind(&record.id)
        .bind(&record.client_id)
        .bind(&record.endpoint)
        .bind(record.request_count as i32)
        .bind(&record.window_start)
        .bind(&record.window_end)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("保存限流记录失败: {}", e)))?;

        Ok(())
    }

    async fn get_rate_limit_record(&self, client_id: &str, endpoint: &str, window_start: DateTime<Utc>) -> SigmaXResult<Option<ApiRateLimitRecord>> {
        let pool = self.db.pool();

        let row = sqlx::query(
            r#"
            SELECT id, client_id, endpoint, request_count, window_start, window_end
            FROM api_rate_limits
            WHERE client_id = $1 AND endpoint = $2 AND window_start = $3
            "#
        )
        .bind(client_id)
        .bind(endpoint)
        .bind(&window_start)
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("获取限流记录失败: {}", e)))?;

        match row {
            Some(row) => Ok(Some(self.row_to_rate_limit_record(row)?)),
            None => Ok(None),
        }
    }

    async fn increment_rate_limit_count(&self, client_id: &str, endpoint: &str, window_start: DateTime<Utc>, window_end: DateTime<Utc>) -> SigmaXResult<u32> {
        let pool = self.db.pool();

        let row = sqlx::query(
            r#"
            INSERT INTO api_rate_limits (id, client_id, endpoint, request_count, window_start, window_end)
            VALUES (gen_random_uuid(), $1, $2, 1, $3, $4)
            ON CONFLICT (client_id, endpoint, window_start) DO UPDATE SET
                request_count = api_rate_limits.request_count + 1,
                window_end = EXCLUDED.window_end
            RETURNING request_count
            "#
        )
        .bind(client_id)
        .bind(endpoint)
        .bind(&window_start)
        .bind(&window_end)
        .fetch_one(pool)
        .await
        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("增加限流计数失败: {}", e)))?;

        let count: i32 = row.get("request_count");
        Ok(count as u32)
    }

    async fn cleanup_expired_rate_limits(&self, before: DateTime<Utc>) -> SigmaXResult<u64> {
        let pool = self.db.pool();

        let result = sqlx::query(
            "DELETE FROM api_rate_limits WHERE window_end < $1"
        )
        .bind(&before)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("清理过期限流记录失败: {}", e)))?;

        Ok(result.rows_affected())
    }
}
