//! 引擎工厂

use sigmax_core::{EngineType, UnifiedEngineConfig, SigmaXResult, SigmaXError, ServiceContainer};
use sigmax_interfaces::EngineConfig;
use sigmax_interfaces::ExecutionEngine;
use crate::{
    backtest::BacktestEngine,
    live::LiveTradingEngine,
    paper::PaperEngine,
    config_validator::ConfigurationManager,
};
use std::sync::Arc;

/// 引擎工厂
pub struct EngineFactory {
    config_manager: ConfigurationManager,
}

impl Default for EngineFactory {
    fn default() -> Self {
        Self::new()
    }
}

impl EngineFactory {
    /// 创建新的引擎工厂
    pub fn new() -> Self {
        Self {
            config_manager: ConfigurationManager::new(),
        }
    }
    
    /// 使用自定义配置管理器创建工厂
    pub fn with_config_manager(config_manager: ConfigurationManager) -> Self {
        Self { config_manager }
    }

    /// 创建回测引擎
    pub async fn create_backtest_engine(
        _config: UnifiedEngineConfig,
        _service_container: Arc<dyn ServiceContainer>,
    ) -> SigmaXResult<Box<dyn ExecutionEngine>> {
        // 临时实现：使用默认配置创建回测引擎
        use super::backtest::{BacktestConfig, BacktestEngine};
        let backtest_config = BacktestConfig::default();
        let engine = BacktestEngine::new(backtest_config).await?;
        
        // 创建一个包装器来实现 ExecutionEngine
        // TODO: 实现 ExecutionEngine for BacktestEngine 或创建适配器
        return Err(SigmaXError::internal(
            sigmax_core::InternalErrorCode::UnexpectedState,
            "BacktestEngine ExecutionEngine implementation pending"
        ));
    }

    /// 创建实盘引擎
    pub async fn create_live_engine(
        config: UnifiedEngineConfig,
        service_container: Arc<dyn ServiceContainer>,
    ) -> SigmaXResult<Box<dyn ExecutionEngine>> {
        let engine = LiveTradingEngine::new(config, service_container).await?;
        Ok(Box::new(engine))
    }

    /// 创建纸上交易引擎
    pub async fn create_paper_engine(
        config: UnifiedEngineConfig,
        service_container: Arc<dyn ServiceContainer>,
    ) -> SigmaXResult<Box<dyn ExecutionEngine>> {
        let engine = PaperEngine::new(
            config.engine_id,
            config,
            service_container,
        ).await?;
        Ok(Box::new(engine))
    }

    /// 根据类型创建引擎
    pub async fn create_engine(
        &self,
        engine_type: EngineType,
        mut config: UnifiedEngineConfig,
        service_container: Arc<dyn ServiceContainer>,
    ) -> SigmaXResult<Box<dyn ExecutionEngine>> {
        // 使用配置管理器验证和丰富配置
        self.config_manager.validate_and_enrich(&engine_type, &mut config)?;

        match engine_type {
            EngineType::Backtest => Self::create_backtest_engine(config, service_container).await,
            EngineType::Live => Self::create_live_engine(config, service_container).await,
            EngineType::Paper => Self::create_paper_engine(config, service_container).await,
            EngineType::Simulation => Self::create_backtest_engine(config, service_container).await,
        }
    }

    /// 获取支持的引擎类型
    pub fn get_supported_engine_types() -> Vec<EngineType> {
        vec![EngineType::Backtest, EngineType::Live, EngineType::Paper, EngineType::Simulation]
    }

    /// 获取引擎类型描述
    pub fn get_engine_description(engine_type: &EngineType) -> &'static str {
        match engine_type {
            EngineType::Backtest => "回测引擎：使用历史数据测试交易策略",
            EngineType::Live => "实盘引擎：连接真实交易所进行实盘交易",
            EngineType::Paper => "纸上交易引擎：使用实时数据但不进行真实交易",
            EngineType::Simulation => "模拟引擎：完全模拟的交易环境",
        }
    }

    /// 验证引擎配置（使用配置管理器）
    pub fn validate_engine_config(
        &self,
        engine_type: &EngineType,
        config: &mut EngineConfig,
    ) -> SigmaXResult<()> {
        self.config_manager.validate_and_enrich(engine_type, config)
    }
    
    /// 获取引擎类型描述 (实例方法)
    pub fn get_engine_description_instance(&self, engine_type: &EngineType) -> Option<&str> {
        self.config_manager.get_engine_description(engine_type)
    }
}

#[cfg(test)]
mod tests;
