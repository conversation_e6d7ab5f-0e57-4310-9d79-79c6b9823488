//! 服务定位器实现
//! 
//! 提供全局服务访问和注册功能

use super::Container;
use sigmax_core::SigmaXResult;
use std::sync::{Arc, RwLock};
use std::collections::HashMap;
use std::any::{TypeId, Any};

/// 服务注册表
/// 
/// 线程安全的全局服务注册表
pub struct ServiceRegistry {
    services: RwLock<HashMap<TypeId, Box<dyn Any + Send + Sync>>>,
    singletons: RwLock<HashMap<TypeId, Arc<dyn Any + Send + Sync>>>,
}

impl ServiceRegistry {
    /// 创建新的服务注册表
    pub fn new() -> Self {
        Self {
            services: RwLock::new(HashMap::new()),
            singletons: RwLock::new(HashMap::new()),
        }
    }
    
    /// 注册服务工厂
    pub fn register_factory<T, F>(&self, factory: F) -> SigmaXResult<()>
    where
        T: Send + Sync + 'static,
        F: Fn() -> SigmaXResult<T> + Send + Sync + 'static,
    {
        let type_id = TypeId::of::<T>();
        let boxed_factory: Box<dyn Fn() -> SigmaXResult<T> + Send + Sync> = Box::new(factory);
        
        let mut services = self.services.write()
            .map_err(|_| sigmax_core::SigmaXError::internal(
                sigmax_core::InternalErrorCode::UnexpectedState,
                "Failed to acquire write lock on services"
            ))?;
        
        services.insert(type_id, Box::new(boxed_factory));
        Ok(())
    }
    
    /// 注册单例服务
    pub fn register_singleton<T>(&self, service: Arc<T>) -> SigmaXResult<()>
    where
        T: Send + Sync + 'static,
    {
        let type_id = TypeId::of::<T>();
        
        let mut singletons = self.singletons.write()
            .map_err(|_| sigmax_core::SigmaXError::internal(
                sigmax_core::InternalErrorCode::UnexpectedState,
                "Failed to acquire write lock on singletons"
            ))?;
        
        singletons.insert(type_id, service);
        Ok(())
    }
    
    /// 解析服务
    pub fn resolve<T>(&self) -> SigmaXResult<Arc<T>>
    where
        T: Send + Sync + 'static,
    {
        let type_id = TypeId::of::<T>();
        
        // 首先检查单例
        {
            let singletons = self.singletons.read()
                .map_err(|_| sigmax_core::SigmaXError::internal(
                    sigmax_core::InternalErrorCode::UnexpectedState,
                    "Failed to acquire read lock on singletons"
                ))?;
            
            if let Some(singleton) = singletons.get(&type_id) {
                if let Some(service) = singleton.clone().downcast::<T>().ok() {
                    return Ok(service);
                }
            }
        }
        
        // 然后检查工厂
        {
            let services = self.services.read()
                .map_err(|_| sigmax_core::SigmaXError::internal(
                    sigmax_core::InternalErrorCode::UnexpectedState,
                    "Failed to acquire read lock on services"
                ))?;
            
            if let Some(factory_any) = services.get(&type_id) {
                if let Some(factory) = factory_any.downcast_ref::<Box<dyn Fn() -> SigmaXResult<T> + Send + Sync>>() {
                    let instance = factory()?;
                    return Ok(Arc::new(instance));
                }
            }
        }
        
        Err(sigmax_core::SigmaXError::internal(
            sigmax_core::InternalErrorCode::UnexpectedState,
            format!("Service not registered: {}", std::any::type_name::<T>())
        ))
    }
    
    /// 检查服务是否已注册
    pub fn contains<T>(&self) -> bool
    where
        T: Send + Sync + 'static,
    {
        let type_id = TypeId::of::<T>();
        
        if let Ok(singletons) = self.singletons.read() {
            if singletons.contains_key(&type_id) {
                return true;
            }
        }
        
        if let Ok(services) = self.services.read() {
            if services.contains_key(&type_id) {
                return true;
            }
        }
        
        false
    }
    
    /// 清除所有服务
    pub fn clear(&self) -> SigmaXResult<()> {
        let mut services = self.services.write()
            .map_err(|_| sigmax_core::SigmaXError::internal(
                sigmax_core::InternalErrorCode::UnexpectedState,
                "Failed to acquire write lock on services"
            ))?;
        
        let mut singletons = self.singletons.write()
            .map_err(|_| sigmax_core::SigmaXError::internal(
                sigmax_core::InternalErrorCode::UnexpectedState,
                "Failed to acquire write lock on singletons"
            ))?;
        
        services.clear();
        singletons.clear();
        Ok(())
    }
    
    /// 获取已注册服务的数量
    pub fn service_count(&self) -> usize {
        let services_count = self.services.read().map(|s| s.len()).unwrap_or(0);
        let singletons_count = self.singletons.read().map(|s| s.len()).unwrap_or(0);
        services_count + singletons_count
    }
}

impl Default for ServiceRegistry {
    fn default() -> Self {
        Self::new()
    }
}

/// 全局服务定位器
/// 
/// 提供全局访问服务的便利接口
pub struct ServiceLocator {
    registry: Arc<ServiceRegistry>,
}

impl ServiceLocator {
    /// 创建新的服务定位器
    pub fn new() -> Self {
        Self {
            registry: Arc::new(ServiceRegistry::new()),
        }
    }
    
    /// 使用现有注册表创建服务定位器
    pub fn with_registry(registry: Arc<ServiceRegistry>) -> Self {
        Self { registry }
    }
    
    /// 获取注册表引用
    pub fn registry(&self) -> &Arc<ServiceRegistry> {
        &self.registry
    }
    
    /// 注册服务工厂
    pub fn register_factory<T, F>(&self, factory: F) -> SigmaXResult<()>
    where
        T: Send + Sync + 'static,
        F: Fn() -> SigmaXResult<T> + Send + Sync + 'static,
    {
        self.registry.register_factory(factory)
    }
    
    /// 注册单例服务
    pub fn register_singleton<T>(&self, service: Arc<T>) -> SigmaXResult<()>
    where
        T: Send + Sync + 'static,
    {
        self.registry.register_singleton(service)
    }
    
    /// 解析服务
    pub fn resolve<T>(&self) -> SigmaXResult<Arc<T>>
    where
        T: Send + Sync + 'static,
    {
        self.registry.resolve()
    }
    
    /// 检查服务是否已注册
    pub fn contains<T>(&self) -> bool
    where
        T: Send + Sync + 'static,
    {
        self.registry.contains::<T>()
    }
    
    /// 清除所有服务
    pub fn clear(&self) -> SigmaXResult<()> {
        self.registry.clear()
    }
    
    /// 获取已注册服务的数量
    pub fn service_count(&self) -> usize {
        self.registry.service_count()
    }
}

impl Default for ServiceLocator {
    fn default() -> Self {
        Self::new()
    }
}

impl Clone for ServiceLocator {
    fn clone(&self) -> Self {
        Self {
            registry: Arc::clone(&self.registry),
        }
    }
}

/// 全局服务定位器实例
static GLOBAL_SERVICE_LOCATOR: std::sync::OnceLock<ServiceLocator> = std::sync::OnceLock::new();

/// 获取全局服务定位器
pub fn global_service_locator() -> &'static ServiceLocator {
    GLOBAL_SERVICE_LOCATOR.get_or_init(|| ServiceLocator::new())
}

/// 初始化全局服务定位器
pub fn initialize_global_service_locator(locator: ServiceLocator) -> Result<(), ServiceLocator> {
    GLOBAL_SERVICE_LOCATOR.set(locator)
}

/// 便利宏：注册服务
#[macro_export]
macro_rules! register_service {
    ($locator:expr, $service_type:ty, $factory:expr) => {
        $locator.register_factory::<$service_type, _>($factory)
    };
}

/// 便利宏：注册单例服务
#[macro_export]
macro_rules! register_singleton {
    ($locator:expr, $service:expr) => {
        $locator.register_singleton($service)
    };
}

/// 便利宏：解析服务
#[macro_export]
macro_rules! resolve_service {
    ($locator:expr, $service_type:ty) => {
        $locator.resolve::<$service_type>()
    };
}

#[cfg(test)]
mod tests {
    use super::*;

    #[derive(Debug)]
    struct TestService {
        value: i32,
    }

    #[derive(Debug)]
    struct AnotherService {
        name: String,
    }

    #[tokio::test]
    async fn test_service_registry_basic() {
        let registry = ServiceRegistry::new();
        
        // 注册工厂
        registry.register_factory(|| Ok(TestService { value: 42 })).unwrap();
        
        // 解析服务
        let service = registry.resolve::<TestService>().unwrap();
        assert_eq!(service.value, 42);
        
        // 检查服务是否存在
        assert!(registry.contains::<TestService>());
        assert!(!registry.contains::<AnotherService>());
    }

    #[tokio::test]
    async fn test_service_registry_singleton() {
        let registry = ServiceRegistry::new();
        
        // 注册单例
        let singleton = Arc::new(TestService { value: 100 });
        registry.register_singleton(singleton.clone()).unwrap();
        
        // 解析单例
        let resolved = registry.resolve::<TestService>().unwrap();
        assert_eq!(resolved.value, 100);
        
        // 验证是同一个实例
        assert!(Arc::ptr_eq(&singleton, &resolved));
    }

    #[tokio::test]
    async fn test_service_locator() {
        let locator = ServiceLocator::new();
        
        // 注册服务
        locator.register_factory(|| Ok(TestService { value: 200 })).unwrap();
        locator.register_singleton(Arc::new(AnotherService { 
            name: "test".to_string() 
        })).unwrap();
        
        // 解析服务
        let test_service = locator.resolve::<TestService>().unwrap();
        assert_eq!(test_service.value, 200);
        
        let another_service = locator.resolve::<AnotherService>().unwrap();
        assert_eq!(another_service.name, "test");
        
        // 检查服务数量
        assert_eq!(locator.service_count(), 2);
    }

    #[tokio::test]
    async fn test_service_locator_macros() {
        let locator = ServiceLocator::new();
        
        // 使用宏注册服务
        register_service!(locator, TestService, || Ok(TestService { value: 300 })).unwrap();
        register_singleton!(locator, Arc::new(AnotherService { 
            name: "macro_test".to_string() 
        })).unwrap();
        
        // 使用宏解析服务
        let test_service = resolve_service!(locator, TestService).unwrap();
        assert_eq!(test_service.value, 300);
        
        let another_service = resolve_service!(locator, AnotherService).unwrap();
        assert_eq!(another_service.name, "macro_test");
    }

    #[tokio::test]
    async fn test_global_service_locator() {
        let global = global_service_locator();
        
        // 清除之前的测试数据
        global.clear().unwrap();
        
        // 注册服务
        global.register_factory(|| Ok(TestService { value: 999 })).unwrap();
        
        // 解析服务
        let service = global.resolve::<TestService>().unwrap();
        assert_eq!(service.value, 999);
    }
}
