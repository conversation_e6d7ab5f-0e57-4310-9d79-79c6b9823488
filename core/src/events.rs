//! SigmaX Core Event Models
//!
//! ## 主要职责
//!
//! 1. **事件模型** - 核心事件概念
//!    - `Event` - 统一的事件模型，记录系统中发生的各种事件
//!    - 支持多种事件类型（交易、策略、风险、系统等）
//!
//! 2. **事件元数据模型** - 事件附加信息
//!    - `EventMetadata` - 事件元数据和上下文信息
//!    - 支持事件关联、追踪和审计
//!
//! 3. **事件业务逻辑** - 实用的事件管理方法
//!    - 事件创建、分类、查询
//!    - 事件关联和上下文管理
//!    - 事件序列化和持久化支持
//!
//! ## 设计原则
//!
//! - **统一模型**: 使用单一Event模型，支持多种事件类型
//! - **类型安全**: 使用强类型的事件类型和数据
//! - **可扩展性**: 支持自定义事件类型和数据结构
//! - **业务友好**: 提供实用的事件管理和查询方法

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use validator::{Validate, ValidationError};
use std::collections::HashMap;

use crate::{
    types::*,
    enums::*,
    error::*,
    SigmaXResult,
};

// ============================================================================
// 事件模型 - 核心事件概念
// ============================================================================

/// 事件 - 统一的事件模型
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Event {
    pub event_id: EventId,

    pub event_type: EventType,
    pub event_category: EventCategory,
    pub severity: EventSeverity,

    #[validate(length(min = 1, message = "Event name cannot be empty"))]
    pub name: String,

    pub description: Option<String>,
    pub event_data: serde_json::Value,

    // 关联信息
    pub source: EventSource,
    pub related_entity_type: Option<String>,
    pub related_entity_id: Option<String>,

    // 上下文信息
    pub correlation_id: Option<String>,
    pub causation_id: Option<EventId>,
    pub session_id: Option<String>,
    pub user_id: Option<String>,

    // 状态信息
    pub status: EventStatus,
    pub retry_count: u32,
    pub max_retries: u32,

    pub created_at: DateTime<Utc>,
    pub processed_at: Option<DateTime<Utc>>,
    pub failed_at: Option<DateTime<Utc>>,
}

impl Event {
    /// 创建新的事件
    pub fn new(
        event_type: EventType,
        event_category: EventCategory,
        name: String,
        event_data: serde_json::Value,
    ) -> Self {
        Self {
            event_id: EventId::new(),
            event_type,
            event_category,
            severity: EventSeverity::Info,
            name,
            description: None,
            event_data,
            source: EventSource::System,
            related_entity_type: None,
            related_entity_id: None,
            correlation_id: None,
            causation_id: None,
            session_id: None,
            user_id: None,
            status: EventStatus::Pending,
            retry_count: 0,
            max_retries: 3,
            created_at: Utc::now(),
            processed_at: None,
            failed_at: None,
        }
    }

    /// 创建交易事件
    pub fn trading_event(
        event_type: EventType,
        name: String,
        event_data: serde_json::Value,
    ) -> Self {
        let mut event = Self::new(event_type, EventCategory::Trading, name, event_data);
        event.severity = EventSeverity::Info;
        event
    }

    /// 创建风险事件
    pub fn risk_event(
        event_type: EventType,
        name: String,
        event_data: serde_json::Value,
        severity: EventSeverity,
    ) -> Self {
        let mut event = Self::new(event_type, EventCategory::Risk, name, event_data);
        event.severity = severity;
        event
    }

    /// 创建系统事件
    pub fn system_event(
        event_type: EventType,
        name: String,
        event_data: serde_json::Value,
        severity: EventSeverity,
    ) -> Self {
        let mut event = Self::new(event_type, EventCategory::System, name, event_data);
        event.severity = severity;
        event
    }

    /// 创建策略事件
    pub fn strategy_event(
        event_type: EventType,
        name: String,
        event_data: serde_json::Value,
    ) -> Self {
        let mut event = Self::new(event_type, EventCategory::Strategy, name, event_data);
        event.severity = EventSeverity::Info;
        event
    }

    /// 设置关联实体
    pub fn with_related_entity(mut self, entity_type: String, entity_id: String) -> Self {
        self.related_entity_type = Some(entity_type);
        self.related_entity_id = Some(entity_id);
        self
    }

    /// 设置关联ID
    pub fn with_correlation_id(mut self, correlation_id: String) -> Self {
        self.correlation_id = Some(correlation_id);
        self
    }

    /// 设置因果关系
    pub fn with_causation(mut self, causation_id: EventId) -> Self {
        self.causation_id = Some(causation_id);
        self
    }

    /// 设置用户上下文
    pub fn with_user_context(mut self, user_id: String, session_id: Option<String>) -> Self {
        self.user_id = Some(user_id);
        self.session_id = session_id;
        self
    }

    /// 设置描述
    pub fn with_description(mut self, description: String) -> Self {
        self.description = Some(description);
        self
    }

    /// 设置严重程度
    pub fn with_severity(mut self, severity: EventSeverity) -> Self {
        self.severity = severity;
        self
    }

    /// 标记为已处理
    pub fn mark_as_processed(&mut self) {
        self.status = EventStatus::Processed;
        self.processed_at = Some(Utc::now());
    }

    /// 标记为处理失败
    pub fn mark_as_failed(&mut self) {
        self.status = EventStatus::Failed;
        self.failed_at = Some(Utc::now());
        self.retry_count += 1;
    }

    /// 重置为待处理状态
    pub fn reset_for_retry(&mut self) {
        if self.retry_count < self.max_retries {
            self.status = EventStatus::Pending;
            self.failed_at = None;
        }
    }

    /// 检查是否可以重试
    pub fn can_retry(&self) -> bool {
        self.status == EventStatus::Failed && self.retry_count < self.max_retries
    }

    /// 检查是否已处理
    pub fn is_processed(&self) -> bool {
        self.status == EventStatus::Processed
    }

    /// 检查是否失败
    pub fn is_failed(&self) -> bool {
        self.status == EventStatus::Failed
    }

    /// 检查是否为高优先级事件
    pub fn is_high_priority(&self) -> bool {
        matches!(self.severity, EventSeverity::Warning | EventSeverity::Error | EventSeverity::Critical)
    }

    /// 检查是否为关键事件
    pub fn is_critical(&self) -> bool {
        self.severity == EventSeverity::Critical
    }

    /// 获取事件年龄（秒）
    pub fn age_seconds(&self) -> i64 {
        Utc::now().signed_duration_since(self.created_at).num_seconds()
    }

    /// 获取处理时长（秒）
    pub fn processing_duration_seconds(&self) -> Option<i64> {
        self.processed_at.map(|processed_at| {
            processed_at.signed_duration_since(self.created_at).num_seconds()
        })
    }

    /// 获取事件摘要
    pub fn summary(&self) -> String {
        format!(
            "{:?} - {} ({:?}, {:?})",
            self.event_type,
            self.name,
            self.event_category,
            self.severity
        )
    }

    /// 获取事件的JSON表示
    pub fn to_json(&self) -> SigmaXResult<String> {
        serde_json::to_string(self).map_err(|e| {
            SigmaXError::internal(
                InternalErrorCode::SerializationError,
                format!("Failed to serialize event: {}", e)
            )
        })
    }

    /// 从JSON创建事件
    pub fn from_json(json: &str) -> SigmaXResult<Self> {
        serde_json::from_str(json).map_err(|e| {
            SigmaXError::internal(
                InternalErrorCode::DeserializationError,
                format!("Failed to deserialize event: {}", e)
            )
        })
    }

    /// 执行完整的事件验证
    pub fn validate_complete(&self) -> SigmaXResult<()> {
        // 首先执行基础验证（通过validator derive宏）
        if let Err(validation_errors) = <Self as Validate>::validate(self) {
            return Err(SigmaXError::from(validation_errors));
        }

        // 跨字段验证：重试次数不能超过最大重试次数
        if self.retry_count > self.max_retries {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "retry_count",
                "Retry count cannot exceed max retries"
            ));
        }

        // 跨字段验证：已处理的事件必须有处理时间
        if self.status == EventStatus::Processed && self.processed_at.is_none() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "processed_at",
                "Processed events must have processing time"
            ));
        }

        Ok(())
    }
}

// ============================================================================
// 事件元数据模型 - 事件附加信息
// ============================================================================

/// 事件元数据 - 事件的附加信息和上下文
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct EventMetadata {
    pub metadata_id: EventMetadataId,
    pub event_id: EventId,

    pub metadata_type: EventMetadataType,
    pub key: String,
    pub value: serde_json::Value,

    pub description: Option<String>,
    pub is_sensitive: bool,
    pub is_searchable: bool,

    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl EventMetadata {
    /// 创建新的事件元数据
    pub fn new(
        event_id: EventId,
        metadata_type: EventMetadataType,
        key: String,
        value: serde_json::Value,
    ) -> Self {
        Self {
            metadata_id: EventMetadataId::new(),
            event_id,
            metadata_type,
            key,
            value,
            description: None,
            is_sensitive: false,
            is_searchable: true,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    /// 创建系统元数据
    pub fn system_metadata(
        event_id: EventId,
        key: String,
        value: serde_json::Value,
    ) -> Self {
        Self::new(event_id, EventMetadataType::System, key, value)
    }

    /// 创建用户元数据
    pub fn user_metadata(
        event_id: EventId,
        key: String,
        value: serde_json::Value,
    ) -> Self {
        Self::new(event_id, EventMetadataType::User, key, value)
    }

    /// 创建业务元数据
    pub fn business_metadata(
        event_id: EventId,
        key: String,
        value: serde_json::Value,
    ) -> Self {
        Self::new(event_id, EventMetadataType::Business, key, value)
    }

    /// 创建技术元数据
    pub fn technical_metadata(
        event_id: EventId,
        key: String,
        value: serde_json::Value,
    ) -> Self {
        Self::new(event_id, EventMetadataType::Technical, key, value)
    }

    /// 设置为敏感数据
    pub fn mark_as_sensitive(mut self) -> Self {
        self.is_sensitive = true;
        self
    }

    /// 设置为不可搜索
    pub fn mark_as_non_searchable(mut self) -> Self {
        self.is_searchable = false;
        self
    }

    /// 设置描述
    pub fn with_description(mut self, description: String) -> Self {
        self.description = Some(description);
        self
    }

    /// 更新值
    pub fn update_value(&mut self, new_value: serde_json::Value) {
        self.value = new_value;
        self.updated_at = Utc::now();
    }

    /// 获取字符串值
    pub fn get_string_value(&self) -> Option<String> {
        self.value.as_str().map(|s| s.to_string())
    }

    /// 获取数字值
    pub fn get_number_value(&self) -> Option<f64> {
        self.value.as_f64()
    }

    /// 获取布尔值
    pub fn get_boolean_value(&self) -> Option<bool> {
        self.value.as_bool()
    }

    /// 获取显示值（敏感数据会被掩码）
    pub fn get_display_value(&self) -> String {
        if self.is_sensitive {
            "***".to_string()
        } else {
            self.value.to_string()
        }
    }

    /// 检查是否匹配搜索条件
    pub fn matches_search(&self, query: &str) -> bool {
        if !self.is_searchable {
            return false;
        }

        let query_lower = query.to_lowercase();

        // 搜索键名
        if self.key.to_lowercase().contains(&query_lower) {
            return true;
        }

        // 搜索值（如果不是敏感数据）
        if !self.is_sensitive {
            if let Some(str_value) = self.get_string_value() {
                return str_value.to_lowercase().contains(&query_lower);
            }
        }

        // 搜索描述
        if let Some(desc) = &self.description {
            return desc.to_lowercase().contains(&query_lower);
        }

        false
    }
}

// ============================================================================
// Tests
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    #[test]
    fn test_event_creation() {
        let event_data = json!({
            "order_id": "12345",
            "symbol": "BTCUSDT",
            "side": "buy"
        });

        let event = Event::new(
            EventType::OrderCreated,
            EventCategory::Trading,
            "Order Created".to_string(),
            event_data.clone(),
        );

        assert_eq!(event.event_type, EventType::OrderCreated);
        assert_eq!(event.event_category, EventCategory::Trading);
        assert_eq!(event.name, "Order Created");
        assert_eq!(event.event_data, event_data);
        assert_eq!(event.severity, EventSeverity::Info);
        assert_eq!(event.status, EventStatus::Pending);
        assert_eq!(event.retry_count, 0);
    }

    #[test]
    fn test_event_builder_methods() {
        let event_data = json!({"test": "data"});

        let event = Event::trading_event(
            EventType::TradeExecuted,
            "Trade Executed".to_string(),
            event_data,
        )
        .with_related_entity("Order".to_string(), "order-123".to_string())
        .with_correlation_id("corr-456".to_string())
        .with_user_context("user-789".to_string(), Some("session-abc".to_string()))
        .with_description("A trade was executed successfully".to_string())
        .with_severity(EventSeverity::Info);

        assert_eq!(event.event_category, EventCategory::Trading);
        assert_eq!(event.related_entity_type, Some("Order".to_string()));
        assert_eq!(event.related_entity_id, Some("order-123".to_string()));
        assert_eq!(event.correlation_id, Some("corr-456".to_string()));
        assert_eq!(event.user_id, Some("user-789".to_string()));
        assert_eq!(event.session_id, Some("session-abc".to_string()));
        assert_eq!(event.description, Some("A trade was executed successfully".to_string()));
        assert_eq!(event.severity, EventSeverity::Info);
    }

    #[test]
    fn test_event_status_management() {
        let mut event = Event::system_event(
            EventType::SystemStarted,
            "System Started".to_string(),
            json!({}),
            EventSeverity::Info,
        );

        // 初始状态
        assert_eq!(event.status, EventStatus::Pending);
        assert!(!event.is_processed());
        assert!(!event.is_failed());

        // 标记为已处理
        event.mark_as_processed();
        assert_eq!(event.status, EventStatus::Processed);
        assert!(event.is_processed());
        assert!(event.processed_at.is_some());

        // 重置为失败状态进行测试
        event.status = EventStatus::Pending;
        event.processed_at = None;

        // 标记为失败
        event.mark_as_failed();
        assert_eq!(event.status, EventStatus::Failed);
        assert!(event.is_failed());
        assert_eq!(event.retry_count, 1);
        assert!(event.failed_at.is_some());
    }

    #[test]
    fn test_event_retry_logic() {
        let mut event = Event::new(
            EventType::NotificationSent,
            EventCategory::System,
            "Notification Sent".to_string(),
            json!({}),
        );

        event.max_retries = 2;

        // 初始状态可以重试
        assert!(event.can_retry());

        // 第一次失败
        event.mark_as_failed();
        assert_eq!(event.retry_count, 1);
        assert!(event.can_retry());

        // 重置重试
        event.reset_for_retry();
        assert_eq!(event.status, EventStatus::Pending);
        assert!(event.failed_at.is_none());

        // 第二次失败
        event.mark_as_failed();
        assert_eq!(event.retry_count, 2);
        assert!(event.can_retry());

        // 第三次失败，超过最大重试次数
        event.mark_as_failed();
        assert_eq!(event.retry_count, 3);
        assert!(!event.can_retry());
    }

    #[test]
    fn test_event_priority_checks() {
        let info_event = Event::system_event(
            EventType::SystemStarted,
            "System Started".to_string(),
            json!({}),
            EventSeverity::Info,
        );
        assert!(!info_event.is_high_priority());
        assert!(!info_event.is_critical());

        let warning_event = Event::risk_event(
            EventType::RiskThresholdExceeded,
            "Risk Warning".to_string(),
            json!({}),
            EventSeverity::Warning,
        );
        assert!(warning_event.is_high_priority());
        assert!(!warning_event.is_critical());

        let critical_event = Event::system_event(
            EventType::SystemError,
            "Critical Error".to_string(),
            json!({}),
            EventSeverity::Critical,
        );
        assert!(critical_event.is_high_priority());
        assert!(critical_event.is_critical());
    }

    #[test]
    fn test_event_serialization() {
        let event = Event::trading_event(
            EventType::OrderCreated,
            "Order Created".to_string(),
            json!({"order_id": "12345"}),
        );

        // 序列化
        let json_str = event.to_json().unwrap();
        assert!(json_str.contains("OrderCreated"));
        assert!(json_str.contains("Order Created"));

        // 反序列化
        let deserialized_event = Event::from_json(&json_str).unwrap();
        assert_eq!(deserialized_event.event_type, event.event_type);
        assert_eq!(deserialized_event.name, event.name);
        assert_eq!(deserialized_event.event_data, event.event_data);
    }

    #[test]
    fn test_event_metadata_creation() {
        let event_id = EventId::new();
        let metadata_value = json!({"key": "value", "number": 42});

        let metadata = EventMetadata::new(
            event_id,
            EventMetadataType::Business,
            "test_key".to_string(),
            metadata_value.clone(),
        );

        assert_eq!(metadata.event_id, event_id);
        assert_eq!(metadata.metadata_type, EventMetadataType::Business);
        assert_eq!(metadata.key, "test_key");
        assert_eq!(metadata.value, metadata_value);
        assert!(!metadata.is_sensitive);
        assert!(metadata.is_searchable);
    }

    #[test]
    fn test_event_metadata_builder_methods() {
        let event_id = EventId::new();

        let metadata = EventMetadata::system_metadata(
            event_id,
            "system_info".to_string(),
            json!("system_value"),
        )
        .mark_as_sensitive()
        .mark_as_non_searchable()
        .with_description("System metadata for testing".to_string());

        assert_eq!(metadata.metadata_type, EventMetadataType::System);
        assert!(metadata.is_sensitive);
        assert!(!metadata.is_searchable);
        assert_eq!(metadata.description, Some("System metadata for testing".to_string()));
    }

    #[test]
    fn test_event_metadata_value_getters() {
        let event_id = EventId::new();

        // 字符串值
        let string_metadata = EventMetadata::new(
            event_id,
            EventMetadataType::User,
            "string_key".to_string(),
            json!("test_string"),
        );
        assert_eq!(string_metadata.get_string_value(), Some("test_string".to_string()));
        assert_eq!(string_metadata.get_number_value(), None);
        assert_eq!(string_metadata.get_boolean_value(), None);

        // 数字值
        let number_metadata = EventMetadata::new(
            event_id,
            EventMetadataType::Technical,
            "number_key".to_string(),
            json!(42.5),
        );
        assert_eq!(number_metadata.get_number_value(), Some(42.5));
        assert_eq!(number_metadata.get_string_value(), None);

        // 布尔值
        let bool_metadata = EventMetadata::new(
            event_id,
            EventMetadataType::Business,
            "bool_key".to_string(),
            json!(true),
        );
        assert_eq!(bool_metadata.get_boolean_value(), Some(true));
    }

    #[test]
    fn test_event_metadata_sensitive_display() {
        let event_id = EventId::new();

        let normal_metadata = EventMetadata::new(
            event_id,
            EventMetadataType::User,
            "normal_key".to_string(),
            json!("visible_value"),
        );
        assert_eq!(normal_metadata.get_display_value(), "\"visible_value\"");

        let sensitive_metadata = EventMetadata::new(
            event_id,
            EventMetadataType::User,
            "sensitive_key".to_string(),
            json!("secret_value"),
        ).mark_as_sensitive();
        assert_eq!(sensitive_metadata.get_display_value(), "***");
    }

    #[test]
    fn test_event_metadata_search() {
        let event_id = EventId::new();

        let searchable_metadata = EventMetadata::new(
            event_id,
            EventMetadataType::Business,
            "business_key".to_string(),
            json!("business_value"),
        ).with_description("Business related metadata".to_string());

        // 搜索键名
        assert!(searchable_metadata.matches_search("business"));
        assert!(searchable_metadata.matches_search("BUSINESS")); // 大小写不敏感

        // 搜索值
        assert!(searchable_metadata.matches_search("value"));

        // 搜索描述
        assert!(searchable_metadata.matches_search("related"));

        // 不匹配的搜索
        assert!(!searchable_metadata.matches_search("nonexistent"));

        // 不可搜索的元数据
        let non_searchable_metadata = searchable_metadata.mark_as_non_searchable();
        assert!(!non_searchable_metadata.matches_search("business"));
    }

    #[test]
    fn test_event_validation() {
        let event = Event::new(
            EventType::OrderCreated,
            EventCategory::Trading,
            "Valid Event".to_string(),
            json!({}),
        );

        // 正常事件应该通过验证
        let result = event.validate_complete();
        assert!(result.is_ok());
    }

    #[test]
    fn test_event_summary() {
        let event = Event::risk_event(
            EventType::RiskThresholdExceeded,
            "Risk Alert".to_string(),
            json!({}),
            EventSeverity::Warning,
        );

        let summary = event.summary();
        assert!(summary.contains("RiskThresholdExceeded"));
        assert!(summary.contains("Risk Alert"));
        assert!(summary.contains("Risk"));
        assert!(summary.contains("Warning"));
    }
}

