//! 增强的性能指标计算器
//!
//! 修复P0问题后，重新验证和增强所有性能指标的准确性
//! 实现专业级的金融指标计算

use sigmax_core::{SigmaXResult, SigmaXError, ValidationErrorCode, Amount, Trade};
// 注意：PortfolioSnapshot 已迁移至 sigmax-risk 模块
// use crate::risk::PortfolioSnapshot;

/// 临时的投资组合快照占位符
/// 注意：实际实现已迁移至 sigmax-risk 模块
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortfolioSnapshot {
    pub total_value: Amount,
    pub timestamp: DateTime<Utc>,
    // 其他占位符字段
    pub placeholder: String,
}
use rust_decimal::Decimal;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};

/// 增强的回测结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnhancedBacktestResult {
    /// 基础指标
    pub total_return: Decimal,
    pub annualized_return: Decimal,
    pub max_drawdown: Decimal,
    pub max_drawdown_duration: i64, // 天数

    /// 风险调整收益指标
    pub sharpe_ratio: Decimal,
    pub sortino_ratio: Decimal,
    pub calmar_ratio: Decimal,
    pub information_ratio: Decimal,

    /// 交易统计
    pub total_trades: u64,
    pub win_rate: Decimal,
    pub profit_factor: Decimal,
    pub average_win: Decimal,
    pub average_loss: Decimal,
    pub largest_win: Decimal,
    pub largest_loss: Decimal,

    /// 高级指标
    pub volatility: Decimal,
    pub downside_deviation: Decimal,
    pub var_95: Decimal, // 95% VaR
    pub cvar_95: Decimal, // 95% CVaR
    pub recovery_factor: Decimal,
    pub profit_to_max_drawdown: Decimal,

    /// 时间分析
    pub trading_period_days: i64,
    pub average_holding_period: Decimal, // 小时
    pub max_consecutive_wins: u32,
    pub max_consecutive_losses: u32,

    /// 计算时间戳
    pub calculated_at: DateTime<Utc>,
}

/// 增强的性能指标计算器
pub struct EnhancedMetricsCalculator {
    /// 投资组合快照
    portfolio_snapshots: Vec<PortfolioSnapshot>,
    /// 交易记录
    trades: Vec<Trade>,
    /// 无风险利率（年化）
    risk_free_rate: Decimal,
    /// 基准收益率（可选）
    benchmark_returns: Option<Vec<Decimal>>,
}

impl EnhancedMetricsCalculator {
    /// 创建新的计算器
    pub fn new(
        portfolio_snapshots: Vec<PortfolioSnapshot>,
        trades: Vec<Trade>,
        risk_free_rate: Decimal,
    ) -> Self {
        Self {
            portfolio_snapshots,
            trades,
            risk_free_rate,
            benchmark_returns: None,
        }
    }

    /// 设置基准收益率
    pub fn with_benchmark(mut self, benchmark_returns: Vec<Decimal>) -> Self {
        self.benchmark_returns = Some(benchmark_returns);
        self
    }

    /// 计算增强的回测结果
    pub fn calculate(&self) -> SigmaXResult<EnhancedBacktestResult> {
        if self.portfolio_snapshots.is_empty() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::Required,
                "portfolio_snapshots",
                "投资组合快照为空，无法计算性能指标"
            ));
        }

        // 计算基础指标
        let total_return = self.calculate_total_return()?;
        let annualized_return = self.calculate_annualized_return()?;
        let (max_drawdown, max_drawdown_duration) = self.calculate_max_drawdown_with_duration()?;

        // 计算风险调整收益指标
        let daily_returns = self.calculate_daily_returns()?;
        let sharpe_ratio = self.calculate_accurate_sharpe_ratio(&daily_returns)?;
        let sortino_ratio = self.calculate_sortino_ratio(&daily_returns)?;
        let calmar_ratio = self.calculate_calmar_ratio(annualized_return, max_drawdown)?;
        let information_ratio = self.calculate_information_ratio(&daily_returns)?;

        // 计算交易统计
        let (win_rate, profit_factor, avg_win, avg_loss, largest_win, largest_loss) =
            self.calculate_trading_statistics()?;

        // 计算高级指标
        let volatility = self.calculate_volatility(&daily_returns)?;
        let downside_deviation = self.calculate_downside_deviation(&daily_returns)?;
        let var_95 = self.calculate_var_95(&daily_returns)?;
        let cvar_95 = self.calculate_cvar_95(&daily_returns)?;
        let recovery_factor = self.calculate_recovery_factor(total_return, max_drawdown)?;
        let profit_to_max_drawdown = self.calculate_profit_to_max_drawdown(total_return, max_drawdown)?;

        // 计算时间分析
        let trading_period_days = self.calculate_trading_period_days()?;
        let average_holding_period = self.calculate_average_holding_period()?;
        let (max_consecutive_wins, max_consecutive_losses) = self.calculate_consecutive_trades()?;

        Ok(EnhancedBacktestResult {
            total_return,
            annualized_return,
            max_drawdown,
            max_drawdown_duration,
            sharpe_ratio,
            sortino_ratio,
            calmar_ratio,
            information_ratio,
            total_trades: self.trades.len() as u64,
            win_rate,
            profit_factor,
            average_win: avg_win,
            average_loss: avg_loss,
            largest_win,
            largest_loss,
            volatility,
            downside_deviation,
            var_95,
            cvar_95,
            recovery_factor,
            profit_to_max_drawdown,
            trading_period_days,
            average_holding_period,
            max_consecutive_wins,
            max_consecutive_losses,
            calculated_at: Utc::now(),
        })
    }

    /// 计算总收益率
    fn calculate_total_return(&self) -> SigmaXResult<Decimal> {
        let initial_value = self.portfolio_snapshots
            .first()
            .ok_or_else(|| SigmaXError::validation(ValidationErrorCode::Required, "portfolio_snapshots", "投资组合快照为空"))?
            .total_value;
        let final_value = self.portfolio_snapshots
            .last()
            .ok_or_else(|| SigmaXError::validation(ValidationErrorCode::Required, "portfolio_snapshots", "投资组合快照为空"))?
            .total_value;

        if initial_value > Amount::ZERO {
            Ok((final_value / initial_value) - Decimal::ONE)
        } else {
            Ok(Decimal::ZERO)
        }
    }

    /// 计算年化收益率
    fn calculate_annualized_return(&self) -> SigmaXResult<Decimal> {
        let total_return = self.calculate_total_return()?;
        let trading_days = self.calculate_trading_period_days()?;

        if trading_days > 0 {
            let years = Decimal::from(trading_days) / Decimal::from(365);
            if years > Decimal::ZERO {
                // 年化收益率 = (1 + 总收益率)^(1/年数) - 1
                // 简化计算：总收益率 * (365 / 交易天数)
                Ok(total_return * Decimal::from(365) / Decimal::from(trading_days))
            } else {
                Ok(total_return)
            }
        } else {
            Ok(Decimal::ZERO)
        }
    }

    /// 计算最大回撤和持续时间
    fn calculate_max_drawdown_with_duration(&self) -> SigmaXResult<(Decimal, i64)> {
        let mut max_value = self.portfolio_snapshots[0].total_value;
        let mut max_drawdown = Decimal::ZERO;
        let mut max_drawdown_duration = 0i64;
        let mut current_drawdown_start: Option<DateTime<Utc>> = None;
        let mut _current_max_duration = 0i64;

        for snapshot in &self.portfolio_snapshots {
            if snapshot.total_value > max_value {
                max_value = snapshot.total_value;
                // 新高，结束回撤期
                if let Some(start) = current_drawdown_start {
                    let duration = (snapshot.timestamp - start).num_days();
                    if duration > max_drawdown_duration {
                        max_drawdown_duration = duration;
                    }
                    current_drawdown_start = None;
                }
            } else if max_value > Amount::ZERO {
                let drawdown = (max_value - snapshot.total_value) / max_value;
                if drawdown > max_drawdown {
                    max_drawdown = drawdown;
                }

                // 开始或继续回撤期
                if current_drawdown_start.is_none() {
                    current_drawdown_start = Some(snapshot.timestamp);
                }
            }
        }

        // 处理未结束的回撤期
        if let Some(start) = current_drawdown_start {
            if let Some(last_snapshot) = self.portfolio_snapshots.last() {
                let duration = (last_snapshot.timestamp - start).num_days();
                if duration > max_drawdown_duration {
                    max_drawdown_duration = duration;
                }
            }
        }

        Ok((max_drawdown, max_drawdown_duration))
    }

    /// 计算日收益率序列
    fn calculate_daily_returns(&self) -> SigmaXResult<Vec<Decimal>> {
        let mut daily_returns = Vec::new();

        for i in 1..self.portfolio_snapshots.len() {
            let prev_value = self.portfolio_snapshots[i-1].total_value;
            let curr_value = self.portfolio_snapshots[i].total_value;

            if prev_value > Amount::ZERO {
                let daily_return = (curr_value - prev_value) / prev_value;
                daily_returns.push(daily_return);
            }
        }

        Ok(daily_returns)
    }

    /// 🔥 修复：计算准确的夏普比率（修复标准差计算）
    fn calculate_accurate_sharpe_ratio(&self, daily_returns: &[Decimal]) -> SigmaXResult<Decimal> {
        if daily_returns.len() < 2 {
            return Ok(Decimal::ZERO);
        }

        // 计算平均日收益率
        let mean_return = daily_returns.iter().sum::<Decimal>() / Decimal::from(daily_returns.len());

        // 计算方差
        let variance = daily_returns.iter()
            .map(|&r| (r - mean_return) * (r - mean_return))
            .sum::<Decimal>() / Decimal::from(daily_returns.len());

        // 🔥 修复：正确计算标准差（平方根）
        let std_dev = if variance > Decimal::ZERO {
            // 使用数值方法计算平方根
            self.decimal_sqrt(variance)?
        } else {
            Decimal::ZERO
        };

        // 计算年化指标
        let annualized_return = mean_return * Decimal::from(252); // 252个交易日
        let annualized_volatility = std_dev * self.decimal_sqrt(Decimal::from(252))?;
        let annualized_risk_free = self.risk_free_rate;

        // 夏普比率 = (年化收益率 - 无风险利率) / 年化波动率
        if annualized_volatility > Decimal::ZERO {
            Ok((annualized_return - annualized_risk_free) / annualized_volatility)
        } else {
            Ok(Decimal::ZERO)
        }
    }

    /// 数值方法计算平方根（Decimal类型）
    fn decimal_sqrt(&self, value: Decimal) -> SigmaXResult<Decimal> {
        if value <= Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }

        // 使用牛顿法计算平方根
        let mut x = value / Decimal::from(2); // 初始猜测
        let precision = Decimal::new(1, 10); // 精度：0.0000000001

        for _ in 0..20 { // 最多迭代20次
            let x_new = (x + value / x) / Decimal::from(2);
            if (x_new - x).abs() < precision {
                return Ok(x_new);
            }
            x = x_new;
        }

        Ok(x)
    }

    /// 计算Sortino比率
    fn calculate_sortino_ratio(&self, daily_returns: &[Decimal]) -> SigmaXResult<Decimal> {
        if daily_returns.len() < 2 {
            return Ok(Decimal::ZERO);
        }

        let mean_return = daily_returns.iter().sum::<Decimal>() / Decimal::from(daily_returns.len());
        let downside_deviation = self.calculate_downside_deviation(daily_returns)?;

        let annualized_return = mean_return * Decimal::from(252);
        let annualized_downside_dev = downside_deviation * self.decimal_sqrt(Decimal::from(252))?;

        if annualized_downside_dev > Decimal::ZERO {
            Ok((annualized_return - self.risk_free_rate) / annualized_downside_dev)
        } else {
            Ok(Decimal::ZERO)
        }
    }

    /// 计算Calmar比率
    fn calculate_calmar_ratio(&self, annualized_return: Decimal, max_drawdown: Decimal) -> SigmaXResult<Decimal> {
        if max_drawdown > Decimal::ZERO {
            Ok(annualized_return / max_drawdown)
        } else {
            Ok(Decimal::ZERO)
        }
    }

    /// 计算信息比率
    fn calculate_information_ratio(&self, daily_returns: &[Decimal]) -> SigmaXResult<Decimal> {
        if let Some(benchmark_returns) = &self.benchmark_returns {
            if benchmark_returns.len() != daily_returns.len() {
                return Ok(Decimal::ZERO);
            }

            // 计算超额收益
            let excess_returns: Vec<Decimal> = daily_returns.iter()
                .zip(benchmark_returns.iter())
                .map(|(r, b)| r - b)
                .collect();

            let mean_excess = excess_returns.iter().sum::<Decimal>() / Decimal::from(excess_returns.len());

            // 计算跟踪误差（超额收益的标准差）
            let tracking_error_variance = excess_returns.iter()
                .map(|&r| (r - mean_excess) * (r - mean_excess))
                .sum::<Decimal>() / Decimal::from(excess_returns.len());

            let tracking_error = self.decimal_sqrt(tracking_error_variance)?;

            if tracking_error > Decimal::ZERO {
                Ok(mean_excess * Decimal::from(252) / (tracking_error * self.decimal_sqrt(Decimal::from(252))?))
            } else {
                Ok(Decimal::ZERO)
            }
        } else {
            Ok(Decimal::ZERO)
        }
    }

    /// 🔥 修复：计算真实的交易统计
    fn calculate_trading_statistics(&self) -> SigmaXResult<(Decimal, Decimal, Decimal, Decimal, Decimal, Decimal)> {
        if self.trades.is_empty() {
            return Ok((Decimal::ZERO, Decimal::ZERO, Decimal::ZERO, Decimal::ZERO, Decimal::ZERO, Decimal::ZERO));
        }

        let mut winning_trades = 0;
        let mut total_profit = Decimal::ZERO;
        let mut total_loss = Decimal::ZERO;
        let mut largest_win = Decimal::ZERO;
        let mut largest_loss = Decimal::ZERO;
        let mut win_sum = Decimal::ZERO;
        let mut loss_sum = Decimal::ZERO;
        let mut win_count = 0;
        let mut loss_count = 0;

        // 计算每笔交易的盈亏
        for trade in &self.trades {
            // 计算交易盈亏：卖出时为正，买入时为负（从现金流角度）
            let pnl = match trade.side {
                sigmax_core::OrderSide::Buy => {
                    // 买入：现金减少，资产增加
                    -(trade.quantity * trade.price + trade.fee)
                }
                sigmax_core::OrderSide::Sell => {
                    // 卖出：现金增加，资产减少
                    trade.quantity * trade.price - trade.fee
                }
            };

            if pnl > Decimal::ZERO {
                winning_trades += 1;
                total_profit += pnl;
                win_sum += pnl;
                win_count += 1;
                if pnl > largest_win {
                    largest_win = pnl;
                }
            } else if pnl < Decimal::ZERO {
                total_loss += pnl.abs();
                loss_sum += pnl.abs();
                loss_count += 1;
                if pnl.abs() > largest_loss {
                    largest_loss = pnl.abs();
                }
            }
        }

        let win_rate = Decimal::from(winning_trades) / Decimal::from(self.trades.len());
        let profit_factor = if total_loss > Decimal::ZERO {
            total_profit / total_loss
        } else {
            Decimal::ZERO
        };

        let average_win = if win_count > 0 {
            win_sum / Decimal::from(win_count)
        } else {
            Decimal::ZERO
        };

        let average_loss = if loss_count > 0 {
            loss_sum / Decimal::from(loss_count)
        } else {
            Decimal::ZERO
        };

        Ok((win_rate, profit_factor, average_win, average_loss, largest_win, largest_loss))
    }

    /// 计算波动率
    fn calculate_volatility(&self, daily_returns: &[Decimal]) -> SigmaXResult<Decimal> {
        if daily_returns.len() < 2 {
            return Ok(Decimal::ZERO);
        }

        let mean_return = daily_returns.iter().sum::<Decimal>() / Decimal::from(daily_returns.len());
        let variance = daily_returns.iter()
            .map(|&r| (r - mean_return) * (r - mean_return))
            .sum::<Decimal>() / Decimal::from(daily_returns.len());

        let daily_volatility = self.decimal_sqrt(variance)?;
        // 年化波动率
        Ok(daily_volatility * self.decimal_sqrt(Decimal::from(252))?)
    }

    /// 计算下行偏差
    fn calculate_downside_deviation(&self, daily_returns: &[Decimal]) -> SigmaXResult<Decimal> {
        if daily_returns.is_empty() {
            return Ok(Decimal::ZERO);
        }

        let target_return = Decimal::ZERO; // 通常以0为目标收益率
        let downside_returns: Vec<Decimal> = daily_returns.iter()
            .filter(|&&r| r < target_return)
            .map(|&r| r - target_return)
            .collect();

        if downside_returns.is_empty() {
            return Ok(Decimal::ZERO);
        }

        let downside_variance = downside_returns.iter()
            .map(|&r| r * r)
            .sum::<Decimal>() / Decimal::from(daily_returns.len()); // 注意：分母是总样本数

        self.decimal_sqrt(downside_variance)
    }

    /// 计算95% VaR
    fn calculate_var_95(&self, daily_returns: &[Decimal]) -> SigmaXResult<Decimal> {
        if daily_returns.is_empty() {
            return Ok(Decimal::ZERO);
        }

        let mut sorted_returns = daily_returns.to_vec();
        sorted_returns.sort();

        let index = (daily_returns.len() as f64 * 0.05) as usize;
        if index < sorted_returns.len() {
            Ok(-sorted_returns[index]) // VaR通常表示为正值
        } else {
            Ok(Decimal::ZERO)
        }
    }

    /// 计算95% CVaR (条件风险价值)
    fn calculate_cvar_95(&self, daily_returns: &[Decimal]) -> SigmaXResult<Decimal> {
        if daily_returns.is_empty() {
            return Ok(Decimal::ZERO);
        }

        let mut sorted_returns = daily_returns.to_vec();
        sorted_returns.sort();

        let cutoff_index = (daily_returns.len() as f64 * 0.05) as usize;
        if cutoff_index == 0 {
            return Ok(Decimal::ZERO);
        }

        let tail_returns = &sorted_returns[..cutoff_index];
        if tail_returns.is_empty() {
            return Ok(Decimal::ZERO);
        }

        let cvar = tail_returns.iter().sum::<Decimal>() / Decimal::from(tail_returns.len());
        Ok(-cvar) // CVaR通常表示为正值
    }

    /// 计算恢复因子
    fn calculate_recovery_factor(&self, total_return: Decimal, max_drawdown: Decimal) -> SigmaXResult<Decimal> {
        if max_drawdown > Decimal::ZERO {
            Ok(total_return / max_drawdown)
        } else {
            Ok(Decimal::ZERO)
        }
    }

    /// 计算收益回撤比
    fn calculate_profit_to_max_drawdown(&self, total_return: Decimal, max_drawdown: Decimal) -> SigmaXResult<Decimal> {
        if max_drawdown > Decimal::ZERO {
            Ok(total_return / max_drawdown)
        } else if total_return > Decimal::ZERO {
            Ok(Decimal::from(1000)) // 无回撤时给一个大值
        } else {
            Ok(Decimal::ZERO)
        }
    }

    /// 计算交易周期天数
    fn calculate_trading_period_days(&self) -> SigmaXResult<i64> {
        if self.portfolio_snapshots.len() < 2 {
            return Ok(0);
        }

        let start_time = self.portfolio_snapshots
            .first()
            .ok_or_else(|| SigmaXError::validation(ValidationErrorCode::Required, "portfolio_snapshots", "投资组合快照为空"))?
            .timestamp;
        let end_time = self.portfolio_snapshots
            .last()
            .ok_or_else(|| SigmaXError::validation(ValidationErrorCode::Required, "portfolio_snapshots", "投资组合快照为空"))?
            .timestamp;
        Ok((end_time - start_time).num_days())
    }

    /// 计算平均持仓时间
    fn calculate_average_holding_period(&self) -> SigmaXResult<Decimal> {
        if self.trades.len() < 2 {
            return Ok(Decimal::ZERO);
        }

        // 简化计算：假设交易是成对的（买入-卖出）
        let mut total_holding_hours = 0i64;
        let mut pairs = 0;

        for i in 0..self.trades.len()-1 {
            let current_trade = &self.trades[i];
            let next_trade = &self.trades[i+1];

            // 如果是买入后卖出，计算持仓时间
            if current_trade.side == sigmax_core::OrderSide::Buy &&
               next_trade.side == sigmax_core::OrderSide::Sell &&
               current_trade.trading_pair == next_trade.trading_pair {
                let holding_duration = next_trade.executed_at - current_trade.executed_at;
                total_holding_hours += holding_duration.num_hours();
                pairs += 1;
            }
        }

        if pairs > 0 {
            Ok(Decimal::from(total_holding_hours) / Decimal::from(pairs))
        } else {
            Ok(Decimal::ZERO)
        }
    }

    /// 计算连续盈亏交易
    fn calculate_consecutive_trades(&self) -> SigmaXResult<(u32, u32)> {
        if self.trades.is_empty() {
            return Ok((0, 0));
        }

        let mut max_consecutive_wins = 0u32;
        let mut max_consecutive_losses = 0u32;
        let mut current_wins = 0u32;
        let mut current_losses = 0u32;

        for trade in &self.trades {
            let pnl = match trade.side {
                sigmax_core::OrderSide::Buy => -(trade.quantity * trade.price + trade.fee),
                sigmax_core::OrderSide::Sell => trade.quantity * trade.price - trade.fee,
            };

            if pnl > Decimal::ZERO {
                current_wins += 1;
                current_losses = 0;
                if current_wins > max_consecutive_wins {
                    max_consecutive_wins = current_wins;
                }
            } else if pnl < Decimal::ZERO {
                current_losses += 1;
                current_wins = 0;
                if current_losses > max_consecutive_losses {
                    max_consecutive_losses = current_losses;
                }
            }
        }

        Ok((max_consecutive_wins, max_consecutive_losses))
    }
}
