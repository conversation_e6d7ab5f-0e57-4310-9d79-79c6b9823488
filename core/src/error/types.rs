//! 错误类型和上下文定义

use serde::{Serialize, Deserialize};
use std::collections::HashMap;

/// 错误上下文信息
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ErrorContext {
    /// 错误发生时间戳
    pub timestamp: i64,
    /// 请求ID (用于分布式追踪)
    pub trace_id: Option<String>,
    /// 用户ID (如果适用)
    pub user_id: Option<String>,
    /// 额外的元数据
    pub metadata: HashMap<String, String>,
    /// 错误堆栈 (仅开发环境)
    #[cfg(debug_assertions)]
    pub stack_trace: Option<String>,
}

impl ErrorContext {
    /// 创建新的错误上下文
    pub fn new() -> Self {
        Self {
            timestamp: chrono::Utc::now().timestamp(),
            trace_id: None,
            user_id: None,
            metadata: HashMap::new(),
            #[cfg(debug_assertions)]
            stack_trace: Some(std::backtrace::Backtrace::capture().to_string()),
        }
    }
}

/// 错误类别
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ErrorCategory {
    /// 业务逻辑错误
    Business,
    /// 技术基础设施错误
    Infrastructure,
    /// 外部依赖错误
    External,
    /// 输入验证错误
    Validation,
}

/// 错误严重程度
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum ErrorSeverity {
    /// 信息
    Info,
    /// 警告
    Warning,
    /// 错误
    Error,
    /// 严重
    Critical,
}

// =================== 错误代码枚举 ===================

/// 交易错误代码
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq)]
pub enum TradingErrorCode {
    InvalidOrder,
    InsufficientBalance,
    OrderNotFound,
    InvalidTradingPair,
    MarketClosed,
    OrderExecutionFailed,
}

/// 策略错误代码
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq)]
pub enum StrategyErrorCode {
    NotFound,
    InvalidConfiguration,
    ExecutionFailed,
    StateInconsistent,
    DependencyMissing,
}

/// 风险管理错误代码
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq)]
pub enum RiskErrorCode {
    LimitExceeded,
    CheckFailed,
    InvalidConfiguration,
    ThresholdTriggered,
    EmergencyStop,
}

/// 数据库错误代码
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq)]
pub enum DatabaseErrorCode {
    ConnectionFailed,
    QueryFailed,
    TransactionFailed,
    MigrationFailed,
    ConstraintViolation,
    InsertFailed,
    UpdateFailed,
    DeleteFailed,
    NotFound,
    DataConversionError,
}

/// 网络错误代码
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq)]
pub enum NetworkErrorCode {
    Timeout,
    ConnectionRefused,
    DnsResolution,
    TlsError,
    HttpError,
}

/// 配置错误代码
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq)]
pub enum ConfigErrorCode {
    MissingValue,
    InvalidValue,
    ParseError,
    FileNotFound,
    PermissionDenied,
}

/// 验证错误代码
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq)]
pub enum ValidationErrorCode {
    Required,
    InvalidFormat,
    OutOfRange,
    InvalidRange,
    InvalidLength,
    InvalidType,
}

/// 交易所错误代码
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq)]
pub enum ExchangeErrorCode {
    ApiKeyInvalid,
    RateLimitExceeded,
    InsufficientPermissions,
    MarketNotAvailable,
    OrderRejected,
}

/// 市场数据错误代码
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq)]
pub enum MarketDataErrorCode {
    SourceUnavailable,
    DataStale,
    InvalidSymbol,
    SubscriptionFailed,
    ParseError,
}

/// 内部错误代码
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq)]
pub enum InternalErrorCode {
    Panic,
    ResourceExhausted,
    ServiceUnavailable,
    ConfigurationError,
    UnexpectedState,
    SerializationError,
    DeserializationError,
}