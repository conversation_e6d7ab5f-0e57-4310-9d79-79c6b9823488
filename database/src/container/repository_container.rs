//! Repository容器实现
//! 
//! 提供类型安全的Repository依赖注入

use super::factory_traits::*;
use super::{Container, DefaultContainer, DefaultContainerBuilder};
use sigmax_core::SigmaXResult;
use std::sync::Arc;

/// Repository容器
/// 
/// 专门用于管理Repository工厂的依赖注入容器
pub struct RepositoryContainer {
    container: DefaultContainer,
    composite_factory: Arc<dyn CompositeRepositoryFactory>,
}

impl RepositoryContainer {
    /// 创建新的Repository容器
    pub fn new(composite_factory: Arc<dyn CompositeRepositoryFactory>) -> Self {
        let mut container = DefaultContainer::new();
        
        // 注册组合工厂
        container.register_singleton(composite_factory.clone());
        
        // 注册各个子工厂
        container.register_singleton(composite_factory.order_factory());
        container.register_singleton(composite_factory.trade_factory());
        container.register_singleton(composite_factory.strategy_factory());
        container.register_singleton(composite_factory.event_factory());
        container.register_singleton(composite_factory.risk_factory());
        
        Self {
            container,
            composite_factory,
        }
    }
    
    /// 获取订单Repository工厂
    pub fn order_factory(&self) -> Arc<dyn OrderRepositoryFactory> {
        self.composite_factory.order_factory()
    }
    
    /// 获取交易Repository工厂
    pub fn trade_factory(&self) -> Arc<dyn TradeRepositoryFactory> {
        self.composite_factory.trade_factory()
    }
    
    /// 获取策略Repository工厂
    pub fn strategy_factory(&self) -> Arc<dyn StrategyRepositoryFactory> {
        self.composite_factory.strategy_factory()
    }
    
    /// 获取事件Repository工厂
    pub fn event_factory(&self) -> Arc<dyn EventRepositoryFactory> {
        self.composite_factory.event_factory()
    }
    
    /// 获取风控Repository工厂
    pub fn risk_factory(&self) -> Arc<dyn RiskRepositoryFactory> {
        self.composite_factory.risk_factory()
    }
    
    /// 获取组合工厂
    pub fn composite_factory(&self) -> Arc<dyn CompositeRepositoryFactory> {
        self.composite_factory.clone()
    }
    
    /// 健康检查所有Repository
    pub async fn health_check(&self) -> SigmaXResult<()> {
        self.composite_factory.health_check_all().await
    }
}

impl Container for RepositoryContainer {
    fn register<T: Send + Sync + 'static>(&mut self, service: T) {
        self.container.register(service);
    }
    
    fn register_singleton<T: Send + Sync + 'static + ?Sized>(&mut self, service: Arc<T>) {
        self.container.register_singleton(service);
    }

    fn resolve<T: Send + Sync + 'static + ?Sized>(&self) -> SigmaXResult<Arc<T>> {
        self.container.resolve()
    }

    fn contains<T: Send + Sync + 'static + ?Sized>(&self) -> bool {
        self.container.contains::<T>()
    }
}

/// Repository容器构建器
pub struct RepositoryContainerBuilder {
    builder: DefaultContainerBuilder,
    composite_factory: Option<Arc<dyn CompositeRepositoryFactory>>,
}

impl RepositoryContainerBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            builder: DefaultContainerBuilder::new(),
            composite_factory: None,
        }
    }
    
    /// 设置组合工厂
    pub fn with_composite_factory(mut self, factory: Arc<dyn CompositeRepositoryFactory>) -> Self {
        self.composite_factory = Some(factory);
        self
    }
    
    /// 添加额外的服务
    pub fn with_service<T: Send + Sync + 'static>(mut self, service: T) -> Self {
        self.builder = self.builder.with_service(service);
        self
    }
    
    /// 添加额外的单例服务
    pub fn with_singleton<T: Send + Sync + 'static>(mut self, service: Arc<T>) -> Self {
        self.builder = self.builder.with_singleton(service);
        self
    }
    
    /// 构建容器
    pub fn build(self) -> SigmaXResult<RepositoryContainer> {
        let composite_factory = self.composite_factory
            .ok_or_else(|| sigmax_core::SigmaXError::validation(
                sigmax_core::ValidationErrorCode::Required,
                "composite_factory",
                "Composite factory is required"
            ))?;
        
        Ok(RepositoryContainer::new(composite_factory))
    }
}

impl Default for RepositoryContainerBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// Repository容器配置
#[derive(Debug, Clone)]
pub struct RepositoryContainerConfig {
    /// 工厂类型
    pub factory_type: String,
    /// 是否启用健康检查
    pub enable_health_check: bool,
    /// 健康检查间隔（秒）
    pub health_check_interval_seconds: u64,
}

impl Default for RepositoryContainerConfig {
    fn default() -> Self {
        Self {
            factory_type: "sqlx".to_string(),
            enable_health_check: true,
            health_check_interval_seconds: 60,
        }
    }
}

/// Repository服务定位器
/// 
/// 提供全局访问Repository的便利接口
pub struct RepositoryServiceLocator {
    container: Arc<RepositoryContainer>,
}

impl RepositoryServiceLocator {
    /// 创建新的服务定位器
    pub fn new(container: RepositoryContainer) -> Self {
        Self {
            container: Arc::new(container),
        }
    }
    
    /// 获取容器引用
    pub fn container(&self) -> &Arc<RepositoryContainer> {
        &self.container
    }
    
    /// 获取订单Repository工厂
    pub fn order_factory(&self) -> Arc<dyn OrderRepositoryFactory> {
        self.container.order_factory()
    }
    
    /// 获取交易Repository工厂
    pub fn trade_factory(&self) -> Arc<dyn TradeRepositoryFactory> {
        self.container.trade_factory()
    }
    
    /// 获取策略Repository工厂
    pub fn strategy_factory(&self) -> Arc<dyn StrategyRepositoryFactory> {
        self.container.strategy_factory()
    }
    
    /// 获取事件Repository工厂
    pub fn event_factory(&self) -> Arc<dyn EventRepositoryFactory> {
        self.container.event_factory()
    }
    
    /// 获取风控Repository工厂
    pub fn risk_factory(&self) -> Arc<dyn RiskRepositoryFactory> {
        self.container.risk_factory()
    }
    
    /// 健康检查
    pub async fn health_check(&self) -> SigmaXResult<()> {
        self.container.health_check().await
    }
}

impl Clone for RepositoryServiceLocator {
    fn clone(&self) -> Self {
        Self {
            container: Arc::clone(&self.container),
        }
    }
}

/// 便利函数：创建SQLx Repository容器
pub fn create_sqlx_repository_container(
    database_manager: Arc<crate::managers::DatabaseManager>
) -> SigmaXResult<RepositoryContainer> {
    use super::sqlx_factory::SqlxFactoryBuilder;

    let composite_factory = SqlxFactoryBuilder::new()
        .with_database_manager(database_manager)
        .build()?;

    let container = RepositoryContainerBuilder::new()
        .with_composite_factory(Arc::new(composite_factory))
        .build()?;

    Ok(container)
}

/// 便利函数：创建Mock Repository容器
pub fn create_mock_repository_container(
    config: MockFactoryConfig
) -> SigmaXResult<RepositoryContainer> {
    use super::mock_factory::MockFactoryBuilder;

    let composite_factory = MockFactoryBuilder::new()
        .with_error_simulation(config.simulate_errors, config.error_probability)
        .with_delay(config.delay_ms)
        .with_verbose_logging(config.verbose_logging)
        .build()?;

    let container = RepositoryContainerBuilder::new()
        .with_composite_factory(Arc::new(composite_factory))
        .build()?;

    Ok(container)
}

#[cfg(test)]
mod tests {
    use super::*;
    use async_trait::async_trait;

    // Mock实现用于测试
    struct MockCompositeFactory;

    #[async_trait]
    impl CompositeRepositoryFactory for MockCompositeFactory {
        fn order_factory(&self) -> Arc<dyn OrderRepositoryFactory> {
            Arc::new(MockOrderFactory)
        }
        
        fn trade_factory(&self) -> Arc<dyn TradeRepositoryFactory> {
            Arc::new(MockTradeFactory)
        }
        
        fn strategy_factory(&self) -> Arc<dyn StrategyRepositoryFactory> {
            Arc::new(MockStrategyFactory)
        }
        
        fn event_factory(&self) -> Arc<dyn EventRepositoryFactory> {
            Arc::new(MockEventFactory)
        }
        
        fn risk_factory(&self) -> Arc<dyn RiskRepositoryFactory> {
            Arc::new(MockRiskFactory)
        }
    }

    struct MockOrderFactory;
    #[async_trait]
    impl RepositoryFactory for MockOrderFactory {
        fn factory_type(&self) -> &'static str { "mock_order" }
        async fn health_check(&self) -> SigmaXResult<()> { Ok(()) }
    }
    #[async_trait]
    impl OrderRepositoryFactory for MockOrderFactory {
        async fn save_order(&self, _order: &sigmax_core::Order) -> SigmaXResult<()> { Ok(()) }
        async fn find_order_by_id(&self, _order_id: sigmax_core::OrderId) -> SigmaXResult<Option<sigmax_core::Order>> { Ok(None) }
        async fn find_all_orders(&self) -> SigmaXResult<Vec<sigmax_core::Order>> { Ok(vec![]) }
        async fn find_orders_by_status(&self, _status: sigmax_core::OrderStatus) -> SigmaXResult<Vec<sigmax_core::Order>> { Ok(vec![]) }
        async fn find_orders_by_strategy(&self, _strategy_id: sigmax_core::StrategyId) -> SigmaXResult<Vec<sigmax_core::Order>> { Ok(vec![]) }
    }

    // 其他Mock实现...
    struct MockTradeFactory;
    #[async_trait]
    impl RepositoryFactory for MockTradeFactory {
        fn factory_type(&self) -> &'static str { "mock_trade" }
        async fn health_check(&self) -> SigmaXResult<()> { Ok(()) }
    }
    #[async_trait]
    impl TradeRepositoryFactory for MockTradeFactory {
        async fn save_trade(&self, _trade: &sigmax_core::Trade) -> SigmaXResult<()> { Ok(()) }
        async fn find_all_trades(&self) -> SigmaXResult<Vec<sigmax_core::Trade>> { Ok(vec![]) }
        async fn find_trades_by_pair(&self, _pair: &sigmax_core::TradingPair) -> SigmaXResult<Vec<sigmax_core::Trade>> { Ok(vec![]) }
        async fn find_trades_by_time_range(&self, _start: chrono::DateTime<chrono::Utc>, _end: chrono::DateTime<chrono::Utc>) -> SigmaXResult<Vec<sigmax_core::Trade>> { Ok(vec![]) }
    }

    struct MockStrategyFactory;
    #[async_trait]
    impl RepositoryFactory for MockStrategyFactory {
        fn factory_type(&self) -> &'static str { "mock_strategy" }
        async fn health_check(&self) -> SigmaXResult<()> { Ok(()) }
    }
    #[async_trait]
    impl StrategyRepositoryFactory for MockStrategyFactory {
        async fn save_strategy_state(&self, _strategy_id: sigmax_core::StrategyId, _state: &sigmax_core::StrategyState) -> SigmaXResult<()> { Ok(()) }
        async fn find_strategy_state_by_id(&self, _strategy_id: sigmax_core::StrategyId) -> SigmaXResult<Option<sigmax_core::StrategyState>> { Ok(None) }
        async fn find_all_strategies(&self) -> SigmaXResult<Vec<sigmax_core::StrategyInfo>> { Ok(vec![]) }
    }

    struct MockEventFactory;
    #[async_trait]
    impl RepositoryFactory for MockEventFactory {
        fn factory_type(&self) -> &'static str { "mock_event" }
        async fn health_check(&self) -> SigmaXResult<()> { Ok(()) }
    }
    #[async_trait]
    impl EventRepositoryFactory for MockEventFactory {
        async fn save_event(&self, _event: &sigmax_core::StoredEvent) -> SigmaXResult<sigmax_core::EventId> { Ok(uuid::Uuid::new_v4()) }
        async fn get_event(&self, _event_id: sigmax_core::EventId) -> SigmaXResult<Option<sigmax_core::StoredEvent>> { Ok(None) }
        async fn get_events(&self, _offset: usize, _limit: usize) -> SigmaXResult<Vec<sigmax_core::StoredEvent>> { Ok(vec![]) }
        async fn get_events_by_type(&self, _event_type: sigmax_core::EventType, _limit: usize) -> SigmaXResult<Vec<sigmax_core::StoredEvent>> { Ok(vec![]) }
        async fn mark_event_processed(&self, _event_id: sigmax_core::EventId) -> SigmaXResult<()> { Ok(()) }
    }

    struct MockRiskFactory;
    #[async_trait]
    impl RepositoryFactory for MockRiskFactory {
        fn factory_type(&self) -> &'static str { "mock_risk" }
        async fn health_check(&self) -> SigmaXResult<()> { Ok(()) }
    }
    #[async_trait]
    impl RiskRepositoryFactory for MockRiskFactory {
        async fn save_risk_check(&self, _record: &crate::RiskCheckRecord) -> SigmaXResult<()> { Ok(()) }
        async fn get_risk_checks(&self, _limit: usize) -> SigmaXResult<Vec<crate::RiskCheckRecord>> { Ok(vec![]) }
        async fn save_risk_rule(&self, _rule: &crate::RiskRuleRecord) -> SigmaXResult<()> { Ok(()) }
        async fn get_risk_rules(&self) -> SigmaXResult<Vec<crate::RiskRuleRecord>> { Ok(vec![]) }
    }

    #[tokio::test]
    async fn test_repository_container() {
        let composite_factory = Arc::new(MockCompositeFactory);
        let container = RepositoryContainer::new(composite_factory);
        
        // 测试获取各种工厂
        let order_factory = container.order_factory();
        assert_eq!(order_factory.factory_type(), "mock_order");
        
        let trade_factory = container.trade_factory();
        assert_eq!(trade_factory.factory_type(), "mock_trade");
        
        // 测试健康检查
        assert!(container.health_check().await.is_ok());
    }

    #[tokio::test]
    async fn test_repository_container_builder() {
        let composite_factory = Arc::new(MockCompositeFactory);
        
        let container = RepositoryContainerBuilder::new()
            .with_composite_factory(composite_factory)
            .build()
            .unwrap();
        
        assert!(container.health_check().await.is_ok());
    }

    #[tokio::test]
    async fn test_repository_service_locator() {
        let composite_factory = Arc::new(MockCompositeFactory);
        let container = RepositoryContainer::new(composite_factory);
        let locator = RepositoryServiceLocator::new(container);
        
        // 测试服务定位器
        let order_factory = locator.order_factory();
        assert_eq!(order_factory.factory_type(), "mock_order");
        
        // 测试克隆
        let locator_clone = locator.clone();
        assert!(locator_clone.health_check().await.is_ok());
    }
}
