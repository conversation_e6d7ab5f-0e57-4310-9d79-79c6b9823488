//! 订单Repository实现
//!
//! 基于SQLx的标准订单Repository实现

use std::sync::Arc;
use async_trait::async_trait;

use sqlx::{Row, QueryBuilder, Postgres};
use uuid::Uuid;

use sigmax_core::{
    Order, OrderId, OrderStatus, OrderType, OrderSide, StrategyId, TradingPair, ExchangeId,
    Price, Quantity, Amount, SigmaXResult, SigmaXError, DatabaseErrorCode
};


use crate::{
    DatabaseManager,
    repositories::traits::{
        Repository, OrderRepository,
        QueryFilter, PaginationResult, SortConfig, SortDirection,
        OrderQueryFilter, OrderStatistics
    }
};

/// 订单Repository实现
pub struct OrderRepositoryImpl {
    db: Arc<DatabaseManager>,
}

impl OrderRepositoryImpl {
    /// 创建新的订单Repository实例
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }
    
    /// 获取数据库连接池
    fn pool(&self) -> &sqlx::PgPool {
        self.db.pool()
    }
    
    /// 将数据库行转换为Order对象
    fn row_to_order(&self, row: &sqlx::postgres::PgRow) -> SigmaXResult<Order> {
        let trading_pair = TradingPair::new(
            row.get::<String, _>("base_asset"),
            row.get::<String, _>("quote_asset")
        );

        let order_type = row.get::<String, _>("order_type").parse::<OrderType>()
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::DataConversionError,
                format!("Invalid order type: {}", e)
            ))?;

        let side = match row.get::<String, _>("side").as_str() {
            "buy" => OrderSide::Buy,
            "sell" => OrderSide::Sell,
            s => return Err(SigmaXError::database(
                DatabaseErrorCode::DataConversionError,
                format!("Invalid order side: {}", s)
            ))
        };

        let status = row.get::<String, _>("status").parse::<OrderStatus>()
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::DataConversionError,
                format!("Invalid order status: {}", e)
            ))?;

        // exchange_id是int4，需要转换
        let exchange_id_int: i32 = row.get("exchange_id");
        let exchange_id = match exchange_id_int {
            1 => ExchangeId::Binance,
            2 => ExchangeId::Coinbase,
            3 => ExchangeId::Kraken,
            4 => ExchangeId::OKX,
            _ => ExchangeId::Simulator,
        };

        Ok(Order {
            id: row.get("id"),
            strategy_id: row.get("strategy_id"),
            exchange_id,
            exchange_order_id: row.get("exchange_order_id"),
            parent_order_id: row.get("parent_order_id"),
            trading_pair,
            side,
            order_type,
            quantity: row.get("quantity"),
            price: row.get("price"),
            stop_price: row.get("stop_price"),
            status,
            filled_quantity: row.get("filled_quantity"),
            average_price: row.get("average_price"),
            total_fee: row.get("total_fee"),
            fee_asset: row.get("fee_asset"),
            time_in_force: row.get("time_in_force"),
            client_order_id: row.get("client_order_id"),
            tags: row.get::<serde_json::Value, _>("tags"),
            metadata: row.get::<serde_json::Value, _>("metadata"),
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
            submitted_at: row.get("submitted_at"),
            filled_at: row.get("filled_at"),
            cancelled_at: row.get("cancelled_at"),
        })
    }
}

#[async_trait]
impl Repository<Order, OrderId> for OrderRepositoryImpl {
    async fn save(&self, entity: &Order) -> SigmaXResult<OrderId> {
        // 将ExchangeId转换为数据库中的int4
        let exchange_id_int = match entity.exchange_id {
            ExchangeId::Binance => 1,
            ExchangeId::Coinbase => 2,
            ExchangeId::Kraken => 3,
            ExchangeId::OKX => 4,
            ExchangeId::Simulator => 0,
        };

        let query = r#"
            INSERT INTO orders (
                id, strategy_id, trading_pair_id, exchange_id, exchange_order_id, parent_order_id,
                order_type, side, quantity, price, stop_price, status, filled_quantity, average_price,
                total_fee, fee_asset, time_in_force, client_order_id, tags, metadata,
                created_at, updated_at, submitted_at, filled_at, cancelled_at
            ) VALUES (
                $1, $2,
                (SELECT id FROM trading_pairs WHERE base_asset = $3 AND quote_asset = $4),
                $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21,
                $22, $23, $24, $25, $26
            )
            ON CONFLICT (id) DO UPDATE SET
                strategy_id = EXCLUDED.strategy_id,
                exchange_id = EXCLUDED.exchange_id,
                exchange_order_id = EXCLUDED.exchange_order_id,
                parent_order_id = EXCLUDED.parent_order_id,
                order_type = EXCLUDED.order_type,
                side = EXCLUDED.side,
                quantity = EXCLUDED.quantity,
                price = EXCLUDED.price,
                stop_price = EXCLUDED.stop_price,
                status = EXCLUDED.status,
                filled_quantity = EXCLUDED.filled_quantity,
                average_price = EXCLUDED.average_price,
                total_fee = EXCLUDED.total_fee,
                fee_asset = EXCLUDED.fee_asset,
                time_in_force = EXCLUDED.time_in_force,
                client_order_id = EXCLUDED.client_order_id,
                tags = EXCLUDED.tags,
                metadata = EXCLUDED.metadata,
                updated_at = EXCLUDED.updated_at,
                submitted_at = EXCLUDED.submitted_at,
                filled_at = EXCLUDED.filled_at,
                cancelled_at = EXCLUDED.cancelled_at
        "#;

        sqlx::query(query)
            .bind(entity.id)
            .bind(entity.strategy_id)
            .bind(&entity.trading_pair.base)
            .bind(&entity.trading_pair.quote)
            .bind(exchange_id_int)
            .bind(&entity.exchange_order_id)
            .bind(entity.parent_order_id)
            .bind(entity.order_type.to_string())
            .bind(entity.side.to_string())
            .bind(entity.quantity)
            .bind(entity.price)
            .bind(entity.stop_price)
            .bind(entity.status.to_string())
            .bind(entity.filled_quantity)
            .bind(entity.average_price)
            .bind(entity.total_fee)
            .bind(&entity.fee_asset)
            .bind(&entity.time_in_force)
            .bind(&entity.client_order_id)
            .bind(&entity.tags)
            .bind(&entity.metadata)
            .bind(entity.created_at)
            .bind(entity.updated_at)
            .bind(entity.submitted_at)
            .bind(entity.filled_at)
            .bind(entity.cancelled_at)
            .execute(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::InsertFailed,
                format!("Failed to save order: {}", e)
            ))?;

        Ok(entity.id)
    }
    
    async fn find_by_id(&self, id: OrderId) -> SigmaXResult<Option<Order>> {
        let query = r#"
            SELECT o.id, o.strategy_id, o.exchange_id, o.exchange_order_id, o.parent_order_id,
                   o.order_type, o.side, o.quantity, o.price, o.stop_price, o.status,
                   o.filled_quantity, o.average_price, o.total_fee, o.fee_asset,
                   o.time_in_force, o.client_order_id, o.tags, o.metadata,
                   o.created_at, o.updated_at, o.submitted_at, o.filled_at, o.cancelled_at,
                   tp.base_asset, tp.quote_asset
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            WHERE o.id = $1
        "#;
        
        let row = sqlx::query(query)
            .bind(id)
            .fetch_optional(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find order by ID: {}", e)
            ))?;
        
        match row {
            Some(row) => Ok(Some(self.row_to_order(&row)?)),
            None => Ok(None),
        }
    }
    
    async fn find_all(&self) -> SigmaXResult<Vec<Order>> {
        let query = r#"
            SELECT o.id, o.strategy_id, o.exchange_id, o.exchange_order_id, o.parent_order_id,
                   o.order_type, o.side, o.quantity, o.price, o.stop_price, o.status,
                   o.filled_quantity, o.average_price, o.total_fee, o.fee_asset,
                   o.time_in_force, o.client_order_id, o.tags, o.metadata,
                   o.created_at, o.updated_at, o.submitted_at, o.filled_at, o.cancelled_at,
                   tp.base_asset, tp.quote_asset
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            ORDER BY o.created_at DESC
        "#;
        
        let rows = sqlx::query(query)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find all orders: {}", e)
            ))?;
        
        let mut orders = Vec::new();
        for row in rows {
            orders.push(self.row_to_order(&row)?);
        }
        
        Ok(orders)
    }
    
    async fn update(&self, entity: &Order) -> SigmaXResult<()> {
        // 对于订单，update和save是相同的操作（使用UPSERT）
        self.save(entity).await?;
        Ok(())
    }
    
    async fn delete(&self, id: OrderId) -> SigmaXResult<()> {
        let query = "DELETE FROM orders WHERE id = $1";
        
        let result = sqlx::query(query)
            .bind(id)
            .execute(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::DeleteFailed,
                format!("Failed to delete order: {}", e)
            ))?;
        
        if result.rows_affected() == 0 {
            return Err(SigmaXError::database(
                DatabaseErrorCode::NotFound,
                format!("Order with ID {} not found", id)
            ));
        }
        
        Ok(())
    }
    
    async fn exists(&self, id: OrderId) -> SigmaXResult<bool> {
        let query = "SELECT EXISTS(SELECT 1 FROM orders WHERE id = $1)";
        
        let exists: bool = sqlx::query_scalar(query)
            .bind(id)
            .fetch_one(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to check order existence: {}", e)
            ))?;
        
        Ok(exists)
    }
    
    async fn count(&self) -> SigmaXResult<u64> {
        let query = "SELECT COUNT(*) FROM orders";
        
        let count: i64 = sqlx::query_scalar(query)
            .fetch_one(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to count orders: {}", e)
            ))?;
        
        Ok(count as u64)
    }

    async fn save_batch(&self, entities: &[Order]) -> SigmaXResult<Vec<OrderId>> {
        let mut ids = Vec::new();
        for entity in entities {
            let id = self.save(entity).await?;
            ids.push(id);
        }
        Ok(ids)
    }

    async fn delete_batch(&self, ids: &[OrderId]) -> SigmaXResult<()> {
        for id in ids {
            self.delete(*id).await?;
        }
        Ok(())
    }

    async fn find_with_pagination(&self, offset: u64, limit: u64) -> SigmaXResult<PaginationResult<Order>> {
        let query = r#"
            SELECT o.id, o.strategy_id, o.exchange_id, o.exchange_order_id, o.parent_order_id,
                   o.order_type, o.side, o.quantity, o.price, o.stop_price, o.status,
                   o.filled_quantity, o.average_price, o.total_fee, o.fee_asset,
                   o.time_in_force, o.client_order_id, o.tags, o.metadata,
                   o.created_at, o.updated_at, o.submitted_at, o.filled_at, o.cancelled_at,
                   tp.base_asset, tp.quote_asset
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            ORDER BY o.created_at DESC
            LIMIT $1 OFFSET $2
        "#;

        let rows = sqlx::query(query)
            .bind(limit as i64)
            .bind(offset as i64)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find orders with pagination: {}", e)
            ))?;

        let orders: Result<Vec<Order>, _> = rows.iter().map(|row| self.row_to_order(row)).collect();
        let orders = orders?;

        // 获取总数
        let count_query = "SELECT COUNT(*) FROM orders";
        let total: i64 = sqlx::query_scalar(count_query)
            .fetch_one(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to count orders: {}", e)
            ))?;

        Ok(PaginationResult::new(
            orders,
            total as u64,
            offset,
            limit,
        ))
    }
}

#[async_trait]
impl OrderRepository for OrderRepositoryImpl {
    async fn find_by_status(&self, status: OrderStatus) -> SigmaXResult<Vec<Order>> {
        let query = r#"
            SELECT o.id, o.strategy_id, o.exchange_id, o.exchange_order_id, o.parent_order_id,
                   o.order_type, o.side, o.quantity, o.price, o.stop_price, o.status,
                   o.filled_quantity, o.average_price, o.total_fee, o.fee_asset,
                   o.time_in_force, o.client_order_id, o.tags, o.metadata,
                   o.created_at, o.updated_at, o.submitted_at, o.filled_at, o.cancelled_at,
                   tp.base_asset, tp.quote_asset
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            WHERE o.status = $1
            ORDER BY o.created_at DESC
        "#;

        let rows = sqlx::query(query)
            .bind(status.to_string())
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find orders by status: {}", e)
            ))?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(self.row_to_order(&row)?);
        }

        Ok(orders)
    }

    async fn find_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Vec<Order>> {
        let query = r#"
            SELECT o.*, tp.base_asset, tp.quote_asset
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            WHERE o.strategy_id = $1
            ORDER BY o.created_at DESC
        "#;

        let rows = sqlx::query(query)
            .bind(strategy_id)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find orders by strategy: {}", e)
            ))?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(self.row_to_order(&row)?);
        }

        Ok(orders)
    }

    async fn find_by_exchange(&self, exchange_id: ExchangeId) -> SigmaXResult<Vec<Order>> {
        let query = r#"
            SELECT o.*, tp.base_asset, tp.quote_asset
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            WHERE o.exchange_id = $1
            ORDER BY o.created_at DESC
        "#;

        let rows = sqlx::query(query)
            .bind(exchange_id.to_string())
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find orders by exchange: {}", e)
            ))?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(self.row_to_order(&row)?);
        }

        Ok(orders)
    }

    async fn find_by_trading_pair(&self, trading_pair: &TradingPair) -> SigmaXResult<Vec<Order>> {
        let query = r#"
            SELECT o.*, tp.base_asset, tp.quote_asset
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            WHERE tp.base_asset = $1 AND tp.quote_asset = $2
            ORDER BY o.created_at DESC
        "#;

        let rows = sqlx::query(query)
            .bind(&trading_pair.base)
            .bind(&trading_pair.quote)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find orders by trading pair: {}", e)
            ))?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(self.row_to_order(&row)?);
        }

        Ok(orders)
    }

    async fn find_by_filter(&self, filter: &OrderQueryFilter) -> SigmaXResult<Vec<Order>> {
        // 验证过滤器
        filter.validate()?;

        let mut query_builder = QueryBuilder::new(r#"
            SELECT o.*, tp.base_asset, tp.quote_asset
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            WHERE 1=1
        "#);

        // 添加过滤条件
        if let Some(statuses) = &filter.status {
            if !statuses.is_empty() {
                query_builder.push(" AND o.status = ANY(");
                query_builder.push_bind(statuses.iter().map(|s| s.to_string()).collect::<Vec<_>>());
                query_builder.push(")");
            }
        }

        if let Some(strategy_ids) = &filter.strategy_ids {
            if !strategy_ids.is_empty() {
                query_builder.push(" AND o.strategy_id = ANY(");
                query_builder.push_bind(strategy_ids.clone());
                query_builder.push(")");
            }
        }

        if let Some(trading_pairs) = &filter.trading_pairs {
            if !trading_pairs.is_empty() {
                query_builder.push(" AND (");
                for (i, pair) in trading_pairs.iter().enumerate() {
                    if i > 0 {
                        query_builder.push(" OR ");
                    }
                    query_builder.push("(tp.base_asset = ");
                    query_builder.push_bind(&pair.base);
                    query_builder.push(" AND tp.quote_asset = ");
                    query_builder.push_bind(&pair.quote);
                    query_builder.push(")");
                }
                query_builder.push(")");
            }
        }

        // 添加时间范围过滤
        if let Some((start, end)) = &filter.created_time_range {
            query_builder.push(" AND o.created_at BETWEEN ");
            query_builder.push_bind(start);
            query_builder.push(" AND ");
            query_builder.push_bind(end);
        }

        // 添加排序
        query_builder.push(" ORDER BY o.created_at DESC");

        let query = query_builder.build();
        let rows = query.fetch_all(self.pool()).await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find orders by filter: {}", e)
            ))?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(self.row_to_order(&row)?);
        }

        Ok(orders)
    }

    async fn find_by_time_range(&self, start: chrono::DateTime<chrono::Utc>, end: chrono::DateTime<chrono::Utc>) -> SigmaXResult<Vec<Order>> {
        let query = r#"
            SELECT o.id, o.strategy_id, o.exchange_id, o.exchange_order_id, o.parent_order_id,
                   o.order_type, o.side, o.quantity, o.price, o.stop_price, o.status,
                   o.filled_quantity, o.average_price, o.total_fee, o.fee_asset,
                   o.time_in_force, o.client_order_id, o.tags, o.metadata,
                   o.created_at, o.updated_at, o.submitted_at, o.filled_at, o.cancelled_at,
                   tp.base_asset, tp.quote_asset
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            WHERE o.created_at >= $1 AND o.created_at <= $2
            ORDER BY o.created_at DESC
        "#;

        let rows = sqlx::query(query)
            .bind(start)
            .bind(end)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find orders by time range: {}", e)
            ))?;

        let orders: Result<Vec<Order>, _> = rows.iter().map(|row| self.row_to_order(row)).collect();
        let orders = orders?;

        Ok(orders)
    }

    async fn find_with_filter_and_pagination(
        &self,
        filter: &OrderQueryFilter,
        sort: &[SortConfig],
        offset: u64,
        limit: u64,
    ) -> SigmaXResult<PaginationResult<Order>> {
        let mut query_builder = QueryBuilder::new(r#"
            SELECT o.id, o.strategy_id, o.exchange_id, o.exchange_order_id, o.parent_order_id,
                   o.order_type, o.side, o.quantity, o.price, o.stop_price, o.status,
                   o.filled_quantity, o.average_price, o.total_fee, o.fee_asset,
                   o.time_in_force, o.client_order_id, o.tags, o.metadata,
                   o.created_at, o.updated_at, o.submitted_at, o.filled_at, o.cancelled_at,
                   tp.base_asset, tp.quote_asset
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            WHERE 1=1
        "#);

        // 应用过滤器
        if let Some(status) = &filter.status {
            if !status.is_empty() {
                query_builder.push(" AND o.status = ANY(");
                query_builder.push_bind(status.iter().map(|s| s.to_string()).collect::<Vec<_>>());
                query_builder.push(")");
            }
        }

        if let Some(strategy_ids) = &filter.strategy_ids {
            if !strategy_ids.is_empty() {
                query_builder.push(" AND o.strategy_id = ANY(");
                query_builder.push_bind(strategy_ids);
                query_builder.push(")");
            }
        }

        if let Some((start_time, end_time)) = &filter.created_time_range {
            query_builder.push(" AND o.created_at >= ");
            query_builder.push_bind(start_time);
            query_builder.push(" AND o.created_at <= ");
            query_builder.push_bind(end_time);
        }

        // 应用排序
        if !sort.is_empty() {
            query_builder.push(" ORDER BY ");
            for (i, sort_config) in sort.iter().enumerate() {
                if i > 0 {
                    query_builder.push(", ");
                }
                query_builder.push(&sort_config.field);
                match sort_config.direction {
                    SortDirection::Asc => query_builder.push(" ASC"),
                    SortDirection::Desc => query_builder.push(" DESC"),
                };
            }
        } else {
            query_builder.push(" ORDER BY o.created_at DESC");
        }

        // 应用分页
        query_builder.push(" LIMIT ");
        query_builder.push_bind(limit as i64);
        query_builder.push(" OFFSET ");
        query_builder.push_bind(offset as i64);

        let query = query_builder.build();
        let rows = query.fetch_all(self.pool()).await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find orders with filter and pagination: {}", e)
            ))?;

        let orders: Result<Vec<Order>, _> = rows.iter().map(|row| self.row_to_order(row)).collect();
        let orders = orders?;

        // 获取总数
        let count_query = "SELECT COUNT(*) FROM orders o WHERE 1=1";
        let total: i64 = sqlx::query_scalar(count_query)
            .fetch_one(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to count orders: {}", e)
            ))?;

        Ok(PaginationResult::new(
            orders,
            total as u64,
            offset,
            limit,
        ))
    }

    async fn get_statistics(&self, filter: Option<&OrderQueryFilter>) -> SigmaXResult<OrderStatistics> {
        let mut query_builder = QueryBuilder::new(r#"
            SELECT
                COUNT(*) as total_count,
                COUNT(CASE WHEN status = 'Filled' THEN 1 END) as filled_count,
                COUNT(CASE WHEN status = 'Cancelled' THEN 1 END) as cancelled_count,
                COUNT(CASE WHEN status = 'Pending' THEN 1 END) as pending_count,
                COALESCE(SUM(quantity), 0) as total_quantity,
                COALESCE(SUM(filled_quantity), 0) as total_filled_quantity,
                COALESCE(AVG(price), 0) as average_price,
                MIN(created_at) as earliest_order,
                MAX(created_at) as latest_order
            FROM orders o
            WHERE 1=1
        "#);

        // 应用过滤器
        if let Some(filter) = filter {
            if let Some(status) = &filter.status {
                if !status.is_empty() {
                    query_builder.push(" AND o.status = ANY(");
                    query_builder.push_bind(status.iter().map(|s| s.to_string()).collect::<Vec<_>>());
                    query_builder.push(")");
                }
            }

            if let Some(strategy_ids) = &filter.strategy_ids {
                if !strategy_ids.is_empty() {
                    query_builder.push(" AND o.strategy_id = ANY(");
                    query_builder.push_bind(strategy_ids);
                    query_builder.push(")");
                }
            }
        }

        let query = query_builder.build();
        let row = query.fetch_one(self.pool()).await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to get order statistics: {}", e)
            ))?;

        let mut status_counts = std::collections::HashMap::new();
        status_counts.insert(OrderStatus::Filled, row.get::<i64, _>("filled_count") as u64);
        status_counts.insert(OrderStatus::Cancelled, row.get::<i64, _>("cancelled_count") as u64);
        status_counts.insert(OrderStatus::Pending, row.get::<i64, _>("pending_count") as u64);

        Ok(OrderStatistics {
            total_count: row.get::<i64, _>("total_count") as u64,
            status_counts,
            total_volume: row.get::<rust_decimal::Decimal, _>("total_quantity"),
            average_order_size: row.get::<Option<rust_decimal::Decimal>, _>("average_price").unwrap_or_default(),
            max_order_size: rust_decimal::Decimal::ZERO, // 需要额外查询
            min_order_size: rust_decimal::Decimal::ZERO, // 需要额外查询
            calculated_at: chrono::Utc::now(),
        })
    }

    async fn count_by_status(&self) -> SigmaXResult<Vec<(OrderStatus, u64)>> {
        let query = r#"
            SELECT status, COUNT(*) as count
            FROM orders
            GROUP BY status
            ORDER BY count DESC
        "#;

        let rows = sqlx::query(query)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to count orders by status: {}", e)
            ))?;

        let mut results = Vec::new();
        for row in rows {
            let status_str: String = row.get("status");
            let status = status_str.parse::<OrderStatus>()
                .map_err(|e| SigmaXError::database(
                    DatabaseErrorCode::DataConversionError,
                    format!("Invalid order status: {}", e)
                ))?;
            let count: i64 = row.get("count");
            results.push((status, count as u64));
        }

        Ok(results)
    }

    async fn count_by_trading_pair(&self, limit: u64) -> SigmaXResult<Vec<(TradingPair, u64)>> {
        let query = r#"
            SELECT tp.base_asset, tp.quote_asset, COUNT(*) as count
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            GROUP BY tp.base_asset, tp.quote_asset
            ORDER BY count DESC
            LIMIT $1
        "#;

        let rows = sqlx::query(query)
            .bind(limit as i64)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to count orders by trading pair: {}", e)
            ))?;

        let mut results = Vec::new();
        for row in rows {
            let base_asset: String = row.get("base_asset");
            let quote_asset: String = row.get("quote_asset");
            let trading_pair = TradingPair::new(base_asset, quote_asset);
            let count: i64 = row.get("count");
            results.push((trading_pair, count as u64));
        }

        Ok(results)
    }

    async fn find_active_orders(&self) -> SigmaXResult<Vec<Order>> {
        let query = r#"
            SELECT o.id, o.strategy_id, o.exchange_id, o.exchange_order_id, o.parent_order_id,
                   o.order_type, o.side, o.quantity, o.price, o.stop_price, o.status,
                   o.filled_quantity, o.average_price, o.total_fee, o.fee_asset,
                   o.time_in_force, o.client_order_id, o.tags, o.metadata,
                   o.created_at, o.updated_at, o.submitted_at, o.filled_at, o.cancelled_at,
                   tp.base_asset, tp.quote_asset
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            WHERE o.status IN ('Pending', 'PartiallyFilled')
            ORDER BY o.created_at DESC
        "#;

        let rows = sqlx::query(query)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find active orders: {}", e)
            ))?;

        let orders: Result<Vec<Order>, _> = rows.iter().map(|row| self.row_to_order(row)).collect();
        let orders = orders?;

        Ok(orders)
    }

    async fn find_recent_orders(&self, limit: u64) -> SigmaXResult<Vec<Order>> {
        let query = r#"
            SELECT o.id, o.strategy_id, o.exchange_id, o.exchange_order_id, o.parent_order_id,
                   o.order_type, o.side, o.quantity, o.price, o.stop_price, o.status,
                   o.filled_quantity, o.average_price, o.total_fee, o.fee_asset,
                   o.time_in_force, o.client_order_id, o.tags, o.metadata,
                   o.created_at, o.updated_at, o.submitted_at, o.filled_at, o.cancelled_at,
                   tp.base_asset, tp.quote_asset
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            ORDER BY o.created_at DESC
            LIMIT $1
        "#;

        let rows = sqlx::query(query)
            .bind(limit as i64)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find recent orders: {}", e)
            ))?;

        let orders: Result<Vec<Order>, _> = rows.iter().map(|row| self.row_to_order(row)).collect();
        let orders = orders?;

        Ok(orders)
    }

    async fn preload_with_relations(&self, order_ids: &[OrderId]) -> SigmaXResult<Vec<Order>> {
        if order_ids.is_empty() {
            return Ok(Vec::new());
        }

        let query = r#"
            SELECT o.id, o.strategy_id, o.exchange_id, o.exchange_order_id, o.parent_order_id,
                   o.order_type, o.side, o.quantity, o.price, o.stop_price, o.status,
                   o.filled_quantity, o.average_price, o.total_fee, o.fee_asset,
                   o.time_in_force, o.client_order_id, o.tags, o.metadata,
                   o.created_at, o.updated_at, o.submitted_at, o.filled_at, o.cancelled_at,
                   tp.base_asset, tp.quote_asset
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            WHERE o.id = ANY($1)
            ORDER BY o.created_at DESC
        "#;

        let rows = sqlx::query(query)
            .bind(order_ids)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to preload orders with relations: {}", e)
            ))?;

        let orders: Result<Vec<Order>, _> = rows.iter().map(|row| self.row_to_order(row)).collect();
        let orders = orders?;

        Ok(orders)
    }
}
