//! 实时监控API处理器

use axum::{
    extract::{State, Path},
    response::Json,
};
use tracing::{info};
use uuid::Uuid;
use crate::{
    state::AppState,
    error::{ApiError, ApiResult,ApiResponse},
    realtime_progress_handler::RealtimeProgressHandler,
};

/// 启动实时进度监控
pub async fn start_realtime_monitoring(
    State(state): State<AppState>,
) -> ApiResult<Json<ApiResponse<RealtimeMonitoringStatus>>> {
    info!("启动实时进度监控");

    // 启动实时进度处理器
    state.realtime_progress_handler.start().await
        .map_err(|e| ApiError::Internal(format!("启动实时进度监控失败: {}", e)))?;

    let response_data = RealtimeMonitoringStatus {
        is_running: true,
        websocket_connections: 0,
        timestamp: chrono::Utc::now(),
        engine_id: None,
        engine_type: None,
    };

    let response = ApiResponse::success(response_data,"实时进度监控已启动");
    
    Ok(Json(response))
}

/// 停止实时进度监控
pub async fn stop_realtime_monitoring(
    State(state): State<AppState>,
) -> ApiResult<Json<ApiResponse<RealtimeMonitoringStatus>>> {
    info!("停止实时进度监控");

    // 停止实时进度处理器
    state.realtime_progress_handler.stop().await
        .map_err(|e| ApiError::Internal(format!("停止实时进度监控失败: {}", e)))?;

    let response_data = RealtimeMonitoringStatus {
        is_running: false,
        websocket_connections: 0,
        timestamp: chrono::Utc::now(),
        engine_id: None,
        engine_type: None,
    };

    let response = ApiResponse::success(response_data, "实时进度监控已停止");
    Ok(Json(response))
}

/// 获取实时监控状态
pub async fn get_realtime_monitoring_status(
    State(state): State<AppState>,
) -> ApiResult<Json<ApiResponse<RealtimeMonitoringStatus>>> {
    let is_running = state.realtime_progress_handler.is_running().await;

    let (connection_count, _) = state.websocket_server.get_connection_stats().await;

    let response_data = RealtimeMonitoringStatus {
        is_running,
        websocket_connections: connection_count,
        timestamp: chrono::Utc::now(),
        engine_id: None,
        engine_type: None,
    };

    let response = ApiResponse::success(response_data, "获取实时监控状态成功");
    Ok(Json(response))
}

/// 连接回测引擎的进度推送
pub async fn connect_backtest_progress(
    State(state): State<AppState>,
    Path(engine_id): Path<String>,
) -> ApiResult<Json<ApiResponse<RealtimeMonitoringStatus>>> {
    let engine_id = Uuid::parse_str(&engine_id)
        .map_err(|_| ApiError::BadRequest("Invalid engine ID format".to_string()))?;

    info!("连接回测引擎进度推送，引擎ID: {}", engine_id);

    // 获取引擎实例
    let engine_exists = state.engine_manager.get_engine_exists(engine_id).await;
    if !engine_exists {
        return Err(ApiError::NotFound("Engine not found".to_string()));
    }
    let _engine_status = state.engine_manager.get_engine_status(engine_id).await
        .map_err(ApiError::from)?;

    // 检查是否为回测引擎
    // 注意：engine变量未定义，暂时使用简化实现
    // if let Some(_backtest_engine) = engine.as_any().downcast_ref::<sigmax_engines::BacktestEngine>() {

    // 创建进度事件通道
    let (_sender, receiver) = RealtimeProgressHandler::create_progress_channel();

    // 设置引擎的事件发送器
    // 注意：这里需要修改BacktestEngine以支持设置事件发送器
    // 由于BacktestEngine的set_event_sender方法需要&mut self，我们需要另一种方法

    // 设置进度处理器的接收器
    state.realtime_progress_handler.set_progress_receiver(receiver).await;

    // 启动实时进度监控（如果还没有启动）
    if !state.realtime_progress_handler.is_running().await {
        state.realtime_progress_handler.start().await
            .map_err(|e| ApiError::Internal(format!("启动实时进度监控失败: {}", e)))?;
    }

    let response_data = RealtimeMonitoringStatus {
        is_running: true,
        websocket_connections: 0,
        timestamp: chrono::Utc::now(),
        engine_id: Some(engine_id),
        engine_type: Some("backtest".to_string()),
    };

    let response = ApiResponse::success(response_data, "已连接回测引擎的进度推送");
    Ok(Json(response))
}

/// 断开回测引擎的进度推送
pub async fn disconnect_backtest_progress(
    State(state): State<AppState>,
    Path(engine_id): Path<String>,
) -> ApiResult<Json<ApiResponse<RealtimeMonitoringStatus>>> {
    let engine_id = Uuid::parse_str(&engine_id)
        .map_err(|_| ApiError::BadRequest("Invalid engine ID format".to_string()))?;

    info!("断开回测引擎进度推送，引擎ID: {}", engine_id);

    // 停止实时进度监控
    state.realtime_progress_handler.stop().await
        .map_err(|e| ApiError::Internal(format!("停止实时进度监控失败: {}", e)))?;

    let response_data = RealtimeMonitoringStatus {
        is_running: false,
        websocket_connections: 0,
        timestamp: chrono::Utc::now(),
        engine_id: None,
        engine_type: None,
    };

    let response = ApiResponse::success(response_data, "已断开回测引擎的进度推送");
    Ok(Json(response))
}

/// 实时监控状态响应
#[derive(Debug, serde::Serialize)]
pub struct RealtimeMonitoringStatus {
    pub is_running: bool,
    pub websocket_connections: usize,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub engine_id: Option<Uuid>,
    pub engine_type: Option<String>,
}

/// 获取WebSocket连接统计
pub async fn realtime_websocket_stats(
    State(state): State<AppState>,
) -> ApiResult<Json<WebSocketStats>> {
    let (connection_count, _) = state.websocket_server.get_connection_stats().await;
    let stats = state.websocket_server.get_server_stats().await;

    Ok(Json(WebSocketStats {
        total_connections: connection_count,
        active_connections: connection_count, // 简化实现，假设所有连接都是活跃的
        total_messages_sent: stats.get("total_messages").and_then(|v| v.as_u64()).unwrap_or(0),
        total_messages_received: stats.get("total_received").and_then(|v| v.as_u64()).unwrap_or(0),
        uptime_seconds: state.start_time.elapsed().as_secs(),
        timestamp: chrono::Utc::now(),
    }))
}

/// WebSocket统计信息
#[derive(Debug, serde::Serialize)]
pub struct WebSocketStats {
    pub total_connections: usize,
    pub active_connections: usize,
    pub total_messages_sent: u64,
    pub total_messages_received: u64,
    pub uptime_seconds: u64,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}
