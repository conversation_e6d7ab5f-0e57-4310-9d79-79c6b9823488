// 基础设施接口 -> 移到interfaces crate
interfaces/
├── src/
│   ├── cache.rs           # CacheService trait
│   ├── metrics.rs         # MetricsCollector trait  
│   ├── config.rs          # ConfigService trait
│   ├── database.rs        # DatabaseOperations trait
│   └── services.rs        # ServiceContainer traits

// 策略接口 -> 移到strategies crate
strategies/
├── src/
│   ├── traits/
│   │   ├── strategy.rs    # Strategy trait
│   │   └── container.rs   # StrategyServiceContainer trait



需要迁移的内容
Strategy trait -> 移到strategies crate
CacheService trait -> 移到interfaces crate
MetricsCollector trait -> 移到interfaces crate
ConfigService trait -> 移到interfaces crate