//! Integration层 - 外部模块集成
//!
//! 负责适配外部模块的接口差异，转换不同模块间的数据格式
//! 隔离外部模块的错误影响，提供监控代理功能

pub mod engines_integration;
pub mod database_integration;
pub mod strategies_integration;
pub mod risk_integration;

// 重新导出集成服务
pub use engines_integration::EnginesIntegration;
pub use database_integration::DatabaseIntegration;
pub use strategies_integration::StrategiesIntegration;
pub use risk_integration::RiskIntegration;

/// 集成服务基类
pub trait BaseIntegration {
    /// 获取集成服务名称
    fn name(&self) -> &str;
    
    /// 健康检查
    async fn health_check(&self) -> bool;
    
    /// 获取集成版本
    fn version(&self) -> &str;
    
    /// 获取集成的外部模块名称
    fn external_module(&self) -> &str;
}
