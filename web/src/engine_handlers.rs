//! 引擎管理API处理器

use axum::{
    extract::{Path, State},
    Json,
};
use uuid::Uuid;
use sigmax_core::{EngineConfig, EngineStatus, EngineType, TradingPair, Amount, UnifiedEngineConfig, UnifiedEngineConfigBuilder};
use sigmax_interfaces::{EngineInfo, EngineRunStatus};
use crate::{
    state::AppState,
    error::{ApiError, ApiResult, ApiResponse},
    websocket::SystemEvent,
};

// ============================================================================
// 请求和响应类型
// ============================================================================

/// 创建引擎请求
#[derive(Debug, serde::Deserialize)]
pub struct CreateEngineRequest {
    pub engine_type: EngineType,
    pub trading_pairs: Vec<TradingPair>,
    pub initial_capital: Amount,
    pub exchange_config: Option<serde_json::Value>,
    pub strategy_config: Option<serde_json::Value>,
    pub risk_config: Option<serde_json::Value>,
}

/// 配置验证结果
#[derive(Debug, serde::Serialize)]
pub struct ValidationResult {
    pub is_valid: bool,
    pub errors: Vec<String>,
}



// ============================================================================
// 引擎管理处理器
// ============================================================================

/// 创建引擎
pub async fn create_engine(
    State(state): State<AppState>,
    Json(request): Json<CreateEngineRequest>,
) -> ApiResult<Json<ApiResponse<EngineInfo>>> {
    // 创建引擎配置
    // 创建统一引擎配置
    let unified_config = UnifiedEngineConfigBuilder::new(
        request.engine_type,
        format!("{:?} Engine", request.engine_type)
    )
    .trading_pairs(request.trading_pairs)
    .initial_capital(request.initial_capital)
    .build()
    .map_err(ApiError::from)?;

    // 创建引擎
    let engine_id = state.engine_manager.create_engine(unified_config).await
        .map_err(ApiError::from)?;

    // 发送WebSocket事件
    if let Err(e) = state.websocket_server.broadcast_event(SystemEvent::EngineCreated(engine_id)).await {
        tracing::warn!("Failed to broadcast engine created event: {}", e);
    }

    // 获取引擎信息
    let all_engines = state.engine_manager.get_all_engines().await
        .map_err(ApiError::from)?;
    let engine_info = all_engines.into_iter()
        .find(|info| info.engine_id == engine_id)
        .ok_or_else(|| ApiError::NotFound("Engine not found".to_string()))?;

    // 返回统一格式的响应
    let response = ApiResponse::success(engine_info, "引擎创建成功");
    Ok(Json(response))
}

/// 启动引擎
pub async fn start_engine(
    State(state): State<AppState>,
    Path(engine_id): Path<String>,
) -> ApiResult<Json<ApiResponse<()>>> {
    let engine_id = Uuid::parse_str(&engine_id)
        .map_err(|_| ApiError::BadRequest("Invalid engine ID format".to_string()))?;

    // 🔥 修复：对于回测引擎，检查是否已设置回测配置
    if state.engine_manager.get_engine_exists(engine_id).await {
        // 注意：engine变量未定义，暂时注释掉整个检查
        // if engine.engine_type() == sigmax_core::EngineType::Backtest {
        //     // 检查回测引擎是否已设置配置
        //     if let Some(backtest_engine) = engine.as_any().downcast_ref::<sigmax_engines::BacktestEngine>() {
        //         if !backtest_engine.is_config_set().await {
        //             return Err(ApiError::BadRequest(
        //                 "回测引擎未设置配置，请先调用 POST /api/v1/engines/{id}/backtest/config 设置回测配置".to_string()
        //             ));
        //         }
        //     }
        // }
    }

    // 启动引擎
    match state.engine_manager.start_engine(engine_id).await {
        Ok(_) => {
            // 发送WebSocket事件
            if let Err(e) = state.websocket_server.broadcast_event(SystemEvent::EngineStarted(engine_id)).await {
                tracing::warn!("Failed to broadcast engine started event: {}", e);
            }
            let response = ApiResponse::<()>::success_with_message("引擎启动成功");
            Ok(Json(response))
        }
        Err(e) => {
            tracing::error!("引擎启动失败: {}", e);
            Err(ApiError::from(e))
        }
    }
}

/// 停止引擎
pub async fn stop_engine(
    State(state): State<AppState>,
    Path(engine_id): Path<String>,
) -> ApiResult<Json<ApiResponse<()>>> {
    let engine_id = Uuid::parse_str(&engine_id)
        .map_err(|_| ApiError::BadRequest("Invalid engine ID format".to_string()))?;

    state.engine_manager.stop_engine(engine_id).await
        .map_err(ApiError::from)?;

    // 发送WebSocket事件
    if let Err(e) = state.websocket_server.broadcast_event(SystemEvent::EngineStopped(engine_id)).await {
        tracing::warn!("Failed to broadcast engine stopped event: {}", e);
    }

    let response = ApiResponse::<()>::success_with_message("引擎停止成功");
    Ok(Json(response))
}

/// 获取引擎状态
pub async fn get_engine_status(
    State(state): State<AppState>,
    Path(engine_id): Path<String>,
) -> ApiResult<Json<ApiResponse<EngineRunStatus>>> {
    let engine_id = Uuid::parse_str(&engine_id)
        .map_err(|_| ApiError::BadRequest("Invalid engine ID format".to_string()))?;

    let status = state.engine_manager.get_engine_status(engine_id).await
        .map_err(ApiError::from)?;

    let response = ApiResponse::success(status, "获取引擎状态成功");
    Ok(Json(response))
}

/// 获取引擎信息
pub async fn get_engine(
    State(state): State<AppState>,
    Path(engine_id): Path<String>,
) -> ApiResult<Json<ApiResponse<EngineInfo>>> {
    let engine_id = Uuid::parse_str(&engine_id)
        .map_err(|_| ApiError::BadRequest("Invalid engine ID format".to_string()))?;

    let all_engines = state.engine_manager.get_all_engines().await
        .map_err(ApiError::from)?;
    let engine_info = all_engines.into_iter()
        .find(|info| info.engine_id == engine_id)
        .ok_or_else(|| ApiError::NotFound("Engine not found".to_string()))?;

    let response = ApiResponse::success(engine_info, "获取引擎信息成功");
    Ok(Json(response))
}

/// 获取所有引擎
pub async fn get_engines(
    State(state): State<AppState>,
) -> ApiResult<Json<ApiResponse<Vec<EngineInfo>>>> {
    let engines = state.engine_manager.get_all_engines().await
        .map_err(ApiError::from)?;

    let response = ApiResponse::success(engines, "获取引擎列表成功");
    Ok(Json(response))
}

/// 删除引擎
pub async fn delete_engine(
    State(state): State<AppState>,
    Path(engine_id): Path<String>,
) -> ApiResult<Json<ApiResponse<()>>> {
    let engine_id = Uuid::parse_str(&engine_id)
        .map_err(|_| ApiError::BadRequest("Invalid engine ID format".to_string()))?;

    state.engine_manager.remove_engine(engine_id).await
        .map_err(ApiError::from)?;

    let response = ApiResponse::<()>::success_with_message("引擎删除成功");
    Ok(Json(response))
}

/// 验证引擎配置
pub async fn validate_engine_config(
    State(_state): State<AppState>,
    Json(request): Json<CreateEngineRequest>,
) -> ApiResult<Json<ApiResponse<ValidationResult>>> {
    let result = validate_config_logic(&request);
    let message = if result.is_valid { "配置验证通过" } else { "配置验证失败" };
    let response = ApiResponse::success(result, message);
    Ok(Json(response))
}

// 提取配置验证逻辑
pub fn validate_config_logic(request: &CreateEngineRequest) -> ValidationResult {
    let mut errors = Vec::new();

    // 验证交易对
    if request.trading_pairs.is_empty() {
        errors.push("At least one trading pair is required".to_string());
    }

    // 验证初始资金
    if request.initial_capital <= Amount::ZERO {
        errors.push("Initial capital must be greater than zero".to_string());
    }

    // 验证引擎类型特定配置
    match request.engine_type {
        EngineType::Live => {
            if request.exchange_config.is_none() {
                errors.push("Exchange configuration is required for live trading".to_string());
            }
        }
        EngineType::Backtest => {
            // 回测特定验证
        }
        EngineType::Paper => {
            // 纸上交易特定验证
        }
        EngineType::Simulation => {
            // 模拟交易特定验证
        }
    }

    ValidationResult {
        is_valid: errors.is_empty(),
        errors,
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal::Decimal;

    #[test]
    fn test_validate_engine_config() {
        // 创建有效配置
        let valid_request = CreateEngineRequest {
            engine_type: EngineType::Paper,
            trading_pairs: vec![TradingPair::new("BTC", "USDT")],
            initial_capital: Decimal::from(10000),
            exchange_config: None,
            strategy_config: None,
            risk_config: None,
        };

        // 直接测试验证逻辑
        let result = validate_config_logic(&valid_request);

        assert!(result.is_valid);
        assert!(result.errors.is_empty());
    }

    #[test]
    fn test_validate_invalid_config() {
        // 创建无效配置
        let invalid_request = CreateEngineRequest {
            engine_type: EngineType::Live,
            trading_pairs: vec![], // 空交易对
            initial_capital: Decimal::ZERO, // 零资金
            exchange_config: None, // 实盘交易缺少交易所配置
            strategy_config: None,
            risk_config: None,
        };

        let result = validate_config_logic(&invalid_request);

        assert!(!result.is_valid);
        assert_eq!(result.errors.len(), 3); // 应该有3个错误
    }


}
