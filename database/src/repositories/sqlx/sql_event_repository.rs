//! SQL 事件仓储实现

use std::sync::Arc;
use sqlx::Row;
use sigmax_core::{ DatabaseErrorCode, InternalErrorCode, 
    StoredEvent, EventId, EventType, SigmaXResult, SigmaXError, SystemEvent,
};
use crate::DatabaseManager;
use crate::repositories::traits::EventRepository;

/// SQL事件仓库实现
pub struct SqlEventRepository {
    db: Arc<DatabaseManager>,
}

impl SqlEventRepository {
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }
}

impl EventRepository for SqlEventRepository {
    async fn save_event(&self, event: &StoredEvent) -> SigmaXResult<EventId> {
        let pool = self.db.pool();

        let event_data_json = serde_json::to_value(&event.event_data)
            .map_err(|e| SigmaXError::internal(InternalErrorCode::UnexpectedState, format!("序列化事件数据失败: {}", e)))?;

        sqlx::query(
            r#"
            INSERT INTO events (
                id, event_type, event_data, timestamp, processed, created_at
            ) VALUES (
                $1, $2, $3, $4, $5, NOW()
            )
            "#
        )
        .bind(&event.id)
        .bind(&format!("{:?}", event.event_type))
        .bind(&event_data_json)
        .bind(&(event.timestamp as i64))
        .bind(&event.processed)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("保存事件失败: {}", e)))?;

        Ok(event.id)
    }

    async fn get_event(&self, event_id: EventId) -> SigmaXResult<Option<StoredEvent>> {
        let pool = self.db.pool();

        let row = sqlx::query(
            r#"
            SELECT * FROM events WHERE id = $1
            "#
        )
        .bind(&event_id)
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("获取事件失败: {}", e)))?;

        if let Some(row) = row {
            Ok(Some(self.row_to_stored_event(row)?))
        } else {
            Ok(None)
        }
    }

    async fn get_events(&self, offset: usize, limit: usize) -> SigmaXResult<Vec<StoredEvent>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT * FROM events
            ORDER BY timestamp DESC
            LIMIT $1 OFFSET $2
            "#
        )
        .bind(limit as i64)
        .bind(offset as i64)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("获取事件列表失败: {}", e)))?;

        let mut events = Vec::new();
        for row in rows {
            events.push(self.row_to_stored_event(row)?);
        }

        Ok(events)
    }

    async fn get_events_by_type(&self, event_type: EventType, limit: usize) -> SigmaXResult<Vec<StoredEvent>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT * FROM events
            WHERE event_type = $1
            ORDER BY timestamp DESC
            LIMIT $2
            "#
        )
        .bind(&format!("{:?}", event_type))
        .bind(limit as i64)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("按类型获取事件失败: {}", e)))?;

        let mut events = Vec::new();
        for row in rows {
            events.push(self.row_to_stored_event(row)?);
        }

        Ok(events)
    }

    async fn get_events_by_timerange(&self, start: u64, end: u64, limit: usize) -> SigmaXResult<Vec<StoredEvent>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT * FROM events
            WHERE timestamp >= $1 AND timestamp <= $2
            ORDER BY timestamp DESC
            LIMIT $3
            "#
        )
        .bind(start as i64)
        .bind(end as i64)
        .bind(limit as i64)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("按时间范围获取事件失败: {}", e)))?;

        let mut events = Vec::new();
        for row in rows {
            events.push(self.row_to_stored_event(row)?);
        }

        Ok(events)
    }

    async fn mark_event_processed(&self, event_id: EventId) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            UPDATE events SET processed = true WHERE id = $1
            "#
        )
        .bind(&event_id)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("标记事件已处理失败: {}", e)))?;

        Ok(())
    }

    async fn get_unprocessed_events(&self, limit: usize) -> SigmaXResult<Vec<StoredEvent>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT * FROM events
            WHERE processed = false
            ORDER BY timestamp ASC
            LIMIT $1
            "#
        )
        .bind(limit as i64)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("获取未处理事件失败: {}", e)))?;

        let mut events = Vec::new();
        for row in rows {
            events.push(self.row_to_stored_event(row)?);
        }

        Ok(events)
    }
}

impl SqlEventRepository {
    // ============================================================================
    // 批量操作
    // ============================================================================

    /// 批量保存事件
    pub async fn save_events_batch(&self, events: &[StoredEvent]) -> SigmaXResult<Vec<EventId>> {
        if events.is_empty() {
            return Ok(Vec::new());
        }

        let pool = self.db.pool();
        let mut tx = pool.begin().await
            .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("开始事务失败: {}", e)))?;

        let mut event_ids = Vec::new();

        for event in events {
            let event_data_json = serde_json::to_value(&event.event_data)
                .map_err(|e| SigmaXError::internal(InternalErrorCode::UnexpectedState, format!("序列化事件数据失败: {}", e)))?;

            sqlx::query(
                r#"
                INSERT INTO events (
                    id, event_type, event_data, timestamp, processed, created_at
                ) VALUES (
                    $1, $2, $3, $4, $5, NOW()
                )
                "#
            )
            .bind(&event.id)
            .bind(&format!("{:?}", event.event_type))
            .bind(&event_data_json)
            .bind(&(event.timestamp as i64))
            .bind(&event.processed)
            .execute(&mut *tx)
            .await
            .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("批量保存事件失败: {}", e)))?;

            event_ids.push(event.id);
        }

        tx.commit().await
            .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("提交事务失败: {}", e)))?;

        Ok(event_ids)
    }

    /// 批量标记事件为已处理
    pub async fn mark_events_processed_batch(&self, event_ids: &[EventId]) -> SigmaXResult<()> {
        if event_ids.is_empty() {
            return Ok(());
        }

        let pool = self.db.pool();
        let mut query_builder = sqlx::QueryBuilder::new("UPDATE events SET processed = true WHERE id = ANY(");
        query_builder.push_bind(event_ids);
        query_builder.push(")");

        let result = query_builder.build()
            .execute(pool)
            .await
            .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("批量标记事件已处理失败: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(SigmaXError::database(DatabaseErrorCode::QueryFailed, "没有事件被标记为已处理".to_string()));
        }

        Ok(())
    }

    /// 批量删除事件
    pub async fn delete_events_batch(&self, event_ids: &[EventId]) -> SigmaXResult<()> {
        if event_ids.is_empty() {
            return Ok(());
        }

        let pool = self.db.pool();
        let mut query_builder = sqlx::QueryBuilder::new("DELETE FROM events WHERE id = ANY(");
        query_builder.push_bind(event_ids);
        query_builder.push(")");

        let result = query_builder.build()
            .execute(pool)
            .await
            .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("批量删除事件失败: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(SigmaXError::database(DatabaseErrorCode::QueryFailed, "没有事件被删除".to_string()));
        }

        Ok(())
    }

    /// 批量获取事件
    pub async fn get_events_batch(&self, event_ids: &[EventId]) -> SigmaXResult<Vec<StoredEvent>> {
        if event_ids.is_empty() {
            return Ok(Vec::new());
        }

        let pool = self.db.pool();
        let mut query_builder = sqlx::QueryBuilder::new("SELECT * FROM events WHERE id = ANY(");
        query_builder.push_bind(event_ids);
        query_builder.push(") ORDER BY timestamp DESC");

        let rows = query_builder.build()
            .fetch_all(pool)
            .await
            .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("批量获取事件失败: {}", e)))?;

        let mut events = Vec::new();
        for row in rows {
            events.push(self.row_to_stored_event(row)?);
        }

        Ok(events)
    }

    /// 清理旧事件（批量删除指定时间之前的已处理事件）
    pub async fn cleanup_old_events(&self, before_timestamp: u64, limit: usize) -> SigmaXResult<u64> {
        let pool = self.db.pool();

        let result = sqlx::query(
            r#"
            DELETE FROM events
            WHERE processed = true
            AND timestamp < $1
            AND id IN (
                SELECT id FROM events
                WHERE processed = true AND timestamp < $1
                ORDER BY timestamp ASC
                LIMIT $2
            )
            "#
        )
        .bind(before_timestamp as i64)
        .bind(limit as i64)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("清理旧事件失败: {}", e)))?;

        Ok(result.rows_affected())
    }
}

impl SqlEventRepository {
    fn row_to_stored_event(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<StoredEvent> {
        let event_type_str: String = row.get("event_type");
        let event_type = match event_type_str.as_str() {
            "OrderCreated" => EventType::OrderCreated,
            "OrderUpdated" => EventType::OrderUpdated,
            "OrderCancelled" => EventType::OrderCancelled,
            "TradeExecuted" => EventType::TradeExecuted,
            "StrategyStarted" => EventType::StrategyStarted,
            "StrategyStoped" => EventType::StrategyStoped,
            "RiskAlert" => EventType::RiskAlert,
            "SystemError" => EventType::SystemError,
            _ => return Err(SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("未知事件类型: {}", event_type_str))),
        };

        let event_data_json: serde_json::Value = row.get("event_data");
        let event_data: SystemEvent = serde_json::from_value(event_data_json)
            .map_err(|e| SigmaXError::database(DatabaseErrorCode::QueryFailed, format!("反序列化事件数据失败: {}", e)))?;

        let timestamp: i64 = row.get("timestamp");

        Ok(StoredEvent {
            id: row.get("id"),
            event_type,
            event_data,
            timestamp: timestamp as u64,
            processed: row.get("processed"),
        })
    }
}
