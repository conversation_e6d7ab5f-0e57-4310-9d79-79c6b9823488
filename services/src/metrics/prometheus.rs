//! Prometheus 指标收集器实现

use async_trait::async_trait;
use prometheus::{Counter, Gauge, Histogram, Registry};
use sigmax_core::{traits::MetricsCollector, SigmaXResult};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::RwLock;
use tracing::debug;

// use crate::errors::{ServiceError, ServiceResult};

/// Prometheus 指标收集器
pub struct PrometheusMetricsCollector {
    registry: Arc<Registry>,
    counters: Arc<RwLock<HashMap<String, Counter>>>,
    gauges: Arc<RwLock<HashMap<String, Gauge>>>,
    histograms: Arc<RwLock<HashMap<String, Histogram>>>,
}

impl PrometheusMetricsCollector {
    /// 创建新的 Prometheus 指标收集器
    pub fn new() -> Self {
        Self {
            registry: Arc::new(Registry::new()),
            counters: Arc::new(RwLock::new(HashMap::new())),
            gauges: Arc::new(RwLock::new(HashMap::new())),
            histograms: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 获取或创建计数器
    async fn get_or_create_counter(&self, name: &str) -> Result<Counter, String> {
        let mut counters = self.counters.write().await;

        if let Some(counter) = counters.get(name) {
            Ok(counter.clone())
        } else {
            let counter = Counter::new(name, &format!("Counter for {}", name))
                .map_err(|e| format!("Failed to create counter: {}", e))?;

            self.registry.register(Box::new(counter.clone()))
                .map_err(|e| format!("Failed to register counter: {}", e))?;

            counters.insert(name.to_string(), counter.clone());
            Ok(counter)
        }
    }

    /// 获取或创建仪表
    async fn get_or_create_gauge(&self, name: &str) -> Result<Gauge, String> {
        let mut gauges = self.gauges.write().await;

        if let Some(gauge) = gauges.get(name) {
            Ok(gauge.clone())
        } else {
            let gauge = Gauge::new(name, &format!("Gauge for {}", name))
                .map_err(|e| format!("Failed to create gauge: {}", e))?;

            self.registry.register(Box::new(gauge.clone()))
                .map_err(|e| format!("Failed to register gauge: {}", e))?;

            gauges.insert(name.to_string(), gauge.clone());
            Ok(gauge)
        }
    }
}

#[async_trait]
impl MetricsCollector for PrometheusMetricsCollector {
    async fn record_risk_check(&self, passed: bool) {
        let counter_name = if passed { "risk_checks_passed" } else { "risk_checks_failed" };
        if let Ok(counter) = self.get_or_create_counter(counter_name).await {
            counter.inc();
            debug!("Recorded risk check: {}", passed);
        }
    }

    async fn record_cache_hit(&self) {
        if let Ok(counter) = self.get_or_create_counter("cache_hits").await {
            counter.inc();
            debug!("Recorded cache hit");
        }
    }

    async fn record_latency(&self, operation: &str, duration: Duration) {
        let histogram_name = format!("operation_latency_{}", operation);
        let mut histograms = self.histograms.write().await;

        let histogram = if let Some(hist) = histograms.get(&histogram_name) {
            hist.clone()
        } else {
            // 简化的直方图创建
            let opts = prometheus::HistogramOpts::new(&histogram_name, &format!("Latency for {}", operation))
                .buckets(vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0])
                .const_label("operation", operation);

            match Histogram::with_opts(opts) {
                Ok(hist) => {
                    if self.registry.register(Box::new(hist.clone())).is_ok() {
                        histograms.insert(histogram_name.clone(), hist.clone());
                        hist
                    } else {
                        return;
                    }
                }
                Err(_) => return,
            }
        };

        histogram.observe(duration.as_secs_f64());
        debug!("Recorded latency for {}: {:?}", operation, duration);
    }

    async fn get_metrics(&self) -> SigmaXResult<HashMap<String, f64>> {
        let metric_families = self.registry.gather();
        let mut metrics = HashMap::new();

        for family in metric_families {
            for metric in family.get_metric() {
                let name = family.get_name();

                // 检查计数器
                if metric.has_counter() {
                    let counter = metric.get_counter();
                    metrics.insert(name.to_string(), counter.get_value());
                }
                // 检查仪表
                else if metric.has_gauge() {
                    let gauge = metric.get_gauge();
                    metrics.insert(name.to_string(), gauge.get_value());
                }
                // 检查直方图
                else if metric.has_histogram() {
                    let histogram = metric.get_histogram();
                    metrics.insert(format!("{}_count", name), histogram.get_sample_count() as f64);
                    metrics.insert(format!("{}_sum", name), histogram.get_sample_sum());
                }
            }
        }

        Ok(metrics)
    }
}

impl Default for PrometheusMetricsCollector {
    fn default() -> Self {
        Self::new()
    }
}
