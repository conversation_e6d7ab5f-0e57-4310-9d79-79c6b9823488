//! 订单风控策略

use async_trait::async_trait;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use crate::core::{RiskPolicy, RiskResult, RiskContext};

/// 订单风控配置
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct OrderRiskConfig {
    /// 最大订单数量
    pub max_order_quantity: Decimal,
    /// 最大订单价值
    pub max_order_value: Decimal,
    /// 最小订单数量
    pub min_order_quantity: Decimal,
    /// 最小订单价值
    pub min_order_value: Decimal,
    /// 是否启用
    pub enabled: bool,
}

impl Default for OrderRiskConfig {
    fn default() -> Self {
        Self {
            max_order_quantity: Decimal::from(10000),
            max_order_value: Decimal::from(100000),
            min_order_quantity: Decimal::from(1),
            min_order_value: Decimal::from(10),
            enabled: true,
        }
    }
}

/// 订单风控策略
pub struct OrderRiskPolicy {
    config: OrderRiskConfig,
}

impl OrderRiskPolicy {
    pub fn new(config: OrderRiskConfig) -> Self {
        Self { config }
    }
}

impl Default for OrderRiskPolicy {
    fn default() -> Self {
        Self::new(OrderRiskConfig::default())
    }
}

#[async_trait]
impl RiskPolicy for OrderRiskPolicy {
    async fn evaluate(&self, context: &RiskContext) -> RiskResult {
        // 如果不是订单上下文，直接通过
        let order = match &context.order {
            Some(order) => order,
            None => return RiskResult::approved(),
        };
        
        let quantity = order.quantity;
        let price = order.price.unwrap_or(rust_decimal::Decimal::ZERO);
        let value = quantity * price;
        
        // 检查最大数量
        if quantity > self.config.max_order_quantity {
            return RiskResult::rejected(format!(
                "订单数量 {} 超过最大限制 {}",
                quantity, self.config.max_order_quantity
            ));
        }
        
        // 检查最小数量
        if quantity < self.config.min_order_quantity {
            return RiskResult::rejected(format!(
                "订单数量 {} 低于最小限制 {}",
                quantity, self.config.min_order_quantity
            ));
        }
        
        // 检查最大价值
        if value > self.config.max_order_value {
            return RiskResult::rejected(format!(
                "订单价值 {} 超过最大限制 {}",
                value, self.config.max_order_value
            ));
        }
        
        // 检查最小价值
        if value < self.config.min_order_value {
            return RiskResult::rejected(format!(
                "订单价值 {} 低于最小限制 {}",
                value, self.config.min_order_value
            ));
        }
        
        RiskResult::approved()
    }
    
    fn name(&self) -> &str {
        "OrderRiskPolicy"
    }
    
    fn description(&self) -> &str {
        "订单数量和价值限制检查"
    }
    
    fn priority(&self) -> i32 {
        100 // 高优先级
    }
    
    fn is_enabled(&self) -> bool {
        self.config.enabled
    }
}
