//! SigmaX Core Validation Examples
//!
//! 展示如何使用validation系统的示例代码

use crate::{
    types::*,
    enums::*,
    validation::*,
};
use rust_decimal::Decimal;
use serde_json::json;

/// 展示基本验证工具的使用
pub fn example_validation_utils() {
    println!("=== Validation Utils Examples ===");
    
    // 字符串长度验证
    match ValidationUtils::validate_string_length("Hello", 1, 10, "greeting") {
        Ok(_) => println!("✅ String length validation passed"),
        Err(e) => println!("❌ String length validation failed: {}", e),
    }
    
    // 数值范围验证
    match ValidationUtils::validate_numeric_range(5.0, 1.0, 10.0, "score") {
        Ok(_) => println!("✅ Numeric range validation passed"),
        Err(e) => println!("❌ Numeric range validation failed: {}", e),
    }
    
    // 正数验证
    match ValidationUtils::validate_positive_number(100.0, "amount") {
        Ok(_) => println!("✅ Positive number validation passed"),
        Err(e) => println!("❌ Positive number validation failed: {}", e),
    }
    
    // 百分比验证
    match ValidationUtils::validate_percentage(75.0, "success_rate") {
        Ok(_) => println!("✅ Percentage validation passed"),
        Err(e) => println!("❌ Percentage validation failed: {}", e),
    }
    
    println!();
}

/// 展示订单验证规则的使用
pub fn example_order_validation_rules() {
    println!("=== Order Validation Rules Examples ===");
    
    // 验证订单数量
    let quantity = Decimal::new(1, 0); // 1.0
    match OrderValidationRules::validate_quantity(quantity) {
        Ok(_) => println!("✅ Order quantity validation passed"),
        Err(e) => println!("❌ Order quantity validation failed: {}", e),
    }
    
    // 验证订单价格
    let price = Some(Decimal::new(50000, 0)); // 50000.0
    match OrderValidationRules::validate_price(price, OrderType::Limit) {
        Ok(_) => println!("✅ Order price validation passed"),
        Err(e) => println!("❌ Order price validation failed: {}", e),
    }
    
    // 验证订单金额
    match OrderValidationRules::validate_order_amount(quantity, price) {
        Ok(_) => println!("✅ Order amount validation passed"),
        Err(e) => println!("❌ Order amount validation failed: {}", e),
    }
    
    // 验证完整订单请求
    let order_request = OrderRequest {
        trading_pair: TradingPair {
            base: "BTC".to_string(),
            quote: "USDT".to_string(),
        },
        side: OrderSide::Buy,
        order_type: OrderType::Limit,
        quantity,
        price,
    };
    
    match OrderValidationRules::validate_order_request(&order_request) {
        Ok(_) => println!("✅ Complete order request validation passed"),
        Err(e) => println!("❌ Complete order request validation failed: {}", e),
    }
    
    println!();
}

/// 展示交易验证规则的使用
pub fn example_trading_validation_rules() {
    println!("=== Trading Validation Rules Examples ===");
    
    // 验证交易对
    let trading_pair = TradingPair {
        base: "ETH".to_string(),
        quote: "BTC".to_string(),
    };
    
    match TradingValidationRules::validate_trading_pair(&trading_pair) {
        Ok(_) => println!("✅ Trading pair validation passed"),
        Err(e) => println!("❌ Trading pair validation failed: {}", e),
    }
    
    // 验证无效的交易对（相同的基础和报价货币）
    let invalid_pair = TradingPair {
        base: "BTC".to_string(),
        quote: "BTC".to_string(),
    };
    
    match TradingValidationRules::validate_trading_pair(&invalid_pair) {
        Ok(_) => println!("✅ Invalid trading pair validation unexpectedly passed"),
        Err(e) => println!("✅ Invalid trading pair correctly rejected: {}", e),
    }
    
    println!();
}

/// 展示风险验证规则的使用
pub fn example_risk_validation_rules() {
    println!("=== Risk Validation Rules Examples ===");
    
    // 验证回撤百分比
    let drawdown = Decimal::new(15, 0); // 15%
    match RiskValidationRules::validate_drawdown(drawdown) {
        Ok(_) => println!("✅ Drawdown validation passed"),
        Err(e) => println!("❌ Drawdown validation failed: {}", e),
    }
    
    // 验证持仓规模
    let position_size = Decimal::new(5000, 0); // 5,000
    let max_size = Decimal::new(10000, 0); // 10,000
    match RiskValidationRules::validate_position_size(position_size, max_size) {
        Ok(_) => println!("✅ Position size validation passed"),
        Err(e) => println!("❌ Position size validation failed: {}", e),
    }
    
    // 验证风险比率
    let risk_amount = Decimal::new(500, 0); // 500
    let total_capital = Decimal::new(10000, 0); // 10,000
    match RiskValidationRules::validate_risk_ratio(risk_amount, total_capital) {
        Ok(_) => println!("✅ Risk ratio validation passed"),
        Err(e) => println!("❌ Risk ratio validation failed: {}", e),
    }
    
    // 验证完整风险限制
    let risk_limits = RiskLimits {
        max_position_size: Decimal::new(100000, 0), // 100,000
        max_drawdown: Decimal::new(20, 0), // 20%
        max_daily_loss: Decimal::new(5000, 0), // 5,000
    };
    
    match RiskValidationRules::validate_risk_limits(&risk_limits) {
        Ok(_) => println!("✅ Complete risk limits validation passed"),
        Err(e) => println!("❌ Complete risk limits validation failed: {}", e),
    }
    
    println!();
}

/// 展示验证器组合的使用
pub async fn example_validator_composer() {
    println!("=== Validator Composer Examples ===");
    
    // 创建订单请求
    let order_request = OrderRequest {
        trading_pair: TradingPair {
            base: "BTC".to_string(),
            quote: "USDT".to_string(),
        },
        side: OrderSide::Buy,
        order_type: OrderType::Limit,
        quantity: Decimal::new(1, 0), // 1.0
        price: Some(Decimal::new(50000, 0)), // 50000.0
    };
    
    // 创建验证器组合
    let composer = ValidatorComposer::new()
        .fail_fast(false) // 不快速失败，收集所有错误
        .add_validator(Box::new(OrderQuantityValidator::default()))
        .add_validator(Box::new(OrderPriceValidator::default()))
        .add_validator(Box::new(TradingPairValidator::allow_all()));
    
    println!("Validator count: {}", composer.validator_count());
    println!("Enabled validator count: {}", composer.enabled_validator_count());
    
    // 执行所有验证器
    match composer.validate_all(&order_request).await {
        Ok(_) => println!("✅ All validators passed"),
        Err(e) => println!("❌ Some validators failed: {}", e),
    }
    
    println!();
}

/// 展示默认验证服务的使用
pub async fn example_default_validation_service() {
    println!("=== Default Validation Service Examples ===");
    
    let service = DefaultValidationService::new();
    
    // 验证策略配置
    let strategy_config = json!({
        "name": "Moving Average Strategy",
        "type": "trend_following",
        "parameters": {
            "short_period": 10,
            "long_period": 20
        }
    });
    
    match service.validate_strategy_config(&strategy_config).await {
        Ok(_) => println!("✅ Strategy config validation passed"),
        Err(e) => println!("❌ Strategy config validation failed: {}", e),
    }
    
    // 验证订单请求
    let order_request = OrderRequest {
        trading_pair: TradingPair {
            base: "ETH".to_string(),
            quote: "USDT".to_string(),
        },
        side: OrderSide::Sell,
        order_type: OrderType::Market,
        quantity: Decimal::new(5, 1), // 0.5
        price: None, // 市价单没有价格
    };
    
    match service.validate_order_request(&order_request).await {
        Ok(_) => println!("✅ Order request validation passed"),
        Err(e) => println!("❌ Order request validation failed: {}", e),
    }
    
    // 验证风险限制
    let risk_limits = RiskLimits {
        max_position_size: Decimal::new(50000, 0), // 50,000
        max_drawdown: Decimal::new(10, 0), // 10%
        max_daily_loss: Decimal::new(2000, 0), // 2,000
    };
    
    match service.validate_risk_limits(&risk_limits).await {
        Ok(_) => println!("✅ Risk limits validation passed"),
        Err(e) => println!("❌ Risk limits validation failed: {}", e),
    }
    
    // 验证投资组合配置
    let portfolio_config = PortfolioConfig {
        name: "Conservative Portfolio".to_string(),
        initial_balance: Decimal::new(25000, 0), // 25,000
        risk_tolerance: 0.3, // 30%
    };
    
    match service.validate_portfolio_config(&portfolio_config).await {
        Ok(_) => println!("✅ Portfolio config validation passed"),
        Err(e) => println!("❌ Portfolio config validation failed: {}", e),
    }
    
    // 验证交易对
    let trading_pair = TradingPair {
        base: "ADA".to_string(),
        quote: "BTC".to_string(),
    };
    
    match service.validate_trading_pair(&trading_pair).await {
        Ok(_) => println!("✅ Trading pair validation passed"),
        Err(e) => println!("❌ Trading pair validation failed: {}", e),
    }
    
    // 验证价格范围
    let min_price = Decimal::new(100, 0); // 100
    let max_price = Decimal::new(500, 0); // 500
    
    match service.validate_price_range(min_price, max_price).await {
        Ok(_) => println!("✅ Price range validation passed"),
        Err(e) => println!("❌ Price range validation failed: {}", e),
    }
    
    println!();
}

/// 运行所有示例
pub async fn run_all_examples() {
    println!("🚀 SigmaX Core Validation System Examples\n");
    
    example_validation_utils();
    example_order_validation_rules();
    example_trading_validation_rules();
    example_risk_validation_rules();
    example_validator_composer().await;
    example_default_validation_service().await;
    
    println!("✨ All examples completed!");
}
