//! 错误处理便捷宏
//!
//! 提供快速创建和处理错误的宏定义

/// 快速创建交易错误
/// 
/// # 示例
/// 
/// ```rust
/// use sigmax_core::{trading_error, TradingErrorCode};
/// 
/// // 基本用法
/// let error = trading_error!(TradingErrorCode::InsufficientBalance, "余额不足");
/// 
/// // 带订单ID
/// let error = trading_error!(TradingErrorCode::OrderNotFound, "订单未找到", order_id);
/// ```
#[macro_export]
macro_rules! trading_error {
    ($code:expr, $msg:expr) => {
        $crate::SigmaXError::trading($code, $msg)
    };
    ($code:expr, $msg:expr, $order_id:expr) => {
        $crate::SigmaXError::trading_with_order($code, $msg, $order_id)
    };
}

/// 快速创建策略错误
/// 
/// # 示例
/// 
/// ```rust
/// use sigmax_core::{strategy_error, StrategyErrorCode};
/// use uuid::Uuid;
/// 
/// let strategy_id = Uuid::new_v4();
/// let error = strategy_error!(
///     StrategyErrorCode::ExecutionFailed,
///     "策略执行失败",
///     strategy_id,
///     "GridStrategy"
/// );
/// ```
#[macro_export]
macro_rules! strategy_error {
    ($code:expr, $msg:expr, $strategy_id:expr, $strategy_type:expr) => {
        $crate::SigmaXError::strategy($code, $msg, $strategy_id, $strategy_type)
    };
}

/// 快速创建验证错误
/// 
/// # 示例
/// 
/// ```rust
/// use sigmax_core::{validation_error, ValidationErrorCode};
/// 
/// let error = validation_error!("quantity", "订单数量必须大于0");
/// let error = validation_error!(ValidationErrorCode::Required, "email", "邮箱不能为空");
/// ```
#[macro_export]
macro_rules! validation_error {
    ($field:expr, $msg:expr) => {
        $crate::SigmaXError::validation(
            $crate::ValidationErrorCode::InvalidFormat,
            $field,
            $msg
        )
    };
    ($code:expr, $field:expr, $msg:expr) => {
        $crate::SigmaXError::validation($code, $field, $msg)
    };
}

/// 快速创建配置错误
/// 
/// # 示例
/// 
/// ```rust
/// use sigmax_core::{config_error, ConfigErrorCode};
/// 
/// let error = config_error!("数据库URL未配置");
/// let error = config_error!(ConfigErrorCode::MissingValue, "缺少必要的配置项");
/// ```
#[macro_export]
macro_rules! config_error {
    ($msg:expr) => {
        $crate::SigmaXError::configuration(
            $crate::ConfigErrorCode::InvalidValue,
            $msg
        )
    };
    ($code:expr, $msg:expr) => {
        $crate::SigmaXError::configuration($code, $msg)
    };
}

/// 快速创建数据库错误
/// 
/// # 示例
/// 
/// ```rust
/// use sigmax_core::{database_error, DatabaseErrorCode};
/// 
/// let error = database_error!("连接数据库失败");
/// let error = database_error!(DatabaseErrorCode::QueryFailed, "SQL查询失败");
/// ```
#[macro_export]
macro_rules! database_error {
    ($msg:expr) => {
        $crate::SigmaXError::database(
            $crate::DatabaseErrorCode::QueryFailed,
            $msg
        )
    };
    ($code:expr, $msg:expr) => {
        $crate::SigmaXError::database($code, $msg)
    };
}

/// 快速创建内部错误
/// 
/// # 示例
/// 
/// ```rust
/// use sigmax_core::{internal_error, InternalErrorCode};
/// 
/// let error = internal_error!("未预期的系统状态");
/// let error = internal_error!(InternalErrorCode::Panic, "系统发生崩溃");
/// ```
#[macro_export]
macro_rules! internal_error {
    ($msg:expr) => {
        $crate::SigmaXError::internal(
            $crate::InternalErrorCode::UnexpectedState,
            $msg
        )
    };
    ($code:expr, $msg:expr) => {
        $crate::SigmaXError::internal($code, $msg)
    };
}

/// 错误重试宏 - 自动重试可重试的错误
/// 
/// # 示例
/// 
/// ```rust
/// use sigmax_core::retry_on_error;
/// 
/// let result = retry_on_error!(risky_operation(), 3).await;
/// let result = retry_on_error!(network_call(), 5, 2000).await; // 最多5次，间隔2秒
/// ```
#[macro_export]
macro_rules! retry_on_error {
    ($expr:expr, $max_attempts:expr) => {{
        let mut attempts = 0;
        loop {
            attempts += 1;
            match $expr {
                Ok(result) => break Ok(result),
                Err(e) if e.is_retryable() && attempts < $max_attempts => {
                    if let Some(delay) = e.retry_delay_ms() {
                        tokio::time::sleep(std::time::Duration::from_millis(delay)).await;
                    }
                    tracing::warn!("重试第 {} 次: {}", attempts, e);
                    continue;
                }
                Err(e) => break Err(e),
            }
        }
    }};
    ($expr:expr, $max_attempts:expr, $delay_ms:expr) => {{
        let mut attempts = 0;
        loop {
            attempts += 1;
            match $expr {
                Ok(result) => break Ok(result),
                Err(e) if e.is_retryable() && attempts < $max_attempts => {
                    tokio::time::sleep(std::time::Duration::from_millis($delay_ms)).await;
                    tracing::warn!("重试第 {} 次: {}", attempts, e);
                    continue;
                }
                Err(e) => break Err(e),
            }
        }
    }};
}

/// 确保操作成功的宏 - 如果失败则记录错误并返回
/// 
/// # 示例
/// 
/// ```rust
/// use sigmax_core::ensure_success;
/// 
/// ensure_success!(validate_input(&data), "输入验证失败");
/// ensure_success!(save_to_database(&record), "保存数据失败: {}", record.id);
/// ```
#[macro_export]
macro_rules! ensure_success {
    ($expr:expr, $msg:expr) => {
        if let Err(e) = $expr {
            tracing::error!("{}: {}", $msg, e);
            return Err(e.with_context("ensure_failed", $msg));
        }
    };
    ($expr:expr, $fmt:expr, $($args:expr),*) => {
        if let Err(e) = $expr {
            let msg = format!($fmt, $($args),*);
            tracing::error!("{}: {}", msg, e);
            return Err(e.with_context("ensure_failed", &msg));
        }
    };
}

/// 条件验证宏 - 如果条件不满足则返回验证错误
/// 
/// # 示例
/// 
/// ```rust
/// use sigmax_core::ensure_condition;
/// 
/// ensure_condition!(quantity > 0, "quantity", "订单数量必须大于0");
/// ensure_condition!(price.is_some(), ValidationErrorCode::Required, "price", "限价单必须指定价格");
/// ```
#[macro_export]
macro_rules! ensure_condition {
    ($condition:expr, $field:expr, $msg:expr) => {
        if !$condition {
            return Err($crate::validation_error!($field, $msg));
        }
    };
    ($condition:expr, $code:expr, $field:expr, $msg:expr) => {
        if !$condition {
            return Err($crate::validation_error!($code, $field, $msg));
        }
    };
}

/// 结果链式处理宏 - 为 Result 添加上下文信息
/// 
/// # 示例
/// 
/// ```rust
/// use sigmax_core::with_context;
/// 
/// let result = risky_operation()
///     .with_context!("operation", "risk_calculation")
///     .with_context!("user_id", user.id);
/// ```
#[macro_export]
macro_rules! with_context {
    ($result:expr, $key:expr, $value:expr) => {
        $result.map_err(|e| e.with_context($key, $value.to_string()))
    };
}

/// 错误日志记录宏 - 根据错误严重程度自动选择日志级别
/// 
/// # 示例
/// 
/// ```rust
/// use sigmax_core::log_sigmax_error;
/// 
/// log_sigmax_error!(error, "交易执行失败");
/// log_sigmax_error!(error, "用户 {} 的订单 {} 执行失败", user_id, order_id);
/// ```
#[macro_export]
macro_rules! log_sigmax_error {
    ($error:expr, $msg:expr) => {
        match $error.severity() {
            $crate::ErrorSeverity::Critical => {
                tracing::error!(
                    error_code = $error.error_code(),
                    error_category = format!("{:?}", $error.category()),
                    "{}: {}",
                    $msg,
                    $error
                );
            },
            $crate::ErrorSeverity::Error => {
                tracing::error!(
                    error_code = $error.error_code(),
                    "{}: {}",
                    $msg,
                    $error
                );
            },
            $crate::ErrorSeverity::Warning => {
                tracing::warn!(
                    error_code = $error.error_code(),
                    "{}: {}",
                    $msg,
                    $error
                );
            },
            $crate::ErrorSeverity::Info => {
                tracing::info!(
                    error_code = $error.error_code(),
                    "{}: {}",
                    $msg,
                    $error
                );
            },
        }
    };
    ($error:expr, $fmt:expr, $($args:expr),*) => {
        let msg = format!($fmt, $($args),*);
        $crate::log_sigmax_error!($error, msg);
    };
}

/// 性能监控宏 - 记录操作执行时间和结果
/// 
/// # 示例
/// 
/// ```rust
/// use sigmax_core::measure_performance;
/// 
/// let result = measure_performance!("database_query", {
///     database.query("SELECT * FROM orders").await
/// });
/// ```
#[macro_export]
macro_rules! measure_performance {
    ($operation_name:expr, $block:block) => {{
        let start = std::time::Instant::now();
        let result = $block;
        let duration = start.elapsed();
        
        match &result {
            Ok(_) => {
                tracing::info!(
                    operation = $operation_name,
                    duration_ms = duration.as_millis(),
                    status = "success",
                    "操作完成"
                );
            },
            Err(e) => {
                tracing::warn!(
                    operation = $operation_name,
                    duration_ms = duration.as_millis(),
                    status = "error",
                    error_code = e.error_code(),
                    "操作失败: {}",
                    e
                );
            }
        }
        
        result
    }};
}

#[cfg(test)]
mod tests {
    use crate::{SigmaXError, TradingErrorCode, ValidationErrorCode, ConfigErrorCode};
    use uuid::Uuid;
    
    #[test]
    fn test_trading_error_macro() {
        let error = trading_error!(TradingErrorCode::InsufficientBalance, "余额不足");
        assert!(matches!(error, SigmaXError::Trading { .. }));
        assert_eq!(error.error_code(), "TRADING_INSUFFICIENT_BALANCE");
    }
    
    #[test]
    fn test_validation_error_macro() {
        let error = validation_error!("quantity", "数量无效");
        assert!(matches!(error, SigmaXError::Validation { .. }));
        
        let error = validation_error!(ValidationErrorCode::Required, "email", "邮箱必填");
        assert!(matches!(error, SigmaXError::Validation { .. }));
    }
    
    #[test]
    fn test_config_error_macro() {
        let error = config_error!("配置错误");
        assert!(matches!(error, SigmaXError::Configuration { .. }));
        
        let error = config_error!(ConfigErrorCode::MissingValue, "缺少配置");
        assert!(matches!(error, SigmaXError::Configuration { .. }));
    }
    
    #[tokio::test]
    async fn test_retry_macro() {
        let mut attempt_count = 0;
        
        let result = retry_on_error!({
            attempt_count += 1;
            if attempt_count < 3 {
                Err(SigmaXError::network(
                    crate::NetworkErrorCode::Timeout,
                    "网络超时"
                ).retryable())
            } else {
                Ok("成功")
            }
        }, 5).await;
        
        assert!(result.is_ok());
        assert_eq!(attempt_count, 3);
    }
}