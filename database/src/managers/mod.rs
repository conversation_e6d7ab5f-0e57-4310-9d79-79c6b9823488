//! 数据库管理器模块
//! 
//! 将原来的单一DatabaseManager拆分为多个专门的管理器，
//! 实现单一职责原则和更好的关注点分离

pub mod connection_manager;
// pub mod transaction_manager; // 已移除，使用 transaction 模块的统一实现
pub mod migration_manager;
pub mod performance_manager;

// 重新导出主要类型
pub use connection_manager::{ConnectionManager, ConnectionConfig, ConnectionStats};
// 从 transaction 模块重新导出事务相关类型
pub use crate::transaction::{
    TransactionManager, DatabaseTransaction, TransactionStats, LongRunningTransaction
};
pub use migration_manager::{
    MigrationManager, MigrationConfig, MigrationStatus, MigrationInfo
};
pub use performance_manager::{
    PerformanceManager, PerformanceConfig, PerformanceStats,
    TimeSeriesQueryMetrics, AggregatedQueryMetrics, QueryType, SlowQuery
};

use sigmax_core::{SigmaXResult, DatabaseConfig};
use sqlx::Row;
use std::sync::Arc;

/// 新的数据库管理器
///
/// 组合了多个专门的管理器，实现了关注点分离
#[derive(Debug)]
pub struct DatabaseManager {
    connection_manager: Arc<ConnectionManager>,
    transaction_manager: Arc<TransactionManager>,
    migration_manager: Arc<MigrationManager>,
    performance_manager: Arc<PerformanceManager>,
}

impl DatabaseManager {
    /// 从旧的DatabaseConfig创建新的DatabaseManager
    pub async fn new(config: DatabaseConfig) -> SigmaXResult<Self> {
        // 转换配置
        let connection_config = ConnectionConfig {
            url: config.url,
            max_connections: config.max_connections,
            connection_timeout: config.connection_timeout,
            idle_timeout: Some(600), // 默认10分钟
            max_lifetime: Some(1800), // 默认30分钟
        };

        let migration_config = MigrationConfig {
            auto_migrate: config.auto_migrate,
            ..Default::default()
        };

        Self::new_with_configs(connection_config, migration_config).await
    }

    /// 使用详细配置创建DatabaseManager
    pub async fn new_with_configs(
        connection_config: ConnectionConfig,
        migration_config: MigrationConfig,
    ) -> SigmaXResult<Self> {
        Self::new_with_all_configs(
            connection_config,
            migration_config,
            PerformanceConfig::default(),
        ).await
    }

    /// 使用所有配置创建DatabaseManager
    pub async fn new_with_all_configs(
        connection_config: ConnectionConfig,
        migration_config: MigrationConfig,
        performance_config: PerformanceConfig,
    ) -> SigmaXResult<Self> {
        // 创建连接管理器
        let connection_manager = Arc::new(ConnectionManager::new(connection_config).await?);

        // 创建事务管理器 - 使用新的统一实现
        let transaction_manager = Arc::new(TransactionManager::new_with_connection_manager(Arc::clone(&connection_manager)));

        // 创建迁移管理器
        let migration_manager = Arc::new(MigrationManager::new(
            Arc::clone(&connection_manager),
            migration_config.clone(),
        ));

        // 创建性能管理器
        let performance_manager = Arc::new(PerformanceManager::new(
            Arc::clone(&connection_manager),
            performance_config,
        ));

        let manager = Self {
            connection_manager,
            transaction_manager,
            migration_manager,
            performance_manager,
        };

        // 如果启用自动迁移，执行迁移
        if migration_config.auto_migrate {
            manager.migration_manager.run_migrations().await?;
        }

        Ok(manager)
    }

    /// 创建用于测试的DatabaseManager
    pub fn new_mock() -> SigmaXResult<Self> {
        let connection_manager = Arc::new(ConnectionManager::new_mock()?);
        let transaction_manager = Arc::new(TransactionManager::new_with_connection_manager(Arc::clone(&connection_manager)));
        let migration_manager = Arc::new(MigrationManager::with_defaults(Arc::clone(&connection_manager)));
        let performance_manager = Arc::new(PerformanceManager::with_defaults(Arc::clone(&connection_manager)));

        Ok(Self {
            connection_manager,
            transaction_manager,
            migration_manager,
            performance_manager,
        })
    }

    // ============================================================================
    // 委托方法 - 提供向后兼容的API
    // ============================================================================

    /// 获取连接池引用
    pub fn pool(&self) -> &sqlx::PgPool {
        self.connection_manager.pool()
    }

    /// 健康检查
    pub async fn health_check(&self) -> SigmaXResult<()> {
        self.connection_manager.health_check().await
    }

    /// 开始事务
    pub async fn begin_transaction(&self) -> SigmaXResult<DatabaseTransaction> {
        self.transaction_manager.begin_transaction().await
    }

    /// 在事务中执行操作
    pub async fn run_in_transaction<F, Fut, R>(&self, operation: F) -> SigmaXResult<R>
    where
        F: FnOnce(&mut sqlx::Transaction<'_, sqlx::Postgres>) -> Fut + Send,
        Fut: std::future::Future<Output = SigmaXResult<R>> + Send,
        R: Send,
    {
        self.transaction_manager.run_in_transaction(operation).await
    }

    /// 运行迁移
    pub async fn run_migrations(&self) -> SigmaXResult<()> {
        self.migration_manager.run_migrations().await
    }

    // ============================================================================
    // 访问器方法 - 获取各个管理器的引用
    // ============================================================================

    /// 获取连接管理器
    pub fn connection_manager(&self) -> &Arc<ConnectionManager> {
        &self.connection_manager
    }

    /// 获取事务管理器
    pub fn transaction_manager(&self) -> &Arc<TransactionManager> {
        &self.transaction_manager
    }

    /// 获取迁移管理器
    pub fn migration_manager(&self) -> &Arc<MigrationManager> {
        &self.migration_manager
    }

    /// 获取性能管理器
    pub fn performance_manager(&self) -> &Arc<PerformanceManager> {
        &self.performance_manager
    }

    // ============================================================================
    // 统计和监控方法
    // ============================================================================

    /// 获取连接统计信息
    pub async fn get_connection_stats(&self) -> SigmaXResult<ConnectionStats> {
        self.connection_manager.get_connection_stats().await
    }

    /// 获取事务统计信息
    pub async fn get_transaction_stats(&self) -> SigmaXResult<TransactionStats> {
        self.transaction_manager.get_transaction_stats().await
    }

    /// 检查长时间运行的事务
    pub async fn check_long_running_transactions(&self, threshold_seconds: u32) -> SigmaXResult<Vec<LongRunningTransaction>> {
        self.transaction_manager.check_long_running_transactions(threshold_seconds).await
    }

    /// 检查迁移状态
    pub async fn check_migration_status(&self) -> SigmaXResult<MigrationStatus> {
        self.migration_manager.check_migration_status().await
    }

    /// 验证数据库架构
    pub async fn validate_schema(&self) -> SigmaXResult<bool> {
        self.migration_manager.validate_schema().await
    }

    /// 获取性能统计信息
    pub async fn get_performance_stats(&self) -> PerformanceStats {
        self.performance_manager.get_stats().await
    }

    /// 获取慢查询列表
    pub async fn get_slow_queries(&self, limit: Option<usize>) -> Vec<SlowQuery> {
        self.performance_manager.get_slow_queries(limit).await
    }

    /// 执行带性能监控的操作
    pub async fn execute_with_monitoring<F, Fut, R>(
        &self,
        query_name: &str,
        operation: F,
    ) -> SigmaXResult<R>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = SigmaXResult<R>>,
    {
        self.performance_manager.execute_with_monitoring(query_name, operation).await
    }

    /// 清理过期的性能监控数据
    pub async fn cleanup_performance_data(&self) -> SigmaXResult<usize> {
        self.performance_manager.cleanup_expired_data().await
    }

    // ============================================================================
    // 生命周期管理
    // ============================================================================

    /// 关闭数据库连接
    pub async fn close(&self) {
        self.connection_manager.close().await;
    }

    /// 检查是否已关闭
    pub fn is_closed(&self) -> bool {
        self.connection_manager.is_closed()
    }
}

impl Clone for DatabaseManager {
    fn clone(&self) -> Self {
        Self {
            connection_manager: Arc::clone(&self.connection_manager),
            transaction_manager: Arc::clone(&self.transaction_manager),
            migration_manager: Arc::clone(&self.migration_manager),
            performance_manager: Arc::clone(&self.performance_manager),
        }
    }
}

// ============================================================================
// 向后兼容的类型别名和结构体
// ============================================================================

/// 向后兼容的数据库统计信息
#[derive(Debug, Clone)]
pub struct DatabaseStats {
    pub trading_pairs_count: u64,
    pub candles_count: u64,
    pub orders_count: u64,
    pub trades_count: u64,
    pub strategies_count: u64,
    pub portfolios_count: u64,
    pub positions_count: u64,
}

impl DatabaseManager {
    /// 获取数据库统计信息（向后兼容方法）
    pub async fn get_stats(&self) -> SigmaXResult<DatabaseStats> {
        let row = sqlx::query(
            r#"
            SELECT 
                (SELECT COUNT(*) FROM trading_pairs) as trading_pairs_count,
                (SELECT COUNT(*) FROM candles) as candles_count,
                (SELECT COUNT(*) FROM orders) as orders_count,
                (SELECT COUNT(*) FROM trades) as trades_count,
                (SELECT COUNT(*) FROM strategies) as strategies_count,
                (SELECT COUNT(*) FROM portfolios) as portfolios_count,
                (SELECT COUNT(*) FROM positions) as positions_count
            "#
        )
        .fetch_one(self.pool())
        .await
        .map_err(|e| sigmax_core::SigmaXError::database(
            sigmax_core::DatabaseErrorCode::QueryFailed, 
            format!("Failed to get database stats: {}", e)
        ))?;
        
        Ok(DatabaseStats {
            trading_pairs_count: row.get::<i64, _>("trading_pairs_count") as u64,
            candles_count: row.get::<i64, _>("candles_count") as u64,
            orders_count: row.get::<i64, _>("orders_count") as u64,
            trades_count: row.get::<i64, _>("trades_count") as u64,
            strategies_count: row.get::<i64, _>("strategies_count") as u64,
            portfolios_count: row.get::<i64, _>("portfolios_count") as u64,
            positions_count: row.get::<i64, _>("positions_count") as u64,
        })
    }

    /// 清理旧数据（向后兼容方法）
    pub async fn cleanup_old_data(&self, days: u32) -> SigmaXResult<u64> {
        let cutoff_date = chrono::Utc::now() - chrono::Duration::days(days as i64);
        
        let result = sqlx::query(
            "DELETE FROM candles WHERE created_at < $1"
        )
        .bind(cutoff_date)
        .execute(self.pool())
        .await
        .map_err(|e| sigmax_core::SigmaXError::database(
            sigmax_core::DatabaseErrorCode::QueryFailed, 
            format!("Failed to cleanup old data: {}", e)
        ))?;

        Ok(result.rows_affected())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_database_manager_mock() {
        let manager = DatabaseManager::new_mock().unwrap();
        assert!(!manager.is_closed());
        
        // 测试克隆
        let _manager_clone = manager.clone();
    }

    #[test]
    fn test_database_stats() {
        let stats = DatabaseStats {
            trading_pairs_count: 10,
            candles_count: 1000,
            orders_count: 50,
            trades_count: 25,
            strategies_count: 3,
            portfolios_count: 1,
            positions_count: 5,
        };
        
        assert_eq!(stats.trading_pairs_count, 10);
        assert_eq!(stats.candles_count, 1000);
    }
}
