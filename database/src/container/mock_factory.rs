//! Mock Repository工厂实现
//! 
//! 实现用于测试的Mock Repository工厂

use super::factory_traits::*;
use sigmax_core::{SigmaXResult, SigmaXError, OrderId, StrategyId, EventId, TradingPair, EventType};
use sigmax_core::{Order, Trade, StrategyState, StoredEvent, OrderStatus, StrategyInfo};
use crate::repositories::traits::risk_repository::{RiskCheckRecord, RiskRuleRecord};
use uuid::Uuid;

// TradeId 类型别名
pub type TradeId = Uuid;
use std::sync::Arc;
use std::collections::HashMap;
use tokio::sync::RwLock;
use async_trait::async_trait;

/// Mock数据存储
#[derive(Debug, Default)]
struct MockStorage {
    orders: RwLock<HashMap<OrderId, Order>>,
    trades: RwLock<HashMap<TradeId, Trade>>,
    strategy_states: RwLock<HashMap<StrategyId, StrategyState>>,
    events: RwLock<HashMap<EventId, StoredEvent>>,
    risk_checks: RwLock<Vec<RiskCheckRecord>>,
    risk_rules: RwLock<Vec<RiskRuleRecord>>,
}

/// Mock订单Repository工厂
pub struct MockOrderRepositoryFactory {
    storage: Arc<MockStorage>,
    config: MockFactoryConfig,
}

impl MockOrderRepositoryFactory {
    pub fn new(storage: Arc<MockStorage>, config: MockFactoryConfig) -> Self {
        Self { storage, config }
    }
    
    async fn simulate_delay(&self) {
        if self.config.delay_ms > 0 {
            tokio::time::sleep(tokio::time::Duration::from_millis(self.config.delay_ms)).await;
        }
    }
    
    fn should_simulate_error(&self) -> bool {
        if !self.config.simulate_errors {
            return false;
        }
        
        use rand::Rng;
        let mut rng = rand::thread_rng();
        rng.gen::<f64>() < self.config.error_probability
    }
    
    fn create_mock_error(&self) -> SigmaXError {
        sigmax_core::SigmaXError::internal(
            sigmax_core::InternalErrorCode::UnexpectedState,
            "Simulated error from mock factory"
        )
    }
}

#[async_trait]
impl RepositoryFactory for MockOrderRepositoryFactory {
    fn factory_type(&self) -> &'static str {
        "mock_order"
    }
    
    async fn health_check(&self) -> SigmaXResult<()> {
        self.simulate_delay().await;
        
        if self.should_simulate_error() {
            return Err(self.create_mock_error());
        }
        
        Ok(())
    }
}

#[async_trait]
impl OrderRepositoryFactory for MockOrderRepositoryFactory {
    async fn save_order(&self, order: &Order) -> SigmaXResult<()> {
        self.simulate_delay().await;
        
        if self.should_simulate_error() {
            return Err(self.create_mock_error());
        }
        
        let mut orders = self.storage.orders.write().await;
        orders.insert(order.id, order.clone());

        if self.config.verbose_logging {
            tracing::info!("Mock: Saved order {}", order.id);
        }
        
        Ok(())
    }
    
    async fn find_order_by_id(&self, order_id: OrderId) -> SigmaXResult<Option<Order>> {
        self.simulate_delay().await;
        
        if self.should_simulate_error() {
            return Err(self.create_mock_error());
        }
        
        let orders = self.storage.orders.read().await;
        let result = orders.get(&order_id).cloned();
        
        if self.config.verbose_logging {
            tracing::info!("Mock: Retrieved order {} -> {:?}", order_id, result.is_some());
        }
        
        Ok(result)
    }
    
    async fn find_all_orders(&self) -> SigmaXResult<Vec<Order>> {
        self.simulate_delay().await;
        
        if self.should_simulate_error() {
            return Err(self.create_mock_error());
        }
        
        let orders = self.storage.orders.read().await;
        let result: Vec<Order> = orders.values().cloned().collect();
        
        if self.config.verbose_logging {
            tracing::info!("Mock: Retrieved {} orders", result.len());
        }
        
        Ok(result)
    }
    
    async fn find_orders_by_status(&self, status: OrderStatus) -> SigmaXResult<Vec<Order>> {
        self.simulate_delay().await;
        
        if self.should_simulate_error() {
            return Err(self.create_mock_error());
        }
        
        let orders = self.storage.orders.read().await;
        let result: Vec<Order> = orders.values()
            .filter(|order| order.status == status)
            .cloned()
            .collect();
        
        if self.config.verbose_logging {
            tracing::info!("Mock: Retrieved {} orders with status {:?}", result.len(), status);
        }
        
        Ok(result)
    }
    
    async fn find_orders_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Vec<Order>> {
        self.simulate_delay().await;
        
        if self.should_simulate_error() {
            return Err(self.create_mock_error());
        }
        
        let orders = self.storage.orders.read().await;
        let result: Vec<Order> = orders.values()
            .filter(|order| order.strategy_id == Some(strategy_id))
            .cloned()
            .collect();
        
        if self.config.verbose_logging {
            tracing::info!("Mock: Retrieved {} orders for strategy {}", result.len(), strategy_id);
        }
        
        Ok(result)
    }
}

/// Mock交易Repository工厂
pub struct MockTradeRepositoryFactory {
    storage: Arc<MockStorage>,
    config: MockFactoryConfig,
}

impl MockTradeRepositoryFactory {
    pub fn new(storage: Arc<MockStorage>, config: MockFactoryConfig) -> Self {
        Self { storage, config }
    }
    
    async fn simulate_delay(&self) {
        if self.config.delay_ms > 0 {
            tokio::time::sleep(tokio::time::Duration::from_millis(self.config.delay_ms)).await;
        }
    }
    
    fn should_simulate_error(&self) -> bool {
        if !self.config.simulate_errors {
            return false;
        }
        
        use rand::Rng;
        let mut rng = rand::thread_rng();
        rng.gen::<f64>() < self.config.error_probability
    }
    
    fn create_mock_error(&self) -> SigmaXError {
        sigmax_core::SigmaXError::internal(
            sigmax_core::InternalErrorCode::UnexpectedState,
            "Simulated error from mock factory"
        )
    }
}

#[async_trait]
impl RepositoryFactory for MockTradeRepositoryFactory {
    fn factory_type(&self) -> &'static str {
        "mock_trade"
    }
    
    async fn health_check(&self) -> SigmaXResult<()> {
        self.simulate_delay().await;
        
        if self.should_simulate_error() {
            return Err(self.create_mock_error());
        }
        
        Ok(())
    }
}

#[async_trait]
impl TradeRepositoryFactory for MockTradeRepositoryFactory {
    async fn save_trade(&self, trade: &Trade) -> SigmaXResult<()> {
        self.simulate_delay().await;
        
        if self.should_simulate_error() {
            return Err(self.create_mock_error());
        }
        
        let mut trades = self.storage.trades.write().await;
        trades.insert(trade.id, trade.clone());

        if self.config.verbose_logging {
            tracing::info!("Mock: Saved trade {}", trade.id);
        }
        
        Ok(())
    }
    
    async fn find_all_trades(&self) -> SigmaXResult<Vec<Trade>> {
        self.simulate_delay().await;
        
        if self.should_simulate_error() {
            return Err(self.create_mock_error());
        }
        
        let trades = self.storage.trades.read().await;
        let result: Vec<Trade> = trades.values().cloned().collect();
        
        if self.config.verbose_logging {
            tracing::info!("Mock: Retrieved {} trades", result.len());
        }
        
        Ok(result)
    }
    
    async fn find_trades_by_pair(&self, pair: &TradingPair) -> SigmaXResult<Vec<Trade>> {
        self.simulate_delay().await;
        
        if self.should_simulate_error() {
            return Err(self.create_mock_error());
        }
        
        let trades = self.storage.trades.read().await;
        let result: Vec<Trade> = trades.values()
            .filter(|trade| trade.trading_pair == *pair)
            .cloned()
            .collect();
        
        if self.config.verbose_logging {
            tracing::info!("Mock: Retrieved {} trades for pair {:?}", result.len(), pair);
        }
        
        Ok(result)
    }
    
    async fn find_trades_by_time_range(
        &self,
        start: chrono::DateTime<chrono::Utc>,
        end: chrono::DateTime<chrono::Utc>
    ) -> SigmaXResult<Vec<Trade>> {
        self.simulate_delay().await;
        
        if self.should_simulate_error() {
            return Err(self.create_mock_error());
        }
        
        let trades = self.storage.trades.read().await;
        let result: Vec<Trade> = trades.values()
            .filter(|trade| {
                let trade_time = trade.executed_at;
                trade_time >= start && trade_time <= end
            })
            .cloned()
            .collect();
        
        if self.config.verbose_logging {
            tracing::info!("Mock: Retrieved {} trades in time range", result.len());
        }
        
        Ok(result)
    }
}

// 为了简洁，我将创建其他Mock工厂的简化版本
// 在实际项目中，应该为每个工厂提供完整的实现

/// Mock策略Repository工厂（简化版）
pub struct MockStrategyRepositoryFactory {
    storage: Arc<MockStorage>,
    config: MockFactoryConfig,
}

impl MockStrategyRepositoryFactory {
    pub fn new(storage: Arc<MockStorage>, config: MockFactoryConfig) -> Self {
        Self { storage, config }
    }
}

#[async_trait]
impl RepositoryFactory for MockStrategyRepositoryFactory {
    fn factory_type(&self) -> &'static str { "mock_strategy" }
    async fn health_check(&self) -> SigmaXResult<()> { Ok(()) }
}

#[async_trait]
impl StrategyRepositoryFactory for MockStrategyRepositoryFactory {
    async fn save_strategy_state(&self, strategy_id: StrategyId, state: &StrategyState) -> SigmaXResult<()> {
        let mut states = self.storage.strategy_states.write().await;
        states.insert(strategy_id, state.clone());
        Ok(())
    }
    
    async fn find_strategy_state_by_id(&self, strategy_id: StrategyId) -> SigmaXResult<Option<StrategyState>> {
        let states = self.storage.strategy_states.read().await;
        Ok(states.get(&strategy_id).cloned())
    }
    
    async fn find_all_strategies(&self) -> SigmaXResult<Vec<StrategyInfo>> {
        // 简化实现
        Ok(vec![])
    }
}

/// Mock事件Repository工厂（简化版）
pub struct MockEventRepositoryFactory {
    storage: Arc<MockStorage>,
    config: MockFactoryConfig,
}

impl MockEventRepositoryFactory {
    pub fn new(storage: Arc<MockStorage>, config: MockFactoryConfig) -> Self {
        Self { storage, config }
    }
}

#[async_trait]
impl RepositoryFactory for MockEventRepositoryFactory {
    fn factory_type(&self) -> &'static str { "mock_event" }
    async fn health_check(&self) -> SigmaXResult<()> { Ok(()) }
}

#[async_trait]
impl EventRepositoryFactory for MockEventRepositoryFactory {
    async fn save_event(&self, event: &StoredEvent) -> SigmaXResult<EventId> {
        let event_id = Uuid::new_v4();
        let mut events = self.storage.events.write().await;
        events.insert(event_id, event.clone());
        Ok(event_id)
    }
    
    async fn get_event(&self, event_id: EventId) -> SigmaXResult<Option<StoredEvent>> {
        let events = self.storage.events.read().await;
        Ok(events.get(&event_id).cloned())
    }
    
    async fn get_events(&self, _offset: usize, _limit: usize) -> SigmaXResult<Vec<StoredEvent>> {
        let events = self.storage.events.read().await;
        Ok(events.values().cloned().collect())
    }
    
    async fn get_events_by_type(&self, _event_type: EventType, _limit: usize) -> SigmaXResult<Vec<StoredEvent>> {
        Ok(vec![])
    }
    
    async fn mark_event_processed(&self, _event_id: EventId) -> SigmaXResult<()> {
        Ok(())
    }
}

/// Mock风控Repository工厂（简化版）
pub struct MockRiskRepositoryFactory {
    storage: Arc<MockStorage>,
    config: MockFactoryConfig,
}

impl MockRiskRepositoryFactory {
    pub fn new(storage: Arc<MockStorage>, config: MockFactoryConfig) -> Self {
        Self { storage, config }
    }
}

#[async_trait]
impl RepositoryFactory for MockRiskRepositoryFactory {
    fn factory_type(&self) -> &'static str { "mock_risk" }
    async fn health_check(&self) -> SigmaXResult<()> { Ok(()) }
}

#[async_trait]
impl RiskRepositoryFactory for MockRiskRepositoryFactory {
    async fn save_risk_check(&self, record: &RiskCheckRecord) -> SigmaXResult<()> {
        let mut checks = self.storage.risk_checks.write().await;
        checks.push(record.clone());
        Ok(())
    }

    async fn get_risk_checks(&self, limit: usize) -> SigmaXResult<Vec<RiskCheckRecord>> {
        let checks = self.storage.risk_checks.read().await;
        Ok(checks.iter().take(limit).cloned().collect())
    }

    async fn save_risk_rule(&self, rule: &RiskRuleRecord) -> SigmaXResult<()> {
        let mut rules = self.storage.risk_rules.write().await;
        rules.push(rule.clone());
        Ok(())
    }

    async fn get_risk_rules(&self) -> SigmaXResult<Vec<RiskRuleRecord>> {
        let rules = self.storage.risk_rules.read().await;
        Ok(rules.clone())
    }
}

/// Mock组合Repository工厂
pub struct MockCompositeRepositoryFactory {
    order_factory: Arc<MockOrderRepositoryFactory>,
    trade_factory: Arc<MockTradeRepositoryFactory>,
    strategy_factory: Arc<MockStrategyRepositoryFactory>,
    event_factory: Arc<MockEventRepositoryFactory>,
    risk_factory: Arc<MockRiskRepositoryFactory>,
}

impl MockCompositeRepositoryFactory {
    pub fn new(config: MockFactoryConfig) -> Self {
        let storage = Arc::new(MockStorage::default());
        
        Self {
            order_factory: Arc::new(MockOrderRepositoryFactory::new(storage.clone(), config.clone())),
            trade_factory: Arc::new(MockTradeRepositoryFactory::new(storage.clone(), config.clone())),
            strategy_factory: Arc::new(MockStrategyRepositoryFactory::new(storage.clone(), config.clone())),
            event_factory: Arc::new(MockEventRepositoryFactory::new(storage.clone(), config.clone())),
            risk_factory: Arc::new(MockRiskRepositoryFactory::new(storage, config)),
        }
    }
}

#[async_trait]
impl CompositeRepositoryFactory for MockCompositeRepositoryFactory {
    fn order_factory(&self) -> Arc<dyn OrderRepositoryFactory> {
        self.order_factory.clone()
    }
    
    fn trade_factory(&self) -> Arc<dyn TradeRepositoryFactory> {
        self.trade_factory.clone()
    }
    
    fn strategy_factory(&self) -> Arc<dyn StrategyRepositoryFactory> {
        self.strategy_factory.clone()
    }
    
    fn event_factory(&self) -> Arc<dyn EventRepositoryFactory> {
        self.event_factory.clone()
    }
    
    fn risk_factory(&self) -> Arc<dyn RiskRepositoryFactory> {
        self.risk_factory.clone()
    }
}

/// Mock工厂构建器
pub struct MockFactoryBuilder {
    config: MockFactoryConfig,
}

impl MockFactoryBuilder {
    pub fn new() -> Self {
        Self {
            config: MockFactoryConfig::default(),
        }
    }
    
    pub fn with_error_simulation(mut self, enabled: bool, probability: f64) -> Self {
        self.config.simulate_errors = enabled;
        self.config.error_probability = probability;
        self
    }
    
    pub fn with_delay(mut self, delay_ms: u64) -> Self {
        self.config.delay_ms = delay_ms;
        self
    }
    
    pub fn with_verbose_logging(mut self, enabled: bool) -> Self {
        self.config.verbose_logging = enabled;
        self
    }
}

impl Default for MockFactoryBuilder {
    fn default() -> Self {
        Self::new()
    }
}

impl RepositoryFactoryBuilder for MockFactoryBuilder {
    type Factory = MockCompositeRepositoryFactory;
    
    fn build(self) -> SigmaXResult<Self::Factory> {
        self.config.validate()?;
        Ok(MockCompositeRepositoryFactory::new(self.config))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_mock_factory_basic() {
        let factory = MockFactoryBuilder::new()
            .with_verbose_logging(true)
            .build()
            .unwrap();
        
        let order_factory = factory.order_factory();
        assert_eq!(order_factory.factory_type(), "mock_order");
        
        // 测试健康检查
        assert!(factory.health_check_all().await.is_ok());
    }

    #[tokio::test]
    async fn test_mock_factory_with_errors() {
        let factory = MockFactoryBuilder::new()
            .with_error_simulation(true, 1.0) // 100%错误概率
            .build()
            .unwrap();
        
        let order_factory = factory.order_factory();
        
        // 应该返回错误
        assert!(order_factory.health_check().await.is_err());
    }
}
