//! 事务上下文管理
//!
//! 提供事务上下文和作用域管理

use async_trait::async_trait;
use sigmax_core::{SigmaXResult, SigmaXError, DatabaseErrorCode};
use sqlx::{Transaction, Postgres, Executor};
use std::sync::Arc;
use tokio::sync::Mutex;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};

/// 事务隔离级别
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum TransactionIsolationLevel {
    /// 读未提交
    ReadUncommitted,
    /// 读已提交
    ReadCommitted,
    /// 可重复读
    RepeatableRead,
    /// 串行化
    Serializable,
}

impl TransactionIsolationLevel {
    /// 转换为SQL语句
    pub fn to_sql(&self) -> &'static str {
        match self {
            TransactionIsolationLevel::ReadUncommitted => "READ UNCOMMITTED",
            TransactionIsolationLevel::ReadCommitted => "READ COMMITTED",
            TransactionIsolationLevel::RepeatableRead => "REPEATABLE READ",
            TransactionIsolationLevel::Serializable => "SERIALIZABLE",
        }
    }
}

impl Default for TransactionIsolationLevel {
    fn default() -> Self {
        TransactionIsolationLevel::ReadCommitted
    }
}

/// 事务作用域
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum TransactionScope {
    /// 必需事务 - 如果没有事务则创建新事务
    Required,
    /// 支持事务 - 如果有事务则加入，否则不使用事务
    Supports,
    /// 强制事务 - 必须在事务中运行
    Mandatory,
    /// 新事务 - 总是创建新事务
    RequiresNew,
    /// 不支持事务 - 如果有事务则挂起
    NotSupported,
    /// 从不使用事务 - 如果有事务则抛出异常
    Never,
}

impl Default for TransactionScope {
    fn default() -> Self {
        TransactionScope::Required
    }
}

/// 事务上下文
#[derive(Debug)]
pub struct TransactionContext {
    /// 事务ID
    pub transaction_id: Uuid,
    /// 事务开始时间
    pub started_at: DateTime<Utc>,
    /// 隔离级别
    pub isolation_level: TransactionIsolationLevel,
    /// 作用域
    pub scope: TransactionScope,
    /// 是否只读
    pub read_only: bool,
    /// 超时时间（秒）
    pub timeout_seconds: Option<u64>,
    /// SQLx事务对象
    transaction: Arc<Mutex<Option<Transaction<'static, Postgres>>>>,
    /// 是否已提交
    is_committed: bool,
    /// 是否已回滚
    is_rolled_back: bool,
}

impl Clone for TransactionContext {
    fn clone(&self) -> Self {
        Self {
            transaction_id: self.transaction_id,
            started_at: self.started_at,
            isolation_level: self.isolation_level,
            scope: self.scope,
            read_only: self.read_only,
            timeout_seconds: self.timeout_seconds,
            transaction: Arc::clone(&self.transaction),
            is_committed: self.is_committed,
            is_rolled_back: self.is_rolled_back,
        }
    }
}

impl TransactionContext {
    /// 创建新的事务上下文
    pub fn new(
        isolation_level: TransactionIsolationLevel,
        scope: TransactionScope,
        read_only: bool,
        timeout_seconds: Option<u64>,
    ) -> Self {
        Self {
            transaction_id: Uuid::new_v4(),
            started_at: Utc::now(),
            isolation_level,
            scope,
            read_only,
            timeout_seconds,
            transaction: Arc::new(Mutex::new(None)),
            is_committed: false,
            is_rolled_back: false,
        }
    }
    
    /// 设置SQLx事务
    pub async fn set_transaction(&mut self, tx: Transaction<'static, Postgres>) -> SigmaXResult<()> {
        let mut transaction = self.transaction.lock().await;
        if transaction.is_some() {
            return Err(SigmaXError::database(
                DatabaseErrorCode::TransactionFailed,
                "事务已经存在".to_string()
            ));
        }
        *transaction = Some(tx);
        Ok(())
    }
    
    /// 获取事务的可变引用
    pub async fn get_transaction_mut(&self) -> SigmaXResult<tokio::sync::MutexGuard<'_, Option<Transaction<'static, Postgres>>>> {
        Ok(self.transaction.lock().await)
    }
    
    /// 检查事务是否活跃
    pub fn is_active(&self) -> bool {
        !self.is_committed && !self.is_rolled_back
    }
    
    /// 检查事务是否已提交
    pub fn is_committed(&self) -> bool {
        self.is_committed
    }
    
    /// 检查事务是否已回滚
    pub fn is_rolled_back(&self) -> bool {
        self.is_rolled_back
    }
    
    /// 标记为已提交
    pub fn mark_committed(&mut self) {
        self.is_committed = true;
    }
    
    /// 标记为已回滚
    pub fn mark_rolled_back(&mut self) {
        self.is_rolled_back = true;
    }
    
    /// 检查是否超时
    pub fn is_timeout(&self) -> bool {
        if let Some(timeout) = self.timeout_seconds {
            let elapsed = Utc::now().signed_duration_since(self.started_at);
            elapsed.num_seconds() as u64 > timeout
        } else {
            false
        }
    }
    
    /// 获取运行时间（秒）
    pub fn elapsed_seconds(&self) -> i64 {
        Utc::now().signed_duration_since(self.started_at).num_seconds()
    }
}

/// 事务上下文管理器
#[async_trait]
pub trait TransactionContextManager: Send + Sync {
    /// 开始事务
    async fn begin_transaction(
        &self,
        isolation_level: TransactionIsolationLevel,
        scope: TransactionScope,
        read_only: bool,
        timeout_seconds: Option<u64>,
    ) -> SigmaXResult<TransactionContext>;
    
    /// 提交事务
    async fn commit_transaction(&self, context: &mut TransactionContext) -> SigmaXResult<()>;
    
    /// 回滚事务
    async fn rollback_transaction(&self, context: &mut TransactionContext) -> SigmaXResult<()>;
    
    /// 在事务中执行操作
    async fn execute_in_transaction<F, Fut, R>(
        &self,
        context: &TransactionContext,
        operation: F,
    ) -> SigmaXResult<R>
    where
        F: FnOnce(&mut Transaction<'_, Postgres>) -> Fut + Send,
        Fut: std::future::Future<Output = SigmaXResult<R>> + Send,
        R: Send;
}

/// 事务配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransactionConfig {
    /// 默认隔离级别
    pub default_isolation_level: TransactionIsolationLevel,
    /// 默认作用域
    pub default_scope: TransactionScope,
    /// 默认是否只读
    pub default_read_only: bool,
    /// 默认超时时间（秒）
    pub default_timeout_seconds: Option<u64>,
    /// 是否启用事务日志
    pub enable_transaction_log: bool,
    /// 最大并发事务数
    pub max_concurrent_transactions: Option<u32>,
}

impl Default for TransactionConfig {
    fn default() -> Self {
        Self {
            default_isolation_level: TransactionIsolationLevel::ReadCommitted,
            default_scope: TransactionScope::Required,
            default_read_only: false,
            default_timeout_seconds: Some(30), // 30秒超时
            enable_transaction_log: true,
            max_concurrent_transactions: Some(100),
        }
    }
}

/// 事务统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransactionStatistics {
    /// 总事务数
    pub total_transactions: u64,
    /// 成功提交的事务数
    pub committed_transactions: u64,
    /// 回滚的事务数
    pub rolled_back_transactions: u64,
    /// 超时的事务数
    pub timeout_transactions: u64,
    /// 平均事务执行时间（毫秒）
    pub average_execution_time_ms: f64,
    /// 最大事务执行时间（毫秒）
    pub max_execution_time_ms: u64,
    /// 当前活跃事务数
    pub active_transactions: u32,
    /// 统计时间
    pub calculated_at: DateTime<Utc>,
}

impl TransactionStatistics {
    /// 创建空的统计信息
    pub fn new() -> Self {
        Self {
            total_transactions: 0,
            committed_transactions: 0,
            rolled_back_transactions: 0,
            timeout_transactions: 0,
            average_execution_time_ms: 0.0,
            max_execution_time_ms: 0,
            active_transactions: 0,
            calculated_at: Utc::now(),
        }
    }
    
    /// 计算成功率
    pub fn success_rate(&self) -> f64 {
        if self.total_transactions == 0 {
            0.0
        } else {
            self.committed_transactions as f64 / self.total_transactions as f64
        }
    }
    
    /// 计算回滚率
    pub fn rollback_rate(&self) -> f64 {
        if self.total_transactions == 0 {
            0.0
        } else {
            self.rolled_back_transactions as f64 / self.total_transactions as f64
        }
    }
    
    /// 计算超时率
    pub fn timeout_rate(&self) -> f64 {
        if self.total_transactions == 0 {
            0.0
        } else {
            self.timeout_transactions as f64 / self.total_transactions as f64
        }
    }
}
