//! 交易Repository实现
//!
//! 基于SQLx的标准交易Repository实现

use std::sync::Arc;
use std::collections::HashMap;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use sqlx::{Row, QueryBuilder, Postgres};
use uuid::Uuid;

use sigmax_core::{
    Trade, TradeId, OrderId, OrderSide, TradingPair, ExchangeId, StrategyId,
    Price, Quantity, Amount, SigmaXResult, SigmaXError, DatabaseErrorCode
};

use crate::{
    DatabaseManager,
    repositories::traits::{
        Repository, TradeRepository,
        QueryFilter, PaginationResult, SortConfig, SortDirection,
        TradeQueryFilter, TradeStatistics
    }
};

/// 交易Repository实现
pub struct TradeRepositoryImpl {
    db: Arc<DatabaseManager>,
}

impl TradeRepositoryImpl {
    /// 创建新的交易Repository实例
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }
    
    /// 获取数据库连接池
    fn pool(&self) -> &sqlx::PgPool {
        self.db.pool()
    }
    
    /// 将数据库行转换为Trade对象
    fn row_to_trade(&self, row: &sqlx::postgres::PgRow) -> SigmaXResult<Trade> {
        let trading_pair = TradingPair::new(
            row.get::<String, _>("base_asset"),
            row.get::<String, _>("quote_asset")
        );
        
        let side = match row.get::<String, _>("side").as_str() {
            "buy" => OrderSide::Buy,
            "sell" => OrderSide::Sell,
            s => return Err(SigmaXError::database(
                DatabaseErrorCode::DataConversionError,
                format!("Invalid trade side: {}", s)
            ))
        };
        
        let exchange_id = row.get::<Option<String>, _>("exchange_id")
            .map(|s| s.parse::<ExchangeId>())
            .transpose()
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::DataConversionError,
                format!("Invalid exchange ID: {}", e)
            ))?;
        
        Ok(Trade {
            id: row.get("id"),
            order_id: row.get("order_id"),
            exchange_id: exchange_id.unwrap_or(ExchangeId::Simulator),
            exchange_trade_id: row.get("exchange_trade_id"),
            trading_pair,
            side,
            quantity: row.get("quantity"),
            price: row.get("price"),
            fee: row.get("fee"),
            fee_asset: row.get("fee_asset"),
            commission_rate: row.get("commission_rate"),
            is_maker: row.get("is_maker"),
            trade_time: row.get("trade_time"),
            executed_at: row.get("executed_at"),
            created_at: row.get("created_at"),
            metadata: row.get::<serde_json::Value, _>("metadata"),
        })
    }
}

#[async_trait]
impl Repository<Trade, TradeId> for TradeRepositoryImpl {
    async fn save(&self, entity: &Trade) -> SigmaXResult<TradeId> {
        let query = r#"
            INSERT INTO trades (
                id, order_id, trading_pair_id, side, quantity, price, fee, fee_asset,
                exchange_id, exchange_trade_id, executed_at, created_at
            ) VALUES (
                $1, $2,
                (SELECT id FROM trading_pairs WHERE base_asset = $3 AND quote_asset = $4),
                $5, $6, $7, $8, $9, $10, $11, $12, $13
            )
            ON CONFLICT (id) DO UPDATE SET
                order_id = EXCLUDED.order_id,
                side = EXCLUDED.side,
                quantity = EXCLUDED.quantity,
                price = EXCLUDED.price,
                fee = EXCLUDED.fee,
                fee_asset = EXCLUDED.fee_asset,
                exchange_id = EXCLUDED.exchange_id,
                exchange_trade_id = EXCLUDED.exchange_trade_id,
                executed_at = EXCLUDED.executed_at
        "#;
        
        sqlx::query(query)
            .bind(entity.id)
            .bind(entity.order_id)
            .bind(&entity.trading_pair.base)
            .bind(&entity.trading_pair.quote)
            .bind(entity.side.to_string())
            .bind(entity.quantity)
            .bind(entity.price)
            .bind(entity.fee)
            .bind(&entity.fee_asset)
            .bind(entity.exchange_id.to_string())
            .bind(None::<String>) // exchange_trade_id
            .bind(entity.executed_at)
            .bind(entity.created_at)
            .execute(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::InsertFailed,
                format!("Failed to save trade: {}", e)
            ))?;
        
        Ok(entity.id)
    }
    
    async fn find_by_id(&self, id: TradeId) -> SigmaXResult<Option<Trade>> {
        let query = r#"
            SELECT t.*, tp.base_asset, tp.quote_asset
            FROM trades t
            JOIN trading_pairs tp ON t.trading_pair_id = tp.id
            WHERE t.id = $1
        "#;
        
        let row = sqlx::query(query)
            .bind(id)
            .fetch_optional(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find trade by ID: {}", e)
            ))?;
        
        match row {
            Some(row) => Ok(Some(self.row_to_trade(&row)?)),
            None => Ok(None),
        }
    }
    
    async fn find_all(&self) -> SigmaXResult<Vec<Trade>> {
        let query = r#"
            SELECT t.*, tp.base_asset, tp.quote_asset
            FROM trades t
            JOIN trading_pairs tp ON t.trading_pair_id = tp.id
            ORDER BY t.executed_at DESC
        "#;
        
        let rows = sqlx::query(query)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find all trades: {}", e)
            ))?;
        
        let mut trades = Vec::new();
        for row in rows {
            trades.push(self.row_to_trade(&row)?);
        }
        
        Ok(trades)
    }
    
    async fn update(&self, entity: &Trade) -> SigmaXResult<()> {
        // 对于交易，update和save是相同的操作（使用UPSERT）
        self.save(entity).await?;
        Ok(())
    }
    
    async fn delete(&self, id: TradeId) -> SigmaXResult<()> {
        let query = "DELETE FROM trades WHERE id = $1";
        
        let result = sqlx::query(query)
            .bind(id)
            .execute(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::DeleteFailed,
                format!("Failed to delete trade: {}", e)
            ))?;
        
        if result.rows_affected() == 0 {
            return Err(SigmaXError::database(
                DatabaseErrorCode::NotFound,
                format!("Trade with ID {} not found", id)
            ));
        }
        
        Ok(())
    }
    
    async fn exists(&self, id: TradeId) -> SigmaXResult<bool> {
        let query = "SELECT EXISTS(SELECT 1 FROM trades WHERE id = $1)";
        
        let exists: bool = sqlx::query_scalar(query)
            .bind(id)
            .fetch_one(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to check trade existence: {}", e)
            ))?;
        
        Ok(exists)
    }
    
    async fn count(&self) -> SigmaXResult<u64> {
        let query = "SELECT COUNT(*) FROM trades";
        
        let count: i64 = sqlx::query_scalar(query)
            .fetch_one(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to count trades: {}", e)
            ))?;
        
        Ok(count as u64)
    }

    async fn save_batch(&self, entities: &[Trade]) -> SigmaXResult<Vec<TradeId>> {
        let mut ids = Vec::new();
        for entity in entities {
            let id = self.save(entity).await?;
            ids.push(id);
        }
        Ok(ids)
    }

    async fn delete_batch(&self, ids: &[TradeId]) -> SigmaXResult<()> {
        for id in ids {
            self.delete(*id).await?;
        }
        Ok(())
    }

    async fn find_with_pagination(&self, offset: u64, limit: u64) -> SigmaXResult<PaginationResult<Trade>> {
        let query = r#"
            SELECT t.id, t.order_id, t.exchange_id, t.exchange_trade_id, t.side, t.quantity,
                   t.price, t.fee, t.fee_asset, t.commission_rate, t.is_maker, t.trade_time,
                   t.executed_at, t.created_at, t.metadata,
                   tp.base_asset, tp.quote_asset
            FROM trades t
            JOIN trading_pairs tp ON t.trading_pair_id = tp.id
            ORDER BY t.created_at DESC
            LIMIT $1 OFFSET $2
        "#;

        let rows = sqlx::query(query)
            .bind(limit as i64)
            .bind(offset as i64)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find trades with pagination: {}", e)
            ))?;

        let trades: Result<Vec<Trade>, _> = rows.iter().map(|row| self.row_to_trade(row)).collect();
        let trades = trades?;

        // 获取总数
        let count_query = "SELECT COUNT(*) FROM trades";
        let total: i64 = sqlx::query_scalar(count_query)
            .fetch_one(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to count trades: {}", e)
            ))?;

        Ok(PaginationResult::new(
            trades,
            total as u64,
            offset,
            limit,
        ))
    }
}

#[async_trait]
impl TradeRepository for TradeRepositoryImpl {


    async fn find_by_trading_pair(&self, trading_pair: &TradingPair) -> SigmaXResult<Vec<Trade>> {
        let query = r#"
            SELECT t.*, tp.base_asset, tp.quote_asset
            FROM trades t
            JOIN trading_pairs tp ON t.trading_pair_id = tp.id
            WHERE tp.base_asset = $1 AND tp.quote_asset = $2
            ORDER BY t.executed_at DESC
        "#;

        let rows = sqlx::query(query)
            .bind(&trading_pair.base)
            .bind(&trading_pair.quote)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find trades by trading pair: {}", e)
            ))?;

        let mut trades = Vec::new();
        for row in rows {
            trades.push(self.row_to_trade(&row)?);
        }

        Ok(trades)
    }

    async fn find_by_exchange(&self, exchange_id: ExchangeId) -> SigmaXResult<Vec<Trade>> {
        let query = r#"
            SELECT t.*, tp.base_asset, tp.quote_asset
            FROM trades t
            JOIN trading_pairs tp ON t.trading_pair_id = tp.id
            WHERE t.exchange_id = $1
            ORDER BY t.executed_at DESC
        "#;

        let rows = sqlx::query(query)
            .bind(exchange_id.to_string())
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find trades by exchange: {}", e)
            ))?;

        let mut trades = Vec::new();
        for row in rows {
            trades.push(self.row_to_trade(&row)?);
        }

        Ok(trades)
    }

    async fn find_by_time_range(&self, start: DateTime<Utc>, end: DateTime<Utc>) -> SigmaXResult<Vec<Trade>> {
        let query = r#"
            SELECT t.*, tp.base_asset, tp.quote_asset
            FROM trades t
            JOIN trading_pairs tp ON t.trading_pair_id = tp.id
            WHERE t.executed_at BETWEEN $1 AND $2
            ORDER BY t.executed_at DESC
        "#;

        let rows = sqlx::query(query)
            .bind(start)
            .bind(end)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find trades by time range: {}", e)
            ))?;

        let mut trades = Vec::new();
        for row in rows {
            trades.push(self.row_to_trade(&row)?);
        }

        Ok(trades)
    }

    async fn find_by_filter(&self, filter: &TradeQueryFilter) -> SigmaXResult<Vec<Trade>> {
        // 验证过滤器
        filter.validate()?;

        let mut query_builder = QueryBuilder::new(r#"
            SELECT t.*, tp.base_asset, tp.quote_asset
            FROM trades t
            JOIN trading_pairs tp ON t.trading_pair_id = tp.id
            WHERE 1=1
        "#);

        // 添加过滤条件
        // 注意：TradeQueryFilter没有order_ids字段，这个过滤条件需要移除或修改
        // if let Some(order_ids) = &filter.order_ids {
        //     if !order_ids.is_empty() {
        //         query_builder.push(" AND t.order_id = ANY(");
        //         query_builder.push_bind(order_ids.clone());
        //         query_builder.push(")");
        //     }
        // }

        if let Some(trading_pairs) = &filter.trading_pairs {
            if !trading_pairs.is_empty() {
                query_builder.push(" AND (");
                for (i, pair) in trading_pairs.iter().enumerate() {
                    if i > 0 {
                        query_builder.push(" OR ");
                    }
                    query_builder.push("(tp.base_asset = ");
                    query_builder.push_bind(&pair.base);
                    query_builder.push(" AND tp.quote_asset = ");
                    query_builder.push_bind(&pair.quote);
                    query_builder.push(")");
                }
                query_builder.push(")");
            }
        }

        // 添加时间范围过滤
        if let Some((start, end)) = &filter.executed_time_range {
            query_builder.push(" AND t.executed_at BETWEEN ");
            query_builder.push_bind(start);
            query_builder.push(" AND ");
            query_builder.push_bind(end);
        }

        // 添加排序
        query_builder.push(" ORDER BY t.executed_at DESC");

        let query = query_builder.build();
        let rows = query.fetch_all(self.pool()).await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find trades by filter: {}", e)
            ))?;

        let mut trades = Vec::new();
        for row in rows {
            trades.push(self.row_to_trade(&row)?);
        }

        Ok(trades)
    }

    async fn get_statistics(&self, filter: Option<&TradeQueryFilter>) -> SigmaXResult<TradeStatistics> {
        // 基础统计查询
        let base_query = if let Some(_filter) = filter {
            // TODO: 如果有过滤器，构建复杂查询
            self.get_statistics(None).await?
        } else {
            // 简单统计查询
            self.get_statistics(None).await?
        };

        Ok(base_query)
    }

    async fn find_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Vec<Trade>> {
        let query = r#"
            SELECT t.id, t.order_id, t.exchange_id, t.exchange_trade_id, t.side, t.quantity,
                   t.price, t.fee, t.fee_asset, t.commission_rate, t.is_maker, t.trade_time,
                   t.executed_at, t.created_at, t.metadata,
                   tp.base_asset, tp.quote_asset
            FROM trades t
            JOIN trading_pairs tp ON t.trading_pair_id = tp.id
            JOIN orders o ON t.order_id = o.id
            WHERE o.strategy_id = $1
            ORDER BY t.executed_at DESC
        "#;

        let rows = sqlx::query(query)
            .bind(strategy_id)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find trades by strategy: {}", e)
            ))?;

        let trades: Result<Vec<Trade>, _> = rows.iter().map(|row| self.row_to_trade(row)).collect();
        let trades = trades?;

        Ok(trades)
    }

    async fn find_by_side(&self, side: OrderSide) -> SigmaXResult<Vec<Trade>> {
        let query = r#"
            SELECT t.id, t.order_id, t.exchange_id, t.exchange_trade_id, t.side, t.quantity,
                   t.price, t.fee, t.fee_asset, t.commission_rate, t.is_maker, t.trade_time,
                   t.executed_at, t.created_at, t.metadata,
                   tp.base_asset, tp.quote_asset
            FROM trades t
            JOIN trading_pairs tp ON t.trading_pair_id = tp.id
            WHERE t.side = $1
            ORDER BY t.executed_at DESC
        "#;

        let rows = sqlx::query(query)
            .bind(side.to_string())
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find trades by side: {}", e)
            ))?;

        let trades: Result<Vec<Trade>, _> = rows.iter().map(|row| self.row_to_trade(row)).collect();
        let trades = trades?;

        Ok(trades)
    }

    async fn find_with_filter_and_pagination(
        &self,
        filter: &TradeQueryFilter,
        sort: &[SortConfig],
        offset: u64,
        limit: u64,
    ) -> SigmaXResult<PaginationResult<Trade>> {
        let mut query_builder = QueryBuilder::new(r#"
            SELECT t.id, t.order_id, t.exchange_id, t.exchange_trade_id, t.side, t.quantity,
                   t.price, t.fee, t.fee_asset, t.commission_rate, t.is_maker, t.trade_time,
                   t.executed_at, t.created_at, t.metadata,
                   tp.base_asset, tp.quote_asset
            FROM trades t
            JOIN trading_pairs tp ON t.trading_pair_id = tp.id
            WHERE 1=1
        "#);

        // 应用过滤器
        if let Some((start_time, end_time)) = &filter.executed_time_range {
            query_builder.push(" AND t.executed_at >= ");
            query_builder.push_bind(start_time);
            query_builder.push(" AND t.executed_at <= ");
            query_builder.push_bind(end_time);
        }

        if let Some((min_amount, max_amount)) = &filter.amount_range {
            query_builder.push(" AND (t.quantity * t.price) >= ");
            query_builder.push_bind(min_amount);
            query_builder.push(" AND (t.quantity * t.price) <= ");
            query_builder.push_bind(max_amount);
        }

        // 应用排序
        if !sort.is_empty() {
            query_builder.push(" ORDER BY ");
            for (i, sort_config) in sort.iter().enumerate() {
                if i > 0 {
                    query_builder.push(", ");
                }
                query_builder.push(&sort_config.field);
                match sort_config.direction {
                    SortDirection::Asc => query_builder.push(" ASC"),
                    SortDirection::Desc => query_builder.push(" DESC"),
                };
            }
        } else {
            query_builder.push(" ORDER BY t.executed_at DESC");
        }

        // 应用分页
        query_builder.push(" LIMIT ");
        query_builder.push_bind(limit as i64);
        query_builder.push(" OFFSET ");
        query_builder.push_bind(offset as i64);

        let query = query_builder.build();
        let rows = query.fetch_all(self.pool()).await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find trades with filter and pagination: {}", e)
            ))?;

        let trades: Result<Vec<Trade>, _> = rows.iter().map(|row| self.row_to_trade(row)).collect();
        let trades = trades?;

        // 获取总数
        let count_query = "SELECT COUNT(*) FROM trades t WHERE 1=1";
        let total: i64 = sqlx::query_scalar(count_query)
            .fetch_one(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to count trades: {}", e)
            ))?;

        Ok(PaginationResult::new(
            trades,
            total as u64,
            offset,
            limit,
        ))
    }

    async fn get_volume_by_trading_pair(&self, limit: u64) -> SigmaXResult<Vec<(TradingPair, rust_decimal::Decimal)>> {
        let query = r#"
            SELECT tp.base_asset, tp.quote_asset, SUM(t.quantity * t.price) as volume
            FROM trades t
            JOIN trading_pairs tp ON t.trading_pair_id = tp.id
            GROUP BY tp.base_asset, tp.quote_asset
            ORDER BY volume DESC
            LIMIT $1
        "#;

        let rows = sqlx::query(query)
            .bind(limit as i64)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to get volume by trading pair: {}", e)
            ))?;

        let mut results = Vec::new();
        for row in rows {
            let base_asset: String = row.get("base_asset");
            let quote_asset: String = row.get("quote_asset");
            let trading_pair = TradingPair::new(base_asset, quote_asset);
            let volume: rust_decimal::Decimal = row.get("volume");
            results.push((trading_pair, volume));
        }

        Ok(results)
    }

    async fn get_volume_by_time_interval(
        &self,
        start: chrono::DateTime<chrono::Utc>,
        end: chrono::DateTime<chrono::Utc>,
        interval_minutes: u32,
    ) -> SigmaXResult<Vec<(chrono::DateTime<chrono::Utc>, rust_decimal::Decimal)>> {
        let query = r#"
            SELECT
                date_trunc('hour', t.executed_at) +
                (EXTRACT(minute FROM t.executed_at)::int / $3) * interval '1 minute' * $3 as time_bucket,
                SUM(t.quantity * t.price) as volume
            FROM trades t
            WHERE t.executed_at >= $1 AND t.executed_at <= $2
            GROUP BY time_bucket
            ORDER BY time_bucket
        "#;

        let rows = sqlx::query(query)
            .bind(start)
            .bind(end)
            .bind(interval_minutes as i32)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to get volume by time interval: {}", e)
            ))?;

        let mut results = Vec::new();
        for row in rows {
            let time_bucket: chrono::DateTime<chrono::Utc> = row.get("time_bucket");
            let volume: rust_decimal::Decimal = row.get("volume");
            results.push((time_bucket, volume));
        }

        Ok(results)
    }

    async fn calculate_realized_pnl(
        &self,
        strategy_id: Option<StrategyId>,
        trading_pair: Option<&TradingPair>,
    ) -> SigmaXResult<rust_decimal::Decimal> {
        let mut query_builder = QueryBuilder::new(r#"
            SELECT
                SUM(CASE WHEN t.side = 'sell' THEN t.quantity * t.price ELSE -t.quantity * t.price END) as realized_pnl
            FROM trades t
            JOIN trading_pairs tp ON t.trading_pair_id = tp.id
        "#);

        let mut has_where = false;

        if let Some(strategy_id) = strategy_id {
            query_builder.push(" JOIN orders o ON t.order_id = o.id WHERE o.strategy_id = ");
            query_builder.push_bind(strategy_id);
            has_where = true;
        }

        if let Some(trading_pair) = trading_pair {
            if has_where {
                query_builder.push(" AND ");
            } else {
                query_builder.push(" WHERE ");
            }
            query_builder.push("tp.base_asset = ");
            query_builder.push_bind(&trading_pair.base);
            query_builder.push(" AND tp.quote_asset = ");
            query_builder.push_bind(&trading_pair.quote);
        }

        let query = query_builder.build();
        let row = query.fetch_one(self.pool()).await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to calculate realized PnL: {}", e)
            ))?;

        let pnl: Option<rust_decimal::Decimal> = row.get("realized_pnl");
        Ok(pnl.unwrap_or_default())
    }

    async fn find_recent_trades(&self, limit: u64) -> SigmaXResult<Vec<Trade>> {
        let query = r#"
            SELECT t.id, t.order_id, t.exchange_id, t.exchange_trade_id, t.side, t.quantity,
                   t.price, t.fee, t.fee_asset, t.commission_rate, t.is_maker, t.trade_time,
                   t.executed_at, t.created_at, t.metadata,
                   tp.base_asset, tp.quote_asset
            FROM trades t
            JOIN trading_pairs tp ON t.trading_pair_id = tp.id
            ORDER BY t.executed_at DESC
            LIMIT $1
        "#;

        let rows = sqlx::query(query)
            .bind(limit as i64)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find recent trades: {}", e)
            ))?;

        let trades: Result<Vec<Trade>, _> = rows.iter().map(|row| self.row_to_trade(row)).collect();
        let trades = trades?;

        Ok(trades)
    }

    async fn find_large_trades(&self, min_amount: rust_decimal::Decimal, limit: u64) -> SigmaXResult<Vec<Trade>> {
        let query = r#"
            SELECT t.id, t.order_id, t.exchange_id, t.exchange_trade_id, t.side, t.quantity,
                   t.price, t.fee, t.fee_asset, t.commission_rate, t.is_maker, t.trade_time,
                   t.executed_at, t.created_at, t.metadata,
                   tp.base_asset, tp.quote_asset
            FROM trades t
            JOIN trading_pairs tp ON t.trading_pair_id = tp.id
            WHERE (t.quantity * t.price) >= $1
            ORDER BY (t.quantity * t.price) DESC
            LIMIT $2
        "#;

        let rows = sqlx::query(query)
            .bind(min_amount)
            .bind(limit as i64)
            .fetch_all(self.pool())
            .await
            .map_err(|e| SigmaXError::database(
                DatabaseErrorCode::QueryFailed,
                format!("Failed to find large trades: {}", e)
            ))?;

        let trades: Result<Vec<Trade>, _> = rows.iter().map(|row| self.row_to_trade(row)).collect();
        let trades = trades?;

        Ok(trades)
    }
}
