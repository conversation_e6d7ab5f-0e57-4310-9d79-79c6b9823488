//! 预警管理服务
//!
//! 负责管理风险预警和通知

use sigmax_interfaces::{Portfolio, risk::{RiskAlert, AlertSeverity}};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use std::collections::HashMap;
use uuid::Uuid;
use tokio::sync::mpsc;

/// 预警管理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertManagerConfig {
    /// 风险阈值
    pub risk_threshold: Decimal,
    /// 持仓集中度阈值
    pub concentration_threshold: Decimal,
    /// 杠杆阈值
    pub leverage_threshold: Decimal,
    /// 是否启用邮件通知
    pub enable_email_notifications: bool,
    /// 是否启用短信通知
    pub enable_sms_notifications: bool,
    /// 预警冷却时间（秒）
    pub alert_cooldown_seconds: u64,
}

impl Default for AlertManagerConfig {
    fn default() -> Self {
        Self {
            risk_threshold: Decimal::from_str_exact("0.8").unwrap(),
            concentration_threshold: Decimal::from_str_exact("0.3").unwrap(),
            leverage_threshold: Decimal::from(5),
            enable_email_notifications: true,
            enable_sms_notifications: false,
            alert_cooldown_seconds: 300, // 5分钟
        }
    }
}

/// 预警管理服务
pub struct AlertManager {
    config: AlertManagerConfig,
    alert_sender: Option<mpsc::UnboundedSender<RiskAlert>>,
    recent_alerts: HashMap<String, DateTime<Utc>>, // 用于防止重复预警
}

impl AlertManager {
    /// 创建新的预警管理器
    pub fn new(config: AlertManagerConfig) -> Self {
        Self {
            config,
            alert_sender: None,
            recent_alerts: HashMap::new(),
        }
    }

    /// 使用默认配置创建预警管理器
    pub fn with_default_config() -> Self {
        Self::new(AlertManagerConfig::default())
    }

    /// 设置预警发送通道
    pub fn set_alert_sender(&mut self, sender: mpsc::UnboundedSender<RiskAlert>) {
        self.alert_sender = Some(sender);
    }

    /// 检查并生成风险预警
    pub async fn check_and_alert(&mut self, context: &sigmax_interfaces::risk::RiskContext) -> AlertResult {
        let mut alerts = Vec::new();

        // 1. 检查整体风险水平
        if let Some(portfolio) = &context.portfolio {
            let risk_score = self.calculate_risk_score(portfolio);
            if risk_score > self.config.risk_threshold {
                let alert = self.create_alert(
                    AlertSeverity::High,
                    format!("High risk score detected: {:.2}%", risk_score * Decimal::from(100)),
                    "risk_score_high".to_string(),
                ).await;
                alerts.push(alert);
            }

            // 2. 检查持仓集中度
            if let Some(alert) = self.check_concentration_risk(portfolio).await {
                alerts.push(alert);
            }

            // 3. 检查杠杆风险
            if let Some(alert) = self.check_leverage_risk(portfolio).await {
                alerts.push(alert);
            }
        }

        // 4. 检查市场风险
        if let Some(alert) = self.check_market_risk(context).await {
            alerts.push(alert);
        }

        AlertResult {
            alerts,
            checked_at: Utc::now(),
        }
    }

    /// 创建预警
    async fn create_alert(&mut self, severity: AlertSeverity, message: String, alert_type: String) -> RiskAlert {
        // 检查是否在冷却期内
        if let Some(last_alert_time) = self.recent_alerts.get(&alert_type) {
            let cooldown_duration = chrono::Duration::seconds(self.config.alert_cooldown_seconds as i64);
            if Utc::now() - *last_alert_time < cooldown_duration {
                // 在冷却期内，不发送重复预警
                return RiskAlert {
                    id: Uuid::new_v4(),
                    severity: AlertSeverity::Info,
                    message: "Alert suppressed due to cooldown".to_string(),
                    source: "AlertManager".to_string(),
                    timestamp: Utc::now(),
                };
            }
        }

        let alert = RiskAlert {
            id: Uuid::new_v4(),
            severity,
            message: message.clone(),
            source: "AlertManager".to_string(),
            timestamp: Utc::now(),
        };

        // 记录预警时间
        self.recent_alerts.insert(alert_type, Utc::now());

        // 发送预警到通道
        if let Some(sender) = &self.alert_sender {
            if let Err(e) = sender.send(alert.clone()) {
                eprintln!("Failed to send alert: {}", e);
            }
        }

        // 根据配置发送通知
        self.send_notifications(&alert).await;

        alert
    }

    /// 检查持仓集中度风险
    async fn check_concentration_risk(&mut self, portfolio: &Portfolio) -> Option<RiskAlert> {
        let total_value = portfolio.total_value;
        if total_value <= Decimal::ZERO {
            return None;
        }

        // 现在可以直接使用balances，因为它们是PortfolioBalance类型
        for balance in &portfolio.balances {
            let asset_value = balance.total(); // 移除price字段，Balance中不包含价格信息
            let concentration = asset_value / total_value;

            if concentration > self.config.concentration_threshold {
                return Some(self.create_alert(
                    AlertSeverity::Medium,
                    format!(
                        "High concentration in {}: {:.2}% (threshold: {:.2}%)",
                        balance.asset,
                        concentration * Decimal::from(100),
                        self.config.concentration_threshold * Decimal::from(100)
                    ),
                    format!("concentration_{}", balance.asset),
                ).await);
            }
        }

        None
    }

    /// 检查杠杆风险
    async fn check_leverage_risk(&mut self, portfolio: &Portfolio) -> Option<RiskAlert> {
        let total_borrowed = portfolio.balances
            .iter()
            .filter(|b| b.locked > Decimal::ZERO) // Balance 中使用 locked 而不是 borrowed
            .map(|b| b.locked) // 直接使用 locked 金额
            .sum::<Decimal>();

        let total_equity = portfolio.balances
            .iter()
            .map(|b| b.available()) // 使用 available() 方法
            .sum::<Decimal>();

        if total_equity > Decimal::ZERO {
            let leverage = (total_borrowed + total_equity) / total_equity;

            if leverage > self.config.leverage_threshold {
                return Some(self.create_alert(
                    AlertSeverity::High,
                    format!(
                        "High leverage detected: {:.2}x (threshold: {:.2}x)",
                        leverage, self.config.leverage_threshold
                    ),
                    "leverage_high".to_string(),
                ).await);
            }
        }

        None
    }

    /// 检查市场风险
    async fn check_market_risk(&mut self, context: &sigmax_interfaces::risk::RiskContext) -> Option<RiskAlert> {
        if let Some(market_data) = &context.market_data {
            // 检查价格大幅波动
            if let (Some(high), Some(low)) = (market_data.high_24h, market_data.low_24h) {
                let price_range = (high - low) / market_data.price;
                let volatility_threshold = Decimal::from_str_exact("0.1").unwrap(); // 10%

                if price_range > volatility_threshold {
                    return Some(self.create_alert(
                        AlertSeverity::Medium,
                        format!(
                            "High market volatility detected: {:.2}% price range in 24h",
                            price_range * Decimal::from(100)
                        ),
                        "market_volatility_high".to_string(),
                    ).await);
                }
            }
        }

        None
    }

    /// 计算风险评分
    fn calculate_risk_score(&self, portfolio: &Portfolio) -> Decimal {
        let mut risk_score = Decimal::ZERO;
        let total_value = portfolio.total_value;

        if total_value <= Decimal::ZERO {
            return risk_score;
        }

        // 基于持仓集中度的风险
        for balance in &portfolio.balances {
            let asset_value = balance.total(); // 移除price字段，Balance中不包含价格信息
            let concentration = asset_value / total_value;

            if concentration > Decimal::from_str_exact("0.1").unwrap() {
                risk_score += concentration * Decimal::from_str_exact("0.5").unwrap();
            }
        }

        // 基于杠杆的风险
        let total_borrowed = portfolio.balances
            .iter()
            .filter(|b| b.locked > Decimal::ZERO) // Balance 中使用 locked 而不是 borrowed
            .map(|b| b.locked) // 直接使用 locked 金额
            .sum::<Decimal>();

        let total_equity = portfolio.balances
            .iter()
            .map(|b| b.available()) // 使用 available() 方法
            .sum::<Decimal>();

        if total_equity > Decimal::ZERO {
            let leverage = total_borrowed / total_equity;
            risk_score += leverage * Decimal::from_str_exact("0.1").unwrap();
        }

        risk_score.min(Decimal::ONE)
    }

    /// 发送通知
    async fn send_notifications(&self, alert: &RiskAlert) {
        // 根据严重程度和配置发送不同类型的通知
        match alert.severity {
            AlertSeverity::Emergency => {
                // 最高级别紧急处理
                if self.config.enable_email_notifications {
                    self.send_email_notification(alert).await;
                }
                if self.config.enable_sms_notifications {
                    self.send_sms_notification(alert).await;
                }
                // 紧急情况下可能需要额外的通知机制
                tracing::error!("EMERGENCY ALERT: {}", alert.message);
            },
            AlertSeverity::Critical | AlertSeverity::High => {
                if self.config.enable_email_notifications {
                    self.send_email_notification(alert).await;
                }
                if self.config.enable_sms_notifications {
                    self.send_sms_notification(alert).await;
                }
            },
            AlertSeverity::Medium => {
                if self.config.enable_email_notifications {
                    self.send_email_notification(alert).await;
                }
            },
            AlertSeverity::Low | AlertSeverity::Info => {
                // 低级别预警只记录日志
                tracing::info!("Risk alert: {}", alert.message);
            },
        }
    }

    /// 发送邮件通知
    async fn send_email_notification(&self, alert: &RiskAlert) {
        // 这里应该集成实际的邮件服务
        tracing::info!("Email notification sent for alert: {}", alert.message);
        // TODO: 实现实际的邮件发送逻辑
    }

    /// 发送短信通知
    async fn send_sms_notification(&self, alert: &RiskAlert) {
        // 这里应该集成实际的短信服务
        tracing::info!("SMS notification sent for alert: {}", alert.message);
        // TODO: 实现实际的短信发送逻辑
    }

    /// 获取预警统计
    pub fn get_alert_statistics(&self) -> AlertStatistics {
        AlertStatistics {
            total_alerts: self.recent_alerts.len(),
            active_cooldowns: self.recent_alerts
                .values()
                .filter(|&&time| {
                    let cooldown_duration = chrono::Duration::seconds(self.config.alert_cooldown_seconds as i64);
                    Utc::now() - time < cooldown_duration
                })
                .count(),
            last_check: Utc::now(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertResult {
    pub alerts: Vec<RiskAlert>,
    pub checked_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertStatistics {
    pub total_alerts: usize,
    pub active_cooldowns: usize,
    pub last_check: DateTime<Utc>,
}

#[derive(Debug, thiserror::Error)]
pub enum AlertManagerError {
    #[error("Alert processing failed: {reason}")]
    ProcessingFailed { reason: String },

    #[error("Notification failed: {service} - {reason}")]
    NotificationFailed { service: String, reason: String },

    #[error("Configuration error: {message}")]
    ConfigurationError { message: String },
}