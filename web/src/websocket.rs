//! WebSocket实时数据推送
//!
//! 提供实时的引擎状态、订单、交易等数据推送功能

use std::{
    collections::HashMap,
    sync::Arc,

};
use tokio::sync::{RwLock, mpsc};
use axum::{
    extract::{
        ws::{WebSocket, Message, WebSocketUpgrade},
        State,
    },
    response::Response,
};
use futures::{SinkExt, StreamExt};
use uuid::Uuid;
use serde::{Serialize, Deserialize};
use sigmax_core::{EngineId, EngineStatus, Order, Trade, SigmaXResult, SigmaXError};
use crate::{
    state::AppState,
    error::ApiError,
    websocket_config::WebSocketConfig,
};

/// 连接ID类型
pub type ConnectionId = Uuid;

/// WebSocket连接信息
#[derive(Debug, Clone)]
pub struct WebSocketConnection {
    pub id: ConnectionId,
    pub sender: mpsc::UnboundedSender<Message>,
    pub subscriptions: Vec<String>,
    pub user_id: Option<String>,
}

/// WebSocket响应消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebSocketResponse {
    pub event: String,
    pub channel: String,
    pub data: serde_json::Value,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// WebSocket请求消息
#[derive(Debug, Deserialize)]
pub struct WebSocketRequest {
    pub action: String,
    pub channel: Option<String>,
    pub data: Option<serde_json::Value>,
}

/// 系统事件类型
#[derive(Debug, Clone, Serialize)]
pub enum SystemEvent {
    EngineCreated(EngineId),
    EngineStarted(EngineId),
    EngineStopped(EngineId),
    EngineStatusChanged(EngineId, EngineStatus),
    OrderCreated(Order),
    OrderUpdated(Order),
    TradeExecuted(Trade),
    SystemStatus(serde_json::Value),
}

/// WebSocket服务器
pub struct WebSocketServer {
    /// 活跃连接
    connections: Arc<RwLock<HashMap<ConnectionId, WebSocketConnection>>>,
    /// 事件广播通道
    event_sender: mpsc::UnboundedSender<SystemEvent>,
    event_receiver: Arc<RwLock<Option<mpsc::UnboundedReceiver<SystemEvent>>>>,
    /// WebSocket配置
    config: Arc<RwLock<WebSocketConfig>>,
}

impl WebSocketServer {
    /// 创建新的WebSocket服务器
    pub fn new() -> Self {
        let (event_sender, event_receiver) = mpsc::unbounded_channel();

        Self {
            connections: Arc::new(RwLock::new(HashMap::new())),
            event_sender,
            event_receiver: Arc::new(RwLock::new(Some(event_receiver))),
            config: Arc::new(RwLock::new(WebSocketConfig::default())),
        }
    }

    /// 获取WebSocket配置
    pub async fn get_config(&self) -> WebSocketConfig {
        self.config.read().await.clone()
    }

    /// 启动事件处理循环
    pub async fn start_event_loop(&self) -> SigmaXResult<()> {
        let mut receiver = {
            let mut guard = self.event_receiver.write().await;
            guard.take().ok_or_else(|| SigmaXError::internal(
                sigmax_core::InternalErrorCode::UnexpectedState,
                "Event loop already started"
            ))?
        };

        let connections = Arc::clone(&self.connections);

        tokio::spawn(async move {
            while let Some(event) = receiver.recv().await {
                let message = Self::event_to_websocket_message(&event).await;
                if let Ok(msg) = message {
                    Self::broadcast_to_connections(&connections, &msg).await;
                }
            }
        });

        Ok(())
    }

    /// 处理WebSocket连接升级
    pub async fn handle_websocket_upgrade(
        State(state): State<AppState>,
        ws: WebSocketUpgrade,
    ) -> Result<Response, ApiError> {
        Ok(ws.on_upgrade(move |socket| async move {
            if let Err(e) = Self::handle_socket(state, socket).await {
                tracing::error!("WebSocket error: {}", e);
            }
        }))
    }

    /// 处理WebSocket连接
    async fn handle_socket(state: AppState, socket: WebSocket) -> SigmaXResult<()> {
        let connection_id = Uuid::new_v4();
        let (mut sender, mut receiver) = socket.split();
        let (tx, mut rx) = mpsc::unbounded_channel();

        // 创建连接信息（默认订阅基础频道）
        let connection = WebSocketConnection {
            id: connection_id,
            sender: tx.clone(),
            subscriptions: vec!["system".to_string()], // 默认只订阅系统频道
            user_id: None,
        };

        // 注册连接
        {
            let mut connections = state.websocket_server.connections.write().await;
            connections.insert(connection_id, connection);
        }

        tracing::info!("WebSocket connection established: {}", connection_id);

        // 发送欢迎消息
        let welcome_msg = WebSocketResponse {
            event: "connected".to_string(),
            channel: "system".to_string(),
            data: serde_json::json!({
                "connection_id": connection_id,
                "message": "Connected to SigmaX WebSocket"
            }),
            timestamp: chrono::Utc::now(),
        };

        if let Ok(msg_text) = serde_json::to_string(&welcome_msg) {
            let _ = tx.send(Message::Text(msg_text));
        }

        // 启动发送任务
        let mut send_task = tokio::spawn(async move {
            while let Some(msg) = rx.recv().await {
                if sender.send(msg).await.is_err() {
                    break;
                }
            }
        });

        // 启动接收任务
        let connections_clone = Arc::clone(&state.websocket_server.connections);
        let config = state.websocket_server.get_config().await;
        let mut recv_task = tokio::spawn(async move {
            while let Some(msg) = receiver.next().await {
                if let Ok(msg) = msg {
                    match msg {
                        Message::Text(text) => {
                            if let Ok(request) = serde_json::from_str::<WebSocketRequest>(&text) {
                                Self::handle_websocket_request(&connections_clone, connection_id, request, &config).await;
                            }
                        }
                        Message::Close(_) => break,
                        _ => {}
                    }
                } else {
                    break;
                }
            }
        });

        // 等待任务完成
        tokio::select! {
            _ = (&mut send_task) => {
                recv_task.abort();
            }
            _ = (&mut recv_task) => {
                send_task.abort();
            }
        }

        // 清理连接
        {
            let mut connections = state.websocket_server.connections.write().await;
            connections.remove(&connection_id);
        }

        tracing::info!("WebSocket connection closed: {}", connection_id);

        Ok(())
    }

    /// 处理WebSocket请求
    async fn handle_websocket_request(
        connections: &Arc<RwLock<HashMap<ConnectionId, WebSocketConnection>>>,
        connection_id: ConnectionId,
        request: WebSocketRequest,
        config: &WebSocketConfig,
    ) {
        match request.action.as_str() {
            "subscribe" => {
                if let Some(channel) = request.channel {
                    // 验证频道是否存在
                    if !config.is_valid_channel(&channel) {
                        // 发送错误响应
                        let connections_guard = connections.read().await;
                        if let Some(connection) = connections_guard.get(&connection_id) {
                            let response = WebSocketResponse {
                                event: "error".to_string(),
                                channel: "system".to_string(),
                                data: serde_json::json!({
                                    "error": "Invalid channel",
                                    "channel": channel,
                                    "message": "Channel does not exist"
                                }),
                                timestamp: chrono::Utc::now(),
                            };

                            if let Ok(msg_text) = serde_json::to_string(&response) {
                                let _ = connection.sender.send(Message::Text(msg_text));
                            }
                        }
                        return;
                    }

                    let mut connections_guard = connections.write().await;
                    if let Some(connection) = connections_guard.get_mut(&connection_id) {
                        if !connection.subscriptions.contains(&channel) {
                            connection.subscriptions.push(channel.clone());

                            let response = WebSocketResponse {
                                event: "subscribed".to_string(),
                                channel: "system".to_string(),
                                data: serde_json::json!({
                                    "channel": channel,
                                    "message": "Successfully subscribed"
                                }),
                                timestamp: chrono::Utc::now(),
                            };

                            if let Ok(msg_text) = serde_json::to_string(&response) {
                                let _ = connection.sender.send(Message::Text(msg_text));
                            }
                        }
                    }
                }
            }
            "unsubscribe" => {
                if let Some(channel) = request.channel {
                    let mut connections_guard = connections.write().await;
                    if let Some(connection) = connections_guard.get_mut(&connection_id) {
                        connection.subscriptions.retain(|c| c != &channel);

                        let response = WebSocketResponse {
                            event: "unsubscribed".to_string(),
                            channel: "system".to_string(),
                            data: serde_json::json!({
                                "channel": channel,
                                "message": "Successfully unsubscribed"
                            }),
                            timestamp: chrono::Utc::now(),
                        };

                        if let Ok(msg_text) = serde_json::to_string(&response) {
                            let _ = connection.sender.send(Message::Text(msg_text));
                        }
                    }
                }
            }
            "ping" => {
                let connections_guard = connections.read().await;
                if let Some(connection) = connections_guard.get(&connection_id) {
                    let response = WebSocketResponse {
                        event: "pong".to_string(),
                        channel: "system".to_string(),
                        data: serde_json::json!({
                            "message": "pong",
                            "timestamp": chrono::Utc::now()
                        }),
                        timestamp: chrono::Utc::now(),
                    };

                    if let Ok(msg_text) = serde_json::to_string(&response) {
                        let _ = connection.sender.send(Message::Text(msg_text));
                    }
                }
            }
            _ => {
                tracing::warn!("Unknown WebSocket action: {}", request.action);
            }
        }
    }

    /// 广播事件到所有连接
    pub async fn broadcast_event(&self, event: SystemEvent) -> SigmaXResult<()> {
        self.event_sender.send(event)
            .map_err(|e| SigmaXError::internal(
                sigmax_core::InternalErrorCode::ServiceUnavailable,
                format!("Failed to send event: {}", e)
            ))?;
        Ok(())
    }

    /// 将系统事件转换为WebSocket消息
    async fn event_to_websocket_message(event: &SystemEvent) -> SigmaXResult<WebSocketResponse> {
        let (event_name, channel, data) = match event {
            SystemEvent::EngineCreated(engine_id) => (
                "engine_created",
                "engines",
                serde_json::json!({"engine_id": engine_id}),
            ),
            SystemEvent::EngineStarted(engine_id) => (
                "engine_started",
                "engines",
                serde_json::json!({"engine_id": engine_id}),
            ),
            SystemEvent::EngineStopped(engine_id) => (
                "engine_stopped",
                "engines",
                serde_json::json!({"engine_id": engine_id}),
            ),
            SystemEvent::EngineStatusChanged(engine_id, status) => (
                "engine_status_changed",
                "engines",
                serde_json::json!({
                    "engine_id": engine_id,
                    "status": status
                }),
            ),
            SystemEvent::OrderCreated(order) => (
                "order_created",
                "orders",
                serde_json::to_value(order)?,
            ),
            SystemEvent::OrderUpdated(order) => (
                "order_updated",
                "orders",
                serde_json::to_value(order)?,
            ),
            SystemEvent::TradeExecuted(trade) => (
                "trade_executed",
                "trades",
                serde_json::to_value(trade)?,
            ),
            SystemEvent::SystemStatus(status) => (
                "system_status",
                "system",
                status.clone(),
            ),
        };

        Ok(WebSocketResponse {
            event: event_name.to_string(),
            channel: channel.to_string(),
            data,
            timestamp: chrono::Utc::now(),
        })
    }

    /// 广播消息到所有连接
    async fn broadcast_to_connections(
        connections: &Arc<RwLock<HashMap<ConnectionId, WebSocketConnection>>>,
        message: &WebSocketResponse,
    ) {
        let connections_guard = connections.read().await;
        let msg_text = match serde_json::to_string(message) {
            Ok(text) => text,
            Err(e) => {
                tracing::error!("Failed to serialize WebSocket message: {}", e);
                return;
            }
        };

        for connection in connections_guard.values() {
            // 检查订阅
            if connection.subscriptions.contains(&message.channel) {
                if let Err(e) = connection.sender.send(Message::Text(msg_text.clone())) {
                    tracing::warn!("Failed to send message to connection {}: {}", connection.id, e);
                }
            }
        }
    }

    /// 获取连接统计
    pub async fn get_connection_stats(&self) -> (usize, Vec<ConnectionId>) {
        let connections = self.connections.read().await;
        let count = connections.len();
        let ids = connections.keys().cloned().collect();
        (count, ids)
    }

    /// 获取详细的连接信息
    pub async fn get_detailed_connections(&self) -> Vec<serde_json::Value> {
        let connections = self.connections.read().await;
        connections.values().map(|conn| {
            serde_json::json!({
                "connection_id": conn.id,
                "user_id": conn.user_id,
                "subscriptions": conn.subscriptions,
                "connection_type": "User", // 默认类型
                "connected_duration": "unknown", // 需要添加连接时间跟踪
                "message_count": 0, // 需要添加消息计数
                "last_activity": "unknown" // 需要添加活动时间跟踪
            })
        }).collect()
    }

    /// 获取服务器统计信息
    pub async fn get_server_stats(&self) -> serde_json::Value {
        let connections = self.connections.read().await;
        let total_connections = connections.len();

        // 按订阅频道统计
        let mut channel_stats = std::collections::HashMap::new();
        for connection in connections.values() {
            for channel in &connection.subscriptions {
                *channel_stats.entry(channel.clone()).or_insert(0) += 1;
            }
        }

        serde_json::json!({
            "total_connections": total_connections,
            "connections_by_type": {
                "User": total_connections,
                "Admin": 0,
                "ApiClient": 0,
                "Monitor": 0
            },
            "total_messages_sent": 0, // 需要添加消息计数
            "channels": channel_stats.into_iter().map(|(name, count)| {
                serde_json::json!({
                    "name": name,
                    "subscriber_count": count,
                    "message_count": 0,
                    "last_message_time": null
                })
            }).collect::<Vec<_>>(),
            "uptime": "unknown" // 需要添加启动时间跟踪
        })
    }

    /// 广播消息到指定频道
    pub async fn broadcast_message_to_channel(
        &self,
        channel: &str,
        event: &str,
        data: serde_json::Value,
    ) -> SigmaXResult<()> {
        let message = WebSocketResponse {
            event: event.to_string(),
            channel: channel.to_string(),
            data,
            timestamp: chrono::Utc::now(),
        };

        Self::broadcast_to_connections(&self.connections, &message).await;
        Ok(())
    }

    /// 清理非活跃连接（模拟实现）
    pub async fn cleanup_inactive_connections(&self) -> usize {
        // 这里是一个模拟实现，实际应该检查连接的活跃状态
        // 由于当前实现没有跟踪连接活跃时间，我们返回0
        0
    }
}

impl Default for WebSocketServer {
    fn default() -> Self {
        Self::new()
    }
}
