//! 回测异常恢复管理器
//!
//! 处理回测过程中的异常情况，提供状态恢复和数据一致性保障

use sigmax_core::{SigmaXResult, SigmaXError, PortfolioManager, Amount};
use sigmax_interfaces::trading::PortfolioSnapshot;
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use tracing::{info, warn, error, debug};

/// 回测状态快照
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BacktestStateSnapshot {
    /// 快照ID
    pub id: uuid::Uuid,
    /// 快照时间
    pub timestamp: DateTime<Utc>,
    /// 当前处理的数据索引
    pub current_data_index: usize,
    /// 投资组合快照
    pub portfolio_snapshot: PortfolioSnapshot,
    /// 已执行的交易数量
    pub executed_trades_count: usize,
    /// 回测进度百分比
    pub progress_percentage: f64,
    /// 状态描述
    pub state_description: String,
}

/// 恢复操作类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RecoveryAction {
    /// 回滚到上一个状态
    Rollback,
    /// 跳过当前错误继续执行
    Skip,
    /// 重试当前操作
    Retry,
    /// 安全停止回测
    SafeStop,
    /// 修复数据不一致
    FixInconsistency,
}

/// 异常恢复结果
#[derive(Debug, Clone)]
pub struct RecoveryResult {
    /// 是否成功恢复
    pub success: bool,
    /// 恢复操作类型
    pub action: RecoveryAction,
    /// 恢复后的状态
    pub recovered_state: Option<BacktestStateSnapshot>,
    /// 恢复消息
    pub message: String,
    /// 恢复时间
    pub recovery_time: DateTime<Utc>,
}

/// 异常恢复管理器
pub struct RecoveryManager {
    /// 状态快照历史
    state_snapshots: Arc<RwLock<Vec<BacktestStateSnapshot>>>,
    /// 最大保存的快照数量
    max_snapshots: usize,
    /// 自动恢复策略
    auto_recovery_enabled: bool,
    /// 最大重试次数
    max_retry_attempts: u32,
    /// 当前重试次数
    current_retry_count: Arc<RwLock<u32>>,
}

impl RecoveryManager {
    /// 创建新的恢复管理器
    pub fn new(max_snapshots: usize, auto_recovery_enabled: bool, max_retry_attempts: u32) -> Self {
        Self {
            state_snapshots: Arc::new(RwLock::new(Vec::new())),
            max_snapshots,
            auto_recovery_enabled,
            max_retry_attempts,
            current_retry_count: Arc::new(RwLock::new(0)),
        }
    }

    /// 创建状态快照
    pub async fn create_snapshot(
        &self,
        current_data_index: usize,
        portfolio_snapshot: PortfolioSnapshot,
        executed_trades_count: usize,
        progress_percentage: f64,
        state_description: String,
    ) -> SigmaXResult<uuid::Uuid> {
        let snapshot = BacktestStateSnapshot {
            id: uuid::Uuid::new_v4(),
            timestamp: Utc::now(),
            current_data_index,
            portfolio_snapshot,
            executed_trades_count,
            progress_percentage,
            state_description,
        };

        let snapshot_id = snapshot.id;
        let mut snapshots = self.state_snapshots.write().await;

        snapshots.push(snapshot);

        // 保持快照数量在限制内
        if snapshots.len() > self.max_snapshots {
            snapshots.remove(0);
        }

        debug!("创建回测状态快照: {} (进度: {:.2}%)", snapshot_id, progress_percentage);
        Ok(snapshot_id)
    }

    /// 处理异常并尝试恢复
    pub async fn handle_exception(
        &self,
        error: &SigmaXError,
        context: &str,
        portfolio_manager: &Arc<dyn PortfolioManager>,
    ) -> SigmaXResult<RecoveryResult> {
        error!("回测异常发生: {} - 上下文: {}", error, context);

        // 根据错误类型决定恢复策略
        let recovery_action = self.determine_recovery_action(error).await;

        match recovery_action {
            RecoveryAction::Rollback => {
                self.perform_rollback(portfolio_manager).await
            }
            RecoveryAction::Skip => {
                self.perform_skip().await
            }
            RecoveryAction::Retry => {
                self.perform_retry().await
            }
            RecoveryAction::SafeStop => {
                self.perform_safe_stop().await
            }
            RecoveryAction::FixInconsistency => {
                self.perform_fix_inconsistency(portfolio_manager).await
            }
        }
    }

    /// 确定恢复策略
    async fn determine_recovery_action(&self, error: &SigmaXError) -> RecoveryAction {
        match error {
            // 投资组合相关错误 - 尝试修复或回滚
            SigmaXError::Validation { .. } if error.to_string().contains("投资组合") => {
                RecoveryAction::FixInconsistency
            }

            // 市场数据错误 - 跳过当前数据点
            SigmaXError::MarketData { .. } => RecoveryAction::Skip,

            // 网络或交易所错误 - 重试
            SigmaXError::Network { .. } | SigmaXError::ExchangeApi { .. } => RecoveryAction::Retry,

            // 配置错误 - 安全停止
            SigmaXError::Configuration { .. } => RecoveryAction::SafeStop,

            // 其他错误 - 根据重试次数决定
            _ => {
                let retry_count = *self.current_retry_count.read().await;
                if retry_count < self.max_retry_attempts {
                    RecoveryAction::Retry
                } else {
                    RecoveryAction::Rollback
                }
            }
        }
    }

    /// 执行回滚操作
    async fn perform_rollback(&self, portfolio_manager: &Arc<dyn PortfolioManager>) -> SigmaXResult<RecoveryResult> {
        let snapshots = self.state_snapshots.read().await;

        if let Some(last_snapshot) = snapshots.last() {
            info!("执行回滚操作到快照: {}", last_snapshot.id);

            // 尝试恢复投资组合状态
            match self.restore_portfolio_state(portfolio_manager, &last_snapshot.portfolio_snapshot).await {
                Ok(()) => {
                    // 重置重试计数
                    *self.current_retry_count.write().await = 0;

                    Ok(RecoveryResult {
                        success: true,
                        action: RecoveryAction::Rollback,
                        recovered_state: Some(last_snapshot.clone()),
                        message: format!("成功回滚到快照 {}", last_snapshot.id),
                        recovery_time: Utc::now(),
                    })
                }
                Err(e) => {
                    error!("投资组合状态恢复失败: {}", e);
                    Ok(RecoveryResult {
                        success: false,
                        action: RecoveryAction::Rollback,
                        recovered_state: None,
                        message: format!("回滚失败: {}", e),
                        recovery_time: Utc::now(),
                    })
                }
            }
        } else {
            warn!("没有可用的状态快照进行回滚");
            Ok(RecoveryResult {
                success: false,
                action: RecoveryAction::Rollback,
                recovered_state: None,
                message: "没有可用的状态快照".to_string(),
                recovery_time: Utc::now(),
            })
        }
    }

    /// 执行跳过操作
    async fn perform_skip(&self) -> SigmaXResult<RecoveryResult> {
        info!("跳过当前错误，继续执行回测");

        Ok(RecoveryResult {
            success: true,
            action: RecoveryAction::Skip,
            recovered_state: None,
            message: "跳过当前错误".to_string(),
            recovery_time: Utc::now(),
        })
    }

    /// 执行重试操作
    async fn perform_retry(&self) -> SigmaXResult<RecoveryResult> {
        let mut retry_count = self.current_retry_count.write().await;
        *retry_count += 1;

        info!("重试操作 (第 {} 次)", *retry_count);

        if *retry_count <= self.max_retry_attempts {
            Ok(RecoveryResult {
                success: true,
                action: RecoveryAction::Retry,
                recovered_state: None,
                message: format!("重试操作 (第 {} 次)", *retry_count),
                recovery_time: Utc::now(),
            })
        } else {
            warn!("重试次数已达上限，转为回滚操作");
            Ok(RecoveryResult {
                success: false,
                action: RecoveryAction::Retry,
                recovered_state: None,
                message: "重试次数已达上限".to_string(),
                recovery_time: Utc::now(),
            })
        }
    }

    /// 执行安全停止操作
    async fn perform_safe_stop(&self) -> SigmaXResult<RecoveryResult> {
        warn!("执行安全停止操作");

        Ok(RecoveryResult {
            success: true,
            action: RecoveryAction::SafeStop,
            recovered_state: None,
            message: "安全停止回测".to_string(),
            recovery_time: Utc::now(),
        })
    }

    /// 执行数据一致性修复
    async fn perform_fix_inconsistency(&self, portfolio_manager: &Arc<dyn PortfolioManager>) -> SigmaXResult<RecoveryResult> {
        info!("尝试修复数据一致性问题");

        // 验证投资组合状态
        match self.validate_portfolio_consistency(portfolio_manager).await {
            Ok(true) => {
                Ok(RecoveryResult {
                    success: true,
                    action: RecoveryAction::FixInconsistency,
                    recovered_state: None,
                    message: "数据一致性验证通过".to_string(),
                    recovery_time: Utc::now(),
                })
            }
            Ok(false) => {
                warn!("数据一致性验证失败，尝试修复");
                // 这里可以添加具体的修复逻辑
                Ok(RecoveryResult {
                    success: false,
                    action: RecoveryAction::FixInconsistency,
                    recovered_state: None,
                    message: "数据一致性修复失败".to_string(),
                    recovery_time: Utc::now(),
                })
            }
            Err(e) => {
                error!("数据一致性检查失败: {}", e);
                Ok(RecoveryResult {
                    success: false,
                    action: RecoveryAction::FixInconsistency,
                    recovered_state: None,
                    message: format!("一致性检查失败: {}", e),
                    recovery_time: Utc::now(),
                })
            }
        }
    }

    /// 恢复投资组合状态
    async fn restore_portfolio_state(
        &self,
        portfolio_manager: &Arc<dyn PortfolioManager>,
        target_snapshot: &PortfolioSnapshot,
    ) -> SigmaXResult<()> {
        // 获取当前投资组合状态
        let _current_balances = portfolio_manager.get_balances().await?;

        // 比较并调整余额（这里需要根据具体的PortfolioManager实现来调整）
        info!("恢复投资组合状态到时间点: {}", target_snapshot.timestamp);

        // 注意：这里的实现取决于PortfolioManager是否支持状态恢复
        // 在实际实现中，可能需要重新计算或重新应用交易历史

        Ok(())
    }

    /// 验证投资组合一致性
    async fn validate_portfolio_consistency(&self, portfolio_manager: &Arc<dyn PortfolioManager>) -> SigmaXResult<bool> {
        // 获取当前余额
        let balances = portfolio_manager.get_balances().await?;

        // 检查余额是否合理
        for (asset, balance) in &balances {
            if balance.free < Amount::ZERO {
                warn!("发现负余额: {} = {}", asset, balance.free);
                return Ok(false);
            }
        }

        debug!("投资组合一致性验证通过");
        Ok(true)
    }

    /// 获取最新的状态快照
    pub async fn get_latest_snapshot(&self) -> Option<BacktestStateSnapshot> {
        let snapshots = self.state_snapshots.read().await;
        snapshots.last().cloned()
    }

    /// 获取所有状态快照
    pub async fn get_all_snapshots(&self) -> Vec<BacktestStateSnapshot> {
        let snapshots = self.state_snapshots.read().await;
        snapshots.clone()
    }

    /// 清理状态快照
    pub async fn clear_snapshots(&self) {
        let mut snapshots = self.state_snapshots.write().await;
        snapshots.clear();
        info!("已清理所有状态快照");
    }

    /// 重置重试计数
    pub async fn reset_retry_count(&self) {
        *self.current_retry_count.write().await = 0;
    }
}
