//! 策略服务容器
//!
//! 提供策略运行所需的服务依赖，采用简单的依赖注入模式

use std::sync::Arc;
use async_trait::async_trait;
use sigmax_core::{
    PortfolioManager, StrategyServiceContainer,
    SigmaXResult, SigmaXError, EventBus, TradingPair, ExchangeId,
    Order, OrderId, OrderStatus, OrderExecutor, ConfigErrorCode, ValidationErrorCode
};
use sigmax_interfaces::DataProvider;
use sigmax_execution::OrderManager;

/// 策略服务容器
///
/// 包含策略运行所需的所有服务依赖
///
/// ## 设计原则
/// - 简单的依赖注入，避免复杂的IoC容器
/// - 使用Arc进行共享，支持多策略并发
/// - 明确的服务边界，便于测试和mock
#[derive(Clone)]
pub struct StrategyServices {
    /// 投资组合管理器
    pub portfolio_manager: Arc<dyn PortfolioManager>,
    /// 数据提供者
    pub data_provider: Arc<dyn DataProvider>,
    /// 订单管理器
    pub order_manager: Arc<OrderManager>,
}

impl std::fmt::Debug for StrategyServices {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("StrategyServices")
            .field("portfolio_manager", &"Arc<dyn PortfolioManager>")
            .field("data_provider", &"Arc<dyn DataProvider>")
            .field("order_manager", &"Arc<OrderManager>")
            .finish()
    }
}

impl StrategyServices {
    /// 创建新的服务容器
    ///
    /// # 参数
    /// - `portfolio_manager`: 投资组合管理器
    /// - `data_provider`: 数据提供者
    /// - `order_manager`: 订单管理器
    pub fn new(
        portfolio_manager: Arc<dyn PortfolioManager>,
        data_provider: Arc<dyn DataProvider>,
        order_manager: Arc<OrderManager>,
    ) -> Self {
        Self {
            portfolio_manager,
            data_provider,
            order_manager,
        }
    }

    /// 验证服务完整性
    ///
    /// 确保所有必要的服务都已提供
    pub fn validate(&self) -> sigmax_core::SigmaXResult<()> {
        // 这里可以添加服务健康检查逻辑
        // 目前简单返回成功
        Ok(())
    }
}

/// 服务构建器
///
/// 提供便捷的服务容器构建方法
pub struct StrategyServicesBuilder {
    portfolio_manager: Option<Arc<dyn PortfolioManager>>,
    data_provider: Option<Arc<dyn DataProvider>>,
    order_manager: Option<Arc<OrderManager>>,
}

impl StrategyServicesBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            portfolio_manager: None,
            data_provider: None,
            order_manager: None,
        }
    }

    /// 设置投资组合管理器
    pub fn with_portfolio_manager(mut self, portfolio_manager: Arc<dyn PortfolioManager>) -> Self {
        self.portfolio_manager = Some(portfolio_manager);
        self
    }



    /// 设置数据提供者
    pub fn with_data_provider(mut self, data_provider: Arc<dyn DataProvider>) -> Self {
        self.data_provider = Some(data_provider);
        self
    }

    /// 设置订单管理器
    pub fn with_order_manager(mut self, order_manager: Arc<OrderManager>) -> Self {
        self.order_manager = Some(order_manager);
        self
    }

    /// 构建服务容器
    ///
    /// # 返回
    /// - `Ok(StrategyServices)`: 构建成功
    /// - `Err(SigmaXError)`: 缺少必要的服务
    pub fn build(self) -> sigmax_core::SigmaXResult<StrategyServices> {
        let portfolio_manager = self.portfolio_manager
            .ok_or_else(|| sigmax_core::SigmaXError::validation(
                ValidationErrorCode::Required,
                "portfolio_manager",
                "Missing portfolio manager"
            ))?;

        let data_provider = self.data_provider
            .ok_or_else(|| sigmax_core::SigmaXError::validation(
                ValidationErrorCode::Required,
                "data_provider",
                "Missing data provider"
            ))?;
        let order_manager = self.order_manager
            .ok_or_else(|| sigmax_core::SigmaXError::validation(
                ValidationErrorCode::Required,
                "order_manager",
                "Missing order manager"
            ))?;

        let services = StrategyServices::new(
            portfolio_manager,
            data_provider,
            order_manager,
        );

        services.validate()?;
        Ok(services)
    }
}

impl Default for StrategyServicesBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 策略服务容器适配器
///
/// 让 StrategyServices 实现 StrategyServiceContainer trait，
/// 提供与 core 模块接口的兼容性
pub struct StrategyServiceContainerAdapter {
    services: StrategyServices,
    trading_pair: TradingPair,
    exchange_id: ExchangeId,
    event_bus: Arc<EventBus>,
}

impl StrategyServiceContainerAdapter {
    /// 创建新的适配器
    pub fn new(
        services: StrategyServices,
        trading_pair: TradingPair,
        exchange_id: ExchangeId,
        event_bus: Arc<EventBus>,
    ) -> Self {
        Self {
            services,
            trading_pair,
            exchange_id,
            event_bus,
        }
    }
}

#[async_trait]
impl StrategyServiceContainer for StrategyServiceContainerAdapter {
    async fn portfolio_manager(&self) -> SigmaXResult<Arc<dyn PortfolioManager>> {
        Ok(Arc::clone(&self.services.portfolio_manager))
    }



    async fn order_executor(&self) -> SigmaXResult<Arc<dyn OrderExecutor>> {
        // 将 OrderManager 适配为 OrderExecutor
        Ok(Arc::new(OrderExecutorAdapter::new(Arc::clone(&self.services.order_manager))))
    }



    async fn event_bus(&self) -> SigmaXResult<Arc<EventBus>> {
        Ok(Arc::clone(&self.event_bus))
    }

    fn trading_pair(&self) -> &TradingPair {
        &self.trading_pair
    }

    fn exchange_id(&self) -> &ExchangeId {
        &self.exchange_id
    }

    async fn health_check(&self) -> SigmaXResult<()> {
        // 简单的健康检查实现
        self.services.validate()?;
        Ok(())
    }
}

/// OrderManager 到 OrderExecutor 的适配器
pub struct OrderExecutorAdapter {
    order_manager: Arc<OrderManager>,
}

impl OrderExecutorAdapter {
    pub fn new(order_manager: Arc<OrderManager>) -> Self {
        Self { order_manager }
    }
}

#[async_trait]
impl OrderExecutor for OrderExecutorAdapter {
    async fn submit_order(&self, order: Order) -> SigmaXResult<OrderId> {
        let order_id = order.id;
        self.order_manager.add_order(order).await?;
        Ok(order_id)
    }

    async fn cancel_order(&self, order_id: OrderId) -> SigmaXResult<()> {
        self.order_manager.cancel_order(order_id).await
    }

    async fn get_order_status(&self, order_id: OrderId) -> SigmaXResult<OrderStatus> {
        match self.order_manager.get_order(order_id).await? {
            Some(order) => Ok(order.status),
            None => Err(SigmaXError::validation(
                ValidationErrorCode::Required,
                "order_id",
                format!("Order {} not found", order_id)
            )),
        }
    }

    async fn get_active_orders(&self) -> SigmaXResult<Vec<Order>> {
        self.order_manager.get_active_orders().await
    }
}

/// 策略服务容器适配器构建器
pub struct StrategyServiceContainerAdapterBuilder {
    services: Option<StrategyServices>,
    trading_pair: Option<TradingPair>,
    exchange_id: Option<ExchangeId>,
    event_bus: Option<Arc<EventBus>>,
}

impl StrategyServiceContainerAdapterBuilder {
    pub fn new() -> Self {
        Self {
            services: None,
            trading_pair: None,
            exchange_id: None,
            event_bus: None,
        }
    }

    pub fn with_services(mut self, services: StrategyServices) -> Self {
        self.services = Some(services);
        self
    }

    pub fn with_trading_pair(mut self, trading_pair: TradingPair) -> Self {
        self.trading_pair = Some(trading_pair);
        self
    }

    pub fn with_exchange_id(mut self, exchange_id: ExchangeId) -> Self {
        self.exchange_id = Some(exchange_id);
        self
    }

    pub fn with_event_bus(mut self, event_bus: Arc<EventBus>) -> Self {
        self.event_bus = Some(event_bus);
        self
    }

    pub fn build(self) -> SigmaXResult<StrategyServiceContainerAdapter> {
        let services = self.services
            .ok_or_else(|| SigmaXError::validation(
                ValidationErrorCode::Required,
                "services",
                "Missing strategy services"
            ))?;
        let trading_pair = self.trading_pair
            .ok_or_else(|| SigmaXError::validation(
                ValidationErrorCode::Required,
                "trading_pair",
                "Missing trading pair"
            ))?;
        let exchange_id = self.exchange_id
            .ok_or_else(|| SigmaXError::validation(
                ValidationErrorCode::Required,
                "exchange_id",
                "Missing exchange ID"
            ))?;
        let event_bus = self.event_bus
            .ok_or_else(|| SigmaXError::validation(
                ValidationErrorCode::Required,
                "event_bus",
                "Missing event bus"
            ))?;

        Ok(StrategyServiceContainerAdapter::new(
            services,
            trading_pair,
            exchange_id,
            event_bus,
        ))
    }
}

impl Default for StrategyServiceContainerAdapterBuilder {
    fn default() -> Self {
        Self::new()
    }
}
