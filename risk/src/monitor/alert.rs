//! 告警服务

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::RwLock;

/// 告警级别
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum AlertLevel {
    /// 信息
    Info,
    /// 警告
    Warning,
    /// 高风险
    High,
    /// 严重
    Critical,
}

/// 告警信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Alert {
    pub level: AlertLevel,
    pub message: String,
    pub timestamp: DateTime<Utc>,
}

/// 告警服务
pub struct AlertService {
    alerts: Arc<RwLock<Vec<Alert>>>,
}

impl AlertService {
    pub fn new() -> Self {
        Self {
            alerts: Arc::new(RwLock::new(Vec::new())),
        }
    }
    
    /// 发送告警
    pub async fn send_alert(&self, level: AlertLevel, message: String) -> Result<(), String> {
        let alert = Alert {
            level,
            message: message.clone(),
            timestamp: Utc::now(),
        };
        
        // 保存告警记录
        let mut alerts = self.alerts.write().await;
        alerts.push(alert);
        
        // 实际实现时这里应该：
        // 1. 发送邮件/短信通知
        // 2. 记录到数据库
        // 3. 推送到监控系统
        
        // 简化实现：只打印日志
        match level {
            AlertLevel::Critical => {
                tracing::error!("CRITICAL ALERT: {}", message);
            }
            AlertLevel::High => {
                tracing::warn!("HIGH RISK ALERT: {}", message);
            }
            AlertLevel::Warning => {
                tracing::warn!("WARNING: {}", message);
            }
            AlertLevel::Info => {
                tracing::info!("INFO: {}", message);
            }
        }
        
        Ok(())
    }
    
    /// 获取最近的告警
    pub async fn get_recent_alerts(&self, limit: usize) -> Vec<Alert> {
        let alerts = self.alerts.read().await;
        let start = if alerts.len() > limit {
            alerts.len() - limit
        } else {
            0
        };
        
        alerts[start..].to_vec()
    }
    
    /// 清理旧告警
    pub async fn cleanup_old_alerts(&self, keep_days: i64) {
        let cutoff = Utc::now() - chrono::Duration::days(keep_days);
        let mut alerts = self.alerts.write().await;
        alerts.retain(|a| a.timestamp > cutoff);
    }
}

impl Default for AlertService {
    fn default() -> Self {
        Self::new()
    }
}
