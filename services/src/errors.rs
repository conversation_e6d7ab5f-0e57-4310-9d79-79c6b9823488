//! 服务模块错误定义
//!
//! 这个模块现在使用新的错误处理系统。
//! 基于 SigmaX 统一错误类型。

use sigmax_core::{SigmaXError, SigmaXResult};
use std::collections::HashMap;

// 重新导出核心错误类型
pub use sigmax_core::{
    SigmaXError as ServiceError,
    SigmaXResult as ServiceResult,
};

/// 创建缓存错误的便利函数
pub fn cache_error(message: &str, cache_type: &str, operation: &str) -> SigmaXError {
    SigmaXError::database(
        sigmax_core::DatabaseErrorCode::ConnectionFailed,
        format!("缓存操作失败 ({}): {} - {}", cache_type, operation, message)
    ).with_context("cache_type", cache_type)
     .with_context("operation", operation)
}

/// 创建指标收集错误的便利函数
pub fn metrics_error(message: &str, component: &str) -> SigmaXError {
    SigmaXError::internal(
        sigmax_core::InternalErrorCode::UnexpectedState,
        format!("指标收集错误 ({}): {}", component, message)
    ).with_context("component", component)
}

/// 创建配置错误的便利函数
pub fn config_error(message: &str, config_key: Option<&str>, config_file: Option<&str>) -> SigmaXError {
    let full_message = match (config_key, config_file) {
        (Some(key), Some(file)) => format!("{} (key: {}, file: {})", message, key, file),
        (Some(key), None) => format!("{} (key: {})", message, key),
        (None, Some(file)) => format!("{} (file: {})", message, file),
        (None, None) => message.to_string(),
    };
    SigmaXError::configuration(
        sigmax_core::ConfigErrorCode::InvalidValue,
        full_message
    )
}

/// 创建网络错误的便利函数
pub fn network_error(message: &str, _endpoint: &str) -> SigmaXError {
    SigmaXError::network(
        sigmax_core::NetworkErrorCode::Timeout,
        message
    )
}

/// 创建数据库错误的便利函数
pub fn database_error(message: &str) -> SigmaXError {
    SigmaXError::database(
        sigmax_core::DatabaseErrorCode::QueryFailed,
        message
    )
}

/// 创建验证错误的便利函数
pub fn validation_error(field: &str, message: &str) -> SigmaXError {
    SigmaXError::validation(
        sigmax_core::ValidationErrorCode::InvalidFormat,
        field,
        message
    )
}
