//! SigmaX高级风险管理功能演示
//!
//! 展示所有新实现的高级风险管理功能

use sigmax_risk::{
    RiskEngine, RiskEngineBuilder, RiskMetrics, RiskLevel,
    create_default_engine, quick_check_order,
    OrderRiskPolicy, PositionRiskPolicy, MarketRiskPolicy,
    RiskMonitor, MonitorConfig, AlertService, AlertLevel,
    RiskAnalyzer, AnalysisConfig,
};
use sigmax_core::{TradingPair, ExchangeId};
use rust_decimal_macros::dec;
use chrono::Utc;
use uuid::Uuid;
use std::collections::HashMap;
use tokio;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🎉 SigmaX高级风险管理功能演示");
    println!("=====================================\n");

    // 1. 高级风险指标演示
    demo_advanced_metrics().await?;
    
    // 2. 相关性风险模型演示
    demo_correlation_model().await?;
    
    // 3. 流动性风险模型演示
    demo_liquidity_model().await?;
    
    // 4. 信用风险模型演示
    demo_credit_model().await?;
    
    // 5. 实时预警系统演示
    demo_alert_system().await?;
    
    // 6. 压力测试演示
    demo_stress_test().await?;
    
    // 7. 蒙特卡洛模拟演示
    demo_monte_carlo().await?;
    
    // 8. 敏感性分析演示
    demo_sensitivity_analysis().await?;

    println!("\n🎊 所有高级风险管理功能演示完成！");
    println!("SigmaX现在具备企业级风险管理能力！");
    
    Ok(())
}

/// 演示高级风险指标
async fn demo_advanced_metrics() -> Result<(), Box<dyn std::error::Error>> {
    println!("📊 1. 高级风险指标演示");
    println!("------------------------");
    
    let mut calculator = AdvancedRiskCalculator::new(252);
    calculator.update_portfolio_value(dec!(1000000));
    
    // 添加模拟收益率数据
    let returns = vec![
        dec!(0.02), dec!(-0.01), dec!(0.015), dec!(-0.03), dec!(0.01),
        dec!(-0.02), dec!(0.025), dec!(-0.015), dec!(0.005), dec!(-0.04),
        dec!(0.03), dec!(-0.025), dec!(0.02), dec!(-0.01), dec!(0.015),
        dec!(-0.035), dec!(0.01), dec!(-0.02), dec!(0.025), dec!(-0.015),
        dec!(0.005), dec!(-0.05), dec!(0.03), dec!(-0.025), dec!(0.02),
        dec!(-0.01), dec!(0.015), dec!(-0.03), dec!(0.01), dec!(-0.02),
        dec!(0.018), dec!(-0.012), dec!(0.022), dec!(-0.018), dec!(0.008),
    ];
    
    for return_rate in returns {
        calculator.add_return(return_rate);
    }
    
    let metrics = calculator.calculate_advanced_metrics()?;
    
    println!("✅ CVaR 95%: ${:.2}", metrics.cvar_95);
    println!("✅ CVaR 99%: ${:.2}", metrics.cvar_99);
    println!("✅ CVaR 99.9%: ${:.2}", metrics.cvar_999);
    println!("✅ 尾部风险: {:.2}%", metrics.tail_risk * 100.0);
    println!("✅ 期望短缺: ${:.2}", metrics.expected_shortfall);
    println!("✅ 最大损失期望: ${:.2}", metrics.maximum_loss_expectation);
    println!("✅ 风险贡献度: {:.4}\n", metrics.risk_contribution);
    
    Ok(())
}

/// 演示相关性风险模型
async fn demo_correlation_model() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔗 2. 相关性风险模型演示");
    println!("---------------------------");
    
    let mut calculator = CorrelationRiskCalculator::new(100, 20);
    
    let btc_usdt = TradingPair::new("BTC".to_string(), "USDT".to_string());
    let eth_usdt = TradingPair::new("ETH".to_string(), "USDT".to_string());
    let ada_usdt = TradingPair::new("ADA".to_string(), "USDT".to_string());
    
    calculator.add_asset(btc_usdt.clone(), 0.5);
    calculator.add_asset(eth_usdt.clone(), 0.3);
    calculator.add_asset(ada_usdt.clone(), 0.2);
    
    // 添加相关的收益率数据
    for i in 0..30 {
        let base_return = (i as f64 * 0.001).sin() * 0.02;
        let btc_return = dec!(base_return);
        let eth_return = dec!(base_return * 0.8 + 0.005); // 与BTC正相关
        let ada_return = dec!(base_return * 0.6 + 0.003); // 与BTC中等相关
        
        calculator.add_asset_return(&btc_usdt, btc_return)?;
        calculator.add_asset_return(&eth_usdt, eth_return)?;
        calculator.add_asset_return(&ada_usdt, ada_return)?;
    }
    
    let metrics = calculator.calculate_correlation_risk()?;
    
    println!("✅ 投资组合相关性风险: {:.2}%", metrics.portfolio_correlation_risk * 100.0);
    println!("✅ 最大相关性: {:.3}", metrics.max_correlation);
    println!("✅ 平均相关性: {:.3}", metrics.average_correlation);
    println!("✅ 集中度风险: {:.2}%", metrics.concentration_risk * 100.0);
    println!("✅ 分散化效益: {:.2}%", metrics.diversification_benefit * 100.0);
    println!("✅ 资产数量: {}", calculator.get_asset_count());
    println!();
    
    Ok(())
}

/// 演示流动性风险模型
async fn demo_liquidity_model() -> Result<(), Box<dyn std::error::Error>> {
    println!("💧 3. 流动性风险模型演示");
    println!("---------------------------");
    
    let mut calculator = LiquidityRiskCalculator::new(50);
    
    let btc_usdt = TradingPair::new("BTC".to_string(), "USDT".to_string());
    calculator.add_asset(btc_usdt.clone());
    
    // 模拟市场深度
    let market_depth = MarketDepth {
        bids: vec![(dec!(50000), dec!(1.0)), (dec!(49990), dec!(2.0)), (dec!(49980), dec!(1.5))],
        asks: vec![(dec!(50010), dec!(1.5)), (dec!(50020), dec!(2.5)), (dec!(50030), dec!(1.0))],
        best_bid: dec!(50000),
        best_ask: dec!(50010),
        timestamp: Utc::now(),
    };
    
    calculator.update_market_data(
        &btc_usdt,
        dec!(2000000), // 200万USDT日成交量
        dec!(0.0002),  // 0.02%价差
        market_depth,
    )?;
    
    let metrics = calculator.calculate_liquidity_risk()?;
    
    println!("✅ 整体流动性风险: {:.2}%", metrics.overall_liquidity_risk * 100.0);
    println!("✅ 市场冲击成本: {:.4}%", metrics.market_impact_cost * 100.0);
    println!("✅ 买卖价差风险: {:.2}%", metrics.bid_ask_spread_risk * 100.0);
    println!("✅ 成交量充足性: {:.2}%", metrics.volume_adequacy_score * 100.0);
    println!("✅ 流动性缓冲建议: {:.1}%", metrics.liquidity_buffer_recommendation * 100.0);
    
    // 估算订单冲击
    use sigmax_core::OrderSide;
    let impact = calculator.estimate_order_impact(&btc_usdt, OrderSide::Buy, dec!(0.5))?;
    println!("✅ 0.5 BTC买单冲击: {:.4}%", impact * 100.0);
    println!();
    
    Ok(())
}

/// 演示信用风险模型
async fn demo_credit_model() -> Result<(), Box<dyn std::error::Error>> {
    println!("🏦 4. 信用风险模型演示");
    println!("------------------------");
    
    let mut calculator = CreditRiskCalculator::new();
    
    // 添加交易所信用信息
    let binance_info = ExchangeCreditInfo {
        exchange_id: ExchangeId::Binance,
        credit_rating: CreditRating::AA,
        default_history: vec![],
        regulatory_status: RegulatoryStatus::FullyCompliant,
        assets_under_management: dec!(100000000000), // 1000亿
        user_fund_protection: FundProtectionLevel::PartialProtection,
        last_updated: Utc::now(),
    };
    
    let okx_info = ExchangeCreditInfo {
        exchange_id: ExchangeId::OKX,
        credit_rating: CreditRating::A,
        default_history: vec![],
        regulatory_status: RegulatoryStatus::PartiallyCompliant,
        assets_under_management: dec!(50000000000), // 500亿
        user_fund_protection: FundProtectionLevel::BasicProtection,
        last_updated: Utc::now(),
    };
    
    calculator.add_exchange_credit_info(binance_info);
    calculator.add_exchange_credit_info(okx_info);
    
    // 设置资金分配
    calculator.update_fund_allocation(&ExchangeId::Binance, dec!(600000));
    calculator.update_fund_allocation(&ExchangeId::OKX, dec!(400000));
    
    let metrics = calculator.calculate_credit_risk()?;
    
    println!("✅ 整体信用风险: {:.2}%", metrics.overall_credit_risk * 100.0);
    println!("✅ 交易对手风险: {:.2}%", metrics.counterparty_risk * 100.0);
    println!("✅ 集中度风险: {:.2}%", metrics.concentration_risk * 100.0);
    println!("✅ 违约概率: {:.4}%", metrics.default_probability * 100.0);
    println!("✅ 信用暴露: ${:.2}", metrics.credit_exposure);
    
    for (exchange, score) in &metrics.exchange_credit_scores {
        println!("   - {}: {:.3}", exchange, score);
    }
    println!();
    
    Ok(())
}

/// 演示实时预警系统
async fn demo_alert_system() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚨 5. 实时预警系统演示");
    println!("-------------------------");
    
    let alert_system = RealTimeAlertSystem::new(100);
    
    // 创建VaR预警规则
    let var_rule = AlertRule {
        rule_id: Uuid::new_v4(),
        name: "VaR超限预警".to_string(),
        alert_type: AlertType::VarBreach,
        threshold_config: ThresholdConfig {
            warning_threshold: 50000.0,
            critical_threshold: 100000.0,
            emergency_threshold: 200000.0,
            threshold_type: ThresholdType::Absolute,
            comparison: ComparisonOperator::GreaterThan,
        },
        enabled: true,
        check_interval_seconds: 60,
        cooldown_seconds: 300,
        last_triggered: None,
        created_at: Utc::now(),
    };
    
    alert_system.add_alert_rule(var_rule).await?;
    
    // 模拟风险数据更新
    let monitoring_data = RiskMonitoringData {
        var_95: 150000.0, // 超过严重阈值
        cvar_95: 200000.0,
        current_drawdown: 0.08,
        max_drawdown: 0.15,
        daily_pnl: -25000.0,
        total_pnl: 50000.0,
        concentration_ratio: 0.4,
        liquidity_score: 0.75,
        correlation_risk: 0.6,
        credit_risk: 0.3,
        updated_at: Utc::now(),
    };
    
    alert_system.update_monitoring_data(monitoring_data).await?;
    
    // 等待预警处理
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    
    let active_alerts = alert_system.get_active_alerts().await;
    let stats = alert_system.get_alert_statistics().await;
    
    println!("✅ 活跃预警数量: {}", stats.total_active_alerts);
    println!("✅ 严重预警数量: {}", stats.critical_alerts);
    println!("✅ 未确认预警: {}", stats.unacknowledged_alerts);
    
    if !active_alerts.is_empty() {
        let alert = &active_alerts[0];
        println!("✅ 最新预警: {} - {}", alert.alert_type, alert.message);
        println!("   当前值: {:.2}, 阈值: {:.2}", alert.current_value, alert.threshold_value);
    }
    println!();
    
    Ok(())
}

/// 演示压力测试
async fn demo_stress_test() -> Result<(), Box<dyn std::error::Error>> {
    println!("💥 6. 压力测试演示");
    println!("-------------------");
    
    let mut engine = StressTestEngine::new();
    
    // 创建投资组合快照
    let mut positions = HashMap::new();
    positions.insert("BTC_USDT".to_string(), dec!(2.0));
    positions.insert("ETH_USDT".to_string(), dec!(20.0));
    
    let mut prices = HashMap::new();
    prices.insert("BTC_USDT".to_string(), dec!(50000));
    prices.insert("ETH_USDT".to_string(), dec!(3000));
    
    let portfolio = PortfolioSnapshot {
        positions,
        prices,
        total_value: dec!(160000), // 100k + 60k
        snapshot_time: Utc::now(),
    };
    
    engine.update_portfolio_snapshot(portfolio);
    
    // 运行所有历史情景测试
    let results = engine.run_all_historical_scenarios()?;
    
    println!("✅ 压力测试完成，共 {} 个情景", results.len());
    println!("✅ 最严重情景分析:");
    
    for (i, result) in results.iter().take(3).enumerate() {
        println!("   {}. {}", i + 1, result.scenario_name);
        println!("      损失: ${:.2} ({:.2}%)", result.portfolio_loss, result.loss_percentage * 100.0);
        println!("      最大回撤: {:.2}%", result.max_drawdown * 100.0);
        println!("      VaR突破概率: {:.1}%", result.var_breach_probability * 100.0);
        println!("      预计恢复时间: {} 天", result.recovery_time_days);
    }
    println!();
    
    Ok(())
}

/// 演示蒙特卡洛模拟
async fn demo_monte_carlo() -> Result<(), Box<dyn std::error::Error>> {
    println!("🎲 7. 蒙特卡洛模拟演示");
    println!("------------------------");
    
    let mut simulator = MonteCarloSimulator::new(Some(42));
    
    // 添加BTC参数
    let btc_params = AssetParameters {
        asset_name: "BTC_USDT".to_string(),
        initial_price: dec!(50000),
        expected_return: 0.20, // 20%年化收益
        volatility: 0.80,      // 80%年化波动率
        jump_intensity: 12.0,  // 每年12次跳跃
        jump_size_mean: -0.02, // 平均-2%跳跃
        jump_size_std: 0.05,   // 5%跳跃标准差
        mean_reversion_speed: 0.5,
        long_term_mean: 0.15,
    };
    
    simulator.add_asset_parameters(btc_params);
    
    let params = MonteCarloParams {
        num_simulations: 5000,
        time_steps: 30,
        time_horizon_days: 30,
        confidence_levels: vec![0.95, 0.99, 0.999],
        random_seed: Some(42),
        model_type: StochasticModel::GeometricBrownianMotion,
        include_jumps: false,
        include_correlation: false,
    };
    
    let result = simulator.run_simulation(params)?;
    
    println!("✅ 蒙特卡洛模拟完成 ({} ms)", result.computation_time_ms);
    println!("✅ 模拟统计:");
    println!("   平均收益: {:.2}%", result.statistics.mean_return * 100.0);
    println!("   收益标准差: {:.2}%", result.statistics.return_std * 100.0);
    println!("   偏度: {:.3}", result.statistics.skewness);
    println!("   峰度: {:.3}", result.statistics.kurtosis);
    println!("   正收益概率: {:.1}%", result.statistics.positive_return_probability * 100.0);
    
    println!("✅ 风险指标:");
    for (level, var) in &result.risk_metrics.var_estimates {
        println!("   VaR {}: {:.2}%", level, var * 100.0);
    }
    for (level, cvar) in &result.risk_metrics.cvar_estimates {
        println!("   CVaR {}: {:.2}%", level, cvar * 100.0);
    }
    println!("   破产概率: {:.2}%", result.risk_metrics.ruin_probability * 100.0);
    println!("   目标达成概率: {:.1}%", result.risk_metrics.target_achievement_probability * 100.0);
    println!();
    
    Ok(())
}

/// 演示敏感性分析
async fn demo_sensitivity_analysis() -> Result<(), Box<dyn std::error::Error>> {
    println!("📈 8. 敏感性分析演示");
    println!("----------------------");
    
    let mut analyzer = SensitivityAnalyzer::new();
    
    let base_metrics = BaseRiskMetrics {
        var_95: 0.05,
        cvar_95: 0.08,
        max_drawdown: 0.15,
        sharpe_ratio: 1.2,
        volatility: 0.20,
        portfolio_value: dec!(1000000),
    };
    
    analyzer.set_base_risk_metrics(base_metrics);
    
    let params = SensitivityParams {
        base_portfolio_value: dec!(1000000),
        perturbation_size: 0.01, // 1%扰动
        parameters_to_analyze: vec![
            "volatility".to_string(),
            "correlation".to_string(),
            "position_size".to_string(),
            "leverage".to_string(),
        ],
        risk_factors_to_analyze: vec![
            "market_risk".to_string(),
            "credit_risk".to_string(),
            "liquidity_risk".to_string(),
        ],
        include_second_order: true,
        include_cross_sensitivities: false,
    };
    
    let result = analyzer.run_sensitivity_analysis(params)?;
    
    println!("✅ 敏感性分析完成 ({} ms)", result.computation_time_ms);
    println!("✅ 参数敏感性:");
    
    for (param, sensitivity) in &result.parameter_sensitivities {
        println!("   {}: {:.4} ({:?})", 
                param, 
                sensitivity.first_order_sensitivity,
                sensitivity.sensitivity_level);
    }
    
    println!("✅ 希腊字母:");
    for (asset, delta) in &result.greeks.delta {
        println!("   {} Delta: {:.3}", asset, delta);
    }
    
    println!("✅ 情景敏感性:");
    for scenario in &result.scenario_sensitivities {
        println!("   {}: {:.2}%", 
                scenario.scenario_name, 
                scenario.change_percentage * 100.0);
    }
    
    // 生成报告
    let report = analyzer.generate_sensitivity_report(&result);
    println!("\n📋 敏感性分析报告:");
    println!("{}", report);
    
    Ok(())
}
