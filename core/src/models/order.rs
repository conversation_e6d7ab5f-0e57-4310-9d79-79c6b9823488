//! SigmaX Core Order Models
//!
//! ## 主要职责
//!
//! 1. **订单数据模型** - 核心交易概念
//!    - `Order` - 统一的订单模型，包含完整的订单生命周期
//!    - 支持多种订单类型和状态管理
//!
//! 2. **订单业务逻辑** - 订单相关的业务方法
//!    - 订单创建、更新、取消等操作
//!    - 成交信息更新和状态转换
//!    - 完整的验证和一致性检查
//!
//! 3. **订单计算功能** - 实用的计算方法
//!    - 剩余数量、完成进度计算
//!    - 订单价值和手续费计算
//!    - 订单状态判断和分类
//!
//! ## 设计原则
//!
//! - **统一模型**: 使用单一Order模型，避免Record/View分离
//! - **状态管理**: 完整的订单状态生命周期管理
//! - **类型安全**: 使用强类型的ID、Price、Quantity等类型
//! - **业务友好**: 提供实用的业务方法和计算功能

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use validator::{Validate, ValidationError};

use crate::{
    types::*,
    enums::*,
    error::*,
    SigmaXResult,
};

// ============================================================================
// 订单模型 - 核心交易概念
// ============================================================================

/// 订单模型 - 统一的订单数据结构
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Order {
    pub order_id: OrderId,
    pub strategy_id: Option<StrategyId>,
    pub exchange_id: ExchangeId,
    pub trading_pair: TradingPair,
    pub side: OrderSide,
    pub order_type: OrderType,
    pub status: OrderStatus,

    #[validate(custom = "validate_positive_quantity")]
    pub quantity: Quantity,
    pub price: Option<Price>,
    pub stop_price: Option<Price>,

    #[validate(custom = "validate_filled_quantity")]
    pub filled_quantity: Quantity,
    pub average_price: Option<Price>,
    pub total_fee: Amount,

    pub exchange_order_id: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl Order {
    /// 创建新的订单
    pub fn new(
        exchange_id: ExchangeId,
        trading_pair: TradingPair,
        side: OrderSide,
        order_type: OrderType,
        quantity: Quantity,
        price: Option<Price>,
    ) -> Self {
        Self {
            order_id: OrderId::new(),
            strategy_id: None,
            exchange_id,
            trading_pair,
            side,
            order_type,
            status: OrderStatus::Pending,
            quantity,
            price,
            stop_price: None,
            filled_quantity: Quantity::ZERO,
            average_price: None,
            total_fee: Amount::ZERO,
            exchange_order_id: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    /// 计算订单完成进度百分比
    pub fn progress_pct(&self) -> Percentage {
        if self.quantity.is_zero() {
            return Percentage::ZERO;
        }
        (self.filled_quantity / self.quantity) * Percentage::from(100)
    }

    /// 获取显示用的订单名称
    pub fn display_name(&self) -> String {
        format!("{} {} {}",
            self.side,
            self.quantity,
            self.trading_pair.base
        )
    }

    /// 检查订单是否已完成
    pub fn is_completed(&self) -> bool {
        matches!(self.status, OrderStatus::Filled | OrderStatus::Cancelled | OrderStatus::Rejected | OrderStatus::Expired)
    }

    /// 检查订单是否为活跃状态
    pub fn is_active(&self) -> bool {
        matches!(self.status, OrderStatus::Pending | OrderStatus::PartiallyFilled)
    }

    /// 获取剩余未成交数量
    pub fn remaining_quantity(&self) -> Quantity {
        self.quantity - self.filled_quantity
    }

    /// 计算订单总价值
    pub fn order_value(&self) -> Option<Amount> {
        self.price.map(|p| p * self.quantity)
    }

    /// 计算已成交价值
    pub fn filled_value(&self) -> Option<Amount> {
        self.average_price.map(|p| p * self.filled_quantity)
    }

    /// 更新订单状态


    /// 执行完整的订单验证
    pub fn validate_complete(&self) -> SigmaXResult<()> {
        // 首先执行基础验证（通过validator derive宏）
        if let Err(validation_errors) = <Self as Validate>::validate(self) {
            return Err(SigmaXError::from(validation_errors));
        }

        // 跨字段验证：限价单必须有价格
        if matches!(self.order_type, OrderType::Limit | OrderType::StopLimit) && self.price.is_none() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::Required,
                "price",
                "Limit orders must have a price"
            ));
        }

        // 跨字段验证：成交数量不能超过订单数量
        if self.filled_quantity > self.quantity {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "filled_quantity",
                "Filled quantity cannot exceed order quantity"
            ));
        }

        // 跨字段验证：止损单必须有止损价格
        if matches!(self.order_type, OrderType::StopLoss | OrderType::StopLimit) && self.stop_price.is_none() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::Required,
                "stop_price",
                "Stop orders must have a stop price"
            ));
        }

        Ok(())
    }
}

// ============================================================================
// 验证函数 - 用于模型验证
// ============================================================================

/// 验证交易对
fn validate_trading_pair(trading_pair: &TradingPair) -> Result<(), ValidationError> {
    if trading_pair.base.is_empty() || trading_pair.quote.is_empty() {
        return Err(ValidationError::new("Trading pair base and quote cannot be empty"));
    }
    Ok(())
}

/// 验证数量为正数
fn validate_positive_quantity(quantity: &Quantity) -> Result<(), ValidationError> {
    if *quantity <= Quantity::ZERO {
        return Err(ValidationError::new("Quantity must be positive"));
    }
    Ok(())
}

/// 验证成交数量为非负数
fn validate_filled_quantity(filled_quantity: &Quantity) -> Result<(), ValidationError> {
    if *filled_quantity < Quantity::ZERO {
        return Err(ValidationError::new("Filled quantity cannot be negative"));
    }
    Ok(())
}

// ============================================================================
// Tests
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal::Decimal;

    #[test]
    fn test_order_creation() {
        let order = Order::new(
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Buy,
            OrderType::Limit,
            Decimal::from(1),
            Some(Decimal::from(50000)),
        );

        assert_eq!(order.side, OrderSide::Buy);
        assert_eq!(order.order_type, OrderType::Limit);
        assert_eq!(order.quantity, Decimal::from(1));
        assert_eq!(order.price, Some(Decimal::from(50000)));
        assert_eq!(order.status, OrderStatus::Pending);
        assert_eq!(order.filled_quantity, Decimal::ZERO);
    }

    #[test]
    fn test_order_progress_calculation() {
        let mut order = Order::new(
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Buy,
            OrderType::Limit,
            Decimal::from(10),
            Some(Decimal::from(50000)),
        );

        // 初始进度应该为0
        assert_eq!(order.progress_pct(), Decimal::ZERO);

        // 部分成交
        order.update_fill(Decimal::from(3), Some(Decimal::from(50000)));
        assert_eq!(order.progress_pct(), Decimal::from(30)); // 30%
        assert_eq!(order.status, OrderStatus::PartiallyFilled);

        // 完全成交
        order.update_fill(Decimal::from(10), Some(Decimal::from(50000)));
        assert_eq!(order.progress_pct(), Decimal::from(100)); // 100%
        assert_eq!(order.status, OrderStatus::Filled);
    }

    #[test]
    fn test_order_status_management() {
        let mut order = Order::new(
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Buy,
            OrderType::Limit,
            Decimal::from(1),
            Some(Decimal::from(50000)),
        );

        // 初始状态
        assert!(order.is_active());
        assert!(!order.is_completed());

        // 取消订单
        order.cancel();
        assert_eq!(order.status, OrderStatus::Cancelled);
        assert!(!order.is_active());
        assert!(order.is_completed());

        // 重置为待处理状态
        order.update_status(OrderStatus::Pending);
        assert!(order.is_active());

        // 拒绝订单
        order.reject();
        assert_eq!(order.status, OrderStatus::Rejected);
        assert!(order.is_completed());
    }

    #[test]
    fn test_order_value_calculations() {
        let order = Order::new(
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Buy,
            OrderType::Limit,
            Decimal::from(2),
            Some(Decimal::from(50000)),
        );

        // 订单总价值
        assert_eq!(order.order_value(), Some(Decimal::from(100000))); // 2 * 50000

        // 剩余数量
        assert_eq!(order.remaining_quantity(), Decimal::from(2));

        // 已成交价值（初始为None，因为没有成交）
        assert_eq!(order.filled_value(), None);
    }

    #[test]
    fn test_order_display_name() {
        let order = Order::new(
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Buy,
            OrderType::Limit,
            Decimal::from(1),
            Some(Decimal::from(50000)),
        );

        assert_eq!(order.display_name(), "buy 1 BTC");
    }

    #[test]
    fn test_order_validation() {
        // 测试限价单必须有价格
        let mut order = Order::new(
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Buy,
            OrderType::Limit,
            Decimal::from(1),
            None, // 限价单没有价格
        );

        let result = order.validate_complete();
        assert!(result.is_err());

        // 修正价格后应该通过验证
        order.price = Some(Decimal::from(50000));
        let result = order.validate_complete();
        assert!(result.is_ok());

        // 测试成交数量不能超过订单数量
        order.filled_quantity = Decimal::from(2); // 超过订单数量1
        let result = order.validate_complete();
        assert!(result.is_err());
    }

    #[test]
    fn test_order_fill_updates() {
        let mut order = Order::new(
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Buy,
            OrderType::Limit,
            Decimal::from(10),
            Some(Decimal::from(50000)),
        );

        // 部分成交
        order.update_fill(Decimal::from(3), Some(Decimal::from(49500)));
        assert_eq!(order.filled_quantity, Decimal::from(3));
        assert_eq!(order.average_price, Some(Decimal::from(49500)));
        assert_eq!(order.status, OrderStatus::PartiallyFilled);
        assert_eq!(order.remaining_quantity(), Decimal::from(7));

        // 完全成交
        order.update_fill(Decimal::from(10), Some(Decimal::from(49800)));
        assert_eq!(order.status, OrderStatus::Filled);
        assert_eq!(order.remaining_quantity(), Decimal::ZERO);
        assert_eq!(order.filled_value(), Some(Decimal::from(498000))); // 10 * 49800
    }

    #[test]
    fn test_market_order() {
        let order = Order::new(
            ExchangeId::Binance,
            TradingPair::new("BTC", "USDT"),
            OrderSide::Sell,
            OrderType::Market,
            Decimal::from(1),
            None, // 市价单没有价格
        );

        // 市价单不需要价格，应该通过验证
        let result = order.validate_complete();
        assert!(result.is_ok());

        assert_eq!(order.order_value(), None); // 市价单没有预设价格
    }
}