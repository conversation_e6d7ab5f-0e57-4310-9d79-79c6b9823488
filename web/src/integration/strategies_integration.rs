//! 策略集成服务
//!
//! 负责与sigmax-strategies模块的集成，提供策略管理和执行

use std::sync::Arc;
use async_trait::async_trait;
use uuid::Uuid;
use tracing::info;

use crate::{
    integration::BaseIntegration,
    error::ApiError,
};

/// 策略集成服务
pub struct StrategiesIntegration {
    // 后续会注入策略管理器
    strategy_manager: Option<Arc<dyn StrategyManager>>,
}

impl StrategiesIntegration {
    /// 创建新的策略集成服务
    pub fn new() -> Self {
        Self {
            strategy_manager: None,
        }
    }
    
    /// 设置策略管理器
    pub fn with_strategy_manager(mut self, strategy_manager: Arc<dyn StrategyManager>) -> Self {
        self.strategy_manager = Some(strategy_manager);
        self
    }
}

#[async_trait]
impl BaseIntegration for StrategiesIntegration {
    fn name(&self) -> &str {
        "StrategiesIntegration"
    }
    
    async fn health_check(&self) -> bool {
        // 检查策略管理器是否可用
        self.strategy_manager.is_some()
    }
    
    fn version(&self) -> &str {
        "1.0.0"
    }
    
    fn external_module(&self) -> &str {
        "sigmax-strategies"
    }
}

impl StrategiesIntegration {
    /// 创建策略
    pub async fn create_strategy(
        &self,
        config: StrategyConfig,
    ) -> Result<Strategy, ApiError> {
        info!("🏗️ 策略集成服务创建策略: {}", config.name);
        
        if let Some(manager) = &self.strategy_manager {
            let strategy = manager.create_strategy(config).await
                .map_err(|e| ApiError::BusinessError(format!("策略创建失败: {}", e)))?;
            
            info!("✅ 策略集成服务完成策略创建: {}", strategy.name);
            Ok(strategy)
        } else {
            Err(ApiError::SystemError("策略管理器未配置".to_string()))
        }
    }
    
    /// 获取策略列表
    pub async fn get_strategies(&self) -> Result<Vec<Strategy>, ApiError> {
        info!("📋 策略集成服务获取策略列表");
        
        if let Some(manager) = &self.strategy_manager {
            let strategies = manager.get_strategies().await
                .map_err(|e| ApiError::BusinessError(format!("获取策略列表失败: {}", e)))?;
            
            Ok(strategies)
        } else {
            Err(ApiError::SystemError("策略管理器未配置".to_string()))
        }
    }
    
    /// 启动策略
    pub async fn start_strategy(&self, strategy_id: Uuid) -> Result<(), ApiError> {
        info!("▶️ 策略集成服务启动策略: {}", strategy_id);
        
        if let Some(manager) = &self.strategy_manager {
            manager.start_strategy(strategy_id).await
                .map_err(|e| ApiError::BusinessError(format!("策略启动失败: {}", e)))?;
            
            info!("✅ 策略集成服务完成策略启动: {}", strategy_id);
            Ok(())
        } else {
            Err(ApiError::SystemError("策略管理器未配置".to_string()))
        }
    }
    
    /// 停止策略
    pub async fn stop_strategy(&self, strategy_id: Uuid) -> Result<(), ApiError> {
        info!("⏹️ 策略集成服务停止策略: {}", strategy_id);
        
        if let Some(manager) = &self.strategy_manager {
            manager.stop_strategy(strategy_id).await
                .map_err(|e| ApiError::BusinessError(format!("策略停止失败: {}", e)))?;
            
            info!("✅ 策略集成服务完成策略停止: {}", strategy_id);
            Ok(())
        } else {
            Err(ApiError::SystemError("策略管理器未配置".to_string()))
        }
    }
}

// 临时类型定义（后续会从专门的模块导入）
#[derive(Debug)]
pub struct StrategyConfig {
    pub name: String,
    pub strategy_type: String,
    pub parameters: serde_json::Value,
}

#[derive(Debug)]
pub struct Strategy {
    pub id: Uuid,
    pub name: String,
    pub status: String,
}

#[async_trait::async_trait]
pub trait StrategyManager: Send + Sync {
    async fn create_strategy(&self, config: StrategyConfig) -> Result<Strategy, Box<dyn std::error::Error>>;
    async fn get_strategies(&self) -> Result<Vec<Strategy>, Box<dyn std::error::Error>>;
    async fn start_strategy(&self, strategy_id: Uuid) -> Result<(), Box<dyn std::error::Error>>;
    async fn stop_strategy(&self, strategy_id: Uuid) -> Result<(), Box<dyn std::error::Error>>;
}
