//! API Gateway模块
//!
//! 提供统一的请求处理入口，包括认证、限流、请求追踪等横切关注点

pub mod auth;
pub mod rate_limit;
pub mod tracing;
pub mod error_handler;

use std::sync::Arc;
use axum::{
    http::{Request, Response, StatusCode},
    body::Body,
    extract::State,
};
use sigmax_core::SigmaXResult;
use crate::state::AppState;

/// API Gateway - 统一入口管理
pub struct ApiGateway {
    auth_service: Arc<dyn AuthenticationService>,
    rate_limiter: Arc<dyn RateLimiter>,
    request_tracer: Arc<dyn RequestTracer>,
}

impl ApiGateway {
    /// 创建新的API Gateway
    pub fn new(
        auth_service: Arc<dyn AuthenticationService>,
        rate_limiter: Arc<dyn RateLimiter>,
        request_tracer: Arc<dyn RequestTracer>,
    ) -> Self {
        Self {
            auth_service,
            rate_limiter,
            request_tracer,
        }
    }

    /// 统一的请求处理入口
    pub async fn handle_request(
        &self,
        request: Request<Body>,
        state: State<Arc<AppState>>,
    ) -> SigmaXResult<Response<Body>> {
        let request_id = uuid::Uuid::new_v4();
        
        // 1. 请求追踪
        self.request_tracer.start_trace(request_id, &request).await;
        
        // 2. 认证授权
        let auth_result = self.auth_service.authenticate(&request).await;
        if let Err(e) = auth_result {
            self.request_tracer.end_trace(request_id, StatusCode::UNAUTHORIZED).await;
            return Ok(Response::builder()
                .status(StatusCode::UNAUTHORIZED)
                .body(Body::from(format!("认证失败: {}", e)))
                .unwrap());
        }
        
        // 3. 限流控制
        let rate_limit_result = self.rate_limiter.check_limit(&request).await;
        if let Err(e) = rate_limit_result {
            self.request_tracer.end_trace(request_id, StatusCode::TOO_MANY_REQUESTS).await;
            return Ok(Response::builder()
                .status(StatusCode::TOO_MANY_REQUESTS)
                .body(Body::from(format!("请求过于频繁: {}", e)))
                .unwrap());
        }
        
        // 4. 路由到具体Controller（这里由Axum框架处理）
        // 我们只需要记录成功的请求
        self.request_tracer.end_trace(request_id, StatusCode::OK).await;
        
        // 5. 返回成功响应（实际的路由处理由Axum完成）
        Ok(Response::builder()
            .status(StatusCode::OK)
            .body(Body::from("请求已通过Gateway验证"))
            .unwrap())
    }
}

/// 认证服务接口
#[async_trait::async_trait]
pub trait AuthenticationService: Send + Sync {
    /// 认证请求
    async fn authenticate(&self, request: &Request<Body>) -> SigmaXResult<()>;
}

/// 限流服务接口
#[async_trait::async_trait]
pub trait RateLimiter: Send + Sync {
    /// 检查限流
    async fn check_limit(&self, request: &Request<Body>) -> SigmaXResult<()>;
}

/// 请求追踪服务接口
#[async_trait::async_trait]
pub trait RequestTracer: Send + Sync {
    /// 开始追踪
    async fn start_trace(&self, request_id: uuid::Uuid, request: &Request<Body>);
    /// 结束追踪
    async fn end_trace(&self, request_id: uuid::Uuid, status: StatusCode);
}

// 重新导出子模块
pub use auth::*;
pub use rate_limit::*;
pub use tracing::*;
pub use error_handler::*;
