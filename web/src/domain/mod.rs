//! Domain Services层 - 专业领域逻辑
//!
//! 封装特定领域的专业逻辑，保证业务约束和不变量
//! 每个服务专注一个业务领域，可被不同应用服务复用

pub mod strategy_domain_service;
pub mod risk_domain_service;
pub mod portfolio_domain_service;
pub mod execution_domain_service;

// 重新导出领域服务
pub use strategy_domain_service::StrategyDomainService;
pub use risk_domain_service::RiskDomainService;
pub use portfolio_domain_service::PortfolioDomainService;
pub use execution_domain_service::ExecutionDomainService;

/// 领域服务基类
pub trait BaseDomainService {
    /// 获取服务名称
    fn name(&self) -> &str;
    
    /// 健康检查
    async fn health_check(&self) -> bool;
    
    /// 获取服务版本
    fn version(&self) -> &str;
    
    /// 获取领域类型
    fn domain_type(&self) -> &str;
}
