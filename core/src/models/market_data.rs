//! SigmaX Core Market Data Models
//!
//! ## 主要职责
//!
//! 1. **K线数据模型** - 基础市场数据概念
//!    - `Candle` - K线数据结构，支持OHLCV数据
//!    - 提供价格变化和技术分析方法
//!
//! 2. **市场深度模型** - 订单簿数据
//!    - `OrderBook` - 买卖盘深度数据
//!    - 提供最优价格和价差计算
//!
//! 3. **统一市场数据** - 通用市场数据结构
//!    - `MarketData` - 统一的市场数据接口
//!    - 支持多种数据源和格式
//!
//! ## 设计原则
//!
//! - **统一模型**: 使用单一模型，避免Record/View分离
//! - **类型安全**: 使用强类型的Price、Quantity等类型
//! - **业务友好**: 提供实用的计算和分析方法
//! - **数据完整**: 包含时间戳和数据源信息

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use validator::{Validate, ValidationError};

use crate::{
    types::*,
    SigmaXResult,
};

// ============================================================================
// K线数据模型 - 基础市场数据概念
// ============================================================================

/// K线数据 - OHLCV数据结构
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Candle {
    pub trading_pair: TradingPair,
    pub timestamp: DateTime<Utc>,

    #[validate(custom = "validate_positive_price")]
    pub open: Price,

    #[validate(custom = "validate_positive_price")]
    pub high: Price,

    #[validate(custom = "validate_positive_price")]
    pub low: Price,

    #[validate(custom = "validate_positive_price")]
    pub close: Price,

    #[validate(custom = "validate_non_negative_quantity")]
    pub volume: Quantity,
}

impl Candle {
    /// 创建新的K线数据
    pub fn new(
        trading_pair: TradingPair,
        timestamp: DateTime<Utc>,
        open: Price,
        high: Price,
        low: Price,
        close: Price,
        volume: Quantity,
    ) -> Self {
        Self {
            trading_pair,
            timestamp,
            open,
            high,
            low,
            close,
            volume,
        }
    }

    /// 计算价格变化
    pub fn price_change(&self) -> Price {
        self.close - self.open
    }

    /// 计算价格变化百分比
    pub fn price_change_pct(&self) -> Percentage {
        if self.open.is_zero() {
            return Percentage::ZERO;
        }
        (self.price_change() / self.open) * Percentage::from(100)
    }

    /// 计算价格振幅
    pub fn amplitude(&self) -> Price {
        self.high - self.low
    }

    /// 计算价格振幅百分比
    pub fn amplitude_pct(&self) -> Percentage {
        if self.open.is_zero() {
            return Percentage::ZERO;
        }
        (self.amplitude() / self.open) * Percentage::from(100)
    }

    /// 检查是否为上涨K线
    pub fn is_bullish(&self) -> bool {
        self.close > self.open
    }

    /// 检查是否为下跌K线
    pub fn is_bearish(&self) -> bool {
        self.close < self.open
    }

    /// 检查是否为十字星（开盘价等于收盘价）
    pub fn is_doji(&self) -> bool {
        self.close == self.open
    }

    /// 获取实体大小（开盘价和收盘价的差值）
    pub fn body_size(&self) -> Price {
        (self.close - self.open).abs()
    }

    /// 获取上影线长度
    pub fn upper_shadow(&self) -> Price {
        self.high - self.open.max(self.close)
    }

    /// 获取下影线长度
    pub fn lower_shadow(&self) -> Price {
        self.open.min(self.close) - self.low
    }

    /// 验证K线数据的有效性
    pub fn is_valid(&self) -> bool {
        self.high >= self.low
            && self.high >= self.open
            && self.high >= self.close
            && self.low <= self.open
            && self.low <= self.close
            && self.volume >= Quantity::ZERO
    }
}

// ============================================================================
// 市场深度模型 - 订单簿数据
// ============================================================================

/// 订单簿深度级别
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DepthLevel {
    pub price: Price,
    pub quantity: Quantity,
}

impl DepthLevel {
    pub fn new(price: Price, quantity: Quantity) -> Self {
        Self { price, quantity }
    }

    /// 计算该级别的总价值
    pub fn value(&self) -> Amount {
        self.price * self.quantity
    }
}

/// 市场深度数据 - 订单簿
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderBook {
    pub trading_pair: TradingPair,
    pub timestamp: DateTime<Utc>,
    pub bids: Vec<DepthLevel>, // 买盘深度，按价格从高到低排序
    pub asks: Vec<DepthLevel>, // 卖盘深度，按价格从低到高排序
}

impl OrderBook {
    /// 创建新的订单簿
    pub fn new(trading_pair: TradingPair, bids: Vec<DepthLevel>, asks: Vec<DepthLevel>) -> Self {
        Self {
            trading_pair,
            timestamp: chrono::Utc::now(),
            bids,
            asks,
        }
    }

    /// 获取最优买价
    pub fn best_bid(&self) -> Option<Price> {
        self.bids.first().map(|level| level.price)
    }

    /// 获取最优卖价
    pub fn best_ask(&self) -> Option<Price> {
        self.asks.first().map(|level| level.price)
    }

    /// 计算买卖价差
    pub fn spread(&self) -> Option<Price> {
        match (self.best_ask(), self.best_bid()) {
            (Some(ask), Some(bid)) => Some(ask - bid),
            _ => None,
        }
    }

    /// 计算中间价
    pub fn mid_price(&self) -> Option<Price> {
        match (self.best_ask(), self.best_bid()) {
            (Some(ask), Some(bid)) => Some((ask + bid) / Price::from(2)),
            _ => None,
        }
    }

    /// 计算买卖价差百分比
    pub fn spread_pct(&self) -> Option<Percentage> {
        match (self.spread(), self.mid_price()) {
            (Some(spread), Some(mid)) if !mid.is_zero() => {
                Some((spread / mid) * Percentage::from(100))
            }
            _ => None,
        }
    }

    /// 计算指定深度的买盘总量
    pub fn bid_volume(&self, depth: usize) -> Quantity {
        self.bids
            .iter()
            .take(depth)
            .map(|level| level.quantity)
            .sum()
    }

    /// 计算指定深度的卖盘总量
    pub fn ask_volume(&self, depth: usize) -> Quantity {
        self.asks
            .iter()
            .take(depth)
            .map(|level| level.quantity)
            .sum()
    }

    /// 计算指定深度的买盘总价值
    pub fn bid_value(&self, depth: usize) -> Amount {
        self.bids
            .iter()
            .take(depth)
            .map(|level| level.value())
            .sum()
    }

    /// 计算指定深度的卖盘总价值
    pub fn ask_value(&self, depth: usize) -> Amount {
        self.asks
            .iter()
            .take(depth)
            .map(|level| level.value())
            .sum()
    }

    /// 检查订单簿是否有效
    pub fn is_valid(&self) -> bool {
        // 检查买盘是否按价格从高到低排序
        let bids_sorted = self.bids.windows(2).all(|w| w[0].price >= w[1].price);

        // 检查卖盘是否按价格从低到高排序
        let asks_sorted = self.asks.windows(2).all(|w| w[0].price <= w[1].price);

        // 检查买卖价差是否合理（最高买价应该小于等于最低卖价）
        let spread_valid = match (self.best_bid(), self.best_ask()) {
            (Some(bid), Some(ask)) => bid <= ask,
            _ => true, // 如果没有买盘或卖盘，认为是有效的
        };

        bids_sorted && asks_sorted && spread_valid
    }
}

// ============================================================================
// 统一市场数据模型 - 通用市场数据结构
// ============================================================================

/// 统一市场数据 - 通用的市场数据接口
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketData {
    pub trading_pair: TradingPair,
    pub timestamp: DateTime<Utc>,
    pub price: Price,
    pub volume: Quantity,
    pub source: String,
}

impl MarketData {
    /// 创建新的市场数据
    pub fn new(
        trading_pair: TradingPair,
        price: Price,
        volume: Quantity,
        source: String,
    ) -> Self {
        Self {
            trading_pair,
            timestamp: chrono::Utc::now(),
            price,
            volume,
            source,
        }
    }

    /// 从K线数据创建市场数据
    pub fn from_candle(candle: &Candle, source: String) -> Self {
        Self {
            trading_pair: candle.trading_pair.clone(),
            timestamp: candle.timestamp,
            price: candle.close,
            volume: candle.volume,
            source,
        }
    }

    /// 从订单簿创建市场数据（使用中间价）
    pub fn from_orderbook(orderbook: &OrderBook, source: String) -> Option<Self> {
        orderbook.mid_price().map(|price| Self {
            trading_pair: orderbook.trading_pair.clone(),
            timestamp: orderbook.timestamp,
            price,
            volume: Quantity::ZERO, // 订单簿没有成交量信息
            source,
        })
    }

    /// 检查数据是否新鲜（在指定秒数内）
    pub fn is_fresh(&self, max_age_seconds: u64) -> bool {
        let now = chrono::Utc::now();
        let age = now.signed_duration_since(self.timestamp);
        age.num_seconds() <= max_age_seconds as i64
    }
}

// ============================================================================
// 验证函数 - 用于模型验证
// ============================================================================

/// 验证价格为正数
fn validate_positive_price(price: &Price) -> Result<(), ValidationError> {
    if *price <= Price::ZERO {
        return Err(ValidationError::new("Price must be positive"));
    }
    Ok(())
}

/// 验证数量为非负数
fn validate_non_negative_quantity(quantity: &Quantity) -> Result<(), ValidationError> {
    if *quantity < Quantity::ZERO {
        return Err(ValidationError::new("Quantity cannot be negative"));
    }
    Ok(())
}

// ============================================================================
// Tests
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal::Decimal;

    #[test]
    fn test_candle_creation() {
        let candle = Candle::new(
            TradingPair::new("BTC", "USDT"),
            chrono::Utc::now(),
            Decimal::from(50000),
            Decimal::from(51000),
            Decimal::from(49000),
            Decimal::from(50500),
            Decimal::from(100),
        );

        assert_eq!(candle.price_change(), Decimal::from(500));
        assert_eq!(candle.price_change_pct(), Decimal::from(1)); // 1%
        assert!(candle.is_bullish());
        assert!(!candle.is_bearish());
        assert!(candle.is_valid());
    }

    #[test]
    fn test_candle_technical_analysis() {
        let candle = Candle::new(
            TradingPair::new("BTC", "USDT"),
            chrono::Utc::now(),
            Decimal::from(50000),
            Decimal::from(52000),
            Decimal::from(48000),
            Decimal::from(49000),
            Decimal::from(100),
        );

        assert_eq!(candle.amplitude(), Decimal::from(4000));
        assert_eq!(candle.body_size(), Decimal::from(1000));
        assert_eq!(candle.upper_shadow(), Decimal::from(2000)); // 52000 - 50000
        assert_eq!(candle.lower_shadow(), Decimal::from(1000)); // 49000 - 48000
        assert!(candle.is_bearish());
    }

    #[test]
    fn test_orderbook_creation() {
        let bids = vec![
            DepthLevel::new(Decimal::from(50000), Decimal::from(1)),
            DepthLevel::new(Decimal::from(49900), Decimal::from(2)),
        ];
        let asks = vec![
            DepthLevel::new(Decimal::from(50100), Decimal::from(1)),
            DepthLevel::new(Decimal::from(50200), Decimal::from(2)),
        ];

        let orderbook = OrderBook::new(TradingPair::new("BTC", "USDT"), bids, asks);

        assert_eq!(orderbook.best_bid(), Some(Decimal::from(50000)));
        assert_eq!(orderbook.best_ask(), Some(Decimal::from(50100)));
        assert_eq!(orderbook.spread(), Some(Decimal::from(100)));
        assert_eq!(orderbook.mid_price(), Some(Decimal::from(50050)));
        assert!(orderbook.is_valid());
    }

    #[test]
    fn test_orderbook_volume_calculations() {
        let bids = vec![
            DepthLevel::new(Decimal::from(50000), Decimal::from(1)),
            DepthLevel::new(Decimal::from(49900), Decimal::from(2)),
            DepthLevel::new(Decimal::from(49800), Decimal::from(3)),
        ];
        let asks = vec![
            DepthLevel::new(Decimal::from(50100), Decimal::from(1)),
            DepthLevel::new(Decimal::from(50200), Decimal::from(2)),
        ];

        let orderbook = OrderBook::new(TradingPair::new("BTC", "USDT"), bids, asks);

        assert_eq!(orderbook.bid_volume(2), Decimal::from(3)); // 1 + 2
        assert_eq!(orderbook.ask_volume(2), Decimal::from(3)); // 1 + 2
        assert_eq!(orderbook.bid_value(1), Decimal::from(50000)); // 50000 * 1
    }

    #[test]
    fn test_market_data_creation() {
        let market_data = MarketData::new(
            TradingPair::new("BTC", "USDT"),
            Decimal::from(50000),
            Decimal::from(100),
            "binance".to_string(),
        );

        assert_eq!(market_data.price, Decimal::from(50000));
        assert_eq!(market_data.volume, Decimal::from(100));
        assert_eq!(market_data.source, "binance");
    }

    #[test]
    fn test_market_data_from_candle() {
        let candle = Candle::new(
            TradingPair::new("BTC", "USDT"),
            chrono::Utc::now(),
            Decimal::from(50000),
            Decimal::from(51000),
            Decimal::from(49000),
            Decimal::from(50500),
            Decimal::from(100),
        );

        let market_data = MarketData::from_candle(&candle, "test".to_string());
        assert_eq!(market_data.price, candle.close);
        assert_eq!(market_data.volume, candle.volume);
    }

    #[test]
    fn test_depth_level() {
        let level = DepthLevel::new(Decimal::from(50000), Decimal::from(2));
        assert_eq!(level.value(), Decimal::from(100000)); // 50000 * 2
    }
}