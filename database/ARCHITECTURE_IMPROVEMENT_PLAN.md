# Database模块架构改进计划

## 🎯 **改进目标**

基于当前架构分析，识别出以下关键问题并提出改进方案：

## 🔍 **当前架构问题**

### **1. 模块职责边界模糊**
- `managers/` 和 `transaction/` 都有事务管理代码，职责重复
- `cache.rs` 作为顶级模块，但功能相对简单
- `storage.rs` 抽象层次不清

### **2. 依赖关系设计不当**
- `DatabaseManager` 成为"上帝对象"，直接依赖所有子管理器
- 各管理器都直接依赖 `ConnectionManager`，缺乏抽象
- 存在潜在的循环依赖风险

### **3. 接口设计不一致**
- Repository接口过多且分散（14个trait文件）
- 功能重叠（如 `order_repository` 和 `enhanced_order_repository`）
- 缺乏统一的查询、分页、排序接口

### **4. 配置管理分散**
- 每个管理器都有独立的Config结构体
- 缺乏统一的配置验证和管理机制
- 配置间依赖关系不清晰

### **5. 错误处理不统一**
- 错误处理模式不一致
- 缺乏统一的错误上下文添加策略
- 错误恢复和重试机制分散

## 🏗️ **改进方案**

### **方案1: 分层架构重构**

```
database/src/
├── core/                           # 核心抽象层
│   ├── traits/                     # 统一的trait定义
│   │   ├── connection.rs           # 连接抽象
│   │   ├── transaction.rs          # 事务抽象
│   │   ├── repository.rs           # Repository基础抽象
│   │   └── mod.rs
│   ├── types/                      # 核心数据类型
│   │   ├── config.rs               # 统一配置类型
│   │   ├── errors.rs               # 数据库特定错误
│   │   └── mod.rs
│   └── mod.rs
├── infrastructure/                 # 基础设施层
│   ├── connection/                 # 连接管理
│   │   ├── manager.rs              # 连接管理器
│   │   ├── pool.rs                 # 连接池
│   │   └── mod.rs
│   ├── transaction/                # 事务管理
│   │   ├── manager.rs              # 事务管理器
│   │   ├── context.rs              # 事务上下文
│   │   └── mod.rs
│   ├── cache/                      # 缓存系统
│   │   ├── manager.rs              # 缓存管理器
│   │   ├── memory.rs               # 内存缓存
│   │   └── mod.rs
│   ├── migration/                  # 数据库迁移
│   │   ├── manager.rs              # 迁移管理器
│   │   ├── runner.rs               # 迁移执行器
│   │   └── mod.rs
│   ├── monitoring/                 # 性能监控
│   │   ├── manager.rs              # 监控管理器
│   │   ├── metrics.rs              # 指标收集
│   │   └── mod.rs
│   └── mod.rs
├── repositories/                   # 数据访问层
│   ├── core/                       # Repository核心
│   │   ├── base.rs                 # 基础Repository
│   │   ├── query.rs                # 查询构建器
│   │   ├── pagination.rs           # 分页支持
│   │   └── mod.rs
│   ├── domain/                     # 领域Repository
│   │   ├── order.rs                # 订单Repository
│   │   ├── trade.rs                # 交易Repository
│   │   ├── strategy.rs             # 策略Repository
│   │   └── mod.rs
│   ├── system/                     # 系统Repository
│   │   ├── config.rs               # 配置Repository
│   │   ├── monitoring.rs           # 监控Repository
│   │   └── mod.rs
│   ├── implementations/            # 具体实现
│   │   ├── sqlx/                   # SQLx实现
│   │   ├── mock/                   # Mock实现
│   │   └── mod.rs
│   └── mod.rs
├── container/                      # 依赖注入容器
│   ├── builder.rs                  # 容器构建器
│   ├── registry.rs                 # 服务注册表
│   ├── factory.rs                  # 工厂模式
│   └── mod.rs
└── lib.rs                          # 模块入口
```

### **方案2: 统一配置管理**

```rust
// database/src/core/types/config.rs
use serde::{Deserialize, Serialize};

/// 数据库模块统一配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseModuleConfig {
    /// 连接配置
    pub connection: ConnectionConfig,
    /// 事务配置
    pub transaction: TransactionConfig,
    /// 缓存配置
    pub cache: CacheConfig,
    /// 迁移配置
    pub migration: MigrationConfig,
    /// 监控配置
    pub monitoring: MonitoringConfig,
}

impl DatabaseModuleConfig {
    /// 验证配置的一致性
    pub fn validate(&self) -> SigmaXResult<()> {
        self.connection.validate()?;
        self.transaction.validate()?;
        self.cache.validate()?;
        self.migration.validate()?;
        self.monitoring.validate()?;
        
        // 验证配置间的依赖关系
        self.validate_dependencies()?;
        
        Ok(())
    }
    
    /// 验证配置间的依赖关系
    fn validate_dependencies(&self) -> SigmaXResult<()> {
        // 例如：事务超时不应该超过连接超时
        if self.transaction.timeout_seconds > self.connection.connection_timeout {
            return Err(SigmaXError::configuration(
                ConfigErrorCode::InvalidValue,
                "Transaction timeout cannot exceed connection timeout"
            ));
        }
        
        Ok(())
    }
}
```

### **方案3: 统一Repository接口**

```rust
// database/src/repositories/core/base.rs
use async_trait::async_trait;
use serde::{Serialize, Deserialize};

/// 统一的Repository基础接口
#[async_trait]
pub trait Repository<T, ID>: Send + Sync {
    /// 根据ID查找实体
    async fn find_by_id(&self, id: ID) -> SigmaXResult<Option<T>>;
    
    /// 查找所有实体
    async fn find_all(&self) -> SigmaXResult<Vec<T>>;
    
    /// 分页查询
    async fn find_paginated(&self, pagination: &Pagination) -> SigmaXResult<PaginatedResult<T>>;
    
    /// 根据条件查询
    async fn find_by_criteria(&self, criteria: &dyn QueryCriteria<T>) -> SigmaXResult<Vec<T>>;
    
    /// 保存实体
    async fn save(&self, entity: &T) -> SigmaXResult<T>;
    
    /// 批量保存
    async fn save_batch(&self, entities: &[T]) -> SigmaXResult<Vec<T>>;
    
    /// 更新实体
    async fn update(&self, entity: &T) -> SigmaXResult<T>;
    
    /// 删除实体
    async fn delete(&self, id: ID) -> SigmaXResult<()>;
    
    /// 统计数量
    async fn count(&self) -> SigmaXResult<u64>;
    
    /// 统计符合条件的数量
    async fn count_by_criteria(&self, criteria: &dyn QueryCriteria<T>) -> SigmaXResult<u64>;
}

/// 查询条件接口
pub trait QueryCriteria<T>: Send + Sync {
    /// 构建查询条件
    fn build_query(&self) -> QueryBuilder;
    
    /// 验证查询条件
    fn validate(&self) -> SigmaXResult<()>;
}

/// 统一的分页参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Pagination {
    pub offset: u64,
    pub limit: u64,
    pub sort: Option<SortConfig>,
}

/// 统一的分页结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginatedResult<T> {
    pub items: Vec<T>,
    pub total: u64,
    pub offset: u64,
    pub limit: u64,
    pub has_next: bool,
    pub has_previous: bool,
}
```

### **方案4: 统一错误处理策略**

```rust
// database/src/core/types/errors.rs
use sigmax_core::{SigmaXError, SigmaXResult};

/// 数据库错误处理扩展
pub trait DatabaseErrorExt {
    /// 添加数据库上下文
    fn with_database_context(self, operation: &str, table: Option<&str>) -> Self;
    
    /// 添加查询上下文
    fn with_query_context(self, query: &str, params: &[&str]) -> Self;
    
    /// 标记为可重试
    fn retryable_database_error(self) -> Self;
}

impl DatabaseErrorExt for SigmaXError {
    fn with_database_context(self, operation: &str, table: Option<&str>) -> Self {
        let mut error = self.with_context("database_operation", operation);
        if let Some(table) = table {
            error = error.with_context("table", table);
        }
        error
    }
    
    fn with_query_context(self, query: &str, params: &[&str]) -> Self {
        self.with_context("query", query)
            .with_context("params", &params.join(", "))
    }
    
    fn retryable_database_error(self) -> Self {
        self.retryable()
    }
}

/// 数据库结果扩展
pub trait DatabaseResultExt<T> {
    /// 添加数据库操作上下文
    fn with_db_context(self, operation: &str, table: Option<&str>) -> SigmaXResult<T>;
    
    /// 自动重试数据库操作
    fn retry_on_connection_error(self, max_retries: u32) -> SigmaXResult<T>;
}

impl<T> DatabaseResultExt<T> for SigmaXResult<T> {
    fn with_db_context(self, operation: &str, table: Option<&str>) -> SigmaXResult<T> {
        self.map_err(|e| e.with_database_context(operation, table))
    }
    
    fn retry_on_connection_error(self, max_retries: u32) -> SigmaXResult<T> {
        // 实现重试逻辑
        self
    }
}
```

### **方案5: 依赖注入重构**

```rust
// database/src/container/builder.rs
use std::sync::Arc;

/// 数据库容器构建器
pub struct DatabaseContainerBuilder {
    config: DatabaseModuleConfig,
    services: HashMap<TypeId, Box<dyn Any + Send + Sync>>,
}

impl DatabaseContainerBuilder {
    pub fn new(config: DatabaseModuleConfig) -> Self {
        Self {
            config,
            services: HashMap::new(),
        }
    }
    
    /// 构建基础设施层
    pub async fn build_infrastructure(mut self) -> SigmaXResult<Self> {
        // 1. 构建连接管理器
        let connection_manager = Arc::new(
            ConnectionManager::new(self.config.connection.clone()).await?
        );
        self.register_service(connection_manager.clone());
        
        // 2. 构建事务管理器
        let transaction_manager = Arc::new(
            TransactionManager::new(connection_manager.clone(), self.config.transaction.clone())
        );
        self.register_service(transaction_manager);
        
        // 3. 构建缓存管理器
        let cache_manager = Arc::new(
            CacheManager::new(self.config.cache.clone())
        );
        self.register_service(cache_manager);
        
        // 4. 构建监控管理器
        let monitoring_manager = Arc::new(
            MonitoringManager::new(connection_manager, self.config.monitoring.clone())
        );
        self.register_service(monitoring_manager);
        
        Ok(self)
    }
    
    /// 构建Repository层
    pub fn build_repositories(mut self) -> SigmaXResult<Self> {
        let connection_manager = self.get_service::<ConnectionManager>()?;
        
        // 构建Repository工厂
        let repository_factory = Arc::new(
            SqlxRepositoryFactory::new(connection_manager)
        );
        self.register_service(repository_factory);
        
        Ok(self)
    }
    
    /// 完成构建
    pub fn build(self) -> SigmaXResult<DatabaseContainer> {
        DatabaseContainer::new(self.services)
    }
    
    fn register_service<T: Send + Sync + 'static>(&mut self, service: Arc<T>) {
        self.services.insert(TypeId::of::<T>(), Box::new(service));
    }
    
    fn get_service<T: Send + Sync + 'static>(&self) -> SigmaXResult<Arc<T>> {
        self.services
            .get(&TypeId::of::<T>())
            .and_then(|service| service.downcast_ref::<Arc<T>>())
            .cloned()
            .ok_or_else(|| SigmaXError::internal(
                InternalErrorCode::ServiceNotFound,
                format!("Service {} not found", std::any::type_name::<T>())
            ))
    }
}
```

## 📋 **实施计划**

### **阶段1: 核心抽象层重构 (1-2周)**
1. 创建 `core/` 模块结构
2. 定义统一的trait接口
3. 实现统一的配置管理
4. 建立统一的错误处理策略

### **阶段2: 基础设施层重构 (2-3周)**
1. 重构连接管理器
2. 统一事务管理
3. 重构缓存系统
4. 重构性能监控

### **阶段3: Repository层重构 (2-3周)**
1. 实现统一的Repository基础接口
2. 重构现有Repository实现
3. 优化查询构建器
4. 完善分页和排序支持

### **阶段4: 依赖注入重构 (1-2周)**
1. 实现新的容器构建器
2. 重构服务注册和解析
3. 优化工厂模式实现

### **阶段5: 测试和文档 (1周)**
1. 更新所有测试用例
2. 完善文档和示例
3. 性能基准测试

## 🎯 **预期收益**

### **架构质量提升**
- **职责清晰**: 每个模块职责单一明确
- **依赖合理**: 清晰的分层架构，避免循环依赖
- **接口统一**: 一致的API设计和使用体验

### **开发效率提升**
- **配置统一**: 集中的配置管理，减少配置错误
- **错误处理**: 统一的错误处理策略，提高调试效率
- **代码复用**: 更好的抽象和复用机制

### **维护性提升**
- **模块化**: 更好的模块边界，便于独立开发和测试
- **扩展性**: 清晰的扩展点，便于添加新功能
- **测试性**: 更好的依赖注入，便于单元测试

## ⚠️ **风险评估**

### **技术风险**
- **重构规模大**: 需要仔细规划，分阶段实施
- **向后兼容**: 需要保持API的向后兼容性
- **性能影响**: 需要确保重构不影响性能

### **时间风险**
- **开发周期长**: 预计需要8-10周完成
- **测试工作量大**: 需要全面的回归测试
- **文档更新**: 需要同步更新相关文档

### **缓解措施**
- **分阶段实施**: 每个阶段都有明确的交付物
- **保持兼容**: 通过适配器模式保持向后兼容
- **充分测试**: 每个阶段都进行充分的测试验证
