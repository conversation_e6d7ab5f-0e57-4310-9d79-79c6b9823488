//! 工作单元模式实现
//!
//! 提供跨Repository的事务操作支持

use async_trait::async_trait;
use sigmax_core::{SigmaXResult, SigmaXError, DatabaseErrorCode};
use super::transaction_context::{TransactionContext, TransactionIsolationLevel, TransactionScope};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};

/// 工作单元接口
/// 
/// 管理一组相关的数据库操作，确保它们在同一个事务中执行
#[async_trait]
pub trait UnitOfWork: Send + Sync {
    /// 开始工作单元
    async fn begin(&mut self) -> SigmaXResult<()>;
    
    /// 提交所有更改
    async fn commit(&mut self) -> SigmaXResult<()>;
    
    /// 回滚所有更改
    async fn rollback(&mut self) -> SigmaXResult<()>;
    
    /// 添加工作项
    async fn add_work(&mut self, work: Box<dyn WorkUnit>) -> SigmaXResult<()>;
    
    /// 获取事务上下文
    fn get_transaction_context(&self) -> Option<&TransactionContext>;
    
    /// 检查是否在事务中
    fn is_in_transaction(&self) -> bool;
    
    /// 获取工作单元ID
    fn get_id(&self) -> Uuid;
}

/// 工作项接口
/// 
/// 表示一个需要在事务中执行的操作
#[async_trait]
pub trait WorkUnit: Send + Sync {
    /// 执行工作项
    async fn execute(&self, context: &TransactionContext) -> SigmaXResult<()>;
    
    /// 获取工作项描述
    fn description(&self) -> String;
    
    /// 获取工作项类型
    fn work_type(&self) -> WorkUnitType;
    
    /// 获取依赖的工作项ID
    fn dependencies(&self) -> Vec<Uuid>;
    
    /// 获取工作项ID
    fn get_id(&self) -> Uuid;
}

/// 工作项类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum WorkUnitType {
    /// 插入操作
    Insert,
    /// 更新操作
    Update,
    /// 删除操作
    Delete,
    /// 自定义操作
    Custom,
}

/// 默认工作单元实现
pub struct DefaultUnitOfWork {
    /// 工作单元ID
    id: Uuid,
    /// 事务上下文
    transaction_context: Option<TransactionContext>,
    /// 工作项列表
    work_units: Vec<Box<dyn WorkUnit>>,
    /// 工作单元状态
    state: UnitOfWorkState,
    /// 创建时间
    created_at: DateTime<Utc>,
    /// 配置
    config: UnitOfWorkConfig,
}

/// 工作单元状态
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
enum UnitOfWorkState {
    /// 初始状态
    Initial,
    /// 已开始
    Started,
    /// 已提交
    Committed,
    /// 已回滚
    RolledBack,
    /// 出错
    Error,
}

impl DefaultUnitOfWork {
    /// 创建新的工作单元
    pub fn new(config: UnitOfWorkConfig) -> Self {
        Self {
            id: Uuid::new_v4(),
            transaction_context: None,
            work_units: Vec::new(),
            state: UnitOfWorkState::Initial,
            created_at: Utc::now(),
            config,
        }
    }
    
    /// 验证工作项依赖关系
    fn validate_dependencies(&self) -> SigmaXResult<()> {
        let work_unit_ids: std::collections::HashSet<Uuid> = self.work_units
            .iter()
            .map(|w| w.get_id())
            .collect();
        
        for work_unit in &self.work_units {
            for dep_id in work_unit.dependencies() {
                if !work_unit_ids.contains(&dep_id) {
                    return Err(SigmaXError::database(
                        DatabaseErrorCode::TransactionFailed,
                        format!("工作项 {} 依赖的工作项 {} 不存在", work_unit.get_id(), dep_id)
                    ));
                }
            }
        }
        
        Ok(())
    }
    
    /// 按依赖关系排序工作项
    fn sort_work_units_by_dependencies(&mut self) -> SigmaXResult<()> {
        // 简单的拓扑排序实现
        let mut sorted_units = Vec::new();
        let mut remaining_units = self.work_units.drain(..).collect::<Vec<_>>();
        let mut processed_ids = std::collections::HashSet::new();
        
        while !remaining_units.is_empty() {
            let mut found_ready = false;
            
            for i in (0..remaining_units.len()).rev() {
                let dependencies = remaining_units[i].dependencies();
                let all_deps_satisfied = dependencies.iter()
                    .all(|dep_id| processed_ids.contains(dep_id));
                
                if all_deps_satisfied {
                    let work_unit = remaining_units.remove(i);
                    processed_ids.insert(work_unit.get_id());
                    sorted_units.push(work_unit);
                    found_ready = true;
                    break;
                }
            }
            
            if !found_ready {
                return Err(SigmaXError::database(
                    DatabaseErrorCode::TransactionFailed,
                    "检测到工作项循环依赖".to_string()
                ));
            }
        }
        
        self.work_units = sorted_units;
        Ok(())
    }
}

#[async_trait]
impl UnitOfWork for DefaultUnitOfWork {
    async fn begin(&mut self) -> SigmaXResult<()> {
        if self.state != UnitOfWorkState::Initial {
            return Err(SigmaXError::database(
                DatabaseErrorCode::TransactionFailed,
                "工作单元已经开始".to_string()
            ));
        }
        
        // 验证依赖关系
        self.validate_dependencies()?;
        
        // 按依赖关系排序
        self.sort_work_units_by_dependencies()?;
        
        // 创建事务上下文
        self.transaction_context = Some(TransactionContext::new(
            self.config.isolation_level,
            self.config.scope,
            self.config.read_only,
            self.config.timeout_seconds,
        ));
        
        self.state = UnitOfWorkState::Started;
        Ok(())
    }
    
    async fn commit(&mut self) -> SigmaXResult<()> {
        if self.state != UnitOfWorkState::Started {
            return Err(SigmaXError::database(
                DatabaseErrorCode::TransactionFailed,
                "工作单元未开始或已完成".to_string()
            ));
        }
        
        let context = self.transaction_context.as_ref()
            .ok_or_else(|| SigmaXError::database(
                DatabaseErrorCode::TransactionFailed,
                "事务上下文不存在".to_string()
            ))?;
        
        // 执行所有工作项
        for work_unit in &self.work_units {
            if let Err(e) = work_unit.execute(context).await {
                self.state = UnitOfWorkState::Error;
                return Err(e);
            }
        }
        
        self.state = UnitOfWorkState::Committed;
        Ok(())
    }
    
    async fn rollback(&mut self) -> SigmaXResult<()> {
        if self.state == UnitOfWorkState::Initial {
            return Ok(()); // 没有开始，无需回滚
        }
        
        self.state = UnitOfWorkState::RolledBack;
        Ok(())
    }
    
    async fn add_work(&mut self, work: Box<dyn WorkUnit>) -> SigmaXResult<()> {
        if self.state != UnitOfWorkState::Initial {
            return Err(SigmaXError::database(
                DatabaseErrorCode::TransactionFailed,
                "工作单元已开始，无法添加新的工作项".to_string()
            ));
        }
        
        self.work_units.push(work);
        Ok(())
    }
    
    fn get_transaction_context(&self) -> Option<&TransactionContext> {
        self.transaction_context.as_ref()
    }
    
    fn is_in_transaction(&self) -> bool {
        self.transaction_context.is_some() && 
        matches!(self.state, UnitOfWorkState::Started)
    }
    
    fn get_id(&self) -> Uuid {
        self.id
    }
}

/// 工作单元配置
#[derive(Debug, Clone)]
pub struct UnitOfWorkConfig {
    /// 事务隔离级别
    pub isolation_level: TransactionIsolationLevel,
    /// 事务作用域
    pub scope: TransactionScope,
    /// 是否只读
    pub read_only: bool,
    /// 超时时间（秒）
    pub timeout_seconds: Option<u64>,
    /// 最大工作项数量
    pub max_work_units: Option<usize>,
    /// 是否启用依赖检查
    pub enable_dependency_check: bool,
}

impl Default for UnitOfWorkConfig {
    fn default() -> Self {
        Self {
            isolation_level: TransactionIsolationLevel::ReadCommitted,
            scope: TransactionScope::Required,
            read_only: false,
            timeout_seconds: Some(30),
            max_work_units: Some(1000),
            enable_dependency_check: true,
        }
    }
}

/// 工作单元构建器
pub struct UnitOfWorkBuilder {
    config: UnitOfWorkConfig,
}

impl UnitOfWorkBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            config: UnitOfWorkConfig::default(),
        }
    }

    /// 设置隔离级别
    pub fn isolation_level(mut self, level: TransactionIsolationLevel) -> Self {
        self.config.isolation_level = level;
        self
    }

    /// 设置作用域
    pub fn scope(mut self, scope: TransactionScope) -> Self {
        self.config.scope = scope;
        self
    }

    /// 设置只读模式
    pub fn read_only(mut self, read_only: bool) -> Self {
        self.config.read_only = read_only;
        self
    }

    /// 设置超时时间
    pub fn timeout_seconds(mut self, timeout: u64) -> Self {
        self.config.timeout_seconds = Some(timeout);
        self
    }

    /// 设置最大工作项数量
    pub fn max_work_units(mut self, max: usize) -> Self {
        self.config.max_work_units = Some(max);
        self
    }

    /// 启用或禁用依赖检查
    pub fn enable_dependency_check(mut self, enable: bool) -> Self {
        self.config.enable_dependency_check = enable;
        self
    }

    /// 构建工作单元
    pub fn build(self) -> DefaultUnitOfWork {
        DefaultUnitOfWork::new(self.config)
    }
}

impl Default for UnitOfWorkBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 简单工作项实现
pub struct SimpleWorkUnit {
    id: Uuid,
    description: String,
    work_type: WorkUnitType,
    dependencies: Vec<Uuid>,
    operation: Box<dyn Fn(&TransactionContext) -> std::pin::Pin<Box<dyn std::future::Future<Output = SigmaXResult<()>> + Send>> + Send + Sync>,
}

impl SimpleWorkUnit {
    /// 创建新的简单工作项
    pub fn new<F, Fut>(
        description: String,
        work_type: WorkUnitType,
        dependencies: Vec<Uuid>,
        operation: F,
    ) -> Self
    where
        F: Fn(&TransactionContext) -> Fut + Send + Sync + 'static,
        Fut: std::future::Future<Output = SigmaXResult<()>> + Send + 'static,
    {
        Self {
            id: Uuid::new_v4(),
            description,
            work_type,
            dependencies,
            operation: Box::new(move |ctx| Box::pin(operation(ctx))),
        }
    }
}

#[async_trait]
impl WorkUnit for SimpleWorkUnit {
    async fn execute(&self, context: &TransactionContext) -> SigmaXResult<()> {
        (self.operation)(context).await
    }

    fn description(&self) -> String {
        self.description.clone()
    }

    fn work_type(&self) -> WorkUnitType {
        self.work_type
    }

    fn dependencies(&self) -> Vec<Uuid> {
        self.dependencies.clone()
    }

    fn get_id(&self) -> Uuid {
        self.id
    }
}
