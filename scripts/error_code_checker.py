#!/usr/bin/env python3
"""
SigmaX 错误代码使用检查工具

检查项目中错误代码的使用情况，确保符合标准化要求
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Set, Tuple
from dataclasses import dataclass, asdict


@dataclass
class ErrorUsage:
    file_path: str
    line_number: int
    pattern: str
    error_type: str
    is_standardized: bool
    suggestion: str = ""


class ErrorCodeChecker:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.rust_files = []
        self.error_usages = []
        
        # 标准化的错误构造器模式
        self.standardized_patterns = [
            r'SigmaXError::trading\(',
            r'SigmaXError::strategy\(',
            r'SigmaXError::risk\(',
            r'SigmaXError::database\(',
            r'SigmaXError::network\(',
            r'SigmaXError::configuration\(',
            r'SigmaXError::validation\(',
            r'SigmaXError::exchange_api\(',
            r'SigmaXError::market_data\(',
            r'SigmaXError::internal\(',
            r'sigmax_core::SigmaXError::trading\(',
            r'sigmax_core::SigmaXError::strategy\(',
            r'sigmax_core::SigmaXError::risk\(',
            r'sigmax_core::SigmaXError::database\(',
            r'sigmax_core::SigmaXError::network\(',
            r'sigmax_core::SigmaXError::configuration\(',
            r'sigmax_core::SigmaXError::validation\(',
            r'sigmax_core::SigmaXError::exchange_api\(',
            r'sigmax_core::SigmaXError::market_data\(',
            r'sigmax_core::SigmaXError::internal\(',
        ]
        
        # 需要标准化的旧模式
        self.legacy_patterns = [
            (r'Err\("([^"]+)"\)', "字符串错误", "使用结构化错误代码"),
            (r'anyhow::anyhow!\("([^"]+)"\)', "anyhow错误", "使用SigmaXError构造器"),
            (r'SigmaXError::Internal\("([^"]+)"\)', "旧内部错误", "使用SigmaXError::internal()"),
            (r'SigmaXError::ValidationError\("([^"]+)"\)', "旧验证错误", "使用SigmaXError::validation()"),
            (r'SigmaXError::NotImplemented\("([^"]+)"\)', "未实现错误", "使用SigmaXError::internal()"),
            (r'SigmaXError::Config\("([^"]+)"\)', "旧配置错误", "使用SigmaXError::configuration()"),
            (r'SigmaXError::Exchange\("([^"]+)"\)', "旧交易所错误", "使用SigmaXError::exchange_api()"),
        ]

    def scan_rust_files(self):
        """扫描所有Rust文件"""
        exclude_dirs = {'target', 'node_modules', '.git'}
        
        for root, dirs, files in os.walk(self.project_root):
            # 过滤掉排除的目录
            dirs[:] = [d for d in dirs if d not in exclude_dirs]
            
            for file in files:
                if file.endswith('.rs'):
                    file_path = Path(root) / file
                    self.rust_files.append(file_path)

    def check_file(self, file_path: Path):
        """检查单个文件的错误代码使用"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
        except (UnicodeDecodeError, IOError):
            return

        # 检查标准化的模式
        for i, line in enumerate(lines, 1):
            # 检查是否使用了标准化的构造器
            for pattern in self.standardized_patterns:
                if re.search(pattern, line):
                    self.error_usages.append(ErrorUsage(
                        file_path=str(file_path.relative_to(self.project_root)),
                        line_number=i,
                        pattern=line.strip(),
                        error_type="标准化",
                        is_standardized=True
                    ))

            # 检查需要标准化的旧模式
            for pattern, error_type, suggestion in self.legacy_patterns:
                if re.search(pattern, line):
                    self.error_usages.append(ErrorUsage(
                        file_path=str(file_path.relative_to(self.project_root)),
                        line_number=i,
                        pattern=line.strip(),
                        error_type=error_type,
                        is_standardized=False,
                        suggestion=suggestion
                    ))

    def analyze_all_files(self):
        """分析所有文件"""
        print("🔍 扫描Rust文件...")
        self.scan_rust_files()
        print(f"找到 {len(self.rust_files)} 个Rust文件")

        print("📋 检查错误代码使用...")
        for file_path in self.rust_files:
            self.check_file(file_path)

    def generate_report(self) -> Dict:
        """生成检查报告"""
        standardized_count = sum(1 for usage in self.error_usages if usage.is_standardized)
        legacy_count = sum(1 for usage in self.error_usages if not usage.is_standardized)
        
        # 按文件分组
        files_report = {}
        for usage in self.error_usages:
            if usage.file_path not in files_report:
                files_report[usage.file_path] = {
                    'standardized': [],
                    'legacy': []
                }
            
            if usage.is_standardized:
                files_report[usage.file_path]['standardized'].append(usage)
            else:
                files_report[usage.file_path]['legacy'].append(usage)

        # 统计各种错误类型
        error_types = {}
        for usage in self.error_usages:
            if usage.error_type not in error_types:
                error_types[usage.error_type] = 0
            error_types[usage.error_type] += 1

        return {
            'summary': {
                'total_files_scanned': len(self.rust_files),
                'total_error_usages': len(self.error_usages),
                'standardized_count': standardized_count,
                'legacy_count': legacy_count,
                'standardization_rate': f"{(standardized_count / len(self.error_usages) * 100):.1f}%" if self.error_usages else "0%"
            },
            'error_types': error_types,
            'files': files_report,
            'all_usages': [asdict(usage) for usage in self.error_usages]
        }

    def print_report(self, report: Dict):
        """打印报告"""
        print("\n" + "="*70)
        print("🎯 SigmaX 错误代码标准化检查报告")
        print("="*70)

        summary = report['summary']
        print(f"📊 总体统计:")
        print(f"   - 扫描文件: {summary['total_files_scanned']} 个")
        print(f"   - 错误使用总数: {summary['total_error_usages']} 个")
        print(f"   - 标准化: {summary['standardized_count']} 个")
        print(f"   - 待标准化: {summary['legacy_count']} 个")
        print(f"   - 标准化率: {summary['standardization_rate']}")

        if report['error_types']:
            print(f"\n📋 错误类型分布:")
            for error_type, count in sorted(report['error_types'].items()):
                status = "✅" if error_type == "标准化" else "⚠️"
                print(f"   {status} {error_type}: {count} 个")

        # 显示需要改进的文件
        legacy_files = {k: v for k, v in report['files'].items() if v['legacy']}
        if legacy_files:
            print(f"\n🔧 需要标准化的文件 ({len(legacy_files)} 个):")
            for file_path, usages in legacy_files.items():
                print(f"\n📁 {file_path}")
                for usage in usages['legacy']:
                    print(f"   ⚠️  行 {usage.line_number}: {usage.error_type}")
                    print(f"      代码: {usage.pattern}")
                    print(f"      建议: {usage.suggestion}")

        # 显示表现良好的文件
        good_files = {k: v for k, v in report['files'].items() 
                     if v['standardized'] and not v['legacy']}
        if good_files:
            print(f"\n✅ 已标准化的文件 ({len(good_files)} 个):")
            for file_path in list(good_files.keys())[:5]:  # 只显示前5个
                count = len(good_files[file_path]['standardized'])
                print(f"   📁 {file_path} ({count} 处)")
            if len(good_files) > 5:
                print(f"   ... 还有 {len(good_files) - 5} 个文件")

        print("\n" + "="*70)

    def save_report(self, report: Dict, output_file: str):
        """保存报告到JSON文件"""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        print(f"📄 详细报告已保存到: {output_file}")


def main():
    import sys
    
    project_root = sys.argv[1] if len(sys.argv) > 1 else "."
    output_file = sys.argv[2] if len(sys.argv) > 2 else "error_code_report.json"
    
    checker = ErrorCodeChecker(project_root)
    checker.analyze_all_files()
    
    report = checker.generate_report()
    checker.print_report(report)
    checker.save_report(report, output_file)
    
    # 返回退出码
    legacy_count = report['summary']['legacy_count']
    if legacy_count > 0:
        print(f"\n⚠️  发现 {legacy_count} 处需要标准化的错误代码使用")
        return 1
    else:
        print(f"\n🎉 所有错误代码使用都已标准化！")
        return 0


if __name__ == "__main__":
    exit(main())
