//! SigmaX Core Strategy Models
//!
//! ## 主要职责
//!
//! 1. **策略实例模型** - 核心策略概念
//!    - `Strategy` - 统一的策略实例模型，管理策略生命周期
//!    - 策略状态管理和配置存储
//!
//! 2. **策略性能模型** - 性能分析和统计
//!    - `StrategyPerformance` - 策略性能指标和统计数据
//!    - 收益率、回撤、夏普比率等关键指标
//!
//! 3. **策略业务逻辑** - 实用的业务方法
//!    - 策略启动、停止、暂停等操作
//!    - 性能计算和风险评估
//!    - 配置验证和状态管理
//!
//! ## 设计原则
//!
//! - **统一模型**: 使用单一Strategy模型，避免State/Info/Config分离
//! - **状态管理**: 完整的策略状态生命周期管理
//! - **类型安全**: 使用强类型的ID、Amount等类型
//! - **业务友好**: 提供实用的策略管理和分析方法

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use validator::{Validate, ValidationError};
use rust_decimal::Decimal;
use rust_decimal::prelude::ToPrimitive;

use crate::{
    types::*,
    enums::*,
    error::*,
    SigmaXResult,
};

// ============================================================================
// 策略模型 - 核心策略概念
// ============================================================================

/// 策略实例 - 统一的策略模型
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Strategy {
    pub strategy_id: StrategyId,

    #[validate(length(min = 1, message = "Strategy name cannot be empty"))]
    pub name: String,

    #[validate(length(min = 1, message = "Strategy type cannot be empty"))]
    pub strategy_type: String,

    pub description: Option<String>,
    pub status: StrategyStatus,
    pub trading_pair: TradingPair,
    pub exchange_id: ExchangeId,

    #[validate(custom = "validate_positive_amount")]
    pub initial_capital: Amount,
    pub current_capital: Amount,

    pub parameters: serde_json::Value,
    pub state_data: serde_json::Value,

    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub started_at: Option<DateTime<Utc>>,
    pub stopped_at: Option<DateTime<Utc>>,
}

impl Strategy {
    /// 创建新的策略实例
    pub fn new(
        name: String,
        strategy_type: String,
        trading_pair: TradingPair,
        exchange_id: ExchangeId,
        initial_capital: Amount,
    ) -> Self {
        Self {
            strategy_id: StrategyId::new(),
            name,
            strategy_type,
            description: None,
            status: StrategyStatus::Stopped,
            trading_pair,
            exchange_id,
            initial_capital,
            current_capital: initial_capital,
            parameters: serde_json::json!({}),
            state_data: serde_json::json!({}),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            started_at: None,
            stopped_at: None,
        }
    }

    /// 启动策略
    pub fn start(&mut self) -> SigmaXResult<()> {
        if self.status == StrategyStatus::Active {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "status",
                "Strategy is already running",
            ));
        }

        self.status = StrategyStatus::Active;
        self.started_at = Some(Utc::now());
        self.stopped_at = None;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 停止策略
    pub fn stop(&mut self) -> SigmaXResult<()> {
        if self.status == StrategyStatus::Stopped {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "status",
                "Strategy is already stopped",
            ));
        }

        self.status = StrategyStatus::Stopped;
        self.stopped_at = Some(Utc::now());
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 暂停策略
    pub fn pause(&mut self) -> SigmaXResult<()> {
        if self.status != StrategyStatus::Active {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "status",
                "Can only pause running strategy",
            ));
        }

        self.status = StrategyStatus::Paused;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 恢复策略
    pub fn resume(&mut self) -> SigmaXResult<()> {
        if self.status != StrategyStatus::Paused {
            return Err(SigmaXError::validation(
                ValidationErrorCode::InvalidFormat,
                "status",
                "Can only resume paused strategy",
            ));
        }

        self.status = StrategyStatus::Active;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 检查策略是否正在运行
    pub fn is_running(&self) -> bool {
        self.status == StrategyStatus::Active
    }

    /// 检查策略是否已停止
    pub fn is_stopped(&self) -> bool {
        self.status == StrategyStatus::Stopped
    }

    /// 检查策略是否已暂停
    pub fn is_paused(&self) -> bool {
        self.status == StrategyStatus::Paused
    }

    /// 计算策略收益率
    pub fn return_rate(&self) -> Percentage {
        if self.initial_capital.is_zero() {
            return Percentage::ZERO;
        }
        ((self.current_capital - self.initial_capital) / self.initial_capital) * Percentage::from(100)
    }

    /// 计算策略盈亏
    pub fn profit_loss(&self) -> Amount {
        self.current_capital - self.initial_capital
    }

    /// 更新当前资金
    pub fn update_capital(&mut self, new_capital: Amount) {
        self.current_capital = new_capital;
        self.updated_at = Utc::now();
    }

    /// 获取策略运行时长（秒）
    pub fn running_duration(&self) -> Option<i64> {
        self.started_at.map(|start| {
            let end = self.stopped_at.unwrap_or_else(Utc::now);
            end.signed_duration_since(start).num_seconds()
        })
    }

    /// 获取显示用的策略名称
    pub fn display_name(&self) -> String {
        format!("{} ({})", self.name, self.strategy_type)
    }

    /// 更新策略参数
    pub fn update_parameters(&mut self, parameters: serde_json::Value) {
        self.parameters = parameters;
        self.updated_at = Utc::now();
    }

    /// 更新策略状态数据
    pub fn update_state_data(&mut self, state_data: serde_json::Value) {
        self.state_data = state_data;
        self.updated_at = Utc::now();
    }

    /// 执行完整的策略验证
    pub fn validate_complete(&self) -> SigmaXResult<()> {
        // 首先执行基础验证（通过validator derive宏）
        if let Err(validation_errors) = <Self as Validate>::validate(self) {
            return Err(SigmaXError::from(validation_errors));
        }

        // 跨字段验证：交易对不能为空
        if self.trading_pair.base.is_empty() || self.trading_pair.quote.is_empty() {
            return Err(SigmaXError::validation(
                ValidationErrorCode::Required,
                "trading_pair",
                "Trading pair base and quote cannot be empty"
            ));
        }

        // 跨字段验证：当前资金不能为负数
        if self.current_capital < Amount::ZERO {
            return Err(SigmaXError::validation(
                ValidationErrorCode::OutOfRange,
                "current_capital",
                "Current capital cannot be negative"
            ));
        }

        Ok(())
    }
}

// ============================================================================
// 策略性能模型 - 性能分析和统计
// ============================================================================

/// 策略性能统计 - 策略表现的关键指标
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct StrategyPerformance {
    pub strategy_id: StrategyId,

    pub total_return: Percentage,
    pub max_drawdown: Percentage,
    pub sharpe_ratio: Decimal,

    pub total_trades: u64,
    pub winning_trades: u64,
    pub losing_trades: u64,

    pub win_rate: Percentage,
    pub profit_factor: Decimal,

    pub total_profit: Amount,
    pub total_loss: Amount,

    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
}

impl StrategyPerformance {
    /// 创建新的策略性能统计
    pub fn new(strategy_id: StrategyId, start_time: DateTime<Utc>) -> Self {
        Self {
            strategy_id,
            total_return: Percentage::ZERO,
            max_drawdown: Percentage::ZERO,
            sharpe_ratio: Decimal::ZERO,
            total_trades: 0,
            winning_trades: 0,
            losing_trades: 0,
            win_rate: Percentage::ZERO,
            profit_factor: Decimal::ZERO,
            total_profit: Amount::ZERO,
            total_loss: Amount::ZERO,
            start_time,
            end_time: Utc::now(),
        }
    }






}

// ============================================================================
// 验证函数 - 用于模型验证
// ============================================================================

/// 验证金额为正数
fn validate_positive_amount(amount: &Amount) -> Result<(), ValidationError> {
    if *amount <= Amount::ZERO {
        return Err(ValidationError::new("Initial capital must be positive"));
    }
    Ok(())
}

// ============================================================================
// Tests
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal::Decimal;

    #[test]
    fn test_strategy_creation() {
        let strategy = Strategy::new(
            "Test Strategy".to_string(),
            "grid".to_string(),
            TradingPair::new("BTC", "USDT"),
            ExchangeId::Binance,
            Decimal::from(10000),
        );

        assert_eq!(strategy.name, "Test Strategy");
        assert_eq!(strategy.strategy_type, "grid");
        assert_eq!(strategy.status, StrategyStatus::Stopped);
        assert_eq!(strategy.initial_capital, Decimal::from(10000));
        assert_eq!(strategy.current_capital, Decimal::from(10000));
    }

    #[test]
    fn test_strategy_lifecycle() {
        let mut strategy = Strategy::new(
            "Test Strategy".to_string(),
            "grid".to_string(),
            TradingPair::new("BTC", "USDT"),
            ExchangeId::Binance,
            Decimal::from(10000),
        );

        // 初始状态
        assert!(strategy.is_stopped());
        assert!(!strategy.is_running());
        assert!(!strategy.is_paused());

        // 启动策略
        let result = strategy.start();
        assert!(result.is_ok());
        assert!(strategy.is_running());
        assert!(strategy.started_at.is_some());

        // 暂停策略
        let result = strategy.pause();
        assert!(result.is_ok());
        assert!(strategy.is_paused());

        // 恢复策略
        let result = strategy.resume();
        assert!(result.is_ok());
        assert!(strategy.is_running());

        // 停止策略
        let result = strategy.stop();
        assert!(result.is_ok());
        assert!(strategy.is_stopped());
        assert!(strategy.stopped_at.is_some());
    }

    #[test]
    fn test_strategy_profit_calculations() {
        let mut strategy = Strategy::new(
            "Test Strategy".to_string(),
            "grid".to_string(),
            TradingPair::new("BTC", "USDT"),
            ExchangeId::Binance,
            Decimal::from(10000),
        );

        // 更新资金
        strategy.update_capital(Decimal::from(12000));

        // 计算收益率和盈亏
        assert_eq!(strategy.return_rate(), Decimal::from(20)); // 20%
        assert_eq!(strategy.profit_loss(), Decimal::from(2000));

        // 亏损情况
        strategy.update_capital(Decimal::from(8000));
        assert_eq!(strategy.return_rate(), Decimal::from(-20)); // -20%
        assert_eq!(strategy.profit_loss(), Decimal::from(-2000));
    }

    #[test]
    fn test_strategy_display_name() {
        let strategy = Strategy::new(
            "My Grid Bot".to_string(),
            "grid".to_string(),
            TradingPair::new("BTC", "USDT"),
            ExchangeId::Binance,
            Decimal::from(10000),
        );

        assert_eq!(strategy.display_name(), "My Grid Bot (grid)");
    }

    #[test]
    fn test_strategy_validation() {
        let strategy = Strategy::new(
            "Test Strategy".to_string(),
            "grid".to_string(),
            TradingPair::new("BTC", "USDT"),
            ExchangeId::Binance,
            Decimal::from(10000),
        );

        // 正常策略应该通过验证
        let result = strategy.validate_complete();
        assert!(result.is_ok());
    }

    #[test]
    fn test_strategy_performance_creation() {
        let performance = StrategyPerformance::new(
            StrategyId::new(),
            Utc::now(),
        );

        assert_eq!(performance.total_trades, 0);
        assert_eq!(performance.winning_trades, 0);
        assert_eq!(performance.losing_trades, 0);
        assert_eq!(performance.win_rate, Decimal::ZERO);
        assert_eq!(performance.total_profit, Decimal::ZERO);
        assert_eq!(performance.total_loss, Decimal::ZERO);
    }

    #[test]
    fn test_strategy_performance_updates() {
        let mut performance = StrategyPerformance::new(
            StrategyId::new(),
            Utc::now(),
        );

        // 添加盈利交易
        performance.update_trade_stats(Decimal::from(100));
        assert_eq!(performance.total_trades, 1);
        assert_eq!(performance.winning_trades, 1);
        assert_eq!(performance.total_profit, Decimal::from(100));

        // 添加亏损交易
        performance.update_trade_stats(Decimal::from(-50));
        assert_eq!(performance.total_trades, 2);
        assert_eq!(performance.losing_trades, 1);
        assert_eq!(performance.total_loss, Decimal::from(50));

        // 检查胜率
        assert_eq!(performance.win_rate, Decimal::from(50)); // 50%

        // 检查盈亏比
        assert_eq!(performance.profit_factor, Decimal::from(2)); // 100/50 = 2
    }

    #[test]
    fn test_strategy_performance_calculations() {
        let mut performance = StrategyPerformance::new(
            StrategyId::new(),
            Utc::now(),
        );

        // 添加多笔交易
        performance.update_trade_stats(Decimal::from(200)); // 盈利
        performance.update_trade_stats(Decimal::from(100)); // 盈利
        performance.update_trade_stats(Decimal::from(-50)); // 亏损

        // 检查平均值
        assert_eq!(performance.average_profit(), Decimal::from(150)); // (200+100)/2
        assert_eq!(performance.average_loss(), Decimal::from(50));    // 50/1

        // 检查净盈亏
        assert_eq!(performance.net_profit(), Decimal::from(250)); // 300-50

        // 检查是否盈利
        assert!(performance.is_profitable());
    }

    #[test]
    fn test_strategy_performance_grading() {
        let mut performance = StrategyPerformance::new(
            StrategyId::new(),
            Utc::now(),
        );

        // 设置优秀的性能指标
        performance.update_return(Decimal::from(50)); // 50% 收益
        performance.win_rate = Decimal::from(80);     // 80% 胜率
        performance.update_sharpe_ratio(Decimal::from(2)); // 夏普比率 2.0
        performance.update_max_drawdown(Decimal::from(5)); // 5% 最大回撤

        let grade = performance.performance_grade();
        assert!(matches!(grade.as_str(), "A+" | "A"));
    }

    #[test]
    fn test_strategy_invalid_state_transitions() {
        let mut strategy = Strategy::new(
            "Test Strategy".to_string(),
            "grid".to_string(),
            TradingPair::new("BTC", "USDT"),
            ExchangeId::Binance,
            Decimal::from(10000),
        );

        // 尝试停止已经停止的策略
        let result = strategy.stop();
        assert!(result.is_err());

        // 尝试暂停未运行的策略
        let result = strategy.pause();
        assert!(result.is_err());

        // 尝试恢复未暂停的策略
        let result = strategy.resume();
        assert!(result.is_err());
    }
}