//! SigmaX Database Module
//!
//! 现代化的数据库访问层，基于依赖注入和面向接口设计
//!
//! ## 🎯 设计原则
//!
//! - **🏗️ 依赖注入** - 真正的面向接口设计，支持多种实现
//! - **🔧 单一职责** - 每个管理器专注于特定功能
//! - **📊 关注点分离** - 连接、事务、迁移、性能监控分离
//! - **⚡ 高内聚低耦合** - 模块间依赖清晰，易于测试和扩展
//! - **🧪 可测试性** - 完善的Mock支持和测试工具
//!
//! ## 🚀 快速开始
//!
//! ### 使用依赖注入容器（推荐）
//! ```rust
//! use sigmax_database::{create_sqlx_repository_container, DatabaseManager};
//!
//! // 创建数据库管理器
//! let db_manager = DatabaseManager::new(config).await?;
//!
//! // 创建Repository容器
//! let container = create_sqlx_repository_container(db_manager)?;
//!
//! // 获取Repository工厂
//! let order_factory = container.order_factory();
//! let orders = order_factory.find_all_orders().await?;
//! ```
//!
//! ### 直接使用管理器
//! ```rust
//! use sigmax_database::{DatabaseManager, ConnectionManager};
//!
//! let db_manager = DatabaseManager::new(config).await?;
//! let connection_manager = db_manager.connection_manager();
//! ```

// ================================
// 核心模块
// ================================
pub mod managers;        // 数据库管理器模块
pub mod container;       // 依赖注入容器
pub mod repositories;    // Repository实现
pub mod transaction;     // 统一事务管理
pub mod cache;          // 缓存系统
pub mod storage;        // 存储抽象

// ================================
// 管理器导出（推荐使用）
// ================================
pub use managers::{
    DatabaseManager,           // 组合式数据库管理器
    ConnectionManager, ConnectionConfig, ConnectionStats,
    DatabaseTransaction, TransactionStats, LongRunningTransaction,
    MigrationManager, MigrationConfig, MigrationStatus, MigrationInfo,
    PerformanceManager, PerformanceConfig, PerformanceStats,
    TimeSeriesQueryMetrics, AggregatedQueryMetrics, QueryType, SlowQuery,
    DatabaseStats,
};

// ================================
// 依赖注入容器导出（推荐使用）
// ================================
pub use container::{
    // 核心容器
    Container, DefaultContainer, DefaultContainerBuilder, ContainerBuilder, ContainerExt,
    // Repository容器
    RepositoryContainer, RepositoryContainerBuilder,
    repository_container::RepositoryServiceLocator,
    repository_container::create_sqlx_repository_container,
    repository_container::create_mock_repository_container,
    // 工厂trait
    RepositoryFactory, OrderRepositoryFactory, TradeRepositoryFactory,
    StrategyRepositoryFactory, EventRepositoryFactory, RiskRepositoryFactory,
    CompositeRepositoryFactory, RepositoryFactoryBuilder, FactoryConfig,
    // 具体工厂
    SqlxCompositeRepositoryFactory, SqlxFactoryBuilder, SqlxFactoryConfig,
    MockCompositeRepositoryFactory, MockFactoryBuilder, MockFactoryConfig,
    // 服务定位器
    ServiceLocator, ServiceRegistry,
    service_locator::global_service_locator,
    service_locator::initialize_global_service_locator,
};

// 其他组件导出
pub use cache::{Cache, MemoryCache, CacheManager, CacheConfig, CacheStats};
pub use storage::{Storage, MemoryStorage};

// 移除了旧的database模块，请使用新的managers::DatabaseManager

// ================================
// Repository接口导出
// ================================
pub use repositories::traits::{
    // 标准接口
    Repository, PaginationResult, QueryFilter, SortConfig, SortDirection,
    OrderRepository, TradeRepository, StrategyRepository,
    OrderQueryFilter, TradeQueryFilter, StrategyQueryFilter,
    OrderStatistics, TradeStatistics, StrategyStatistics,

    // 其他接口
    EventRepository, EnhancedOrderRepository, Pagination, OrderSort,
    OrderSortBy, OrderRepositoryBuilder,
    RiskRepository, RiskCheckRecord, RiskRuleRecord,
    RiskViolationRecord, RiskQueryFilter, RiskRuleFilter,
    SystemConfigRepository, ConfigStatistics
};

// ================================
// 事务管理导出
// ================================
pub use transaction::{
    // 标准事务管理器
    TransactionManager, TransactionConfig,
    // 高级事务管理功能
    UnitOfWork, UnitOfWorkBuilder, WorkUnit,
    TransactionContext, TransactionScope, TransactionIsolationLevel
};

// ================================
// 具体实现导出（可选）
// ================================
pub use repositories::sqlx::{
    // 标准实现
    OrderRepositoryImpl, TradeRepositoryImpl, StrategyRepositoryImpl,
    // 其他实现
    SqlEnhancedOrderRepository, SqlEventRepository, SqlRiskRepository
};

// ================================
// 性能监控类型已合并到managers模块
// ================================

// ================================
// 可选功能模块
// ================================
#[cfg(feature = "diesel")]
pub mod schema;
#[cfg(feature = "diesel")]
pub mod diesel_models;

// ================================
// 便利类型别名
// ================================
/// 默认的Repository容器类型
pub type DefaultRepositoryContainer = RepositoryContainer;

/// 默认的数据库管理器类型
pub type DefaultDatabaseManager = DatabaseManager;
