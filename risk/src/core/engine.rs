//! 风控引擎 - 核心协调器

use async_trait::async_trait;
use std::sync::Arc;
use sigmax_core::{Order, Balance, SigmaXResult, RiskCheckResult};
use sigmax_interfaces::{RiskController};
use super::{RiskPolicy, RiskResult, RiskContext, RiskMetrics};

/// 风控引擎 - 简单直接的实现
pub struct RiskEngine {
    policies: Vec<Box<dyn RiskPolicy>>,
    metrics: RiskMetrics,
}

impl RiskEngine {
    /// 创建新的风控引擎
    pub fn new(policies: Vec<Box<dyn RiskPolicy>>) -> Self {
        Self {
            policies,
            metrics: RiskMetrics::default(),
        }
    }

    /// 检查订单风险
    pub async fn check_order(&self, order: &Order) -> RiskResult {
        let context = RiskContext::for_order(order.clone());
        
        // 依次执行所有启用的策略
        for policy in &self.policies {
            if !policy.is_enabled() {
                continue;
            }
            
            let result = policy.evaluate(&context).await;
            if !result.is_approved() {
                return result;
            }
        }
        
        RiskResult::approved()
    }

    /// 检查持仓风险
    pub async fn check_position(&self, balances: &[Balance]) -> RiskResult {
        let context = RiskContext::for_balances(balances.to_vec());
        
        for policy in &self.policies {
            if !policy.is_enabled() {
                continue;
            }
            
            let result = policy.evaluate(&context).await;
            if !result.is_approved() {
                return result;
            }
        }
        
        RiskResult::approved()
    }

    /// 获取当前风险指标
    pub fn metrics(&self) -> &RiskMetrics {
        &self.metrics
    }
}

/// 风控引擎构建器
pub struct RiskEngineBuilder {
    policies: Vec<Box<dyn RiskPolicy>>,
}

impl RiskEngineBuilder {
    pub fn new() -> Self {
        Self {
            policies: Vec::new(),
        }
    }

    pub fn add_policy(mut self, policy: Box<dyn RiskPolicy>) -> Self {
        self.policies.push(policy);
        self
    }

    pub fn build(self) -> RiskEngine {
        RiskEngine::new(self.policies)
    }
}

impl Default for RiskEngineBuilder {
    fn default() -> Self {
        Self::new()
    }
}

// 实现接口兼容性
#[async_trait]
impl RiskController for RiskEngine {
    async fn check_order_risk(
        &self,
        order: &Order,
        _context: Option<&sigmax_interfaces::RiskContext>,
    ) -> SigmaXResult<RiskCheckResult> {
        let result = self.check_order(order).await;
        
        if result.is_approved() {
            Ok(RiskCheckResult::pass())
        } else {
            Ok(RiskCheckResult::fail(result.reason().unwrap_or("风控拒绝").to_string()))
        }
    }

    async fn check_position_risk(
        &self,
        balances: &[Balance],
        _context: Option<&sigmax_interfaces::RiskContext>,
    ) -> SigmaXResult<RiskCheckResult> {
        let result = self.check_position(balances).await;
        
        if result.is_approved() {
            Ok(RiskCheckResult::pass())
        } else {
            Ok(RiskCheckResult::fail(result.reason().unwrap_or("持仓风险过高").to_string()))
        }
    }

    async fn reload_rules(&self) -> SigmaXResult<()> {
        // 简化实现：重新加载策略配置
        Ok(())
    }

    async fn get_risk_metrics(&self) -> SigmaXResult<sigmax_core::RiskMetrics> {
        // 返回当前风险指标
        Ok(sigmax_core::RiskMetrics {
            exposure: rust_decimal::Decimal::ZERO,
            leverage: rust_decimal::Decimal::ONE,
            var: None,
            max_drawdown: None,
            sharpe_ratio: None,
            calculated_at: chrono::Utc::now(),
        })
    }

    async fn get_max_order_size(
        &self,
        _trading_pair: &sigmax_core::TradingPair,
        _context: Option<&sigmax_interfaces::RiskContext>,
    ) -> SigmaXResult<sigmax_core::Quantity> {
        // 返回默认最大订单量
        Ok(rust_decimal::Decimal::from(1000))
    }

    // 批量操作 - 简单串行实现
    async fn batch_check_orders(
        &self,
        orders: &[Order],
        context: Option<&sigmax_interfaces::RiskContext>,
    ) -> SigmaXResult<Vec<RiskCheckResult>> {
        let mut results = Vec::new();
        for order in orders {
            let result = self.check_order_risk(order, context).await?;
            results.push(result);
        }
        Ok(results)
    }

    async fn parallel_check_orders(
        &self,
        orders: &[Order],
        context: Option<&sigmax_interfaces::RiskContext>,
    ) -> SigmaXResult<sigmax_interfaces::risk::BatchRiskResult> {
        use tokio::time::Instant;
        let start = Instant::now();
        
        let results = self.batch_check_orders(orders, context).await?;
        let duration = start.elapsed().as_millis() as u64;
        
        let success_count = results.iter().filter(|r| r.passed).count();
        
        Ok(sigmax_interfaces::risk::BatchRiskResult {
            results,
            success_count,
            failure_count: orders.len() - success_count,
            total_processing_time_ms: duration,
            avg_processing_time_ms: if orders.is_empty() { 0.0 } else { duration as f64 / orders.len() as f64 },
            parallelism: 1,
            processed_at: chrono::Utc::now(),
        })
    }

    async fn pre_check_order(
        &self,
        order: &Order,
        _context: Option<&sigmax_interfaces::RiskContext>,
    ) -> SigmaXResult<bool> {
        // 快速预检查
        Ok(order.quantity > rust_decimal::Decimal::ZERO)
    }

    async fn cache_risk_calculation(
        &self,
        _key: &str,
        _context: &sigmax_interfaces::RiskContext,
        _ttl: Option<u64>,
    ) -> SigmaXResult<()> {
        // 暂不实现缓存
        Ok(())
    }

    async fn get_cached_risk_calculation(
        &self,
        _key: &str,
    ) -> SigmaXResult<Option<sigmax_core::RiskMetrics>> {
        // 暂不实现缓存
        Ok(None)
    }
}
