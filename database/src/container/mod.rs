//! 依赖注入容器
//! 
//! 提供真正的依赖注入支持，解决async trait的限制
//! 使用泛型和类型擦除技术实现面向接口的设计

pub mod repository_container;
pub mod service_locator;
pub mod factory_traits;
pub mod sqlx_factory;
pub mod mock_factory;

pub use repository_container::{RepositoryContainer, RepositoryContainerBuilder};
pub use service_locator::{ServiceLocator, ServiceRegistry};
pub use factory_traits::{
    RepositoryFactory, OrderRepositoryFactory, TradeRepositoryFactory,
    StrategyRepositoryFactory, EventRepositoryFactory, RiskRepositoryFactory,
    CompositeRepositoryFactory, RepositoryFactoryBuilder, FactoryConfig,
    MockFactoryConfig, SqlxFactoryConfig
};
pub use sqlx_factory::{
    SqlxCompositeRepositoryFactory, SqlxFactoryBuilder,
    SqlxOrderRepositoryFactory, SqlxTradeRepositoryFactory,
    SqlxStrategyRepositoryFactory, SqlxEventRepositoryFactory, SqlxRiskRepositoryFactory
};
pub use mock_factory::{
    MockCompositeRepositoryFactory, MockFactoryBuilder,
    MockOrderRepositoryFactory, MockTradeRepositoryFactory,
    MockStrategyRepositoryFactory, MockEventRepositoryFactory, MockRiskRepositoryFactory
};

use sigmax_core::SigmaXResult;
use std::sync::Arc;

/// 依赖注入容器接口
/// 
/// 提供类型安全的依赖注入功能
pub trait Container: Send + Sync {
    /// 注册服务
    fn register<T: Send + Sync + 'static>(&mut self, service: T);
    
    /// 注册单例服务
    fn register_singleton<T: Send + Sync + 'static + ?Sized>(&mut self, service: Arc<T>);
    
    /// 解析服务
    fn resolve<T: Send + Sync + 'static + ?Sized>(&self) -> SigmaXResult<Arc<T>>;
    
    /// 检查服务是否已注册
    fn contains<T: Send + Sync + 'static + ?Sized>(&self) -> bool;
}

/// 容器构建器接口
pub trait ContainerBuilder {
    type Container: Container;
    
    /// 构建容器
    fn build(self) -> SigmaXResult<Self::Container>;
}

/// 默认的依赖注入容器实现
#[derive(Default)]
pub struct DefaultContainer {
    services: std::collections::HashMap<std::any::TypeId, Box<dyn std::any::Any + Send + Sync>>,
}

impl DefaultContainer {
    /// 创建新的容器
    pub fn new() -> Self {
        Self {
            services: std::collections::HashMap::new(),
        }
    }
}

impl Container for DefaultContainer {
    fn register<T: Send + Sync + 'static>(&mut self, service: T) {
        let type_id = std::any::TypeId::of::<T>();
        self.services.insert(type_id, Box::new(Arc::new(service)));
    }
    
    fn register_singleton<T: Send + Sync + 'static + ?Sized>(&mut self, service: Arc<T>) {
        let type_id = std::any::TypeId::of::<T>();
        self.services.insert(type_id, Box::new(service));
    }
    
    fn resolve<T: Send + Sync + 'static + ?Sized>(&self) -> SigmaXResult<Arc<T>> {
        let type_id = std::any::TypeId::of::<T>();

        self.services
            .get(&type_id)
            .and_then(|service| service.downcast_ref::<Arc<T>>())
            .cloned()
            .ok_or_else(|| sigmax_core::SigmaXError::internal(
                sigmax_core::InternalErrorCode::UnexpectedState,
                format!("Service not registered: {}", std::any::type_name::<T>())
            ))
    }
    
    fn contains<T: Send + Sync + 'static + ?Sized>(&self) -> bool {
        let type_id = std::any::TypeId::of::<T>();
        self.services.contains_key(&type_id)
    }
}

/// 容器构建器
pub struct DefaultContainerBuilder {
    container: DefaultContainer,
}

impl DefaultContainerBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            container: DefaultContainer::new(),
        }
    }
    
    /// 注册服务
    pub fn with_service<T: Send + Sync + 'static>(mut self, service: T) -> Self {
        self.container.register(service);
        self
    }
    
    /// 注册单例服务
    pub fn with_singleton<T: Send + Sync + 'static>(mut self, service: Arc<T>) -> Self {
        self.container.register_singleton(service);
        self
    }
}

impl Default for DefaultContainerBuilder {
    fn default() -> Self {
        Self::new()
    }
}

impl ContainerBuilder for DefaultContainerBuilder {
    type Container = DefaultContainer;
    
    fn build(self) -> SigmaXResult<Self::Container> {
        Ok(self.container)
    }
}

/// 容器扩展trait，提供便利方法
pub trait ContainerExt: Container {
    /// 尝试解析服务，如果不存在则返回None
    fn try_resolve<T: Send + Sync + 'static>(&self) -> Option<Arc<T>> {
        self.resolve::<T>().ok()
    }
    
    /// 解析或创建服务
    fn resolve_or_create<T, F>(&self, factory: F) -> SigmaXResult<Arc<T>>
    where
        T: Send + Sync + 'static,
        F: FnOnce() -> SigmaXResult<T>,
    {
        if let Ok(service) = self.resolve::<T>() {
            Ok(service)
        } else {
            let service = Arc::new(factory()?);
            // 注意：这里无法修改容器，因为self是不可变引用
            // 在实际使用中，应该在构建时注册所有服务
            Ok(service)
        }
    }
}

impl<T: Container> ContainerExt for T {}

#[cfg(test)]
mod tests {
    use super::*;

    #[derive(Debug)]
    struct TestService {
        value: i32,
    }

    #[tokio::test]
    async fn test_container_basic_operations() {
        let mut container = DefaultContainer::new();
        
        // 注册服务
        let service = TestService { value: 42 };
        container.register(service);
        
        // 解析服务
        let resolved = container.resolve::<TestService>().unwrap();
        assert_eq!(resolved.value, 42);
        
        // 检查服务是否存在
        assert!(container.contains::<TestService>());
        assert!(!container.contains::<String>());
    }

    #[tokio::test]
    async fn test_container_builder() {
        let container = DefaultContainerBuilder::new()
            .with_service(TestService { value: 100 })
            .with_singleton(Arc::new(String::from("test")))
            .build()
            .unwrap();
        
        let test_service = container.resolve::<TestService>().unwrap();
        assert_eq!(test_service.value, 100);
        
        let string_service = container.resolve::<String>().unwrap();
        assert_eq!(*string_service, "test");
    }

    #[tokio::test]
    async fn test_container_extensions() {
        let container = DefaultContainerBuilder::new()
            .with_service(TestService { value: 200 })
            .build()
            .unwrap();
        
        // 测试try_resolve
        let service = container.try_resolve::<TestService>();
        assert!(service.is_some());
        assert_eq!(service.unwrap().value, 200);
        
        let missing = container.try_resolve::<String>();
        assert!(missing.is_none());
    }
}
