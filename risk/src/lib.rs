//! SigmaX 风险管理模块 - 简化重构版
//!
//! 基于 SOLID 原则和 KISS 原则的全新设计
//! 
//! ## 核心设计理念
//! - 简单直接，避免过度设计
//! - 职责单一，模块边界清晰
//! - 易于测试和扩展

pub mod core;
pub mod policies;
pub mod monitor;
pub mod analysis;

// 核心导出
pub use core::{
    RiskEngine, RiskEngineBuilder,
    RiskPolicy, RiskResult, RiskLevel,
    RiskMetrics, RiskContext,
};

// 策略导出
pub use policies::{
    OrderRiskPolicy, OrderRiskConfig,
    PositionRiskPolicy, PositionRiskConfig,
    MarketRiskPolicy, MarketRiskConfig,
};

// 监控导出
pub use monitor::{
    RiskMonitor, MonitorConfig,
    AlertService, AlertLevel,
};

// 分析导出（可选功能）
pub use analysis::{
    RiskAnalyzer, AnalysisResult,
};

/// 创建默认的风控引擎
pub fn create_default_engine() -> RiskEngine {
    RiskEngineBuilder::new()
        .add_policy(Box::new(OrderRiskPolicy::default()))
        .add_policy(Box::new(PositionRiskPolicy::default()))
        .add_policy(Box::new(MarketRiskPolicy::default()))
        .build()
}

/// 快速风控检查
pub async fn quick_check_order(order: &sigmax_core::Order) -> RiskResult {
    let engine = create_default_engine();
    engine.check_order(order).await
}

#[cfg(test)]
mod test_module;