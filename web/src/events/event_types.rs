//! Web模块事件类型定义
//!
//! 扩展core模块的事件类型，添加Web模块特有的事件

use serde::{Serialize, Deserialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};

/// Web模块特有的事件类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub enum WebEvent {
    // 策略管理事件
    StrategyCreated(StrategyEvent),
    StrategyUpdated(StrategyEvent),
    StrategyDeleted(StrategyEvent),
    StrategyStarted(StrategyEvent),
    StrategyStopped(StrategyEvent),
    
    // 投资组合事件
    PortfolioCreated(PortfolioEvent),
    PortfolioUpdated(PortfolioEvent),
    BalanceChanged(BalanceEvent),
    
    // 风险事件
    RiskCheckCompleted(RiskEvent),
    RiskAlertTriggered(RiskAlertEvent),
    RiskLimitExceeded(RiskLimitEvent),
    
    // 订单事件
    OrderCreated(OrderEvent),
    OrderUpdated(OrderEvent),
    OrderExecuted(OrderEvent),
    
    // 系统事件
    UserLogin(UserEvent),
    UserLogout(UserEvent),
    ApiRequest(ApiRequestEvent),
    ErrorOccurred(ErrorEvent),
}

/// 策略事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyEvent {
    pub strategy_id: Uuid,
    pub name: String,
    pub action: String,
    pub timestamp: DateTime<Utc>,
    pub user_id: Option<String>,
    pub metadata: serde_json::Value,
}

/// 投资组合事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortfolioEvent {
    pub portfolio_id: Uuid,
    pub strategy_id: Uuid,
    pub action: String,
    pub timestamp: DateTime<Utc>,
    pub changes: serde_json::Value,
}

/// 余额变更事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BalanceEvent {
    pub portfolio_id: Uuid,
    pub currency: String,
    pub old_balance: f64,
    pub new_balance: f64,
    pub change_amount: f64,
    pub timestamp: DateTime<Utc>,
    pub reason: String,
}

/// 风险事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskEvent {
    pub strategy_id: Uuid,
    pub risk_score: f64,
    pub risk_level: String,
    pub timestamp: DateTime<Utc>,
    pub details: serde_json::Value,
}

/// 风险警报事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAlertEvent {
    pub alert_id: Uuid,
    pub strategy_id: Uuid,
    pub alert_type: String,
    pub severity: String,
    pub message: String,
    pub timestamp: DateTime<Utc>,
}

/// 风险限制事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskLimitEvent {
    pub strategy_id: Uuid,
    pub limit_type: String,
    pub limit_value: f64,
    pub current_value: f64,
    pub timestamp: DateTime<Utc>,
}

/// 订单事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderEvent {
    pub order_id: Uuid,
    pub strategy_id: Uuid,
    pub action: String,
    pub timestamp: DateTime<Utc>,
    pub order_data: serde_json::Value,
}

/// 用户事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserEvent {
    pub user_id: String,
    pub action: String,
    pub timestamp: DateTime<Utc>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
}

/// API请求事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiRequestEvent {
    pub request_id: Uuid,
    pub endpoint: String,
    pub method: String,
    pub user_id: Option<String>,
    pub timestamp: DateTime<Utc>,
    pub response_time_ms: u64,
    pub status_code: u16,
}

/// 错误事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorEvent {
    pub error_id: Uuid,
    pub error_type: String,
    pub message: String,
    pub stack_trace: Option<String>,
    pub timestamp: DateTime<Utc>,
    pub context: serde_json::Value,
}
