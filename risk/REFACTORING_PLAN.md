# Risk 模块重构计划

## 🎯 重构目标

基于软件设计最佳实践，将 Risk 模块重构为更简洁、更易维护的架构。

## 📋 设计原则

### 1. **SOLID 原则**
- **S**ingle Responsibility - 每个模块只负责一个职责
- **O**pen/Closed - 对扩展开放，对修改关闭
- **L**iskov Substitution - 子类型必须能够替换其基类型
- **I**nterface Segregation - 客户端不应依赖它不需要的接口
- **D**ependency Inversion - 依赖抽象而非具体实现

### 2. **KISS 原则** (Keep It Simple, Stupid)
- 优先选择简单的解决方案
- 避免过度设计

### 3. **DRY 原则** (Don't Repeat Yourself)
- 消除代码重复
- 提取共同逻辑

## 🏗️ 新架构设计

```
risk/
├── src/
│   ├── lib.rs              # 模块导出
│   ├── core/               # 核心功能（必需）
│   │   ├── mod.rs
│   │   ├── engine.rs       # 风控引擎
│   │   ├── policy.rs       # 策略接口定义
│   │   └── metrics.rs      # 指标计算
│   ├── policies/           # 具体策略实现
│   │   ├── mod.rs
│   │   ├── order.rs        # 订单风控策略
│   │   ├── position.rs     # 持仓风控策略
│   │   └── market.rs       # 市场风控策略
│   ├── monitor/            # 监控和预警
│   │   ├── mod.rs
│   │   ├── monitor.rs      # 实时监控
│   │   └── alert.rs        # 预警系统
│   └── analysis/           # 分析功能（可选）
│       ├── mod.rs
│       ├── analyzer.rs     # 风险分析
│       └── reporter.rs     # 报告生成
```

## 📝 重构步骤

### 第一阶段：简化核心功能

#### 1.1 创建核心引擎
```rust
// src/core/engine.rs
pub struct RiskEngine {
    policies: Vec<Box<dyn RiskPolicy>>,
    monitor: RiskMonitor,
}

impl RiskEngine {
    pub async fn check_order(&self, order: &Order) -> RiskResult {
        // 简单直接的实现
        for policy in &self.policies {
            let result = policy.evaluate_order(order).await?;
            if !result.is_approved() {
                return result;
            }
        }
        RiskResult::approved()
    }
}
```

#### 1.2 定义清晰的策略接口
```rust
// src/core/policy.rs
#[async_trait]
pub trait RiskPolicy: Send + Sync {
    /// 评估订单风险
    async fn evaluate_order(&self, order: &Order) -> RiskResult;
    
    /// 评估持仓风险
    async fn evaluate_position(&self, position: &Position) -> RiskResult;
    
    /// 策略名称
    fn name(&self) -> &str;
    
    /// 是否启用
    fn is_enabled(&self) -> bool { true }
}
```

### 第二阶段：实现具体策略

#### 2.1 订单风控策略
```rust
// src/policies/order.rs
pub struct OrderRiskPolicy {
    max_order_size: Decimal,
    max_order_value: Decimal,
}

#[async_trait]
impl RiskPolicy for OrderRiskPolicy {
    async fn evaluate_order(&self, order: &Order) -> RiskResult {
        // 清晰的业务逻辑
        if order.quantity > self.max_order_size {
            return RiskResult::rejected("订单数量超过限制");
        }
        
        if order.value() > self.max_order_value {
            return RiskResult::rejected("订单价值超过限制");
        }
        
        RiskResult::approved()
    }
    
    async fn evaluate_position(&self, _position: &Position) -> RiskResult {
        // 此策略不评估持仓
        RiskResult::approved()
    }
    
    fn name(&self) -> &str {
        "OrderRiskPolicy"
    }
}
```

### 第三阶段：简化监控系统

#### 3.1 统一监控接口
```rust
// src/monitor/monitor.rs
pub struct RiskMonitor {
    alert_service: AlertService,
    metrics_collector: MetricsCollector,
}

impl RiskMonitor {
    pub async fn monitor_portfolio(&self, portfolio: &Portfolio) -> MonitorResult {
        let metrics = self.metrics_collector.collect(portfolio).await?;
        
        if metrics.risk_level > RiskLevel::High {
            self.alert_service.send_alert(
                AlertLevel::Warning,
                format!("高风险警告: {:?}", metrics)
            ).await?;
        }
        
        Ok(metrics)
    }
}
```

### 第四阶段：可选的分析功能

#### 4.1 独立的分析模块
```rust
// src/analysis/analyzer.rs
pub struct RiskAnalyzer {
    // 仅包含分析所需的依赖
}

impl RiskAnalyzer {
    pub async fn analyze_historical_risk(
        &self,
        data: &[HistoricalData]
    ) -> AnalysisResult {
        // 独立的分析逻辑
    }
}
```

## 🔄 迁移策略

### 1. **并行开发**
- 在新结构中逐步实现功能
- 保持旧代码可用

### 2. **接口适配**
```rust
// 临时适配器
pub struct LegacyRiskControllerAdapter {
    engine: RiskEngine,
}

impl RiskController for LegacyRiskControllerAdapter {
    // 适配旧接口到新实现
}
```

### 3. **逐步切换**
- 先在测试环境验证
- 逐个模块切换到新实现
- 最后删除旧代码

## 📊 预期效果

| 指标 | 当前 | 重构后 | 改进 |
|------|------|--------|------|
| **代码行数** | ~5000 | ~2000 | -60% |
| **模块数量** | 15+ | 8 | -47% |
| **测试覆盖率** | 40% | 80% | +100% |
| **圈复杂度** | 高 | 低 | ⬇️ |
| **耦合度** | 高 | 低 | ⬇️ |

## ✅ 成功标准

1. **功能完整性** - 所有现有功能都能正常工作
2. **性能不降低** - 响应时间保持或提升
3. **可测试性提升** - 单元测试覆盖率达到 80%
4. **可维护性提升** - 新功能添加时间减少 50%
5. **文档完善** - 每个公开 API 都有清晰文档

## 🚀 实施时间表

| 阶段 | 任务 | 时间 | 负责人 |
|------|------|------|--------|
| 第一周 | 核心引擎开发 | 5天 | - |
| 第二周 | 策略实现 | 5天 | - |
| 第三周 | 监控系统 | 3天 | - |
| 第三周 | 测试和文档 | 2天 | - |
| 第四周 | 迁移和验证 | 5天 | - |

## 🎯 关键决策

1. **删除 unified_engine** - 过度抽象，直接使用 RiskEngine
2. **合并 control 和 analytics** - 按功能而非技术分层
3. **简化类型系统** - 使用标准类型，避免过度包装
4. **减少工厂模式** - 只在必要时使用依赖注入

## 📚 参考资料

- Clean Architecture by Robert C. Martin
- Domain-Driven Design by Eric Evans
- Refactoring by Martin Fowler
- The Pragmatic Programmer by David Thomas & Andrew Hunt
